{"__meta": {"id": "X080ae19cae5b68b3b9cf1650a9695e73", "datetime": "2025-07-30 07:29:41", "utime": **********.985314, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.166625, "end": **********.985342, "duration": 0.8187170028686523, "duration_str": "819ms", "measures": [{"label": "Booting", "start": **********.166625, "relative_start": 0, "end": **********.914209, "relative_end": **********.914209, "duration": 0.7475838661193848, "duration_str": "748ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.914224, "relative_start": 0.****************, "end": **********.985344, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "71.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "K69cLDwMS22OV8gYKWkCc6yKrDlnQ08GZDzfdcYJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-397329935 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-397329935\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-618707967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-618707967\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-604827966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-604827966\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2130086647 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130086647\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-570768465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570768465\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1834174677 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:29:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktKU2J0aUtDcEtJblR5QlVORkg0Unc9PSIsInZhbHVlIjoidHpoUzlLRW5La1hQUm5wbUJhQWZSUFEvOHhhaHRyTUZRS1BTUTZZVEN0L3lCZjc2V2pmMGlQZkFWSmtOVU03M1RualAxL0IrK3N0d2ovTmg5d255MmVkU1NoR1BCd0hmU0MzZnJYZzFJdVg4MzNrVFNXVlVDYjg2YmV6aXIxWXNXWm9vOXNEU2lmM0ExeXorYzZrcE8ybnlXUllpamRpVVFZYTRWMFVCWFhjR2QrVllQL3VOWjUrbEtpQWwrWHNLdEkwYkJQaTNRejJHSndQZnRZOVdHVUlaMVRHSnMySGpFZGhFbG1pRmxiRUQ1ZVNvcWcvQzFqby83Nk1rTDdFSzNPQnNkc3MyR25EbU9JNFN6NVZBbFh6bE5MYS9ad3hsbzdpMGJDZUpZWnoxMHlRU1Q3YWJNTnlFS0tnMU1tWHZ3eStuSVFhV1RidDdRTXd5bERhVURNbjhMbmwxeGptSit2cE9CMldrYkl3elpIcTRHazczNHh4VkxsVWVpdE5zSFIvQVBlbFU5eXBjWjVsUERTaVhDS1poNitqazZDdTU5VVl1WDdCUTFEK3FGclQzRUk5T005V2xubXF2RHJmWjM4bG9aRVBJcXk5UllkMXQwNzBpS3JUWjJ6UlFNQ0VraTVuMlVhWmd6SXpYSjA2Ty9wOTJ6aDFRc3ZHcmFlVWkiLCJtYWMiOiI0MGVhYmE2ZjliMDA5MTc3MTMxMTNiNTY5YWVhZDUyZWI0ZGU4YTgxMmUxOTMxNzc4MWZhMTc5MGJmMjdiNGE3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:29:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik14b0lXUnYwSFpqcmFFREI2SDBYZmc9PSIsInZhbHVlIjoiT05IcGVoWTIrbXlESjJJUTJ3dDFWZzA5amQ5Q1ozdm0vRjQvZVpjMW5YeFNlcFArclhsbFBuOW9nVnhyT2JHZ0hoUXVwM1JYeURRSy8xMlExaUloQlFoSlhhZVluK2xmR1BEVlNpT2JOTE1Feld5VFNkR1lxMndBWXFIM1BzQndpTW81N2JYN2o0d3kvQldRVW5qRHpoZnVaMWIydEZLdmZ3bXE2MVhqQkpiVC9BQTY0MTJzeERmbGQ4bThhSnBJaUpLU0tYWXVucXo1TW5RUzVCOG9ZSGxJd3VkV2NOZHVNTnZOWDl2YS8wcDE1eGxDOXBvRjVUeFk3Zjh0c1VLOVNnSTB1Vlh5czBEM2twUXNqdXJnUExlcU9rM0lUZmxremZoc2dBRUZ4ZFRBbHFSWnc1ZkdjRmEzUzlvTlVhRkc0ZGNWVkJpOVdncEZuby9nZnFVbWg2T2YxZHlYbzJMMlhyQmNONFluNVBKaGs2elRhaDUxRmRHSEZFUTZVWVpSbHczMFJpU2FRNFdEa3B6bmdCMllPeVFDRWZ5L0o0T2dueHlNay9xdzlMSWFpcjM2ckJWSGFiaWIwejR2azFhQjdnQzQ4ZXk3UXlab29ETE56WWJrRlRwQWszRFJ6SG13a2QzMm11V3AxRGJQMHRYZHhqSUpJOWJmcVlGSnMvUm4iLCJtYWMiOiJiMjlmZjE1OTgyNzlkNGRmZDYzODJhM2I4MGMxNzNmMWI4NjU0OTdmNDlkY2RhNjQwNGE5ODJlMDA2ZDMxZTcwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:29:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktKU2J0aUtDcEtJblR5QlVORkg0Unc9PSIsInZhbHVlIjoidHpoUzlLRW5La1hQUm5wbUJhQWZSUFEvOHhhaHRyTUZRS1BTUTZZVEN0L3lCZjc2V2pmMGlQZkFWSmtOVU03M1RualAxL0IrK3N0d2ovTmg5d255MmVkU1NoR1BCd0hmU0MzZnJYZzFJdVg4MzNrVFNXVlVDYjg2YmV6aXIxWXNXWm9vOXNEU2lmM0ExeXorYzZrcE8ybnlXUllpamRpVVFZYTRWMFVCWFhjR2QrVllQL3VOWjUrbEtpQWwrWHNLdEkwYkJQaTNRejJHSndQZnRZOVdHVUlaMVRHSnMySGpFZGhFbG1pRmxiRUQ1ZVNvcWcvQzFqby83Nk1rTDdFSzNPQnNkc3MyR25EbU9JNFN6NVZBbFh6bE5MYS9ad3hsbzdpMGJDZUpZWnoxMHlRU1Q3YWJNTnlFS0tnMU1tWHZ3eStuSVFhV1RidDdRTXd5bERhVURNbjhMbmwxeGptSit2cE9CMldrYkl3elpIcTRHazczNHh4VkxsVWVpdE5zSFIvQVBlbFU5eXBjWjVsUERTaVhDS1poNitqazZDdTU5VVl1WDdCUTFEK3FGclQzRUk5T005V2xubXF2RHJmWjM4bG9aRVBJcXk5UllkMXQwNzBpS3JUWjJ6UlFNQ0VraTVuMlVhWmd6SXpYSjA2Ty9wOTJ6aDFRc3ZHcmFlVWkiLCJtYWMiOiI0MGVhYmE2ZjliMDA5MTc3MTMxMTNiNTY5YWVhZDUyZWI0ZGU4YTgxMmUxOTMxNzc4MWZhMTc5MGJmMjdiNGE3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:29:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik14b0lXUnYwSFpqcmFFREI2SDBYZmc9PSIsInZhbHVlIjoiT05IcGVoWTIrbXlESjJJUTJ3dDFWZzA5amQ5Q1ozdm0vRjQvZVpjMW5YeFNlcFArclhsbFBuOW9nVnhyT2JHZ0hoUXVwM1JYeURRSy8xMlExaUloQlFoSlhhZVluK2xmR1BEVlNpT2JOTE1Feld5VFNkR1lxMndBWXFIM1BzQndpTW81N2JYN2o0d3kvQldRVW5qRHpoZnVaMWIydEZLdmZ3bXE2MVhqQkpiVC9BQTY0MTJzeERmbGQ4bThhSnBJaUpLU0tYWXVucXo1TW5RUzVCOG9ZSGxJd3VkV2NOZHVNTnZOWDl2YS8wcDE1eGxDOXBvRjVUeFk3Zjh0c1VLOVNnSTB1Vlh5czBEM2twUXNqdXJnUExlcU9rM0lUZmxremZoc2dBRUZ4ZFRBbHFSWnc1ZkdjRmEzUzlvTlVhRkc0ZGNWVkJpOVdncEZuby9nZnFVbWg2T2YxZHlYbzJMMlhyQmNONFluNVBKaGs2elRhaDUxRmRHSEZFUTZVWVpSbHczMFJpU2FRNFdEa3B6bmdCMllPeVFDRWZ5L0o0T2dueHlNay9xdzlMSWFpcjM2ckJWSGFiaWIwejR2azFhQjdnQzQ4ZXk3UXlab29ETE56WWJrRlRwQWszRFJ6SG13a2QzMm11V3AxRGJQMHRYZHhqSUpJOWJmcVlGSnMvUm4iLCJtYWMiOiJiMjlmZjE1OTgyNzlkNGRmZDYzODJhM2I4MGMxNzNmMWI4NjU0OTdmNDlkY2RhNjQwNGE5ODJlMDA2ZDMxZTcwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:29:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834174677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1060403395 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K69cLDwMS22OV8gYKWkCc6yKrDlnQ08GZDzfdcYJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060403395\", {\"maxDepth\":0})</script>\n"}}