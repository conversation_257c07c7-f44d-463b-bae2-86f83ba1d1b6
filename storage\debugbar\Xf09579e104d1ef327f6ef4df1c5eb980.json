{"__meta": {"id": "Xf09579e104d1ef327f6ef4df1c5eb980", "datetime": "2025-07-30 06:11:44", "utime": **********.383572, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855903.2589, "end": **********.383672, "duration": 1.124772071838379, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753855903.2589, "relative_start": 0, "end": **********.30738, "relative_end": **********.30738, "duration": 1.0484800338745117, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307413, "relative_start": 1.****************, "end": **********.383675, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "76.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hlrjguhdidADVAccf4a026jFPvA8GjxV11lsK2Jd", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-749474494 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-749474494\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-375343433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-375343433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-649466334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-649466334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-282596420 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282596420\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1375375446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1375375446\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-52618947 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:11:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRqeUdZQTZmUVplTVN1RUlMVjd4WWc9PSIsInZhbHVlIjoiVExYZktSL2JCNGNmK0JDRkdOdUtGUnNXRElMUGlBQVo1WTBJY3hVSjAxVHRyQ1E0ZzZvSXI3NzgxYitNbzFub2hZS21Ed1hvVGdEWXFubzVwUnZOdUhGTlRuM2JGRnU0ZVQyN1JIWm42T2gycER1eUx5S3NmK3NVSkIrV05EZjN4S0N4RGoydUtWUjlXMmhkYmNGV2ZhYUlMS3FuV1MyVjRKV3FyMU9KM3JqdjFGYTNEZFVWQk9lalcrM09hODg5S3BmWU5GUzQzWFBYSGd6UW8rbXFXQTRvSEt3WHVlWDR0Mm1xaFpNYy9mckt3TzdoWllrSlZZNWFJUno2eDlXNVRpSDRPZWNxdzl4anBTaHQ5K0pRekF2Tm5VWkd0SzNuakdlN0RIMGJ2ZmJqUXAxWStHS2c0UElQNmd3bnluNHk3c3RweEJCbGY0VW9OTUQrSHpoelo3TStWYlhIZ0pPYVMyRkc4VGErZEpSZ3h6UmZ0Q2dNUmJJbG1wci9yVng1Q0lNQ1ZzeWtzM2UxWjFuYnYyYnN0TG1VRy9ZRTFndWNQcFVoa2ZjdldIaWRNdzNkNkM2UUNKOEZ2d1ZudDFidi94SEUwVGFBM1doTDdiMVZzQmlUMGxoY0ovUkVUb1Jud2dvRE9wZDYyVVN4K0U2WjFreXV4MTNRbGp6MFNoN2EiLCJtYWMiOiIwZWYwMzFkZWUzYmRkMjQ3OWM5N2EyNDU3ZjA2NDM4NzQ5MjMwZjdkNmZmY2JmNmE4M2QyMjljZDU5N2JjYWQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:11:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVYQnplZTB3TnBMYmx4aTdmVU1OS0E9PSIsInZhbHVlIjoienpkb051YndYdTFQeklqaHN3SXpxVnBaUnFNS1l6MmlmeFFZcVBPdVlrZWZjMHBsUHRXWlJYWnNudDdkTktWcWtjWGRWaHY3RW9la0pqdTVOejdxSnZuekNvSTFVeC9vdVhCRm9XOFBDMXdkRGR4UlAwdWE4c1R4RURvNXZkZm8wemJGblZnRUwvd0tEU2U3aENGR1VVWDZrbjh6dUxpN0FHWUlDUTlWbE1EenA1WWlhcW1QNjhtUUdpeFJURHp3a3B3VjUrQWwyaG10eGIwcWZGTnZSbHU0SEt0aFV5SFZQZXQ5V0t4NFRmYnZ3ZEVUd2ZwYkFwRmVkUFU3WVRPQStEbDJWQTFhZ3ZUcklrRjFmdXkxREhOcHJNWTFuRVdIVHd0TnRFL0RhN0lqYncxc3kybXlkcWZET0hLalF4Q2pJU0xLR001bWd4VnJyMGpKU3FPeTNlNGJTTjNYODNHK3hIS1RMUDBEaUcybVYydFVFMUd0bm1qN0I2NUg2QVBLSmdRMlZaVWNaMzVnaXl4VTF1a1ZSK3BPT2ZBRC9rSnFCYlN0K0pCZlhmVjl5cmxZSXVFdDRKWFJCQXBjVEZacjQ0NXVPT1ovaG1WcXZ6L0NBVHd1QmR0SHVOdmpOV21UTkZpbFFNeC9pSzBnSmtmaGF4MGR0U1Jqb1E5U0h6WnUiLCJtYWMiOiJiNGM4NmEzNjJhNTlkOTFkOTI2NDc2NWJmYmQxM2Y0Zjg5MjgzOWViZGI2M2Q1MmNjODgxMGIxNWEzOGRiYjY5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:11:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRqeUdZQTZmUVplTVN1RUlMVjd4WWc9PSIsInZhbHVlIjoiVExYZktSL2JCNGNmK0JDRkdOdUtGUnNXRElMUGlBQVo1WTBJY3hVSjAxVHRyQ1E0ZzZvSXI3NzgxYitNbzFub2hZS21Ed1hvVGdEWXFubzVwUnZOdUhGTlRuM2JGRnU0ZVQyN1JIWm42T2gycER1eUx5S3NmK3NVSkIrV05EZjN4S0N4RGoydUtWUjlXMmhkYmNGV2ZhYUlMS3FuV1MyVjRKV3FyMU9KM3JqdjFGYTNEZFVWQk9lalcrM09hODg5S3BmWU5GUzQzWFBYSGd6UW8rbXFXQTRvSEt3WHVlWDR0Mm1xaFpNYy9mckt3TzdoWllrSlZZNWFJUno2eDlXNVRpSDRPZWNxdzl4anBTaHQ5K0pRekF2Tm5VWkd0SzNuakdlN0RIMGJ2ZmJqUXAxWStHS2c0UElQNmd3bnluNHk3c3RweEJCbGY0VW9OTUQrSHpoelo3TStWYlhIZ0pPYVMyRkc4VGErZEpSZ3h6UmZ0Q2dNUmJJbG1wci9yVng1Q0lNQ1ZzeWtzM2UxWjFuYnYyYnN0TG1VRy9ZRTFndWNQcFVoa2ZjdldIaWRNdzNkNkM2UUNKOEZ2d1ZudDFidi94SEUwVGFBM1doTDdiMVZzQmlUMGxoY0ovUkVUb1Jud2dvRE9wZDYyVVN4K0U2WjFreXV4MTNRbGp6MFNoN2EiLCJtYWMiOiIwZWYwMzFkZWUzYmRkMjQ3OWM5N2EyNDU3ZjA2NDM4NzQ5MjMwZjdkNmZmY2JmNmE4M2QyMjljZDU5N2JjYWQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:11:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVYQnplZTB3TnBMYmx4aTdmVU1OS0E9PSIsInZhbHVlIjoienpkb051YndYdTFQeklqaHN3SXpxVnBaUnFNS1l6MmlmeFFZcVBPdVlrZWZjMHBsUHRXWlJYWnNudDdkTktWcWtjWGRWaHY3RW9la0pqdTVOejdxSnZuekNvSTFVeC9vdVhCRm9XOFBDMXdkRGR4UlAwdWE4c1R4RURvNXZkZm8wemJGblZnRUwvd0tEU2U3aENGR1VVWDZrbjh6dUxpN0FHWUlDUTlWbE1EenA1WWlhcW1QNjhtUUdpeFJURHp3a3B3VjUrQWwyaG10eGIwcWZGTnZSbHU0SEt0aFV5SFZQZXQ5V0t4NFRmYnZ3ZEVUd2ZwYkFwRmVkUFU3WVRPQStEbDJWQTFhZ3ZUcklrRjFmdXkxREhOcHJNWTFuRVdIVHd0TnRFL0RhN0lqYncxc3kybXlkcWZET0hLalF4Q2pJU0xLR001bWd4VnJyMGpKU3FPeTNlNGJTTjNYODNHK3hIS1RMUDBEaUcybVYydFVFMUd0bm1qN0I2NUg2QVBLSmdRMlZaVWNaMzVnaXl4VTF1a1ZSK3BPT2ZBRC9rSnFCYlN0K0pCZlhmVjl5cmxZSXVFdDRKWFJCQXBjVEZacjQ0NXVPT1ovaG1WcXZ6L0NBVHd1QmR0SHVOdmpOV21UTkZpbFFNeC9pSzBnSmtmaGF4MGR0U1Jqb1E5U0h6WnUiLCJtYWMiOiJiNGM4NmEzNjJhNTlkOTFkOTI2NDc2NWJmYmQxM2Y0Zjg5MjgzOWViZGI2M2Q1MmNjODgxMGIxNWEzOGRiYjY5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:11:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52618947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-22150819 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hlrjguhdidADVAccf4a026jFPvA8GjxV11lsK2Jd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22150819\", {\"maxDepth\":0})</script>\n"}}