{"__meta": {"id": "X8cdb03bedf90a879713b3224478823c8", "datetime": "2025-07-30 05:42:35", "utime": **********.441624, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854154.328619, "end": **********.441657, "duration": 1.1130380630493164, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1753854154.328619, "relative_start": 0, "end": **********.362254, "relative_end": **********.362254, "duration": 1.033634901046753, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.362283, "relative_start": 1.****************, "end": **********.441661, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "79.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NL3B8uuULZ1mQhYb649ZYYNsEwNXazK49MTemwY2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1559572153 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1559572153\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-74210568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-74210568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-662780821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-662780821\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-716673005 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716673005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1628943186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1628943186\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-168955596 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:42:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijk3YUQ4bFFEbmc3VGlIYnpXK3NjVGc9PSIsInZhbHVlIjoiV0diWVl6UkFmd0FaNHlzMGZPbWtmdE5XcTU5YWw3endJL29BWEE5NVZHRWRLcDlQVW55QkJTWG9IOEVBZDBFcHYxcG9qcU41cGJoN3BiQUROZmZZU1lIWUVuMldJYko4SXdYZVFsYjg1MEFTd1Z3b0Z2Q0FTRXpmNWErODlkSkF6TVdGTVcweDlKWFQwNmtscDROdkIzcEV2TDhvZXJXLzR5UDUvM002eXhzbGl5dUJ4ajZpKzVHVi9lcnJLNzJlRGExWExUMVR6QjRrMGk0TmpRUXJydHVrby9MT2F4V3dYQlZYbE5qTGwyVnVZSkJ4Y2xRcVFTOGxuNGRjWUdZK0NkcFR3U3dlQWNEN2RwYnhGUjFjbkVaYkp0WGxTZHY2TjVScTdpL3VrZnMxaTRQL2NobFd3b1hkUG1qNDZrQjE3djJGbEMyYlBUR1Q3dGdJVEE5VzNNY3FTWGliTzlhRnpNVW5Fd2JmcWRRUm1BMm4zUUk5K1FuZmlsRFMwY0pKdEJzNkN3RWlrbnRJaUx2ZzRLN0xEL2x5eDZ2SXRHOStXaEd4RzZmUGNFWllseW1KSDVDZGtUTGMvQ2J4VFlTTTVUMVptMDVacnVZNjZ0WFRsc3lhTlBKeWxiMGFHSDUySDhVSVl3aU1IOVBVSmpXcTY4ZXFjOEVLOEMzT0t5Tm0iLCJtYWMiOiIyMzQ5MmI0ZTEzZDc5MGY1ZDhmYWJjMjk3MTc2YmJlM2RjNTJjMzY4MjJmYjc5MmIyZDViY2IxNmEwN2NlOTdjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:42:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJreVZrWTlzUFlxczNuc1cvd3ptY3c9PSIsInZhbHVlIjoiZ05zdkRabXRXdXVGdDRMV0Z4NTJpcm1OL0k2VWY1bHZNRko1ZTVqU3ZXVEhjTmhtVXoxMnV5TU1PendvMFZCQW1vZ3VZNE16cUNmSjlVcm9MTmNHQTNzempkUHB3eVp3YUVSNk9IeEs4RTJoNVVnWTF2TStYL1lwYUo4R21qZzFZRkRORTViWmEzTjJMUjMxNkdBV01oWFgrWmFaMGtuOG9NWnhvV2dZVTQ2WGMyWjR5QmtlUy9uTUJydng5K2xkdFJ4Yy9BSmEwZ3NQdlZvclpWM0JianZhWS9iemF0M01WazhQMVBydWlsbVdNWTJVY0lYUWl5THF2MWN5WjY0WWVxbGZ2SjEzS3VETkYvcFhzQkd4MkxGM2Y2THMxKzV3aWVYTDk2YUswWlVNR2JteUZ2V2s4Ym0xYm9zOU5NR1FZMThFd1RPQkVNbUVmbkE1RkNGTW5JNXhWS210RjJCdm96Ty9Gb2tUZVJ3eTRac2RmclcwSnovU1JzcWsxZzcwUkZlbThFQi9SSjdPZXpFSEdaSld0anpXejVNakh6RVkwaHFVemlUNG0zM1owb0VxT2pKbi9FZEJBbmpnRTlsN1JKWFZoa1NOMnhEc1pSeHpsbzZZUEFuNHVFbmVzY0lQYjgzOGpMZHNKb2dBRWVYVW1PQVE2MG1yUWoyWWdpT0QiLCJtYWMiOiIwYjczODk1YzUzY2UzNDY5MjBlNjQ5ZjUzNjBiNzExYjZlM2RjMzk2YzRkMTM5N2E3ZmJlNWM4OTkyMjQxOWNjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:42:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijk3YUQ4bFFEbmc3VGlIYnpXK3NjVGc9PSIsInZhbHVlIjoiV0diWVl6UkFmd0FaNHlzMGZPbWtmdE5XcTU5YWw3endJL29BWEE5NVZHRWRLcDlQVW55QkJTWG9IOEVBZDBFcHYxcG9qcU41cGJoN3BiQUROZmZZU1lIWUVuMldJYko4SXdYZVFsYjg1MEFTd1Z3b0Z2Q0FTRXpmNWErODlkSkF6TVdGTVcweDlKWFQwNmtscDROdkIzcEV2TDhvZXJXLzR5UDUvM002eXhzbGl5dUJ4ajZpKzVHVi9lcnJLNzJlRGExWExUMVR6QjRrMGk0TmpRUXJydHVrby9MT2F4V3dYQlZYbE5qTGwyVnVZSkJ4Y2xRcVFTOGxuNGRjWUdZK0NkcFR3U3dlQWNEN2RwYnhGUjFjbkVaYkp0WGxTZHY2TjVScTdpL3VrZnMxaTRQL2NobFd3b1hkUG1qNDZrQjE3djJGbEMyYlBUR1Q3dGdJVEE5VzNNY3FTWGliTzlhRnpNVW5Fd2JmcWRRUm1BMm4zUUk5K1FuZmlsRFMwY0pKdEJzNkN3RWlrbnRJaUx2ZzRLN0xEL2x5eDZ2SXRHOStXaEd4RzZmUGNFWllseW1KSDVDZGtUTGMvQ2J4VFlTTTVUMVptMDVacnVZNjZ0WFRsc3lhTlBKeWxiMGFHSDUySDhVSVl3aU1IOVBVSmpXcTY4ZXFjOEVLOEMzT0t5Tm0iLCJtYWMiOiIyMzQ5MmI0ZTEzZDc5MGY1ZDhmYWJjMjk3MTc2YmJlM2RjNTJjMzY4MjJmYjc5MmIyZDViY2IxNmEwN2NlOTdjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:42:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJreVZrWTlzUFlxczNuc1cvd3ptY3c9PSIsInZhbHVlIjoiZ05zdkRabXRXdXVGdDRMV0Z4NTJpcm1OL0k2VWY1bHZNRko1ZTVqU3ZXVEhjTmhtVXoxMnV5TU1PendvMFZCQW1vZ3VZNE16cUNmSjlVcm9MTmNHQTNzempkUHB3eVp3YUVSNk9IeEs4RTJoNVVnWTF2TStYL1lwYUo4R21qZzFZRkRORTViWmEzTjJMUjMxNkdBV01oWFgrWmFaMGtuOG9NWnhvV2dZVTQ2WGMyWjR5QmtlUy9uTUJydng5K2xkdFJ4Yy9BSmEwZ3NQdlZvclpWM0JianZhWS9iemF0M01WazhQMVBydWlsbVdNWTJVY0lYUWl5THF2MWN5WjY0WWVxbGZ2SjEzS3VETkYvcFhzQkd4MkxGM2Y2THMxKzV3aWVYTDk2YUswWlVNR2JteUZ2V2s4Ym0xYm9zOU5NR1FZMThFd1RPQkVNbUVmbkE1RkNGTW5JNXhWS210RjJCdm96Ty9Gb2tUZVJ3eTRac2RmclcwSnovU1JzcWsxZzcwUkZlbThFQi9SSjdPZXpFSEdaSld0anpXejVNakh6RVkwaHFVemlUNG0zM1owb0VxT2pKbi9FZEJBbmpnRTlsN1JKWFZoa1NOMnhEc1pSeHpsbzZZUEFuNHVFbmVzY0lQYjgzOGpMZHNKb2dBRWVYVW1PQVE2MG1yUWoyWWdpT0QiLCJtYWMiOiIwYjczODk1YzUzY2UzNDY5MjBlNjQ5ZjUzNjBiNzExYjZlM2RjMzk2YzRkMTM5N2E3ZmJlNWM4OTkyMjQxOWNjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:42:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168955596\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-445973228 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NL3B8uuULZ1mQhYb649ZYYNsEwNXazK49MTemwY2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445973228\", {\"maxDepth\":0})</script>\n"}}