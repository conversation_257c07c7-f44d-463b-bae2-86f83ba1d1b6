{"__meta": {"id": "Xa48624c3d27eca4e78e1ff8f9f85b938", "datetime": "2025-07-30 07:57:35", "utime": **********.145228, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862253.548777, "end": **********.145267, "duration": 1.5964899063110352, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1753862253.548777, "relative_start": 0, "end": **********.028651, "relative_end": **********.028651, "duration": 1.4798738956451416, "duration_str": "1.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.028702, "relative_start": 1.****************, "end": **********.145271, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lbFAfn4rNvaVcCY2x9VyeUg7Jept9YgQKXij9vwf", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1183582306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1183582306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-476524863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-476524863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-517885681 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-517885681\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1225080740 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225080740\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1848192444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848192444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-260584918 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:57:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1YK3BvejdZTWIrZW1Hc3ZGNURxUVE9PSIsInZhbHVlIjoiMnpqRlZUVjlnZCtTSGJ4UTNvOVJCeU93bjlDalFsaThUSHNsbjhzbS9hZ2c3SDFHMnZVenprSXRlM3dTWE4yenI3YTN0aUdKRmVYWE1YKy9IdEFTUVJkdVN0SkRlKzFZVmNqdDhGZm1MTHBraFF6UTQvZytwd1UvZ1lnYnFIeGYwRGRjc3J6OW5Qb0xndTkrVFM1WnNzRmtsdzM4WlY0OCsySnVTV3g2T0QwK0Jwb1QzVEhweTJzRWc3Mk40VitZOU1CQ2dzd3RQUEF1M3padnZPdEk4b1pITUduazFab2ZObVQ5ZTVncDNuTjkvQjRjVVQzSEhLL3dvRXJPaWQ4bWROV3VVS2k3TGtTbDBwUzY1cEh4SFR1aXkreEt3ZW41WGE1VEdXcGkzUjFMQXErd3JhRlkrUmFzd2piVUNaajliVSthajVDckRERllPcVVVNVZTNDZGWkJlaVhpbzJoZ3ZjenJkVWxvN0daVGRtSDRobkRrdkVUNHI5eWdXMHVxQ0dYSmJPL3FRdmlDSDRWTnJXWlZwUkFERXYzbXFpbmM2dmVHeXdMZDVzSUJKN0dKRm1PSTJVTklraWFBSHpSWWg1bHpMUzlyYlRsMW1TS2lhMWVwTTlQU2JOYnJNVzUzOFhKcmFBc2Z1QlpqNzYrK0k5RUtaeXFLeE5sQkNDZE0iLCJtYWMiOiI1ZDkxYTU4ZDY1ZDk1ZjU4Y2Q1NmVhMzkwZDAxZmY2MmY3Y2ZjMTAwMGQ3OWUxMDc1NDQ2YTdlNjg3ZTYyNTE5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:57:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlErWUl1ZndXNzNBT2hQT3RNa21TMlE9PSIsInZhbHVlIjoiMjFpY2U1YzEyV1VQS0psamp5NzB4L3FuVnZRcVN0b29qVTlJYXgyU1NVRmhRa0ljckYyRHdKcnFYWHQxSTBrTlpacGRzMUZ0RHVFNkRGQ0d2bkJWMmtqS3g3Q0lRdllITlMwbElqVmVnSm9hK2I2MHlhTmNVQVBRTXAzdXRXa2ovYlQyTkxBWnVhZW04cWtoNkViWUJHbGl5RVJPY1hGVEJIM2VDRzlyUW5jaWxRRkhjZjRoU0pIc25jeGlyN0IzcGxQaksxSHFxdlRjcERWVWJwVytEckRvS0FKUUhzUlhMNTBnVkhLeU1uUTd1NjFQMkJuWG9mRVNQLzNsc1lPZHVGNFJyRUYxaGtqajByZUFhOWg0VFJOSnVJRDlXQW93ZFo2MHJQVUxtRjNaOFpPWGoxNVVrU0dQTVVGTzdMRlZHUWdUZ1VveWpBRXpyN1R1QVphWUJjSzB1MENPbG8xbE9sd3RocXZ1M3pZZ21FaFltMlFrYmkvN1duYkRheW1neWtlV25XM0x3ZU1xWjRLVmxhRzZDYWZsMzVpOHpoNFNKSytBNVRCZFAraVpqWVlJM1Vpc0E1U2l4bjlPTlNtbTZPTnNYc1RpdURXTHQ5bi96bGhoWExlZ3NqejVIWm9DT1pQVTFUNnVDR3NTMXhONVRTZkhCaTh3OFRXRkdxKzciLCJtYWMiOiJkYmFjN2YzOWIwYmYyZjhlNzE5ZDViMGM0MDkyZmEwMzc0NmU1MjAzMWNmMGMzYTJlZGIyMDExMjYzNzcwZDgxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:57:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1YK3BvejdZTWIrZW1Hc3ZGNURxUVE9PSIsInZhbHVlIjoiMnpqRlZUVjlnZCtTSGJ4UTNvOVJCeU93bjlDalFsaThUSHNsbjhzbS9hZ2c3SDFHMnZVenprSXRlM3dTWE4yenI3YTN0aUdKRmVYWE1YKy9IdEFTUVJkdVN0SkRlKzFZVmNqdDhGZm1MTHBraFF6UTQvZytwd1UvZ1lnYnFIeGYwRGRjc3J6OW5Qb0xndTkrVFM1WnNzRmtsdzM4WlY0OCsySnVTV3g2T0QwK0Jwb1QzVEhweTJzRWc3Mk40VitZOU1CQ2dzd3RQUEF1M3padnZPdEk4b1pITUduazFab2ZObVQ5ZTVncDNuTjkvQjRjVVQzSEhLL3dvRXJPaWQ4bWROV3VVS2k3TGtTbDBwUzY1cEh4SFR1aXkreEt3ZW41WGE1VEdXcGkzUjFMQXErd3JhRlkrUmFzd2piVUNaajliVSthajVDckRERllPcVVVNVZTNDZGWkJlaVhpbzJoZ3ZjenJkVWxvN0daVGRtSDRobkRrdkVUNHI5eWdXMHVxQ0dYSmJPL3FRdmlDSDRWTnJXWlZwUkFERXYzbXFpbmM2dmVHeXdMZDVzSUJKN0dKRm1PSTJVTklraWFBSHpSWWg1bHpMUzlyYlRsMW1TS2lhMWVwTTlQU2JOYnJNVzUzOFhKcmFBc2Z1QlpqNzYrK0k5RUtaeXFLeE5sQkNDZE0iLCJtYWMiOiI1ZDkxYTU4ZDY1ZDk1ZjU4Y2Q1NmVhMzkwZDAxZmY2MmY3Y2ZjMTAwMGQ3OWUxMDc1NDQ2YTdlNjg3ZTYyNTE5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:57:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlErWUl1ZndXNzNBT2hQT3RNa21TMlE9PSIsInZhbHVlIjoiMjFpY2U1YzEyV1VQS0psamp5NzB4L3FuVnZRcVN0b29qVTlJYXgyU1NVRmhRa0ljckYyRHdKcnFYWHQxSTBrTlpacGRzMUZ0RHVFNkRGQ0d2bkJWMmtqS3g3Q0lRdllITlMwbElqVmVnSm9hK2I2MHlhTmNVQVBRTXAzdXRXa2ovYlQyTkxBWnVhZW04cWtoNkViWUJHbGl5RVJPY1hGVEJIM2VDRzlyUW5jaWxRRkhjZjRoU0pIc25jeGlyN0IzcGxQaksxSHFxdlRjcERWVWJwVytEckRvS0FKUUhzUlhMNTBnVkhLeU1uUTd1NjFQMkJuWG9mRVNQLzNsc1lPZHVGNFJyRUYxaGtqajByZUFhOWg0VFJOSnVJRDlXQW93ZFo2MHJQVUxtRjNaOFpPWGoxNVVrU0dQTVVGTzdMRlZHUWdUZ1VveWpBRXpyN1R1QVphWUJjSzB1MENPbG8xbE9sd3RocXZ1M3pZZ21FaFltMlFrYmkvN1duYkRheW1neWtlV25XM0x3ZU1xWjRLVmxhRzZDYWZsMzVpOHpoNFNKSytBNVRCZFAraVpqWVlJM1Vpc0E1U2l4bjlPTlNtbTZPTnNYc1RpdURXTHQ5bi96bGhoWExlZ3NqejVIWm9DT1pQVTFUNnVDR3NTMXhONVRTZkhCaTh3OFRXRkdxKzciLCJtYWMiOiJkYmFjN2YzOWIwYmYyZjhlNzE5ZDViMGM0MDkyZmEwMzc0NmU1MjAzMWNmMGMzYTJlZGIyMDExMjYzNzcwZDgxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:57:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260584918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1929695791 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lbFAfn4rNvaVcCY2x9VyeUg7Jept9YgQKXij9vwf</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929695791\", {\"maxDepth\":0})</script>\n"}}