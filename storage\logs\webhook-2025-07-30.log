[2025-07-30 06:12:48] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-30T06:12:48.675980Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"entity_type":"Lead","entity_id":11,"status":"dispatching"} 
[2025-07-30 06:12:52] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-30T06:12:52.665094Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3976.0,"user_id":79,"entity_id":11,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-30T06:12:48.689070Z","data":{"id":11,"name":"Parichay Singha","contact_type":"Lead","tags":null,"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+916896589568","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"Booking","user_id":81,"pipeline_id":23,"stage_id":86,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-07-19","next_follow_up_date":null,"created_at":"2025-07-19T07:04:45.000000Z","updated_at":"2025-07-21T10:06:47.000000Z","stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"users":[{"id":79,"name":"Parichay Singha AI","email":"<EMAIL>","email_verified_at":"2025-07-19T06:00:35.000000Z","plan":10,"plan_expire_date":"2026-07-19","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"logo-dark-removebg-preview_1752904903.png","messenger_color":"#2180f3","lang":"en","default_pipeline":23,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-19T06:00:35.000000Z","updated_at":"2025-07-30T05:11:50.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":11,"user_id":79}},{"id":81,"name":"Gungun Rani","email":"<EMAIL>","email_verified_at":"2025-07-19T07:03:24.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":79,"created_at":"2025-07-19T07:03:24.000000Z","updated_at":"2025-07-19T07:03:24.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":11,"user_id":81}}],"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"old_stage_id":86,"new_stage_id":"87","triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-30 06:12:52] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-07-30T06:12:52.667159Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-30 07:58:27] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-30T07:58:27.986456Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-30 07:58:30] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-30T07:58:30.303142Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2288.0,"user_id":79,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-30T07:58:28.015114Z","data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-07-30","date_of_birth":"2025-07-30","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":"ok","postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"sit","business_gst":"sit","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-07-30T07:58:27.000000Z","created_at":"2025-07-30T07:58:27.000000Z","id":12,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-30 07:58:30] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-30T07:58:30.306142Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-30 07:59:29] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-30T07:59:29.043635Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":13,"status":"dispatching"} 
[2025-07-30 07:59:31] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-30T07:59:31.234096Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2163.0,"user_id":79,"entity_id":13,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-30T07:59:29.070872Z","data":{"name":"Gungun Rani","email":"<EMAIL>","phone":"8654201236","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-07-30","date_of_birth":"2025-07-31","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":"HElo","postal_code":"734429","city":"Siliguri","state":"Dadra and Nagar Haveli","country":"India","business_name":"NA","business_gst":"NA","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","dnd_settings":"{\"all\":false,\"emails\":true,\"whatsapp\":true,\"sms\":true,\"calls\":true}","updated_at":"2025-07-30T07:59:28.000000Z","created_at":"2025-07-30T07:59:28.000000Z","id":13,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-30 07:59:31] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-30T07:59:31.236181Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-30 08:01:06] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-30T08:01:06.600646Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"entity_type":"Lead","entity_id":13,"status":"dispatching"} 
[2025-07-30 08:01:08] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-30T08:01:08.761073Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2135.0,"user_id":79,"entity_id":13,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-30T08:01:06.625734Z","data":{"id":13,"name":"Gungun Rani","contact_type":"Lead","tags":"HElo","postal_code":"734429","city":"Siliguri","state":"Dadra and Nagar Haveli","country":"India","business_name":"NA","business_gst":"NA","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","dnd_settings":"{\"all\":false,\"emails\":true,\"whatsapp\":true,\"sms\":true,\"calls\":true}","email":"<EMAIL>","phone":"8654201236","date_of_birth":"2025-07-31","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"New Contact","user_id":79,"pipeline_id":23,"stage_id":86,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-07-30","next_follow_up_date":null,"created_at":"2025-07-30T07:59:28.000000Z","updated_at":"2025-07-30T07:59:28.000000Z","stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"users":[{"id":79,"name":"Parichay Singha AI","email":"<EMAIL>","email_verified_at":"2025-07-19T06:00:35.000000Z","plan":10,"plan_expire_date":"2026-07-19","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"logo-dark-removebg-preview_1752904903.png","messenger_color":"#2180f3","lang":"en","default_pipeline":23,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-19T06:00:35.000000Z","updated_at":"2025-07-30T05:11:50.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":13,"user_id":79}}],"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"old_stage_id":86,"new_stage_id":"88","triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-30 08:01:08] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-07-30T08:01:08.766205Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
