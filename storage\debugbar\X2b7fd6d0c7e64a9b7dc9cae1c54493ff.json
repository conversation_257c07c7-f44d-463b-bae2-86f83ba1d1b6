{"__meta": {"id": "X2b7fd6d0c7e64a9b7dc9cae1c54493ff", "datetime": "2025-07-30 07:49:45", "utime": **********.179232, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753861783.625389, "end": **********.17929, "duration": 1.553900957107544, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1753861783.625389, "relative_start": 0, "end": **********.027157, "relative_end": **********.027157, "duration": 1.4017679691314697, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.027178, "relative_start": 1.****************, "end": **********.179296, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fa10GDowhZofZ9vq0OBaQD1H1IwRYNwO6qZxFnlT", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-830802856 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-830802856\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-647910795 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-647910795\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-608332424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608332424\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1574505246 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574505246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-863902063 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-863902063\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1851074835 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:49:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVBTkVDT25SWm5PRlNsMWdwVXlIQkE9PSIsInZhbHVlIjoiOU9zMkF6YzlrdnkxaTYxOTFCc1VVM0FwU21EYWllS0Vsc2tUUmdPL0xkemFqSHFkQXlITGlqcW5rTTdhQndoVFc0VXZJL1FhbTlwaDVVc20rbHJlK0F1NTNtcnpWNG9QSnlMQmdEUjl2TTdVQS9XZG45WWJCMlFtZmw1endJZElMS25nNnQ0Q1E0VzFzWEdqOC9mUWU4WEUzL3lnczlVZllueUdlRmYyN1U5TEZ3RDh5bDc0RkdMbXA3djE3WUxNcTh3bzFlWFhIOGJMZ0dlbnBPLy9Jb2YrSVpiSncwQ3NQUGdvaTZKaHp6MFQ5bDMyTnFkNnJZT2VPMzVRYTRZbDNYVndYOFdMa2dsUlNnc3FhVzB4eENCYi9QTVZiVFFCZHB4QjlpSGpFZnFlMlpUK1IzOXVXSmJhZERDRzRzN3VodVp0SDluV0tZcDBIU1UzNEhkcjZ6RmhMVXdURFBjQ1BTeVN2K0ZYMi82ZjFNek5iTDhIb1FCTWY5NXUyU2tXVXR5bzQ3Z2UzN1E4S21walp6TDMrVXY3MVAydGN0TTFkd0FNL3U0K1M4Z3M1K3FITmxaUS85ZWZHZC90UGxSRHFQMmtZNFlqNG5ZYWdodUYxTzBLb1IvaXhDLy9sQUdIQ3NsMEVzUGgvcFVLWktweklhVXMzR0RuZVFBMU5BSmEiLCJtYWMiOiI5ZTRlNTEyMDExNzE1MGQzMDMzNWZmYzdjNmZmNTU5Yzk1NjIxMWM5Y2E0ODljMTMwNzUxODhlMTE4NTMzZTJiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:49:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkREeU43YnNmMWV2NnZJZy9KQnZYWEE9PSIsInZhbHVlIjoiQ2xoblM5cjhzUExxazcwQ3hiemdhY1pVeEVjN3ltaUpaVVdoMFJta0xvTExyaXVvUlNDb09HM29iWDR3NlRpbm9ScFR5TkRXUE9SUHFDUjBFMitVc1FzSkJqQXI2b3JrQ2ZUYy9tY01PUTFwTFBTNUNOSUppdkw4RGNtWWUxTDBkOUd4aFhPckdidW1waFhUaU9wWjNjbXFLUjBFOEg0TWp1RmhmVlAxTWhncFpkaGRwMnJBRVNrV05NYW1Fbi9qYVd5endyY21ZbFR1OVJPcEEraTJwaUN2RTMwaHdlc2psT0FIbzRvbmx0K1psYjRqS3NoMHJtbHVBdjhpVWRnTHZ2VTZpNDcxbmNkK0MyMTZSakg5SEpBNG8vcGF6YXRwNUdGWXVsV2FGQ3h5bzl3MVNXQTlLTWozRktkVWRZUGRLemNJb0lLTHJHMjlaVnpNTEI4NkVMT2t5UnBuRVR2MGJYRk1UbTZsMndBMlV5L3p0NFJTQmIwVDlrQXdkUFB5cXlYcGx0eUZpOHRlSlZoR1d0VFkzM3JBbDRMdExsTjByNDR5bG1YYjV4ckhkYUpMZk4yMXFTNGpJbUh6dE5sa0VMRkJERngwNkV0c2JkdGVIVVlTYy9ROUtrK2ZiT1ZYbnZMbnFReU1FRTAwUGwyMjErRXpaL3ZtVlIrR0JVVEkiLCJtYWMiOiIyZWQ4MDhjNzkyNWJhZmIyNjcxZWRhMTc2YWYyNmVmYTEyMGY3YjFjZWZhOTY4YjllZGQ1MTEzYmM3NWM0YWNkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:49:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVBTkVDT25SWm5PRlNsMWdwVXlIQkE9PSIsInZhbHVlIjoiOU9zMkF6YzlrdnkxaTYxOTFCc1VVM0FwU21EYWllS0Vsc2tUUmdPL0xkemFqSHFkQXlITGlqcW5rTTdhQndoVFc0VXZJL1FhbTlwaDVVc20rbHJlK0F1NTNtcnpWNG9QSnlMQmdEUjl2TTdVQS9XZG45WWJCMlFtZmw1endJZElMS25nNnQ0Q1E0VzFzWEdqOC9mUWU4WEUzL3lnczlVZllueUdlRmYyN1U5TEZ3RDh5bDc0RkdMbXA3djE3WUxNcTh3bzFlWFhIOGJMZ0dlbnBPLy9Jb2YrSVpiSncwQ3NQUGdvaTZKaHp6MFQ5bDMyTnFkNnJZT2VPMzVRYTRZbDNYVndYOFdMa2dsUlNnc3FhVzB4eENCYi9QTVZiVFFCZHB4QjlpSGpFZnFlMlpUK1IzOXVXSmJhZERDRzRzN3VodVp0SDluV0tZcDBIU1UzNEhkcjZ6RmhMVXdURFBjQ1BTeVN2K0ZYMi82ZjFNek5iTDhIb1FCTWY5NXUyU2tXVXR5bzQ3Z2UzN1E4S21walp6TDMrVXY3MVAydGN0TTFkd0FNL3U0K1M4Z3M1K3FITmxaUS85ZWZHZC90UGxSRHFQMmtZNFlqNG5ZYWdodUYxTzBLb1IvaXhDLy9sQUdIQ3NsMEVzUGgvcFVLWktweklhVXMzR0RuZVFBMU5BSmEiLCJtYWMiOiI5ZTRlNTEyMDExNzE1MGQzMDMzNWZmYzdjNmZmNTU5Yzk1NjIxMWM5Y2E0ODljMTMwNzUxODhlMTE4NTMzZTJiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:49:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkREeU43YnNmMWV2NnZJZy9KQnZYWEE9PSIsInZhbHVlIjoiQ2xoblM5cjhzUExxazcwQ3hiemdhY1pVeEVjN3ltaUpaVVdoMFJta0xvTExyaXVvUlNDb09HM29iWDR3NlRpbm9ScFR5TkRXUE9SUHFDUjBFMitVc1FzSkJqQXI2b3JrQ2ZUYy9tY01PUTFwTFBTNUNOSUppdkw4RGNtWWUxTDBkOUd4aFhPckdidW1waFhUaU9wWjNjbXFLUjBFOEg0TWp1RmhmVlAxTWhncFpkaGRwMnJBRVNrV05NYW1Fbi9qYVd5endyY21ZbFR1OVJPcEEraTJwaUN2RTMwaHdlc2psT0FIbzRvbmx0K1psYjRqS3NoMHJtbHVBdjhpVWRnTHZ2VTZpNDcxbmNkK0MyMTZSakg5SEpBNG8vcGF6YXRwNUdGWXVsV2FGQ3h5bzl3MVNXQTlLTWozRktkVWRZUGRLemNJb0lLTHJHMjlaVnpNTEI4NkVMT2t5UnBuRVR2MGJYRk1UbTZsMndBMlV5L3p0NFJTQmIwVDlrQXdkUFB5cXlYcGx0eUZpOHRlSlZoR1d0VFkzM3JBbDRMdExsTjByNDR5bG1YYjV4ckhkYUpMZk4yMXFTNGpJbUh6dE5sa0VMRkJERngwNkV0c2JkdGVIVVlTYy9ROUtrK2ZiT1ZYbnZMbnFReU1FRTAwUGwyMjErRXpaL3ZtVlIrR0JVVEkiLCJtYWMiOiIyZWQ4MDhjNzkyNWJhZmIyNjcxZWRhMTc2YWYyNmVmYTEyMGY3YjFjZWZhOTY4YjllZGQ1MTEzYmM3NWM0YWNkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:49:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851074835\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1597695519 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fa10GDowhZofZ9vq0OBaQD1H1IwRYNwO6qZxFnlT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597695519\", {\"maxDepth\":0})</script>\n"}}