<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\Lead;
use App\Models\ProductService;
use App\Models\Subscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SubscriptionManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $customer;
    protected $lead;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'type' => 'company',
            'created_by' => 1
        ]);

        // Create a test customer
        $this->customer = Customer::factory()->create([
            'created_by' => $this->user->creatorId()
        ]);

        // Create a test lead
        $this->lead = Lead::factory()->create([
            'created_by' => $this->user->creatorId(),
            'is_active' => 1,
            'is_converted' => 0
        ]);

        // Create a test product
        $this->product = ProductService::factory()->create([
            'created_by' => $this->user->creatorId()
        ]);
    }

    /** @test */
    public function it_can_create_a_subscription()
    {
        $this->actingAs($this->user);

        $subscriptionData = [
            'customer_id' => $this->customer->id,
            'customer_type' => 'customer',
            'customer_email' => $this->customer->email,
            'customer_phone' => $this->customer->contact,
            'product_id' => $this->product->id,
            'start_date' => '2025-01-01',
            'product_price' => 1000,
            'down_payment' => 200,
            'discount_amount' => 50,
            'total_emis' => 12,
            'billing_cycle' => 'monthly',
            'payment_method' => 'offline',
            'description' => 'Test subscription'
        ];

        $response = $this->postJson(route('finance.sales.store-subscription'), $subscriptionData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Subscription created successfully.'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'product_price' => 1000,
            'down_payment' => 200,
            'discount_amount' => 50,
            'total_emis' => 12,
            'billing_cycle' => 'monthly'
        ]);
    }

    /** @test */
    public function it_can_cancel_a_subscription()
    {
        $this->actingAs($this->user);

        $subscription = Subscription::factory()->create([
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'created_by' => $this->user->creatorId(),
            'status' => 'active'
        ]);

        $response = $this->postJson(route('finance.sales.cancel-subscription', $subscription->id));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Subscription cancelled successfully.'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'status' => 'cancelled'
        ]);
    }

    /** @test */
    public function it_can_update_subscription_notes()
    {
        $this->actingAs($this->user);

        $subscription = Subscription::factory()->create([
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'created_by' => $this->user->creatorId()
        ]);

        $notes = 'Updated subscription notes';

        $response = $this->postJson(route('finance.sales.update-subscription-notes', $subscription->id), [
            'notes' => $notes
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Notes updated successfully.'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'notes' => $notes
        ]);
    }

    /** @test */
    public function it_can_update_subscription_details()
    {
        $this->actingAs($this->user);

        $subscription = Subscription::factory()->create([
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'created_by' => $this->user->creatorId(),
            'status' => 'active',
            'paid_amount' => 200
        ]);

        $updateData = [
            'status' => 'paused',
            'paid_amount' => 400,
            'pending_amount' => 600,
            'next_emi_date' => '2025-02-01',
            'receipt_url' => 'https://example.com/receipt.pdf'
        ];

        $response = $this->postJson(route('finance.sales.update-subscription', $subscription->id), $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Subscription updated successfully.'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'status' => 'paused',
            'paid_amount' => 400,
            'pending_amount' => 600,
            'receipt_url' => 'https://example.com/receipt.pdf'
        ]);
    }

    /** @test */
    public function it_can_delete_a_subscription()
    {
        $this->actingAs($this->user);

        $subscription = Subscription::factory()->create([
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'created_by' => $this->user->creatorId()
        ]);

        $response = $this->deleteJson(route('finance.sales.delete-subscription', $subscription->id));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Subscription deleted successfully.'
                ]);

        $this->assertDatabaseMissing('subscriptions', [
            'id' => $subscription->id
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_subscription()
    {
        $this->actingAs($this->user);

        $response = $this->postJson(route('finance.sales.store-subscription'), []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'customer_id',
                    'customer_email',
                    'product_id',
                    'start_date',
                    'product_price',
                    'total_emis',
                    'billing_cycle',
                    'payment_method'
                ]);
    }

    /** @test */
    public function it_can_create_subscription_with_lead()
    {
        $this->actingAs($this->user);

        $subscriptionData = [
            'customer_id' => $this->lead->id,
            'customer_type' => 'lead',
            'customer_email' => $this->lead->email,
            'customer_phone' => $this->lead->phone,
            'product_id' => $this->product->id,
            'start_date' => '2025-01-01',
            'product_price' => 1500,
            'down_payment' => 300,
            'discount_amount' => 100,
            'total_emis' => 10,
            'billing_cycle' => 'monthly',
            'payment_method' => 'online',
            'description' => 'Test subscription from lead'
        ];

        $response = $this->postJson(route('finance.sales.store-subscription'), $subscriptionData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Subscription created successfully.'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'customer_id' => $this->lead->id,
            'customer_name' => $this->lead->name,
            'customer_email' => $this->lead->email,
            'product_id' => $this->product->id,
            'product_price' => 1500,
            'down_payment' => 300,
            'discount_amount' => 100
        ]);
    }

    /** @test */
    public function it_can_search_contacts()
    {
        $this->actingAs($this->user);

        $response = $this->getJson(route('finance.sales.search-contacts', ['search' => $this->customer->name]));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'contacts' => [
                        '*' => [
                            'id',
                            'type',
                            'name',
                            'email',
                            'phone',
                            'display_name',
                            'display_info'
                        ]
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_customer_contact_details()
    {
        $this->actingAs($this->user);

        $response = $this->getJson(route('finance.sales.get-contact-details', [
            'type' => 'customer',
            'id' => $this->customer->id
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'contact' => [
                        'id' => $this->customer->id,
                        'type' => 'customer',
                        'name' => $this->customer->name,
                        'email' => $this->customer->email
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_lead_contact_details()
    {
        $this->actingAs($this->user);

        $response = $this->getJson(route('finance.sales.get-contact-details', [
            'type' => 'lead',
            'id' => $this->lead->id
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'contact' => [
                        'id' => $this->lead->id,
                        'type' => 'lead',
                        'name' => $this->lead->name,
                        'email' => $this->lead->email
                    ]
                ]);
    }

    /** @test */
    public function it_validates_customer_type_when_creating_subscription()
    {
        $this->actingAs($this->user);

        $subscriptionData = [
            'customer_id' => $this->customer->id,
            'customer_type' => 'invalid_type',
            'customer_email' => $this->customer->email,
            'product_id' => $this->product->id,
            'start_date' => '2025-01-01',
            'product_price' => 1000,
            'total_emis' => 12,
            'billing_cycle' => 'monthly',
            'payment_method' => 'offline'
        ];

        $response = $this->postJson(route('finance.sales.store-subscription'), $subscriptionData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['customer_type']);
    }
}
