{"__meta": {"id": "X876a9be69876ce50589b423b1b1de1b1", "datetime": "2025-07-30 06:35:20", "utime": **********.58096, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753857319.747234, "end": **********.581004, "duration": 0.8337697982788086, "duration_str": "834ms", "measures": [{"label": "Booting", "start": 1753857319.747234, "relative_start": 0, "end": **********.512054, "relative_end": **********.512054, "duration": 0.764819860458374, "duration_str": "765ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.512082, "relative_start": 0.***************, "end": **********.581007, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "68.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bOr3SLTBstBycyteYVp2DfK9QDTbJaBWV8wCAFnA", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1676403172 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1676403172\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1371094427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1371094427\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1299865266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1299865266\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1624859019 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624859019\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1924237928 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1924237928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-463407040 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:35:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhQMTRxTkFxbkJubms3SEZIZEVZZ3c9PSIsInZhbHVlIjoieURjQmFpM1k2TjFUYndqSVppZ09CWGtaTzJtRi9IdGVFeUZ0ODVHdHlhMWpNZEdMNElqbVIwS05TbHUrMi83TDZtUm1wYUtXWVVPVEVpV3h2dlM4dGt4L2FPbWFLOU5UN1VaWlo5L1lITEFtdktRUXU3QVJJUzNRUlV0UytiejFjZGppTHQ0ZkdDTERFTVFJYWtxcDBBSXVlM0RIT2IrY203UElEdVZld0w1bTBTVldXMjNiaDFFUjltR0tNTGtHRTlzL0RMYmZEOENhckxKQmdNeXg1dXVJR01qZm5McVZwcTB0dkVMSW5FTC9zSEQrQ1djb3I0TFB4RStrNjJBWm9wMmYrWFJpbm9lSzJlTkZueEg1OThVU0FseFh1ZVFZYjJTQnU4UHVlcW9JWFRzKzB5ajVnY0VxUVg5OEw5T3cwNzhSb3h6bitXNnhaTHQwZ2NHVDdJMTdOSjg1NGZYajUwKzRhRElGcmRURGsrc2NSWVFGMGRPbDFKbGEwNFRrS3MvcCtzcG16eWVqSTBsanlUWFYxcVBseTRCYTBMU1pGaFVhU2RiR0FlZmtLZTBYeE9jeWdwY2NSeW9jOC9QK0xZVkpzTTMwV25nRFNYdjFBWDlzajkrZ1J1a25aWTE0U3RtZUlQWTMrY1NDSnRYSnYvRjlERG5SYUcxcHc1ZWEiLCJtYWMiOiIwOWI1YWEwNGFiNGQxZDc2ZTIxYzY0MmY3MDQ5ZWEyNTk0YmIwYjdmMzE2MjczZGYxNzdjNTM5NDE4ODFjNGE4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:35:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1UWFRCYnUxeG1sa1Q0YzdGTTZaVlE9PSIsInZhbHVlIjoiT0F0c1hEUXFqOW40R1hqMWFTUDlhcmdMWElPSVJsb3BGYlhqYmx1Zlo1a05kMDNjUHZidHlzVXNzMVhoR0RHWUZKdVNDVzJ3RVlzS1JybHFsWTltUG1hZFo4dGErKzJaV2h4cWJJbUxlMzBuYUVHVk1JKzJnaFFaemkxNmhGc0R2eUxVME1hUHphRkd5c2ExdkxQYXVSeGllRmEvL0tzZGJvZHhPVWN4bjRvSkxyVFdkQW1lRmpPeG1aVkllT21aMmZTbGpKRk5rSGY3YzVldGJ1QzFtNWJidnd0YnhaWktkNmJMRWpHOEIreFZHYkdVRmlpNEhzN2JlQkhxb1NncExRNnhLQzlmM3FQSkZhWTFwL2VDYUNmdHVFN2ZnaWs1WUZ6QkFST1BvOXZUTkRrMTR6S3REMzZTUmtLZ0hRa3g3aUdzTjV0U29oR2o0dFdETFJLUEF0NW9rSDMyVjdqcjV5R24zUVBNQU4yaDgrUXgzS3RtUWFTVFVScXBraDNBajVXOEs1a1NtQUl4aGprbyt0T0UycVcybEJzZ0s5eFA1S3BnWThCOTJyOGx3NVVDL0dNV3V1OG5abTZDdGRYTi9VKzZNL25BNWtna1dtamE1MmRtOUFsbmdLWFgzczZydVJ5VmNVQzBzVEd3L0M2amk3QU4vaXVqaE9oOWtjNnIiLCJtYWMiOiI3ZTY4ZWYzNDgzOTg3ZjMyMjkxMDJmOTI2NDVmMmZkYzU4MDM1ZWM3OTk1NzQ0Zjg3MWE4M2FmODI5ZjUzM2RmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:35:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhQMTRxTkFxbkJubms3SEZIZEVZZ3c9PSIsInZhbHVlIjoieURjQmFpM1k2TjFUYndqSVppZ09CWGtaTzJtRi9IdGVFeUZ0ODVHdHlhMWpNZEdMNElqbVIwS05TbHUrMi83TDZtUm1wYUtXWVVPVEVpV3h2dlM4dGt4L2FPbWFLOU5UN1VaWlo5L1lITEFtdktRUXU3QVJJUzNRUlV0UytiejFjZGppTHQ0ZkdDTERFTVFJYWtxcDBBSXVlM0RIT2IrY203UElEdVZld0w1bTBTVldXMjNiaDFFUjltR0tNTGtHRTlzL0RMYmZEOENhckxKQmdNeXg1dXVJR01qZm5McVZwcTB0dkVMSW5FTC9zSEQrQ1djb3I0TFB4RStrNjJBWm9wMmYrWFJpbm9lSzJlTkZueEg1OThVU0FseFh1ZVFZYjJTQnU4UHVlcW9JWFRzKzB5ajVnY0VxUVg5OEw5T3cwNzhSb3h6bitXNnhaTHQwZ2NHVDdJMTdOSjg1NGZYajUwKzRhRElGcmRURGsrc2NSWVFGMGRPbDFKbGEwNFRrS3MvcCtzcG16eWVqSTBsanlUWFYxcVBseTRCYTBMU1pGaFVhU2RiR0FlZmtLZTBYeE9jeWdwY2NSeW9jOC9QK0xZVkpzTTMwV25nRFNYdjFBWDlzajkrZ1J1a25aWTE0U3RtZUlQWTMrY1NDSnRYSnYvRjlERG5SYUcxcHc1ZWEiLCJtYWMiOiIwOWI1YWEwNGFiNGQxZDc2ZTIxYzY0MmY3MDQ5ZWEyNTk0YmIwYjdmMzE2MjczZGYxNzdjNTM5NDE4ODFjNGE4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:35:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1UWFRCYnUxeG1sa1Q0YzdGTTZaVlE9PSIsInZhbHVlIjoiT0F0c1hEUXFqOW40R1hqMWFTUDlhcmdMWElPSVJsb3BGYlhqYmx1Zlo1a05kMDNjUHZidHlzVXNzMVhoR0RHWUZKdVNDVzJ3RVlzS1JybHFsWTltUG1hZFo4dGErKzJaV2h4cWJJbUxlMzBuYUVHVk1JKzJnaFFaemkxNmhGc0R2eUxVME1hUHphRkd5c2ExdkxQYXVSeGllRmEvL0tzZGJvZHhPVWN4bjRvSkxyVFdkQW1lRmpPeG1aVkllT21aMmZTbGpKRk5rSGY3YzVldGJ1QzFtNWJidnd0YnhaWktkNmJMRWpHOEIreFZHYkdVRmlpNEhzN2JlQkhxb1NncExRNnhLQzlmM3FQSkZhWTFwL2VDYUNmdHVFN2ZnaWs1WUZ6QkFST1BvOXZUTkRrMTR6S3REMzZTUmtLZ0hRa3g3aUdzTjV0U29oR2o0dFdETFJLUEF0NW9rSDMyVjdqcjV5R24zUVBNQU4yaDgrUXgzS3RtUWFTVFVScXBraDNBajVXOEs1a1NtQUl4aGprbyt0T0UycVcybEJzZ0s5eFA1S3BnWThCOTJyOGx3NVVDL0dNV3V1OG5abTZDdGRYTi9VKzZNL25BNWtna1dtamE1MmRtOUFsbmdLWFgzczZydVJ5VmNVQzBzVEd3L0M2amk3QU4vaXVqaE9oOWtjNnIiLCJtYWMiOiI3ZTY4ZWYzNDgzOTg3ZjMyMjkxMDJmOTI2NDVmMmZkYzU4MDM1ZWM3OTk1NzQ0Zjg3MWE4M2FmODI5ZjUzM2RmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:35:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463407040\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-338481994 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bOr3SLTBstBycyteYVp2DfK9QDTbJaBWV8wCAFnA</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338481994\", {\"maxDepth\":0})</script>\n"}}