{"__meta": {"id": "X06a2a57f70b7b8232b32d3afdba9bef0", "datetime": "2025-07-30 06:05:59", "utime": **********.499765, "method": "GET", "uri": "/contact-groups/available-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855558.508044, "end": **********.499797, "duration": 0.991753101348877, "duration_str": "992ms", "measures": [{"label": "Booting", "start": 1753855558.508044, "relative_start": 0, "end": **********.383064, "relative_end": **********.383064, "duration": 0.8750200271606445, "duration_str": "875ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.383079, "relative_start": 0.8750350475311279, "end": **********.499799, "relative_end": 1.9073486328125e-06, "duration": 0.11671996116638184, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46733472, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/available-contacts", "middleware": "web, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getAvailableContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.available-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=221\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:221-260</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01801, "accumulated_duration_str": "18.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.449554, "duration": 0.015050000000000001, "duration_str": "15.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 83.565}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.4774358, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 83.565, "width_percent": 9.051}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 79 and `contact_group_id` is null and `is_active` = 1 and `is_deleted` = 0 order by `name` asc", "type": "query", "params": [], "bindings": ["79", "1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.48441, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:231", "source": "app/Http/Controllers/ContactGroupController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=231", "ajax": false, "filename": "ContactGroupController.php", "line": "231"}, "connection": "radhe_same", "start_percent": 92.615, "width_percent": 7.385}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/contact-groups/available-contacts", "status_code": "<pre class=sf-dump id=sf-dump-840064096 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-840064096\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1121035792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1121035792\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1091592424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091592424\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1932626010 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InFXU3JYNlduTXJ0QkdFcmF4TS9vOFE9PSIsInZhbHVlIjoiVktEai9uZmFCL1ZCVXljL29TdVdiOFNPSS9UK01hekRKZFY3UHg4aVJ5R3NubXVqb0hKbFNPL0J5Q0Q0NmNxSTFtT3Z3djlkUjdReVVRZy9lNEVVMEJMR1JmSzhJMmN4U2tWZWMzYXc0Vmh5Q2lxellkZ0pQeHVTN0tyVlhSVGhmVGxxMGxubUtQVVJqdkM2QlgxOXpwZWxCNERIOTdzZTFLMGdLM0tuN0w0YThyUE5tcUJhZHNieWUvenhheGsrcm5uSG1LSElWSDltbklTenJSZlduaUt0MjRyeXU4RjVicllKNkdVTnl1UDVtVWl4SEhZRmh3bUFHUjR3Q1IxZGhlTGhXTG11QmZSOEFzSEpZaGs1d3U4cWUrVHcvY09ZR3I4N1llbEVMZVRCK2RPWTl1SDMxak95SXNCQkdkQWtBRzZpNFpHN1ZXaU8wVXNaWFM5RnhjQ0pjZkNzSGovcU9oVkVIaGppSlIwcHp0WW5hdGg3TjFDWVNQbFV2V3dTaTBvRVNMTW15K3I3UEVkTWdOWE9aWjFJVjRYa2dBWUNrUlVIMVNQT2FyMFB3WDVoRkhxemg2QXNsSElpUkRYMlRPNE1zZUZ5ay8wWTdDZmkyTkVLa0lHWFpuTEpmUy9kOW9DWVRSNlJTMHZzOGdZNHNuTURLVmErazRwdlp0S04iLCJtYWMiOiI0YTMxYWNkMmIzMjUyOGFiNDU5NjU0ZjMzMzRhODJlYTc0NTZhODRhNmIyZjc5NDUyMDU4ODQyMjU2YjcxNGIxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImpaR2ZsdUdCUU0xUklTeCsvMGhmbWc9PSIsInZhbHVlIjoiY3V2MmlITXRIa01lR0NrZXgvN2RyNGJ2VEsxQnE2N2dtZGtuYzFVRUo1OEtJaysyWXdPSVlsZUpSWjQ3YWJIdGx0Qld6cHgxbjM2RjVwSC8ySFZyMlA2bUpYeThYUmZPZkxMUkNERG9GUTAwY3dPTEk4S2ViZ1JQYjV4Y3poRy81MXhGNE1tSUtXYnZ5SWRBNDVyUk9rUHhFWkMzSUpYVTRUbVZYWXBFQWUyQXNPYjUvODg0WHdpQUxSMjEzQ0NGMXArZWh0V3ZkbG5SYWlVTG5HZUZuYUFlaTFVU0xWVjRhMDhXVjVvOXhQRnJpLzM1UGxTYlNxUnNhdnFEOXdsY21xSUROSFFVTEZyZWs0aTRkTnFvNWdrclJMZzA1UTNIMTMxYTM5cE5MRWtEYXBHNllQcmtTaWIyd21yRmZkWUkrVTRJTUdSQUNVcjdmUXNTNXhyMjV0SzYrM0Nra1R1RUJXWXpmTkhuZklKY0VzVDh5NzhZMzRqdmFmaThIdFBqQWRMZUlWOWF6Si9qMnN1blNhMm1vSG8yVXFqbCtkK2tyMGVHNlBFMWIyd3NjdnV0c0k3UlZlZTFaQUdYZGlrWkZlZ3RTMC9SaXFFb0lhQkE2dWpMRGpOWElNS3BjTXg4dU4xckZvUTErb3NiTFgxQTBjV3lMdGlkNjQ5UUMvMkgiLCJtYWMiOiJiZGNkYzA2ODNlNGY2MDViZWZhNjdiMjJiZDlkYzVkMDRmYjkzNjg3ZWQ2ZmQ1ZGRjYTZkYjU4N2VmYmU5NjRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932626010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1909641446 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909641446\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1775265611 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkzUytMeGZNMVJqM1prYUtYeFNNRmc9PSIsInZhbHVlIjoibXN5ZmNXOVJPZk5VcUVKME40NkRvekRZYzFoZnAwWGpDQ2YrRTVrY29scTFobnhFdG5CT0w5UzlsTHZCdG1JYXdzVE1sbU5vZnl3SXYxcEpsMlQ5RGZyUVJQZkNtWFZkUFpvZVJsZFdMdm9hVUhmbFR6Rnh6SlB0bDltZzNkdGxsdmE3Y3hDVlNUYjFDa2ZjN3o3VllPMCtScWlWaHZGR0FDSDRFWW9Jc3locURqbzRjV1lycmNlK1FQS0lLM3lYT3p5TmVSNEg0ZFltNVV6UDMzb1E4dWVaeDRNRHlJU1ZjVlBqOEZMT0RnczNsU1Z6YlI3UWJ1eG5xbFJBOHprajBDd0g1amhRd1FzS2xuWGdLQUtJS0t4QVhNb3hIajdjK1VkRllXdUdqU0tOc3JXUXpwRWJYckVmcVBNdUdlMXFBTzVRL3JYYTQ4REJxbmFVUDdkMFhBZENwK2I5QzNQMmNaUHRXTy9oYm4wNXdxNjkyUUM2Q3E4RGdrUzJXMExnZlhHTEJoMElsWjFVY1hvOW5qMERVZ0g4MDVtZmF4bFptTE1ITFNFWlhnZkpQT2hyR0Z3QUp6MjZTUDdOaUpsK2o4MGZEMHBmN2duQS9zbVd2RXVIYnFxbFA0Q0VWVEdHaFludXgrUjZ0UW5McTRFenVrb0Zva2pXUE14ekNBMU8iLCJtYWMiOiIzM2Y0ODg4MmZlM2VhMzAxN2VkYjY4NGI0YzM0NWI0ZTZmMmI3OGM5MzlhZjU2ZWU1YjhlYzY5MGQxZTQzNWFmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkY4ZndMZlhNT3Q5aGZiUTFXeUJoUGc9PSIsInZhbHVlIjoiVzBpaGhQR3pHYlZ1UjNkdk9lUjA1RTJzcEJhaUpYamJiTVFBb2J5WG1XY2pmZlhVQ2FIczYrNnd3cVhwblNIaVduaitWeFVRLzdCcS8yb2o1OTVjUWNidERvdlFpdS9KYzlkRnNMWHlNSy84YzlIaDRJVWtkVElVTUQwNE1zYkF3VjNGQVczbThubTFmbEI1YlBhL1J2SEZ4aEhxeG1ZWjJldDRib2grTjBoWWZwUlh4RVpWek8zOHlrSHEvUUlEaDY0bXZzalZaYTZEL1N6WUk0K0FYQlZZdEY5Wm9OcGtmNHhETTNwOFVLcE9pdzczK0djYWZKUTJkbkZ1YzJDT2ZWcjNKdCtpR0Z0Qk9BWkZGQ3Z2cThodGI1dDQrbzMzSHVpZ1psMXZWbjlxNUFHUSs4WDhSenMrcTkvM3EwS0xTdjBrNGVJa1RjTWRPQWV2SzkxZDBXNzNnM0xQT1E5bEprb0ZpdTRiS2FaUDI0NnNDa2hzTThDaHBKR0YzamJQbFhZSEhJSWJTTGl4Y3JVR040VTAvYU5JR0pCYUtJMkRVdThrdXpXcXlibWxCZ21ra3dGQlpNSUxpaWpIMGk0ZDlaeFJ4UXpMT1I5R2hCbEl6TVhIY2FybnBiQzdpMzBiSHRDNk0xWktpMW43Mi9qc0NndkZzVnJCZzhuMDdtMjAiLCJtYWMiOiI3Yjc3OGU2MjBjNzYyMmFiNGVlZTliYmYxNDQ0ZmYzNjM3NTFkYTIyMWE5MTk5NGJhZWUxNmE1YTQxMzJhZGM5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkzUytMeGZNMVJqM1prYUtYeFNNRmc9PSIsInZhbHVlIjoibXN5ZmNXOVJPZk5VcUVKME40NkRvekRZYzFoZnAwWGpDQ2YrRTVrY29scTFobnhFdG5CT0w5UzlsTHZCdG1JYXdzVE1sbU5vZnl3SXYxcEpsMlQ5RGZyUVJQZkNtWFZkUFpvZVJsZFdMdm9hVUhmbFR6Rnh6SlB0bDltZzNkdGxsdmE3Y3hDVlNUYjFDa2ZjN3o3VllPMCtScWlWaHZGR0FDSDRFWW9Jc3locURqbzRjV1lycmNlK1FQS0lLM3lYT3p5TmVSNEg0ZFltNVV6UDMzb1E4dWVaeDRNRHlJU1ZjVlBqOEZMT0RnczNsU1Z6YlI3UWJ1eG5xbFJBOHprajBDd0g1amhRd1FzS2xuWGdLQUtJS0t4QVhNb3hIajdjK1VkRllXdUdqU0tOc3JXUXpwRWJYckVmcVBNdUdlMXFBTzVRL3JYYTQ4REJxbmFVUDdkMFhBZENwK2I5QzNQMmNaUHRXTy9oYm4wNXdxNjkyUUM2Q3E4RGdrUzJXMExnZlhHTEJoMElsWjFVY1hvOW5qMERVZ0g4MDVtZmF4bFptTE1ITFNFWlhnZkpQT2hyR0Z3QUp6MjZTUDdOaUpsK2o4MGZEMHBmN2duQS9zbVd2RXVIYnFxbFA0Q0VWVEdHaFludXgrUjZ0UW5McTRFenVrb0Zva2pXUE14ekNBMU8iLCJtYWMiOiIzM2Y0ODg4MmZlM2VhMzAxN2VkYjY4NGI0YzM0NWI0ZTZmMmI3OGM5MzlhZjU2ZWU1YjhlYzY5MGQxZTQzNWFmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkY4ZndMZlhNT3Q5aGZiUTFXeUJoUGc9PSIsInZhbHVlIjoiVzBpaGhQR3pHYlZ1UjNkdk9lUjA1RTJzcEJhaUpYamJiTVFBb2J5WG1XY2pmZlhVQ2FIczYrNnd3cVhwblNIaVduaitWeFVRLzdCcS8yb2o1OTVjUWNidERvdlFpdS9KYzlkRnNMWHlNSy84YzlIaDRJVWtkVElVTUQwNE1zYkF3VjNGQVczbThubTFmbEI1YlBhL1J2SEZ4aEhxeG1ZWjJldDRib2grTjBoWWZwUlh4RVpWek8zOHlrSHEvUUlEaDY0bXZzalZaYTZEL1N6WUk0K0FYQlZZdEY5Wm9OcGtmNHhETTNwOFVLcE9pdzczK0djYWZKUTJkbkZ1YzJDT2ZWcjNKdCtpR0Z0Qk9BWkZGQ3Z2cThodGI1dDQrbzMzSHVpZ1psMXZWbjlxNUFHUSs4WDhSenMrcTkvM3EwS0xTdjBrNGVJa1RjTWRPQWV2SzkxZDBXNzNnM0xQT1E5bEprb0ZpdTRiS2FaUDI0NnNDa2hzTThDaHBKR0YzamJQbFhZSEhJSWJTTGl4Y3JVR040VTAvYU5JR0pCYUtJMkRVdThrdXpXcXlibWxCZ21ra3dGQlpNSUxpaWpIMGk0ZDlaeFJ4UXpMT1I5R2hCbEl6TVhIY2FybnBiQzdpMzBiSHRDNk0xWktpMW43Mi9qc0NndkZzVnJCZzhuMDdtMjAiLCJtYWMiOiI3Yjc3OGU2MjBjNzYyMmFiNGVlZTliYmYxNDQ0ZmYzNjM3NTFkYTIyMWE5MTk5NGJhZWUxNmE1YTQxMzJhZGM5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775265611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-942621051 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942621051\", {\"maxDepth\":0})</script>\n"}}