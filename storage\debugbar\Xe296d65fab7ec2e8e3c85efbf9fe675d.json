{"__meta": {"id": "Xe296d65fab7ec2e8e3c85efbf9fe675d", "datetime": "2025-07-30 06:06:29", "utime": **********.529357, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855588.565859, "end": **********.529405, "duration": 0.9635460376739502, "duration_str": "964ms", "measures": [{"label": "Booting", "start": 1753855588.565859, "relative_start": 0, "end": **********.450323, "relative_end": **********.450323, "duration": 0.8844640254974365, "duration_str": "884ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.450337, "relative_start": 0.****************, "end": **********.529408, "relative_end": 2.86102294921875e-06, "duration": 0.***************, "duration_str": "79.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uGYMMS74gGWUKiX18RKd6dJvVpIwJjjMchPreQfP", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-948078231 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-948078231\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-363034399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363034399\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-887496680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-887496680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1208939770 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208939770\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1676404671 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1676404671\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:06:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndTNXZrb002dlNxbk9Qc0I1cmZERVE9PSIsInZhbHVlIjoicHRscTIzM1J0UFRQeDU3UXRTbnh0N3pSc3pIVkxDV0Y3Vm9Bd0h0emRZM0ZCRkhUU01VbC8yYTFSNU5nT1VrcnI5ZnN3YnA3b0NtU3lGTzg4c0wzSDJOK2pVME1oeDVGUUEyRXhHMjhEdGFIZjNQK2lIaU8ySVZGZlFxdloxVmp6WG9CU2hHZUlOMmxvWkI0Q09GUThSM3Y4Yk1LM0VtZEwwU0t3ZDJGblFHVlN3cnM0K3ZEZFR4VUdvaENONHV0SzV6U1YyTEpYbkNSNmpEWHk5dDlhbnUwdEkyaVpjZmNwM1ZES05PeHJ1Vlg4RlpqMTdnbWg5N0l6bDJYRDIzcTdDVDVZUXdxWkJDcWN0WEJwNjI5eE00ek10T0JKNHVGR3p1YjZjbUE0Q2JhY3A3c1BSdW1zK29mMUNlOVY3YXRtY2xGc1BSU0JuWmQzZ3ZrOThKZnBmYlNFY1ZReFpLeHFMc2E4d1VuNW5lVXFnNjc3dWQ0OUZqZTlrSmRzWjZHVk9TekJ5dmtPOEZ6b2ZMazNOeUlFVndqWVJxWGh6ZWtOSFJJRFMwOWhFdUhISmY1MmFwNkhRUVlvTWhlNnJpZCt4OERFbFA5OTUvclk1eTFBb3Y3SDlzaGJoc1d2WTNQYXBmcm5zTzhIZVYyLy9YR3RtNmg4VUxBN3k5a1IrUzYiLCJtYWMiOiIwMjU3YTU3ZjdlMDAxYWEyZGU1NWE0NDdkMDEzZWFkYjJkN2JlMGMzNzAyZDg5MjYyMjBkZDQ0ZDliZWE0NTQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlQvbjRaNUtmZUc4M0FQTjVGZE05Z2c9PSIsInZhbHVlIjoibjFHbDhqVUQ1S2Z1bWhPL0dyNUIzMlRINE5adHlsb2ZJSmlIS2tWN3VBY051UjBqU3kvZ3JCWGVuRm5nT2F1Wmp6UVNzSENpQUR3aHl6QzNEQlFPa2pZbUxuTlpRc0c2WE52ZHk3eFU3bGhBQTJVa0NkOFJ4ZDcrQjIvcE9VSHAzcm1MU2laajJUZ1N1azVoQW1UNXVaUmdZYkxUMlpYYTNwRkJGSDNhYzF6Ym9ZbnA1aGxKL3J5THJqWEYrd0JZSkNUSXJZVFl4TklxYjNYa0NmZWpSUnpZYkJ0WExYYldqQVd5dmYzMy93YURmUjVvNHZDZjNWVVd5VEdTNHQ4SytYdnlYRWJmMVZMUUJBbFgrYUhucUxSblZuY09YUDArQnNjdVduckRaWE1ORjRFb2JpVnpuNmtkQ2J3Zkp5MkEycEpBOGJMbEFJakxDR1FVbjE1WDBTMzFUZi92blU5d3pDeCtmUUZyU3BVcHFZRWxFeEt5ZWFYckFSS05iSHA2T2RoTlltZE5kMi91MGlmaHdOZStjTEdUU2p5WnYybmEybUhjdlNRaGs4d0NBeFdTNitoSDNjOHFvVmNtKzVGenpSamhiWFRXWGMyYUZvRzJ5WTRPUWdpMTZheWdZNXliTXNxbHg3ZVN0NFdDUFNSOGJUVHlMeGtpb1grM00xZDIiLCJtYWMiOiJiZjk3NzkxMDFmZjQwYzFlMTI5MzYzNmMwNjliYWZhMDNmMzlmOTBiOTU4N2IzMzQxZjdlYjMxYjEyZDcyMTY2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndTNXZrb002dlNxbk9Qc0I1cmZERVE9PSIsInZhbHVlIjoicHRscTIzM1J0UFRQeDU3UXRTbnh0N3pSc3pIVkxDV0Y3Vm9Bd0h0emRZM0ZCRkhUU01VbC8yYTFSNU5nT1VrcnI5ZnN3YnA3b0NtU3lGTzg4c0wzSDJOK2pVME1oeDVGUUEyRXhHMjhEdGFIZjNQK2lIaU8ySVZGZlFxdloxVmp6WG9CU2hHZUlOMmxvWkI0Q09GUThSM3Y4Yk1LM0VtZEwwU0t3ZDJGblFHVlN3cnM0K3ZEZFR4VUdvaENONHV0SzV6U1YyTEpYbkNSNmpEWHk5dDlhbnUwdEkyaVpjZmNwM1ZES05PeHJ1Vlg4RlpqMTdnbWg5N0l6bDJYRDIzcTdDVDVZUXdxWkJDcWN0WEJwNjI5eE00ek10T0JKNHVGR3p1YjZjbUE0Q2JhY3A3c1BSdW1zK29mMUNlOVY3YXRtY2xGc1BSU0JuWmQzZ3ZrOThKZnBmYlNFY1ZReFpLeHFMc2E4d1VuNW5lVXFnNjc3dWQ0OUZqZTlrSmRzWjZHVk9TekJ5dmtPOEZ6b2ZMazNOeUlFVndqWVJxWGh6ZWtOSFJJRFMwOWhFdUhISmY1MmFwNkhRUVlvTWhlNnJpZCt4OERFbFA5OTUvclk1eTFBb3Y3SDlzaGJoc1d2WTNQYXBmcm5zTzhIZVYyLy9YR3RtNmg4VUxBN3k5a1IrUzYiLCJtYWMiOiIwMjU3YTU3ZjdlMDAxYWEyZGU1NWE0NDdkMDEzZWFkYjJkN2JlMGMzNzAyZDg5MjYyMjBkZDQ0ZDliZWE0NTQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlQvbjRaNUtmZUc4M0FQTjVGZE05Z2c9PSIsInZhbHVlIjoibjFHbDhqVUQ1S2Z1bWhPL0dyNUIzMlRINE5adHlsb2ZJSmlIS2tWN3VBY051UjBqU3kvZ3JCWGVuRm5nT2F1Wmp6UVNzSENpQUR3aHl6QzNEQlFPa2pZbUxuTlpRc0c2WE52ZHk3eFU3bGhBQTJVa0NkOFJ4ZDcrQjIvcE9VSHAzcm1MU2laajJUZ1N1azVoQW1UNXVaUmdZYkxUMlpYYTNwRkJGSDNhYzF6Ym9ZbnA1aGxKL3J5THJqWEYrd0JZSkNUSXJZVFl4TklxYjNYa0NmZWpSUnpZYkJ0WExYYldqQVd5dmYzMy93YURmUjVvNHZDZjNWVVd5VEdTNHQ4SytYdnlYRWJmMVZMUUJBbFgrYUhucUxSblZuY09YUDArQnNjdVduckRaWE1ORjRFb2JpVnpuNmtkQ2J3Zkp5MkEycEpBOGJMbEFJakxDR1FVbjE1WDBTMzFUZi92blU5d3pDeCtmUUZyU3BVcHFZRWxFeEt5ZWFYckFSS05iSHA2T2RoTlltZE5kMi91MGlmaHdOZStjTEdUU2p5WnYybmEybUhjdlNRaGs4d0NBeFdTNitoSDNjOHFvVmNtKzVGenpSamhiWFRXWGMyYUZvRzJ5WTRPUWdpMTZheWdZNXliTXNxbHg3ZVN0NFdDUFNSOGJUVHlMeGtpb1grM00xZDIiLCJtYWMiOiJiZjk3NzkxMDFmZjQwYzFlMTI5MzYzNmMwNjliYWZhMDNmMzlmOTBiOTU4N2IzMzQxZjdlYjMxYjEyZDcyMTY2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1102911179 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uGYMMS74gGWUKiX18RKd6dJvVpIwJjjMchPreQfP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102911179\", {\"maxDepth\":0})</script>\n"}}