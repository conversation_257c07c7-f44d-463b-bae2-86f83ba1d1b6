{"__meta": {"id": "Xe133a304f07e3c533f1e29c404e480cb", "datetime": "2025-07-30 05:30:54", "utime": 1753853454.011199, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:30:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753853454.006143, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753853452.370675, "end": 1753853454.011237, "duration": 1.640561819076538, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1753853452.370675, "relative_start": 0, "end": **********.709245, "relative_end": **********.709245, "duration": 1.3385698795318604, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.709274, "relative_start": 1.3385989665985107, "end": 1753853454.01124, "relative_end": 3.0994415283203125e-06, "duration": 0.30196595191955566, "duration_str": "302ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48333392, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.01578, "accumulated_duration_str": "15.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8031352, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 29.785}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.837501, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 29.785, "width_percent": 6.971}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8460162, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 36.755, "width_percent": 7.541}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.851881, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 44.297, "width_percent": 4.373}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.877016, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 48.669, "width_percent": 5.07}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.894305, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 53.739, "width_percent": 6.464}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.904558, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 60.203, "width_percent": 5.64}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9127262, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 65.843, "width_percent": 34.157}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard?_token=LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP&code=229351&description=&discount=890&discount_type=fixed&end_date=2025-08-07&is_active=on&limit=25&name=Manik&product_id=&start_date=2025-07-30\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-402808218 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-402808218\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-579269863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-579269863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-377561437 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377561437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1312559694 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"232 characters\">http://127.0.0.1:8000/finance/dashboard?_token=LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP&amp;name=Manik&amp;code=229351&amp;product_id=&amp;discount_type=fixed&amp;discount=890&amp;limit=25&amp;start_date=2025-07-30&amp;end_date=2025-08-07&amp;is_active=on&amp;description=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImtBNUxTOHoyTVAvOTFMWHdQaUZ3WXc9PSIsInZhbHVlIjoiNHlZN0t5aytweWJpTTBZKzRZM2ppVTdieE9lZlBNdWJyMmc4NmlxWEJRcU9pRHNIMHZGNHBjVms5VGt0d1NhaTBreHhQenZJaXd2Q2lSRFQ1eXVkY0h6eDlEQUFnYU5XeWZ2OGRkempPNEtxcXlHdUFWVXJQK1RNckVscmhvanVCV2RKTm9lajltUGpaVUtlWkkrbUxBazRzYWdHeG02MWhWWXM4M1VPSXZCcFFXcEJBcko3MDI2dE82azAwa3pRZ015UHlJamVKak9YZ0dNTkYzQjBpVWxKWWsrZkU0cEpoYW5iT1Vhd0VvVzJRNzA3YTZwM1A4WkpGUjBvajkrcjFtdWxUNVBDVTdlRzBERG5UdDdWZGRpUjJOUWVvNDhhb0tpNU54OTdGaHQwUnNsVFlqVFo0Ulc3MTVxaGdpOWRUa2lLREtrSm5aS1N4L1EvcUpjaTlYZTE3SEtmbVUyMy9PMDI0cGFrWGVMbmRGY1FBOXp2SFlVZFArbWhHMEN2WVhEbi9UMkczQ3pXc05scXY4d2g2WjQ3UDBVYk0xMGFkVTJxQ0N4MXBucTlmRlhHT1lkNHZpV2tFam5nbDdGUHR3OUk2bUpxaCtXYnhjSG9EM3kxL2VJMEtUUDZFTnRkU2Fld1A0REIzbnp3RjNKVkRYdExxK2xWNlkyNXFXZGUiLCJtYWMiOiJkNzE5NTI0ODIyNzkyZjUxYWMyNTUyNjk2OWVhOWQ0YjBhYzA5NmVmNjBmZWNkZDdiZjQzN2JlMTEyNTMyN2U3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IklDRE4wdThhUGRqVE5KU085eSsyNUE9PSIsInZhbHVlIjoiU0NycGZNd0VPRURiVWxjMEpNYmZZdDNDTUVOMFp0b3Ridk0xVUdCcGcycUkyTmN0Rmd5VEN5RzIrRWhwWVZiZUM5YWRSTEs3bkNPMy90TU8wSzh2REFuZTBic2dWOXEyTzR6M0R5c1NEWVRBQkdFaHFGVzI3M0FYcmNzdDZoWkNHRlJEc0E0WHZTRVAxelh3L0tadjN1R1RrZThPMXZncUQ2WWliNTljd2lIZDEvK3VXbFdIVXl1aWZxcmJITnlDRjVOd1B5M0ozYjdhaDI0a1R0ZGxPSElFQlArVnpDdFF4U1pQY2Uzc3FCYjV4L2VtNHdQaFh0TURsNmQ2UTRKNTBTcDFqWEtIa2M2eWlmaWpRcTRkNWhyT0pjTC80UXI0RFZEYk9PZGxXWWhYbWpHLzd4WUtxWUt0a09pT2NvMWZCWHQ0YUZsU3A5OEFIZXRPbmdUZXowMEV0eklQSDRXUU5zYWwycDNzb1hNWmhQTTBQcEVhaUtsWGkrMUd2bVo4Mkd0STNXUTEwYjNTRnV5QkNsYURTRGUxSi9DUEhEM09vcm5sUjdaWUZmZlRmWXJNbDNoZGVVYUpQdkhWa2xKQ1lkcXRmOHQ1djNpeUdzL1pvMGpEMDdKdUdLbUx5NFBRMzZIQjMra3YxUHBDbjBLUloxOWUzRXptSVVjVkl2VGEiLCJtYWMiOiI5MTVjNjczZDgxOTQwNDRiM2I1NmFhNDY1OGRlOTNlMWIwOGE2Zjc1ZTZjMDlmMWQzNjQ3MzlmMWYwYjk5YWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312559694\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-339963540 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339963540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1458440174 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:30:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitSRkJnTS9JcTFoelpZMWREYnlxdkE9PSIsInZhbHVlIjoicGhqbU5aRk1udWFkVGMrNEx3d3M5NUxKNm9FZ0laU0hmRmlKTGpUU0J5OEZxUFh3VDF3eTFFUC91Y3M2SnkxbmNCZkZ2ZFNqbG8vODlmeWwvR2VOZytmN0NNR0s1dkN4MWZmeklMQUpIYlVpcHVWZU1aNDB2YTNCMUlBc0kyeGtEdHlaL1ZrYTJrNFdyQVpIUHRDM0orekNmMWF4ckl2VlB6MUNNQkM2WlBxaytRS09IVEEyUUpIYjlMRTczcFF6MnVzVkNHUzJaMXhzeFFDS203NldFSGNZdWFobnhseXRmR2pRcWk5NDhibUt3aENFd0EvNHpqYlBtSnowNkRiclRIUG05OE1menVaK2srRHNIYWZXNVJScVVPZjZjTlNpdGtqSEw2cFRFM2JuSi9VaURVZDErNGNLSWJWNTRjY0FnLzRzUUY4aWpPMzl6TGlNdCtzYlRrMnNLbFJLUE9FUEZaK0pOWllaSmI0bVFBdXFCbDIrUVFNT2dENXUzOEFGb2hsUnlQcSszU0RjVHVza1hHSXBJNkU5SVFnN0xFUUEwT3cxeVlFUFJCTG9aV3YwNlVLVFVFMkxkaUJ5WVpMdlN6Ly9CeUZVMEUrSit3K0hWTFljTTdmWEc1YlFNTGNURFU0MXRWdkhNUVpmaWdUTGhFN0owVzB2b3N1NXhHRjYiLCJtYWMiOiIyMGExY2RhOTkzZjQ2NzBjNjhmNTcwMWVkYmYzODhiNTI1ZGY0MzY5ZWU2ZWJiOGQ3NDYyMjUzOGI2OTczYWEyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitWUWxUVzFseUplbUxIaHNCd3c4NkE9PSIsInZhbHVlIjoiT3pEdTM5UGlrUFREY1c2aW1NckM5ODRJZmlBRlhwRGdPNUZyem9QYW52OUwydmlkdllyTGY3N3dsWEczWGp2VFMwK2plZW9MTERoRW9NMFY2WUMwcVZFZW1JaDRlN3RiaGQ5bVZZa0krSFM4eFBwM2hTRDU3LzhuWnV1Q3M3NU5acjRzb01xZnBFMG0zYk51K1hrbzBkS3N4UTBJUHBpdURYRGJPUE9vblZaNy9zVkZrUVVrSzFrTksrYS9MeU4yUU1vUEJTamY0aSszU2JmNVByMzZKN1cxZTJ0MWo2MEgvQkUzeTA2Y2lQei9OUzhxa3ZWbEdmV3p0T2lxQ0NYY0pZcTFxaEdTdUxVTDQzczlLVUU2MXc4U0dUVWYrcVhDanF6azZ2RFJ6TDl5eHRweTlrM2poOTNIVXNVeHZxYkVPVUxobFZKMXhMc3dtR0tjMkh0U2l4eDdZcmJuU1lxZUZaMndYMk5TZit6eWV5dlpFc0MxZFF2Uk5DQ1ZZcE9MeWdtWHJ5dHlFRkdIczRmazBZeGRhclB1STZJK0RwSnBBR1hEZTNtcnFZWm5zNDV6d1RWZE1vYUE0TVFMYklwREJmdEZ0MlR1Y0dFRHozU044a0h1NFVZSFFjNEI5b2IxZm54SlBqQ25RQ2s0ZE1vMkdEYTZYNW1tQmxCWHQ3em8iLCJtYWMiOiJiYjVmMjE4NDY1ZDg0Mzc3M2U1MjFjMjRjMjQ4YmFjNTAwMDVjODU0NGNiNjRjMjQ2ZGNkYjFiM2M0YTNiZjE0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitSRkJnTS9JcTFoelpZMWREYnlxdkE9PSIsInZhbHVlIjoicGhqbU5aRk1udWFkVGMrNEx3d3M5NUxKNm9FZ0laU0hmRmlKTGpUU0J5OEZxUFh3VDF3eTFFUC91Y3M2SnkxbmNCZkZ2ZFNqbG8vODlmeWwvR2VOZytmN0NNR0s1dkN4MWZmeklMQUpIYlVpcHVWZU1aNDB2YTNCMUlBc0kyeGtEdHlaL1ZrYTJrNFdyQVpIUHRDM0orekNmMWF4ckl2VlB6MUNNQkM2WlBxaytRS09IVEEyUUpIYjlMRTczcFF6MnVzVkNHUzJaMXhzeFFDS203NldFSGNZdWFobnhseXRmR2pRcWk5NDhibUt3aENFd0EvNHpqYlBtSnowNkRiclRIUG05OE1menVaK2srRHNIYWZXNVJScVVPZjZjTlNpdGtqSEw2cFRFM2JuSi9VaURVZDErNGNLSWJWNTRjY0FnLzRzUUY4aWpPMzl6TGlNdCtzYlRrMnNLbFJLUE9FUEZaK0pOWllaSmI0bVFBdXFCbDIrUVFNT2dENXUzOEFGb2hsUnlQcSszU0RjVHVza1hHSXBJNkU5SVFnN0xFUUEwT3cxeVlFUFJCTG9aV3YwNlVLVFVFMkxkaUJ5WVpMdlN6Ly9CeUZVMEUrSit3K0hWTFljTTdmWEc1YlFNTGNURFU0MXRWdkhNUVpmaWdUTGhFN0owVzB2b3N1NXhHRjYiLCJtYWMiOiIyMGExY2RhOTkzZjQ2NzBjNjhmNTcwMWVkYmYzODhiNTI1ZGY0MzY5ZWU2ZWJiOGQ3NDYyMjUzOGI2OTczYWEyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitWUWxUVzFseUplbUxIaHNCd3c4NkE9PSIsInZhbHVlIjoiT3pEdTM5UGlrUFREY1c2aW1NckM5ODRJZmlBRlhwRGdPNUZyem9QYW52OUwydmlkdllyTGY3N3dsWEczWGp2VFMwK2plZW9MTERoRW9NMFY2WUMwcVZFZW1JaDRlN3RiaGQ5bVZZa0krSFM4eFBwM2hTRDU3LzhuWnV1Q3M3NU5acjRzb01xZnBFMG0zYk51K1hrbzBkS3N4UTBJUHBpdURYRGJPUE9vblZaNy9zVkZrUVVrSzFrTksrYS9MeU4yUU1vUEJTamY0aSszU2JmNVByMzZKN1cxZTJ0MWo2MEgvQkUzeTA2Y2lQei9OUzhxa3ZWbEdmV3p0T2lxQ0NYY0pZcTFxaEdTdUxVTDQzczlLVUU2MXc4U0dUVWYrcVhDanF6azZ2RFJ6TDl5eHRweTlrM2poOTNIVXNVeHZxYkVPVUxobFZKMXhMc3dtR0tjMkh0U2l4eDdZcmJuU1lxZUZaMndYMk5TZit6eWV5dlpFc0MxZFF2Uk5DQ1ZZcE9MeWdtWHJ5dHlFRkdIczRmazBZeGRhclB1STZJK0RwSnBBR1hEZTNtcnFZWm5zNDV6d1RWZE1vYUE0TVFMYklwREJmdEZ0MlR1Y0dFRHozU044a0h1NFVZSFFjNEI5b2IxZm54SlBqQ25RQ2s0ZE1vMkdEYTZYNW1tQmxCWHQ3em8iLCJtYWMiOiJiYjVmMjE4NDY1ZDg0Mzc3M2U1MjFjMjRjMjQ4YmFjNTAwMDVjODU0NGNiNjRjMjQ2ZGNkYjFiM2M0YTNiZjE0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458440174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1788877306 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"232 characters\">http://127.0.0.1:8000/finance/dashboard?_token=LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP&amp;code=229351&amp;description=&amp;discount=890&amp;discount_type=fixed&amp;end_date=2025-08-07&amp;is_active=on&amp;limit=25&amp;name=Manik&amp;product_id=&amp;start_date=2025-07-30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788877306\", {\"maxDepth\":0})</script>\n"}}