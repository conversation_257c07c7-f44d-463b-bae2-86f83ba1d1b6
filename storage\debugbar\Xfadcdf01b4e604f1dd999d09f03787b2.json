{"__meta": {"id": "Xfadcdf01b4e604f1dd999d09f03787b2", "datetime": "2025-07-30 05:39:26", "utime": **********.816611, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753853965.441136, "end": **********.81664, "duration": 1.3755040168762207, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1753853965.441136, "relative_start": 0, "end": **********.712569, "relative_end": **********.712569, "duration": 1.2714331150054932, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.712592, "relative_start": 1.***************, "end": **********.816642, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jxycVvdw0xerjcP0l1yRtEKAVWEzDjepcFDn1Yzc", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-86552225 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-86552225\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-474411608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-474411608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-949523956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949523956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-190390559 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190390559\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1526489724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526489724\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2087732400 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:39:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1aY0h0c2loRUM2N2hBVlhNQjJlRlE9PSIsInZhbHVlIjoiaEhDOEJqWlA3QVUyYWJTak4rUFY3dnVnRnlRTHBlVmdFTnhzakVZNHpMSG45U2xUSFBPTWdwUWQxbUhOV0RPLzUyRCtISGhWd2NMdVdra3ZYWGFRV1M0cUxyck5pclowNjVlc0tnQW1VdzgzV1orRDNDbmpUOStIWC9IdzNEOURKQzFHMWxBVW9XOVQxeGlFVmZaNERhcnNCME5ZaGZ0YzluODhQWDdzb0ZkTjVGRytyN1YvWi9YNnJJRHUrT0IvamlrdE5nYWVsZ09ZYjFTWlFLa0xlaEJueUVYUm92R3BPMEpvS3hzbUoxZ3UxaDBqWGI0bWhoaTl3R0I4dXhqWkhwRUt4enBwYWt3citQY3AxY0dqSW10ZzNDVE4xRG5WVWlvYTY3MDdHSXAxQjJoam50dFFpQ1U4d3FpVmd1SmFUakFuV2hGZUtiUU5namxDTjJISStWS0lGNk1XbE92VTBkdHhaY0htMnRWRG5hUzJXSDNHNWNhdUEraHRxRXRPRUhBTUhiY0dPazJlSjNyd0p0Zk1lOHl2QW1zdUhGV2tucnVqdmFQSmZKYWliSzhZN3UrV0xzRmdsZUlaU1dtUG5yYTE3QWpGOHlwd3hoaDl4RzgyZ1VKODBDVml4eXJXUjhITThuT0hlWkt2Vm1NVHJtcTNLNFBwNTlPeXhFQTMiLCJtYWMiOiJlOGU1ZjI5OWUxMGYzNzkwY2NiYjUwNWEwMTAxYzIyMTk1ZGQyNTlkN2FjYzRiOWNiMWJjMDA3YTZmN2YzM2I5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:39:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IndqOGpIRUVtS1RISnNUbkpJRlNpNnc9PSIsInZhbHVlIjoibjV1aWM3WS9QZ016ck5WRklGeFB4OStmY0ZtZHYvdVlCb25hWmVKakV5WGtSUXYvRzBNbmw4OFJLRzE5UE96S2Nqa2xZNEFRSVQ1TzBVbStGdXVzNDBBcFVjaUd2dWEyN01qOGtLYUllNjhqY0IxVW0zaCsweTBjcnlOWlJSY3BMVm54VGFreVRma09wU0VHeXF4bVo3MmxYd1YzWkFscXBKUHB6MzdZWENpK0VOSGRmQ2I3UTNTVkxWR0JYTHJJTUZmMGFFL0NSOFlWZjVmNzZCRzhNd2dpR0RDV2pPMWV5VGRUMlJadGpFakE0aWV5MS9mS0Z5OGJqZ01OaEpwS3Z3UXpOcVV0TUVhc3JZcmFlQUlxb041ODJZb0hDWmNSTU14blJXMkttS0lZdXhVRjNNWmRSVFg5dVlSOGkrQlc3bXpaczZsN3dKZUcwY2pleXIwQ24xKzdiR3NneklZUFByNjNnNlNtbjVGbFhpMFJrc0tyaW13SnpVS3NCWm0wd0FJRFlsMzcvQ0M3QkFTVzhjT3h6SlpSeVIvU0EvNllrZnNPR1ZKeGtVVjlxcEloZmxKN3YvK2hhU2RCTnR3bmhhNDFpaWdXenFYUEVCZTJLSXdTbmV2ZVg2UDhCUzUrdFZKdHIyVStZaW1mVUtha0FiTHFUUXlwNDAyNlhpZEUiLCJtYWMiOiJlNGIxYzE1MGU3MzMxOTI4MGMxMDgzYjMyNjUxY2M2N2Q0NjYzYzE0YmYzMDJkZDBmYmM2NDEyODMyN2ZhMDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:39:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1aY0h0c2loRUM2N2hBVlhNQjJlRlE9PSIsInZhbHVlIjoiaEhDOEJqWlA3QVUyYWJTak4rUFY3dnVnRnlRTHBlVmdFTnhzakVZNHpMSG45U2xUSFBPTWdwUWQxbUhOV0RPLzUyRCtISGhWd2NMdVdra3ZYWGFRV1M0cUxyck5pclowNjVlc0tnQW1VdzgzV1orRDNDbmpUOStIWC9IdzNEOURKQzFHMWxBVW9XOVQxeGlFVmZaNERhcnNCME5ZaGZ0YzluODhQWDdzb0ZkTjVGRytyN1YvWi9YNnJJRHUrT0IvamlrdE5nYWVsZ09ZYjFTWlFLa0xlaEJueUVYUm92R3BPMEpvS3hzbUoxZ3UxaDBqWGI0bWhoaTl3R0I4dXhqWkhwRUt4enBwYWt3citQY3AxY0dqSW10ZzNDVE4xRG5WVWlvYTY3MDdHSXAxQjJoam50dFFpQ1U4d3FpVmd1SmFUakFuV2hGZUtiUU5namxDTjJISStWS0lGNk1XbE92VTBkdHhaY0htMnRWRG5hUzJXSDNHNWNhdUEraHRxRXRPRUhBTUhiY0dPazJlSjNyd0p0Zk1lOHl2QW1zdUhGV2tucnVqdmFQSmZKYWliSzhZN3UrV0xzRmdsZUlaU1dtUG5yYTE3QWpGOHlwd3hoaDl4RzgyZ1VKODBDVml4eXJXUjhITThuT0hlWkt2Vm1NVHJtcTNLNFBwNTlPeXhFQTMiLCJtYWMiOiJlOGU1ZjI5OWUxMGYzNzkwY2NiYjUwNWEwMTAxYzIyMTk1ZGQyNTlkN2FjYzRiOWNiMWJjMDA3YTZmN2YzM2I5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:39:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IndqOGpIRUVtS1RISnNUbkpJRlNpNnc9PSIsInZhbHVlIjoibjV1aWM3WS9QZ016ck5WRklGeFB4OStmY0ZtZHYvdVlCb25hWmVKakV5WGtSUXYvRzBNbmw4OFJLRzE5UE96S2Nqa2xZNEFRSVQ1TzBVbStGdXVzNDBBcFVjaUd2dWEyN01qOGtLYUllNjhqY0IxVW0zaCsweTBjcnlOWlJSY3BMVm54VGFreVRma09wU0VHeXF4bVo3MmxYd1YzWkFscXBKUHB6MzdZWENpK0VOSGRmQ2I3UTNTVkxWR0JYTHJJTUZmMGFFL0NSOFlWZjVmNzZCRzhNd2dpR0RDV2pPMWV5VGRUMlJadGpFakE0aWV5MS9mS0Z5OGJqZ01OaEpwS3Z3UXpOcVV0TUVhc3JZcmFlQUlxb041ODJZb0hDWmNSTU14blJXMkttS0lZdXhVRjNNWmRSVFg5dVlSOGkrQlc3bXpaczZsN3dKZUcwY2pleXIwQ24xKzdiR3NneklZUFByNjNnNlNtbjVGbFhpMFJrc0tyaW13SnpVS3NCWm0wd0FJRFlsMzcvQ0M3QkFTVzhjT3h6SlpSeVIvU0EvNllrZnNPR1ZKeGtVVjlxcEloZmxKN3YvK2hhU2RCTnR3bmhhNDFpaWdXenFYUEVCZTJLSXdTbmV2ZVg2UDhCUzUrdFZKdHIyVStZaW1mVUtha0FiTHFUUXlwNDAyNlhpZEUiLCJtYWMiOiJlNGIxYzE1MGU3MzMxOTI4MGMxMDgzYjMyNjUxY2M2N2Q0NjYzYzE0YmYzMDJkZDBmYmM2NDEyODMyN2ZhMDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:39:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087732400\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1513141258 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxycVvdw0xerjcP0l1yRtEKAVWEzDjepcFDn1Yzc</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513141258\", {\"maxDepth\":0})</script>\n"}}