{"__meta": {"id": "X6a4090bf1cf2296a80facfee93e201d0", "datetime": "2025-07-30 08:37:43", "utime": **********.888176, "method": "GET", "uri": "/finance/sales/products/search", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753864661.199744, "end": **********.888214, "duration": 2.6884701251983643, "duration_str": "2.69s", "measures": [{"label": "Booting", "start": 1753864661.199744, "relative_start": 0, "end": **********.374189, "relative_end": **********.374189, "duration": 2.1744449138641357, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.374222, "relative_start": 2.174478054046631, "end": **********.888218, "relative_end": 3.814697265625e-06, "duration": 0.513995885848999, "duration_str": "514ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46700512, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1041\" onclick=\"\">app/Http/Controllers/FinanceController.php:1041-1084</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026250000000000002, "accumulated_duration_str": "26.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7881489, "duration": 0.02407, "duration_str": "24.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.695}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.848795, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 91.695, "width_percent": 4.762}, {"sql": "select * from `product_services` where `created_by` = 79 order by `name` asc", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1055}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.859664, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1055", "source": "app/Http/Controllers/FinanceController.php:1055", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1055", "ajax": false, "filename": "FinanceController.php", "line": "1055"}, "connection": "radhe_same", "start_percent": 96.457, "width_percent": 3.543}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-1321901135 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1321901135\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-426074873 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-426074873\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1426271604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1426271604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1725664828 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkhWZ09pVGJGbHpBR1liYWhwVG0rVFE9PSIsInZhbHVlIjoiUE1MekN2OVlBbTl0NEJFenRlRHJGYS9kQ1JlR054RkdWTXgwd3VRRFh1TkZ3STJQR1didkZWUHJoVUp4bDc5UmtmVXBiT1JBeWZqc250RnF2elZPRjhHYlVIcVJjRm56TUQ0b3ZoSEc0TDFuVGNma0lreVNraHRXMWo0bklIbFNRc0J0dUN2UFI3RkIvSzJleE1TVWY1bHVaV0dITkIvMXpuQXdjUWM0djJYR2xhU1MrVEIvZkxOYlVXQW5wdThFSGtOM3UwQnhGRU5tRjE3QnJLdk56U1owZTVGb2VwL1A4WUVWOUhoMm8rejhvZzREMVM1V21pcDRRZ1VyNldyNU14YmVIdm9YOG1WZXRmRlQ0eldvTWYrRmNsd2N0M1duandITnFzcThoeS8vWG5ENjJIcy90QWo0T1hxMDNWRENkQTg4U2ZKWWhNM2t6R3h0NWlTZkFlb2JIckZJN0RUZnl2TkQ2bFVvMlZhNGpNOFExakVTMkFrRjgvZjdqME1rOFZpVzFVRXNOQ0xjd0ozOVlWMTRhS3RsNnlhU2t0aEdLcHhxVUJoMk05eHhQaW1QVHNCbkFqVDBLTFZCVjZwbWxzWjZ0ejZVbEl5WW9ZNDJGaVJobXdmN3Z4cE0yc0NoQnhqOVliNzljYWQ0MjNGUS9GTUdpdHVDblJNTC9WNVMiLCJtYWMiOiI1NmQ1Yzc2OTNmNDcyN2NkODhjMDNmNTBiZThlMjQ2MWNjODAwYjczYzI3OWI0MmJiZWE2MTJiOTQ1MjA1N2YyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1yOFo4ZjB5MzlRcVB1MVlKQTd3NVE9PSIsInZhbHVlIjoiZTA1SzFzYkJqSExuQ1FRUklrUDN0ellVSVpsUnFYTTMzbmRmOTJwdys1VjIyL0tObmZzaUhQaUFVcmg2bWgrRGdnWXU2ZHJXL3dJZHNCTWppWmVWM3hrZFFKTUhPRjFwd2s0QnZQQW55VWVDS2JnU3QyTDZndVVHTFdNN2cxUVd3c1l4cXdpdWFxdlNuT3Q0cmpaTXFuc0NFUmlVUTdPbXpDeDBnS2pqNnJmZkxhay9XVDdHQ3BtekJBRWdtMzJLSnpEUEhLZnBYcFJacDJIeTBaUzlQYThJa2FRVmVzbDl6QXh4Njg3aUVkaFV6K2x6d2o1bUtDK0xNTmJqMG5Zb2VLS0R4S1dCaFUyUHYyZGJNa3hMWDQ4aExJb2FZUUkxNUl4NEVsSVFYZml0S1I0azcxcURaaHA2UUFPbEg4amowUTR5NmRvNnVrWC81WjRvS05xRkkrdGZDR2FINTM1NDRhM2dqQTRtMHdNRVVtb2grdjZtcjYxVU9UanFFV1l6KzhzV0RQR2VQTzg2aXBSYnl4aGt0WTRKcmQ4RXo2eUxzc2VnYlAyTC9ydEx4QnNvL1c3bkdrVFBLWFY5dlZNV2IzcTZ1QkVwRVlqek5tblp6T3NTcmQ4eldicWowTllpMXYvK004NTNDY2NFeGwxRmhtd2JIQjZCQWlOWCs2NGIiLCJtYWMiOiI0OTk2NzUzOTdkOWM5YjE2YmE1YWM4ZTJiOWIxNmQyZjU0NDBiYjFmNWExZTIwZGE0YTljZGE3NGY2NGE2NTFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725664828\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1785537763 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785537763\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1066374497 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:37:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhQNTkzWkhTNGFhUkhRSEhZYld6OWc9PSIsInZhbHVlIjoiUjR3VjFvZXV0TFZpTDFmVUdvdC9MS1JnYUhlMkpodFBVQlhhTExzTlNYcjdsOHplMFlIV0s2Z0Rab3ZrME5pOUhqZGlTSDJMWjZGNjU2amFieFByQWNzTDVTWjhXZlJVeUpvVGVhNE5kUEhTTjEzejB2Zjg5YmRqWm1LdXo1ckR6KysvRjBybzUxYmZBV2RaRVY1NkMyNGs5LzV0aXA4SmpjVE5RMnpxWERzM1pFa1B5S3E3UGdUNEgrUVp1bGtjSE9zNWozdmw2NTZodlpaYXRxZkNDaGlYY0ZLZENUZzllMWdWa3JHY1NEbzdzNm9HQ0FnUnBRQVBqZlpTVmF4Z0JzRm84VmRWTHBqUHpYd2tmTysrbzR1N0wxU014OEVJUTJzdzNYMkRUanRkYVhnVTNjZEVXRlFrSTdOUTUyU3dSUTdkRnppZmV0M3FZUVgrd1g2SlNzUzBwWHZnc2pmbi9HTXUrekdXajEwL1UvV1czMStONXJOTTdWUW9rRFBuVm8yK0p5azZSNk5Zc2tXNW9kZFJoMWV6SWMvRWxjWWoycC9mWkM2dmt3RWZZazFvakJ1bzYrdktUOXNoTEpLd2tSQ2V0dmk1Z1dMMzcxanVMRkwyNjZFOE1iMFZ1by9NU1ppcDlMQjlPKytHaXlXVjQzRjQwRHM2Y0R5KzBtMDciLCJtYWMiOiI1M2VlODk4Y2U1NWY5ZDBjMzE3NzY4ZDZlYmFlMGQxZjY4MWRhZTQ0ZTllMTE4NzVmODUwODMxYTM2MTk3ZjE4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:37:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklQc0F5UEpsdzVyanZIS1JFTkczVEE9PSIsInZhbHVlIjoiZTBwMHFtZW9OeS9lanNPTTJseTczcVorVFRUbUgzeDBScDZ2OFRGd2s1dW9vcFd6Uk1FbElORFJWQUVuOXR4d25qbGNsdDJCN3FkWU1aWWJIWHhYZ0hoOTNQTzJGblhpVGROZUIxVzlERzBVZlFBYjV1dWJKY0hUWnYrOGZ6bm5NaS9PaDV3b2NoQWZ1TVk1ZjgxM2t3TEliRTZYbXY3dUtxb1IyODgyVERTTi9hWmlRRWxZMjJxOWduK2VFKzdtRVRMazVaMCtDSy8wa2dnVGRTakpZN1VicEdKL21RWnh2aE1WYklPdk40Q1JDL1BHYTNBcnBZQ2Ewc1NDdThCWTk4ck96a0RQcjZnaHl4OFVTR0J2NzFYdnpSZ3Fqc1MxOFNVdlRLUFpnRTBGclVyZzloQUg5NzltOEZJeDU3aXJ6eU9vZGRzK1NXV1ZXTE5uOFg1UUlVT25nRWJPd3RBV1dHK0QwcW5XYkxUL0svbmRMbDBtS2QrUEE0OGh1T1BnRGVVYkFWeG4wRXNJYWdGSkhWR0NvM1NIdklqbU9ZcGEwdnVORDF3TXkrVkdYREJESTU3a3A1NG5RK2hZT3kzdHdxQVRyR2QrZE4xa1JqZFA3dU1rLytEVFBlMWdmTEk4MDdqMWtpRUErY1hEQ1VPUUN4QUJDZTRleVFyZnhOZnAiLCJtYWMiOiIxMDUyZDc3NWIxNTUxZTFkMjZkYWI1NWYxMDMxZGYzMzc2Y2IzNWI4ZjU1ZjY0NGM1ZjI1MTM2NTY0ODAwOTU1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:37:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhQNTkzWkhTNGFhUkhRSEhZYld6OWc9PSIsInZhbHVlIjoiUjR3VjFvZXV0TFZpTDFmVUdvdC9MS1JnYUhlMkpodFBVQlhhTExzTlNYcjdsOHplMFlIV0s2Z0Rab3ZrME5pOUhqZGlTSDJMWjZGNjU2amFieFByQWNzTDVTWjhXZlJVeUpvVGVhNE5kUEhTTjEzejB2Zjg5YmRqWm1LdXo1ckR6KysvRjBybzUxYmZBV2RaRVY1NkMyNGs5LzV0aXA4SmpjVE5RMnpxWERzM1pFa1B5S3E3UGdUNEgrUVp1bGtjSE9zNWozdmw2NTZodlpaYXRxZkNDaGlYY0ZLZENUZzllMWdWa3JHY1NEbzdzNm9HQ0FnUnBRQVBqZlpTVmF4Z0JzRm84VmRWTHBqUHpYd2tmTysrbzR1N0wxU014OEVJUTJzdzNYMkRUanRkYVhnVTNjZEVXRlFrSTdOUTUyU3dSUTdkRnppZmV0M3FZUVgrd1g2SlNzUzBwWHZnc2pmbi9HTXUrekdXajEwL1UvV1czMStONXJOTTdWUW9rRFBuVm8yK0p5azZSNk5Zc2tXNW9kZFJoMWV6SWMvRWxjWWoycC9mWkM2dmt3RWZZazFvakJ1bzYrdktUOXNoTEpLd2tSQ2V0dmk1Z1dMMzcxanVMRkwyNjZFOE1iMFZ1by9NU1ppcDlMQjlPKytHaXlXVjQzRjQwRHM2Y0R5KzBtMDciLCJtYWMiOiI1M2VlODk4Y2U1NWY5ZDBjMzE3NzY4ZDZlYmFlMGQxZjY4MWRhZTQ0ZTllMTE4NzVmODUwODMxYTM2MTk3ZjE4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:37:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklQc0F5UEpsdzVyanZIS1JFTkczVEE9PSIsInZhbHVlIjoiZTBwMHFtZW9OeS9lanNPTTJseTczcVorVFRUbUgzeDBScDZ2OFRGd2s1dW9vcFd6Uk1FbElORFJWQUVuOXR4d25qbGNsdDJCN3FkWU1aWWJIWHhYZ0hoOTNQTzJGblhpVGROZUIxVzlERzBVZlFBYjV1dWJKY0hUWnYrOGZ6bm5NaS9PaDV3b2NoQWZ1TVk1ZjgxM2t3TEliRTZYbXY3dUtxb1IyODgyVERTTi9hWmlRRWxZMjJxOWduK2VFKzdtRVRMazVaMCtDSy8wa2dnVGRTakpZN1VicEdKL21RWnh2aE1WYklPdk40Q1JDL1BHYTNBcnBZQ2Ewc1NDdThCWTk4ck96a0RQcjZnaHl4OFVTR0J2NzFYdnpSZ3Fqc1MxOFNVdlRLUFpnRTBGclVyZzloQUg5NzltOEZJeDU3aXJ6eU9vZGRzK1NXV1ZXTE5uOFg1UUlVT25nRWJPd3RBV1dHK0QwcW5XYkxUL0svbmRMbDBtS2QrUEE0OGh1T1BnRGVVYkFWeG4wRXNJYWdGSkhWR0NvM1NIdklqbU9ZcGEwdnVORDF3TXkrVkdYREJESTU3a3A1NG5RK2hZT3kzdHdxQVRyR2QrZE4xa1JqZFA3dU1rLytEVFBlMWdmTEk4MDdqMWtpRUErY1hEQ1VPUUN4QUJDZTRleVFyZnhOZnAiLCJtYWMiOiIxMDUyZDc3NWIxNTUxZTFkMjZkYWI1NWYxMDMxZGYzMzc2Y2IzNWI4ZjU1ZjY0NGM1ZjI1MTM2NTY0ODAwOTU1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:37:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066374497\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-916400263 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916400263\", {\"maxDepth\":0})</script>\n"}}