{"__meta": {"id": "X283f61f82db72ab352130bd397313461", "datetime": "2025-07-30 08:11:47", "utime": **********.036672, "method": "GET", "uri": "/chart-of-account/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.636331, "end": **********.036707, "duration": 1.****************, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": **********.636331, "relative_start": 0, "end": **********.786689, "relative_end": **********.786689, "duration": 1.***************, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.786714, "relative_start": 1.****************, "end": **********.036711, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x chartOfAccount.create", "param_count": null, "params": [], "start": **********.990608, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.phpchartOfAccount.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2FchartOfAccount%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "chartOfAccount.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.021194, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET chart-of-account/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "chart-of-account.create", "controller": "App\\Http\\Controllers\\ChartOfAccountController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=54\" onclick=\"\">app/Http/Controllers/ChartOfAccountController.php:54-76</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.010400000000000001, "accumulated_duration_str": "10.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.912859, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 48.846}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9407969, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 48.846, "width_percent": 17.404}, {"sql": "select * from `chart_of_account_types` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChartOfAccountController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ChartOfAccountController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.955002, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ChartOfAccountController.php:57", "source": "app/Http/Controllers/ChartOfAccountController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=57", "ajax": false, "filename": "ChartOfAccountController.php", "line": "57"}, "connection": "radhe_same", "start_percent": 66.25, "width_percent": 9.904}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4779}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.993924, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4779", "source": "app/Models/Utility.php:4779", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4779", "ajax": false, "filename": "Utility.php", "line": "4779"}, "connection": "radhe_same", "start_percent": 76.154, "width_percent": 13.75}, {"sql": "select * from `plans` where `plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4784}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0026171, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4784", "source": "app/Models/Utility.php:4784", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4784", "ajax": false, "filename": "Utility.php", "line": "4784"}, "connection": "radhe_same", "start_percent": 89.904, "width_percent": 10.096}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/chart-of-account\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chart-of-account/create", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InNWTndqL3FtMDVvOE9GSjlEV05ubXc9PSIsInZhbHVlIjoiNW84RVdCcU4xdWVJcGdsbDFxOVdpMjcwTkJ0NnVadG0zOFdjWjYrVWUyOW1sVENlUHVwTklJL25SL1htN1NjKzNLMkVDREJFUE8xd3BEa3l3LzRDcmhvWVNoMFl3aUxWLzEvSFd2bkFkY1gydVhla3VpOTIwVjVvU0VRbDh6NXUxdjV4N3RxM1NSMU9SVFhWR3JXdHJxejFtcmdtYmo0WCtmUitqc0QrZG9OQW1rNXQ3Zko2UDN2UnNQYzdjSmVSNXp0WG5RSldTanFBei94TU8vN1VNSTRHNGNZR2lqeFpZd0trWGprbmhnU2ptZzlKaG1idE9jMDVya1dXeE5zK1MvaExBMVFrM01wSlhESW1zVTdCdWxGYk9iNXZ2RlZCUWNRdDBBNlFwNXBwQkVWUm82K3dNb3RqMVViVmNIWE5aaXJzOGhwOVdvVjdoNUVXSWJJUHlURW1oUGlNcDgzMDIzWTJaN2YzUlBGa09wUk53cEhEdHNGUXZoODI0NU9NOGtkSEFrNU02elA2aVBDd1JUak1RTmNwZHBMWkZxakM5NE9FbjNCT01lT3IvbEV6OTBvWVVFTG5aZGQ1OHo4MU1JOFovbU1kK3hydHQ3blBtMElyNk9QKzJDeTE5Vk8yUlU2d2lWTENpSlphdTdZYWZSWFFqSlpoNkN2SmlqdWIiLCJtYWMiOiIyOGM4NzQ5NmFlMGI1ODY0MTA3YzM0YjAxMzk4OTkzODQ1YjBjZTgwNTAzYmU5ZjdkYmMzYTk4ZjEwYzQ5YmM2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ild4ajhMeWpOeDlTVWs1bWd5UytXcWc9PSIsInZhbHVlIjoic0o1N0l1UWt5OFlhTUNSS1dhV0NUcnE1RWQ1MFhUUCtwWFd1TXJnYS9RaHJKemxLWU9taElpcmd0Y0tJZkZmL0FnR0ZVUW9EdUxGVmdtRnhlc3ZaK2FpUXlQZHEzUnV6dHRCNGdCbldkU0psZEhPdEZwdkVGUVF3YXQwMHNVM2NuWnYvRGwxOFBmL2RTQ1IvblpSaVVCaG1RemhVNlEzQjBUTWVQUGtUU2luMEhLK1NLS1U0NkpoVDdWMmJBdUcxeEpCVFlLbmJVSUM2YUlRdWNuNlFiVFFET0tCSER1MzRnQWJia0QvcGwzSnEycDRFbFJTNlpaMjJwYXdLKzFZelMvY1cyTE1JOHR2bDJEaVhmaDRnRWJDMzFKSm9Xc0tRQXNYWkZLY3dCL0hvRmtlQXNZblZ3RDYrOTBkNmVJWGIzbURDZy9oaTFQTDdUSE1WeFNJWFVDWThxSGZJSkVZWnJNQVZqMXhLVmxjaXhqaEJyQ00vUm9BYm1aQ2F6Wnh6c0duaklycXk2V3lzYW51YkV6dVlURnJtU2VEODR0RlZ5Vk5paTUxNGV0ZVBUVWV0ZVF6SEZMK051UUE0RjZJcFp6VTQwUXc5NDljRU9BKzMreDFUNUtiYS9qUm1pUzN5eEthUEdoS1J4MG9rOWEyTTA1cmxCUE1CdkNKUHpEMTgiLCJtYWMiOiI0M2U2Y2RjMTEyY2QzNjUyODc1ZDZjYjA2YjFlZTRlMDA3ZmQ1ZDMyMTEwNmMyYjRiODVjZTA2OTc3YTBmMzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-271176888 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271176888\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1504475049 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:11:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9VbFBtRnBFZVpqbmNYRW50b1VvRnc9PSIsInZhbHVlIjoiK3ZHc2ZyWXRYSWRkL3lIL2J6ajgxSzB1emN3clVjMUcwcVNRYktUek5JUHZpQmhLeVJuMHhYbC91TXR5bGFtWXFkZmJ3eDJlanlkQVVrSnVKL3pQeEtBN0hENU5Qd3FxUEVNZ3JDVFEwS0VhMmdSa3BBOGh2dEJCOTZ2NWpsSFplRkpabEFqMnVUd0xRcnE4T3JNaWptNEVmNVQ2Vk1RZ25iUXpsdkRBRHFYZE5tKy81ZStsZ25LYlVrbElSdU9nenBTcVhPSW5iNm1YMkVtSEk5R01pVnA1ZEhqRG9OYnVqMU10ckhIeG5WbGl3OStaQzdZZm4rMUF4enJEN0Z2M1N4VlYrRXdMLzZlNEdieUorQUNXb1dBSGxrK0VYbTAvUW1hcHlPUUJmVzkvR3JRTVZpNWwzVXlWeE1zcURpZmVQbVhnUkt0SDNibFZicXBiajZsc09ISWJnc1k1dSt3RFVJenpGQWRJYzg5MndjZ2RlSDd2eCthSldKT0krWXNicXpjNlZTU2d2VCtPY0UrRWo0YXdKVTFGc2M4emVWRW5zZng5YXVDYUEvYVN5NGtzQWpLMWJIdWRjT2VUcnlBenNGaFFpMUw3cThuZk9ldVNYT0NSekFDdlZya0V2OVRUSGk5ZnNLZ01TU0llWisyVjI3aGpkOGpKanB0Q3V2dXMiLCJtYWMiOiI2OTdiMDQzOWQ2MjBiMjY3N2IxOTgyZDU0NWNkYjZmNTU2ZDU5NjhlN2ZkN2YxMzMyZTBiMGY2OWU0ZThjZmVkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im84WXBWWDRLcEp3N0VDa3ZVWnJXUHc9PSIsInZhbHVlIjoiN2pMcSt1K3RsUkpyYmtNeG5WMCtPVjNJeEtYclpnMldFQ1ljMjI5ZStaS1ZRY3ZvRHZHK2ROamhPQ215Z3pQYjZrRjRpZWtKQVBFUThWMkNHQ3doajhlaUh3ZHpraXhleVkzVFBHeWk0RGRuaWJKcW9xMHpvd0JHWFRORzdUMTVjVjljaisrMzRXbWYwLzVYNlJGNXRqTXhzU1hKNnRPL1N5TEt2VlFnUVNteUZFQ2FqUGhvMFpwWkJPUWp5alYwNlZBSGhzQVBNWnJZSzRpUnhGZkRZeURvVEV0N0tjMHRDd3JxangwbHllbXNUL1BhSHhnN3Q3a2tKYVdiZ3pDak0vdlh3amwzVGlycERMb2dMRi9HbEM0Ukl0NW9pcllaaGpha1Vxc001bHl6SndVeVorUENWV0RINjA3czduUnNjRGZEUUdGeDNRWW1mUzJOaGx4dmlvUjVoSHUxekFSRGZmbytPUE5FTEo0dzFNTUpMd1VsK0ttV3F3WjJZY0tlQ0t2RXdMLzhTcXptaUVzRlg4L2taZllVUnRpSkkwNWtpbVhiVTNVRzVpeDdWUnNXbWFseE9sdVhvclFzYi9BWVRra3BKandMbkZBMHdBb2FtNDF4U2UwRDRjV2RaVFgxcjRFSmgyU1ZkaVVLOC9oMlNBbTFqRUROM2FLRU9RS3UiLCJtYWMiOiI1NzYwYTQ3OTBmMWE3NjhjOGVlNzRkODI5YzZmYjM4NzZkNDQwNjAxMGY2NWQzMzUxYzM0NTEzMGE3MWMwYzA1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9VbFBtRnBFZVpqbmNYRW50b1VvRnc9PSIsInZhbHVlIjoiK3ZHc2ZyWXRYSWRkL3lIL2J6ajgxSzB1emN3clVjMUcwcVNRYktUek5JUHZpQmhLeVJuMHhYbC91TXR5bGFtWXFkZmJ3eDJlanlkQVVrSnVKL3pQeEtBN0hENU5Qd3FxUEVNZ3JDVFEwS0VhMmdSa3BBOGh2dEJCOTZ2NWpsSFplRkpabEFqMnVUd0xRcnE4T3JNaWptNEVmNVQ2Vk1RZ25iUXpsdkRBRHFYZE5tKy81ZStsZ25LYlVrbElSdU9nenBTcVhPSW5iNm1YMkVtSEk5R01pVnA1ZEhqRG9OYnVqMU10ckhIeG5WbGl3OStaQzdZZm4rMUF4enJEN0Z2M1N4VlYrRXdMLzZlNEdieUorQUNXb1dBSGxrK0VYbTAvUW1hcHlPUUJmVzkvR3JRTVZpNWwzVXlWeE1zcURpZmVQbVhnUkt0SDNibFZicXBiajZsc09ISWJnc1k1dSt3RFVJenpGQWRJYzg5MndjZ2RlSDd2eCthSldKT0krWXNicXpjNlZTU2d2VCtPY0UrRWo0YXdKVTFGc2M4emVWRW5zZng5YXVDYUEvYVN5NGtzQWpLMWJIdWRjT2VUcnlBenNGaFFpMUw3cThuZk9ldVNYT0NSekFDdlZya0V2OVRUSGk5ZnNLZ01TU0llWisyVjI3aGpkOGpKanB0Q3V2dXMiLCJtYWMiOiI2OTdiMDQzOWQ2MjBiMjY3N2IxOTgyZDU0NWNkYjZmNTU2ZDU5NjhlN2ZkN2YxMzMyZTBiMGY2OWU0ZThjZmVkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im84WXBWWDRLcEp3N0VDa3ZVWnJXUHc9PSIsInZhbHVlIjoiN2pMcSt1K3RsUkpyYmtNeG5WMCtPVjNJeEtYclpnMldFQ1ljMjI5ZStaS1ZRY3ZvRHZHK2ROamhPQ215Z3pQYjZrRjRpZWtKQVBFUThWMkNHQ3doajhlaUh3ZHpraXhleVkzVFBHeWk0RGRuaWJKcW9xMHpvd0JHWFRORzdUMTVjVjljaisrMzRXbWYwLzVYNlJGNXRqTXhzU1hKNnRPL1N5TEt2VlFnUVNteUZFQ2FqUGhvMFpwWkJPUWp5alYwNlZBSGhzQVBNWnJZSzRpUnhGZkRZeURvVEV0N0tjMHRDd3JxangwbHllbXNUL1BhSHhnN3Q3a2tKYVdiZ3pDak0vdlh3amwzVGlycERMb2dMRi9HbEM0Ukl0NW9pcllaaGpha1Vxc001bHl6SndVeVorUENWV0RINjA3czduUnNjRGZEUUdGeDNRWW1mUzJOaGx4dmlvUjVoSHUxekFSRGZmbytPUE5FTEo0dzFNTUpMd1VsK0ttV3F3WjJZY0tlQ0t2RXdMLzhTcXptaUVzRlg4L2taZllVUnRpSkkwNWtpbVhiVTNVRzVpeDdWUnNXbWFseE9sdVhvclFzYi9BWVRra3BKandMbkZBMHdBb2FtNDF4U2UwRDRjV2RaVFgxcjRFSmgyU1ZkaVVLOC9oMlNBbTFqRUROM2FLRU9RS3UiLCJtYWMiOiI1NzYwYTQ3OTBmMWE3NjhjOGVlNzRkODI5YzZmYjM4NzZkNDQwNjAxMGY2NWQzMzUxYzM0NTEzMGE3MWMwYzA1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504475049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}