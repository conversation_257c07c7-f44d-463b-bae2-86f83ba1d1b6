{"__meta": {"id": "X57d0bda1cb0c0ba39197f5f3c9228e9f", "datetime": "2025-07-30 02:45:37", "utime": **********.429568, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843534.817604, "end": **********.429616, "duration": 2.6120119094848633, "duration_str": "2.61s", "measures": [{"label": "Booting", "start": 1753843534.817604, "relative_start": 0, "end": **********.238486, "relative_end": **********.238486, "duration": 2.420881986618042, "duration_str": "2.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238519, "relative_start": 2.***************, "end": **********.429621, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GPbdOGoKV3u1MAjt0cnPJMGD3Pn36bojbVVG4Ee", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1552273926 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1552273926\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1400003690 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1400003690\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-827962302 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-827962302\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1652594406 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652594406\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1075944226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1075944226\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:45:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllDazlwSVZtSkk1dm1tRlplUGs0SGc9PSIsInZhbHVlIjoia2dKUzE4cVZaRXdpaXoyWmdha3ZzaWpnQWF4bGNaSEg1VEg0bTVOS0U0dVpmKzIwdWZNYkdLVjd2cXRwRnBjOHM3MW4zVHZIV3E2WU14S21nRE9sNHVoTFI4QkNhbG5mQ2owZ25zNERzTFA2cnVDNEFicU13ejQxVCtwNHhiZVJTR1BpSU1iaFhuS3BOWkJ6L29wZVU1dnFPTGdERk81YTd4ZEIyVThkaDZZQjRlQjBWSEJsbzRBcE0zQ092d0xyM3BMYkRRQ0VYU3JRUkpKM2lBcU1IR0lsT2NXZzFmVFFUUWJHakpZRVFUZlRUaGhRMlYzcUV2NkFrR2F4dkZoY1ZKSm9GV0RGU05UMzRId0k2T01ITWJWbXc3VG9lWDJWalZ5WElIa0I4MjlFUUNhN0RZbXZzSE5Xc0hLSWdUS2h3WjJCWkxrQVp2YXp2QXR2VVNCNWNoNzFiYzgyN01mc3dHN2syYkkvVTNrelJPUTl6ZHpnaHdLaXhjdjQ0OTEvU2JYeFFjQ051NjJMc2tudzBrdzVGdDdaZTlQOXc5cktpQnp4Tk5IY0NCOGhlSTB6dzRicGZuelpMNml3M2hCMFUrS3BlRk1ZZlJocGJxcFE2dWx5UjhiM1psSXdBcDhVd0FQYWlKNzhVcFFqL2l5ek10c05ScDJVcTJXdFA4RDEiLCJtYWMiOiI1ODQ5MDI1NTkxNDM3OWI0ZTFjNTU4NzhiZmNlMzljMTM4ZGMzMmFmZTNkODA3OGIyNDMzNDc4NGQ4YTY4ZThhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:45:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ijc3OC9JblpvYnlMRDd5Q0xzLzZZNHc9PSIsInZhbHVlIjoiNHRSRGxsd2lIenBXbm4wMFFZdml2eVBVMFdGMk0vemtEeHM0VUJNdDNZeE1ZWTR2RFc0SlcxVXhTRVFML1hjaVFJek9wR1pFTnBaNmEzd0NlZ2wyaElKVnIrZlFDZkhSeXlBeHJJdXVoTWRsZXlWMEk1eEQ0Slc2czI4TDU2c0NKSzJEZUI2LzJTdElhYXRRTFg5N0dsNW5xV1J6eVljaVRrNGZneWhKZWxIZDZ6OUx3VElUNmhLZUNXM2xzcTFOSDREZmpwU3RSc0FMOXBRaE9WMDFDYlZaY1RESlNZeGZ5ZzlyVWtXdnE2UWZzUjVrOVQxcmx3QXAvV1RLSHk2MnZVWjhid3ViTnFqeE01WStKNWlLMzBJRnYyT20yUmhZbVdqNXErTGdCejJJRHN4MEszRmVMbnd6RnBnS3p6ZG44NmMrUkxqZWxYUXBrTmlhK0NWdTQyTFJuVzBGenREaUVTbSs2OVNHYm9wUjExSVBSeUxCcTZTWjY0bzJRcGVreU80QkRXVTFnUm1ITmpVbGxvNDYzL1FHT0J1OVBwZWtkNE9lVmc3THV5emo2WE5MYmg4aGFFaklPYVhEckZDVHpjc3g4Y3lCc09qaHNtVWF2aU5CYW9WajRFdE00K2s1Z3liS2t1QUFTYzFSU1lTNmZ2Q3gwVjRZb2ttdTR5czciLCJtYWMiOiJmNDI0N2RlNDEyNmViMmJkZDM2NWJiNTZkOTJhZjI3Y2NiYTBiZGRlYjhiYzk1ZjVhYTA3NzhhYTU2ZmEwMGVmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:45:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllDazlwSVZtSkk1dm1tRlplUGs0SGc9PSIsInZhbHVlIjoia2dKUzE4cVZaRXdpaXoyWmdha3ZzaWpnQWF4bGNaSEg1VEg0bTVOS0U0dVpmKzIwdWZNYkdLVjd2cXRwRnBjOHM3MW4zVHZIV3E2WU14S21nRE9sNHVoTFI4QkNhbG5mQ2owZ25zNERzTFA2cnVDNEFicU13ejQxVCtwNHhiZVJTR1BpSU1iaFhuS3BOWkJ6L29wZVU1dnFPTGdERk81YTd4ZEIyVThkaDZZQjRlQjBWSEJsbzRBcE0zQ092d0xyM3BMYkRRQ0VYU3JRUkpKM2lBcU1IR0lsT2NXZzFmVFFUUWJHakpZRVFUZlRUaGhRMlYzcUV2NkFrR2F4dkZoY1ZKSm9GV0RGU05UMzRId0k2T01ITWJWbXc3VG9lWDJWalZ5WElIa0I4MjlFUUNhN0RZbXZzSE5Xc0hLSWdUS2h3WjJCWkxrQVp2YXp2QXR2VVNCNWNoNzFiYzgyN01mc3dHN2syYkkvVTNrelJPUTl6ZHpnaHdLaXhjdjQ0OTEvU2JYeFFjQ051NjJMc2tudzBrdzVGdDdaZTlQOXc5cktpQnp4Tk5IY0NCOGhlSTB6dzRicGZuelpMNml3M2hCMFUrS3BlRk1ZZlJocGJxcFE2dWx5UjhiM1psSXdBcDhVd0FQYWlKNzhVcFFqL2l5ek10c05ScDJVcTJXdFA4RDEiLCJtYWMiOiI1ODQ5MDI1NTkxNDM3OWI0ZTFjNTU4NzhiZmNlMzljMTM4ZGMzMmFmZTNkODA3OGIyNDMzNDc4NGQ4YTY4ZThhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:45:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ijc3OC9JblpvYnlMRDd5Q0xzLzZZNHc9PSIsInZhbHVlIjoiNHRSRGxsd2lIenBXbm4wMFFZdml2eVBVMFdGMk0vemtEeHM0VUJNdDNZeE1ZWTR2RFc0SlcxVXhTRVFML1hjaVFJek9wR1pFTnBaNmEzd0NlZ2wyaElKVnIrZlFDZkhSeXlBeHJJdXVoTWRsZXlWMEk1eEQ0Slc2czI4TDU2c0NKSzJEZUI2LzJTdElhYXRRTFg5N0dsNW5xV1J6eVljaVRrNGZneWhKZWxIZDZ6OUx3VElUNmhLZUNXM2xzcTFOSDREZmpwU3RSc0FMOXBRaE9WMDFDYlZaY1RESlNZeGZ5ZzlyVWtXdnE2UWZzUjVrOVQxcmx3QXAvV1RLSHk2MnZVWjhid3ViTnFqeE01WStKNWlLMzBJRnYyT20yUmhZbVdqNXErTGdCejJJRHN4MEszRmVMbnd6RnBnS3p6ZG44NmMrUkxqZWxYUXBrTmlhK0NWdTQyTFJuVzBGenREaUVTbSs2OVNHYm9wUjExSVBSeUxCcTZTWjY0bzJRcGVreU80QkRXVTFnUm1ITmpVbGxvNDYzL1FHT0J1OVBwZWtkNE9lVmc3THV5emo2WE5MYmg4aGFFaklPYVhEckZDVHpjc3g4Y3lCc09qaHNtVWF2aU5CYW9WajRFdE00K2s1Z3liS2t1QUFTYzFSU1lTNmZ2Q3gwVjRZb2ttdTR5czciLCJtYWMiOiJmNDI0N2RlNDEyNmViMmJkZDM2NWJiNTZkOTJhZjI3Y2NiYTBiZGRlYjhiYzk1ZjVhYTA3NzhhYTU2ZmEwMGVmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:45:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2029832433 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GPbdOGoKV3u1MAjt0cnPJMGD3Pn36bojbVVG4Ee</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029832433\", {\"maxDepth\":0})</script>\n"}}