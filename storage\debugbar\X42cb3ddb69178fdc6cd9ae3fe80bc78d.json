{"__meta": {"id": "X42cb3ddb69178fdc6cd9ae3fe80bc78d", "datetime": "2025-07-30 07:40:44", "utime": 1753861244.054279, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:40:44] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753861244.046557, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753861242.135001, "end": 1753861244.054339, "duration": 1.9193379878997803, "duration_str": "1.92s", "measures": [{"label": "Booting", "start": 1753861242.135001, "relative_start": 0, "end": **********.679972, "relative_end": **********.679972, "duration": 1.544970989227295, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.680031, "relative_start": 1.545030117034912, "end": 1753861244.054344, "relative_end": 5.0067901611328125e-06, "duration": 0.3743128776550293, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48355984, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.04004, "accumulated_duration_str": "40.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.778898, "duration": 0.02115, "duration_str": "21.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 52.822}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.843031, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 52.822, "width_percent": 4.97}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.858684, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 57.792, "width_percent": 4.695}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.870348, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 62.488, "width_percent": 2.922}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.9163308, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 65.41, "width_percent": 4.096}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.942532, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 69.505, "width_percent": 4.82}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.957025, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 74.326, "width_percent": 4.346}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9741201, "duration": 0.008539999999999999, "duration_str": "8.54ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 78.671, "width_percent": 21.329}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2032456471 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2032456471\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2134806859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2134806859\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-173009926 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173009926\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-290134929 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Inh1OU9hUWVtWFRMeTRmU1hEU2JYMVE9PSIsInZhbHVlIjoiMmV2S1JGVC9TYTFPc0tmdEJiOW9DNHptQndCTEkxcW80RVErU1p5YU91TkEzdk5VOFBhN041WnFLTzIrS0xRM0hSQjhwSjFLSmMvdDdIbnZJUWczcEwvWGEyeUpLWGQyRktWMWtsSFY4UDBGbUxsUkVvbStLQ2FrQk14UkZRR3MzQkFFbnFHdlhYRlVlUk9rN002alQ4cGw4RE5xV3ZnSDVJWUVBTXBjb0hPcWJuWUhBamdDMWN3N0hhWWp6MS9QVXo3M3EvNG1uRms1aTlhcFJtMk51RXdBZWlMSmYrYkdOSno3WWI0RGUzcSsyMWtzdzRObXYzMi80cjRuVzJSQ3JzZE15MXVjU2ljOWp3SmRxdFlYeFpsRmJuQy9EMFlGeTA0Z1ArMCs2RU1KUGllMGVMNmplME5UaWc3Z0pzdThPanh4UzFsbGxyQTlkdU5mV1ptSjZwVW1vR0pKdlBmMXZlOTlBZ09ZaGZiL2ljOHhpNUFSYmxXME1PYjR0WGJtM012elFuempWZ25KQ0FvWTRtRTcvZUJuRmRYTTk2MXAzYnNkcE9SZlpNMm03c1ZNTGp4OWhJemZqdVRGdWU3WXVkUDJ1WEhoakJlL25TOXNZL3pkM25aSWhmeUNCcHF6TXd0ZHRnNU85bmhDQU1BVDAxWkpZVFJQVFhScGZkSXAiLCJtYWMiOiJiMWJiZTFmYWIzZjkzNWI2OTE3MjI0MzNkNzM3MTBhNDVhZWIyNDg5NjdiYzBkYzg2NmZkYTQ0MGUzNzhiODFiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik54d3YxZzZkUE1hNWtvRWpmK3lJc0E9PSIsInZhbHVlIjoiOFc1OUM3aE80NDJoTVpQREVLY0UzVXUzSU5LWXgzOHdKYy8veU1aU0JYbkNFbXNFMEk5MXlVRzg5aC9yNXRJT2R0bzFnL3kzengxdTRvSWRVY3lGRU15dnZacTNpdFE4YkkwVUxzVWdPYjJVOC9RK2owVCtXM0JwbW5nbFJvQ1I5bkcxeEtJRmovNzUyb1lGNE1zWllzUjdaK0NHdS9DQXZsN0RZU0xuV2Jja2g2RVVXS0UrcnJoVlpORFVmazNFNmNaTnFqZElMM3ZlYkRyc3ZwakpKOUhEbTNkMmdITVFqQmYyZmpKSEJrcmV2OW5jSUdTQ0U5dmEwWlBGclBVVklJR1E1QSt4cTFicEJsY3RGNHZ3eE5XS2RBTithUXVWZDZnZmw1dXdxRnRNUnN3MFMwczI3eVdEdjhQNk5NeExLZ1JWQTZqNDJ3VjNUNkQvOUo0akJhcFJrV0Q2czd6aWVocXJ2TzB1b3RQY2RPdWRKbVVGcEU4dUlCdmhMYlNoTXZ3bmJBWm5EbUJaQ3Vib1pUWllXMVFad2tObTY5VlMxVWRaVElJN1N1QzlaYnhUeEI2bW5DUDVGNC8wQWd6WUhmRFFFSzVXUW1xeXlWREhOVW5oVTZkeGhjVzh6RkhPR25FNzdGMDRzeTF3R3Exb2JNbUhncFZCNnhQVUZTUmEiLCJtYWMiOiJiYmU4MGMyZDU2NGFlODlmZDc2ODcyNDdjYTUwZjIyNjExZWFjNDk0YWE1OTE4YWIxNGQ0MmJkNmE2OTI2NDBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290134929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1863561646 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863561646\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-920158575 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:40:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZnS1VPR2w0MGpwNExwRjhpQjFMTHc9PSIsInZhbHVlIjoiSXVOYmFyS01BbGlNSFhXdUlhSXZLV0xmYTVIdjh1aEV3TnBnc0htQTIwa1pFSy8wMmRRUGxWV3l3L1ZaMXVEN3g1eG1Gc1hWSUR6Nk9pK2Z2akpNR0oxZFB0ZTVDcXlWQkhLZFZmYzEwVUxjb203WnZoOEFCc0ErckF3VXVpZWVDR0lCOUxzVEdlZWE0emtxNC83Q1JkbENVYklZYmpTalYzVmovOWNIU1o5MVBHZzFVNEZJSHprSDFGcTJoRUN0R1Q0ODRUZ3FRemRKa2VyRkFnT0pqTldqVTVWYlFaSDRudExCeTlOdnRHb1hldEFNUGx0dHI2Mks5V1dIc1FKdjlBY2x3Z2FFUkZ6VW5pOHpFY2tEa0szTkcyOXdYdmtJRUhQenYvUjlmU3E2cUl3d3dJdHdoWS8rb3dkT2t0RG9hWEVvZDZ6T1Evem54WkxYQlpjSG9TdHBQbDZ0WStrTFMrdXp0VGg2bjlIR1U4bElpZ2M4a0Q5dzl5SDBESVpwclRDdC9lSFBtTjZ3OThTb3haL0ZJbHJPN3ByWGYwT05xRkNzUEIyQWRVWjNTNHpZRjhaSUNrUlN1RjY4SmNnUFVYb0FwbFhMcGFOTG1BUnVDZVM2NXdVdm5rQzM4SjdxWE1SWHpKcWtBY1FSTm1Gb0tQN3NGdGRrK3hscEl4RTgiLCJtYWMiOiJiYTFhNmQ3Y2ZkNjQwNWRiOThmNWQyYTEyZDY4MWUyY2M1MTQxZTJhOGQyNmNjZjcxOWQ5NTE0MzllM2UxMjFjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:40:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik5NNXZzcXNpYVc2ZUFWRUltLzdjYWc9PSIsInZhbHVlIjoiOVNsUWVTK0tMZ2tUMVhPUWhhN01FSHUxTVUzZ2Fid0VSZ08wS2ozSmRJeTdYK0g1QXFPdnRvSThqdXF3SGdocVhNR2NQeFFIWVFTa1J1SEpZcWpUUldncFJSaUpHK3hMMGtyNGpmQmNnUi9TUlUrN0xqRkd0ayttajNBWldPR3FmNEVyMjBzRHdtVmJjQUVLVmVoYlZGRlpMckdFQjFFU3RlcXhuL09OZ1kzajBZdVpxRVdib2NlTXdSb0dCaTIyRnVlL0grUzhTL095UFluTDg4QUFkYndnZTR0cDBYRHNHSHloY21SZmFpejFRR1p1ZEc2dmJ0TTNuL2VvMVY1OGZPNkxQc1VmSzZRNHcxYjZqNlRCMTE5NTRvTU1tN0pVLzNTbStPVjNUOG4wMmJ5TldSOUYxUy9CZS9lU0g3cVYxWGdpeUo1ZmtzV0drREVWZE9KZWdZdXhrNVkrVkxJNnJhdm1Pbmppa1BiRHliaUNheSt2ejArcER2UzBnQzJPSzJWVFJBc2NMdjFzZU9GOVFFWVV2Y1VXNVZ5QzEyUjRqcGRqdEtBM0hTa1JrcXZIR1Zab1hnRHgxMlJiRDVTN2dQaFVrbFJGWkZEU3JJWXZLQ2hUcHZMRmJXRkhTS2U5VXIzWjZVYURMYTVhYmZ6ZmdHQXUyMUhBKzB3K09hekMiLCJtYWMiOiIyNjJmMDUxN2UxNWMxNTNjNWEwYWYzZDk4MjI1NzEyMTM4MWRmNTU2OGUzZDIwZTQ1N2ZjZGQxZDVmZTUwMTE3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:40:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZnS1VPR2w0MGpwNExwRjhpQjFMTHc9PSIsInZhbHVlIjoiSXVOYmFyS01BbGlNSFhXdUlhSXZLV0xmYTVIdjh1aEV3TnBnc0htQTIwa1pFSy8wMmRRUGxWV3l3L1ZaMXVEN3g1eG1Gc1hWSUR6Nk9pK2Z2akpNR0oxZFB0ZTVDcXlWQkhLZFZmYzEwVUxjb203WnZoOEFCc0ErckF3VXVpZWVDR0lCOUxzVEdlZWE0emtxNC83Q1JkbENVYklZYmpTalYzVmovOWNIU1o5MVBHZzFVNEZJSHprSDFGcTJoRUN0R1Q0ODRUZ3FRemRKa2VyRkFnT0pqTldqVTVWYlFaSDRudExCeTlOdnRHb1hldEFNUGx0dHI2Mks5V1dIc1FKdjlBY2x3Z2FFUkZ6VW5pOHpFY2tEa0szTkcyOXdYdmtJRUhQenYvUjlmU3E2cUl3d3dJdHdoWS8rb3dkT2t0RG9hWEVvZDZ6T1Evem54WkxYQlpjSG9TdHBQbDZ0WStrTFMrdXp0VGg2bjlIR1U4bElpZ2M4a0Q5dzl5SDBESVpwclRDdC9lSFBtTjZ3OThTb3haL0ZJbHJPN3ByWGYwT05xRkNzUEIyQWRVWjNTNHpZRjhaSUNrUlN1RjY4SmNnUFVYb0FwbFhMcGFOTG1BUnVDZVM2NXdVdm5rQzM4SjdxWE1SWHpKcWtBY1FSTm1Gb0tQN3NGdGRrK3hscEl4RTgiLCJtYWMiOiJiYTFhNmQ3Y2ZkNjQwNWRiOThmNWQyYTEyZDY4MWUyY2M1MTQxZTJhOGQyNmNjZjcxOWQ5NTE0MzllM2UxMjFjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:40:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik5NNXZzcXNpYVc2ZUFWRUltLzdjYWc9PSIsInZhbHVlIjoiOVNsUWVTK0tMZ2tUMVhPUWhhN01FSHUxTVUzZ2Fid0VSZ08wS2ozSmRJeTdYK0g1QXFPdnRvSThqdXF3SGdocVhNR2NQeFFIWVFTa1J1SEpZcWpUUldncFJSaUpHK3hMMGtyNGpmQmNnUi9TUlUrN0xqRkd0ayttajNBWldPR3FmNEVyMjBzRHdtVmJjQUVLVmVoYlZGRlpMckdFQjFFU3RlcXhuL09OZ1kzajBZdVpxRVdib2NlTXdSb0dCaTIyRnVlL0grUzhTL095UFluTDg4QUFkYndnZTR0cDBYRHNHSHloY21SZmFpejFRR1p1ZEc2dmJ0TTNuL2VvMVY1OGZPNkxQc1VmSzZRNHcxYjZqNlRCMTE5NTRvTU1tN0pVLzNTbStPVjNUOG4wMmJ5TldSOUYxUy9CZS9lU0g3cVYxWGdpeUo1ZmtzV0drREVWZE9KZWdZdXhrNVkrVkxJNnJhdm1Pbmppa1BiRHliaUNheSt2ejArcER2UzBnQzJPSzJWVFJBc2NMdjFzZU9GOVFFWVV2Y1VXNVZ5QzEyUjRqcGRqdEtBM0hTa1JrcXZIR1Zab1hnRHgxMlJiRDVTN2dQaFVrbFJGWkZEU3JJWXZLQ2hUcHZMRmJXRkhTS2U5VXIzWjZVYURMYTVhYmZ6ZmdHQXUyMUhBKzB3K09hekMiLCJtYWMiOiIyNjJmMDUxN2UxNWMxNTNjNWEwYWYzZDk4MjI1NzEyMTM4MWRmNTU2OGUzZDIwZTQ1N2ZjZGQxZDVmZTUwMTE3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:40:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920158575\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-329087990 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329087990\", {\"maxDepth\":0})</script>\n"}}