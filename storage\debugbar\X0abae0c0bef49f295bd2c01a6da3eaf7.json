{"__meta": {"id": "X0abae0c0bef49f295bd2c01a6da3eaf7", "datetime": "2025-07-30 05:56:06", "utime": **********.383666, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854965.533327, "end": **********.383705, "duration": 0.8503777980804443, "duration_str": "850ms", "measures": [{"label": "Booting", "start": 1753854965.533327, "relative_start": 0, "end": **********.30528, "relative_end": **********.30528, "duration": 0.7719528675079346, "duration_str": "772ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.305332, "relative_start": 0.****************, "end": **********.383724, "relative_end": 1.9073486328125e-05, "duration": 0.*****************, "duration_str": "78.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Wh4bi1nqCFZ53bRK5lxvYlUyKmo0Y6MaMMHdd7aI", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1633645208 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1633645208\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2144367138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2144367138\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-211880355 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211880355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1904107035 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904107035\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2070427727 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2070427727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2127656595 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:56:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRLdXA1Z29keFNwVGFaWUFGdHBvOVE9PSIsInZhbHVlIjoibnRiQnZTYXNuV21DVTZ6cG5nR3ZXYmxnNVlZWXFON1dlMmxnaStKUDEzY3BHb3FmbXlzTnd4bjJhaGFGM3dpY1NyNmgvQ1ZIdGpTQVJOZTJjd3laS0VTemdha2NHS3BwL2hpclRkOTlsWHJVSkVVWkUwUzlhbzR3MFRHRkRrUmE3NXFndHhEaDNHdHJYVnE2Ri9RUXc3bW9aWEVxMmI5bTcxaVAweEZPSVl3czlFbkNubFRmeGlPUFN5Ky9QL3paRSs3bzZ4SHZRWUYveGE4L0dHR1RjOE56ZkdXZnUrQy9DU2NhOU9NWHZ3S203dnlhb2grcjh3UTBWM05VNWtLeGY4a2R2dmZDTXd5N3VidzZBRGV5ZzE5MUpSOHUwN2o4MFdYbmk5V0k2QmJ3bUFRQnlybTVGdzhZb3hTRVNqZENyZUQxbWtLNkIzUnRkckR0ZndHVGx5SkZTTzBvYzlwL0plZWhMVUYvRDJUeTI5SEI1dEVkRTdPQ1FVVFFhaVM4T3JmK2JYQXBMc08vaXoxRyt0OFNtQU1qQlY2WWFYa0ZDMFFycnJtd3d5bmN1TjU5ODZMRHlucmkrUmx2K1kxWE1SMkF2b3V5Mkh4ZUMwVlpKcHlqT3FIUkZkMWZxd1BZRUJoRHl6dkdONXFQOTFsWTR2MDVXd0ZPV0lnYmJiTnAiLCJtYWMiOiIwODA0YWM5ZjQ5OGJjMjRlY2MyODcxYWY4MDJmODZmMzk4OWRiZjFmZGRjNzU4YWZiYWQyNGY3MzMzOGQyOWQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:56:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjQ3cld0NHJ2NU0zSVpOUStqOWF0UGc9PSIsInZhbHVlIjoiSDYyNkg5b3N0NTZQb1JzRHVzNVhyQVo2eTBGN1l4a2p1Z2w2S0hrU01hOEdhL0wxV0FhTFVRSjNjb2t3S2tzNS8yMzFLOUUxMzR2cnNYaFg2em03WXZUSEZZTzZlR3RSQzgraFp2SXJUcGNMOWdZZG5mRnRZUUN3czF2QVZMcmlCSUNMVUFTcXhYUmozamh5VVN4UzgrMTd2S2hNQTlRbHcreEZqL2E0ZW1XdlZLYlVUeGNVOGo3M1NLM3YzazNPWWNMTGpYd2MyRHc5THU5M2IxbU1acUtVTHJYQ09GTENxUVJVMjNKV2JLT0FIL056RllKQWlsTnlBRzdDdFBtL21aNkNEd2x3SnFTMEptWVVZNjVwZzFITXZxTlFZY0dnOUdObUpkQmFxcDlvT0lydjkyQ015YWhQNWpPdkdrR1owRWhwVXdTQ2hlTEJiMFZpNHhEL1c5MUJtNkl6aWRGRWM4cHg4YytZaVR5SlVnOTU3T0lBS1M2Mm9MTDkvT2thalh2YVFMOTIwQjE2Vk9FaS82TzJBR2dDRDhOZytteHRnT3M0WkhZSDJSdTd5MUdWbDVoS1lNcW12Y1laUTJ1WUhhcTZ2S0N6TFlYYjRIVHdyUXlCZzJ1aDRpN01hUHIrdCtiQ0NIWWJ3Q1BNMUR6akpIcDZDem9aK25zck5FRmkiLCJtYWMiOiI1M2FlZTJiMjAxZDM1N2M3YjA5OWQ3YjIyMmYzODE0N2Q4ODM2ZTBkNTc1OTcwNDBmYzNhZTQ1Y2U5MjQyNDM0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:56:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRLdXA1Z29keFNwVGFaWUFGdHBvOVE9PSIsInZhbHVlIjoibnRiQnZTYXNuV21DVTZ6cG5nR3ZXYmxnNVlZWXFON1dlMmxnaStKUDEzY3BHb3FmbXlzTnd4bjJhaGFGM3dpY1NyNmgvQ1ZIdGpTQVJOZTJjd3laS0VTemdha2NHS3BwL2hpclRkOTlsWHJVSkVVWkUwUzlhbzR3MFRHRkRrUmE3NXFndHhEaDNHdHJYVnE2Ri9RUXc3bW9aWEVxMmI5bTcxaVAweEZPSVl3czlFbkNubFRmeGlPUFN5Ky9QL3paRSs3bzZ4SHZRWUYveGE4L0dHR1RjOE56ZkdXZnUrQy9DU2NhOU9NWHZ3S203dnlhb2grcjh3UTBWM05VNWtLeGY4a2R2dmZDTXd5N3VidzZBRGV5ZzE5MUpSOHUwN2o4MFdYbmk5V0k2QmJ3bUFRQnlybTVGdzhZb3hTRVNqZENyZUQxbWtLNkIzUnRkckR0ZndHVGx5SkZTTzBvYzlwL0plZWhMVUYvRDJUeTI5SEI1dEVkRTdPQ1FVVFFhaVM4T3JmK2JYQXBMc08vaXoxRyt0OFNtQU1qQlY2WWFYa0ZDMFFycnJtd3d5bmN1TjU5ODZMRHlucmkrUmx2K1kxWE1SMkF2b3V5Mkh4ZUMwVlpKcHlqT3FIUkZkMWZxd1BZRUJoRHl6dkdONXFQOTFsWTR2MDVXd0ZPV0lnYmJiTnAiLCJtYWMiOiIwODA0YWM5ZjQ5OGJjMjRlY2MyODcxYWY4MDJmODZmMzk4OWRiZjFmZGRjNzU4YWZiYWQyNGY3MzMzOGQyOWQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:56:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjQ3cld0NHJ2NU0zSVpOUStqOWF0UGc9PSIsInZhbHVlIjoiSDYyNkg5b3N0NTZQb1JzRHVzNVhyQVo2eTBGN1l4a2p1Z2w2S0hrU01hOEdhL0wxV0FhTFVRSjNjb2t3S2tzNS8yMzFLOUUxMzR2cnNYaFg2em03WXZUSEZZTzZlR3RSQzgraFp2SXJUcGNMOWdZZG5mRnRZUUN3czF2QVZMcmlCSUNMVUFTcXhYUmozamh5VVN4UzgrMTd2S2hNQTlRbHcreEZqL2E0ZW1XdlZLYlVUeGNVOGo3M1NLM3YzazNPWWNMTGpYd2MyRHc5THU5M2IxbU1acUtVTHJYQ09GTENxUVJVMjNKV2JLT0FIL056RllKQWlsTnlBRzdDdFBtL21aNkNEd2x3SnFTMEptWVVZNjVwZzFITXZxTlFZY0dnOUdObUpkQmFxcDlvT0lydjkyQ015YWhQNWpPdkdrR1owRWhwVXdTQ2hlTEJiMFZpNHhEL1c5MUJtNkl6aWRGRWM4cHg4YytZaVR5SlVnOTU3T0lBS1M2Mm9MTDkvT2thalh2YVFMOTIwQjE2Vk9FaS82TzJBR2dDRDhOZytteHRnT3M0WkhZSDJSdTd5MUdWbDVoS1lNcW12Y1laUTJ1WUhhcTZ2S0N6TFlYYjRIVHdyUXlCZzJ1aDRpN01hUHIrdCtiQ0NIWWJ3Q1BNMUR6akpIcDZDem9aK25zck5FRmkiLCJtYWMiOiI1M2FlZTJiMjAxZDM1N2M3YjA5OWQ3YjIyMmYzODE0N2Q4ODM2ZTBkNTc1OTcwNDBmYzNhZTQ1Y2U5MjQyNDM0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:56:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127656595\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1893445948 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Wh4bi1nqCFZ53bRK5lxvYlUyKmo0Y6MaMMHdd7aI</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893445948\", {\"maxDepth\":0})</script>\n"}}