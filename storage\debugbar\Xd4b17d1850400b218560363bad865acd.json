{"__meta": {"id": "Xd4b17d1850400b218560363bad865acd", "datetime": "2025-07-30 05:30:29", "utime": **********.135856, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753853427.709174, "end": **********.136017, "duration": 1.4268431663513184, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753853427.709174, "relative_start": 0, "end": 1753853428.992897, "relative_end": 1753853428.992897, "duration": 1.2837231159210205, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753853428.992933, "relative_start": 1.****************, "end": **********.136034, "relative_end": 1.6927719116210938e-05, "duration": 0.*****************, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UuWQbDV7WBTpCBbIy30YKNCCM8Vkj1fovZN063cs", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-367379397 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-367379397\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-715860012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-715860012\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1366447740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1366447740\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-929865519 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929865519\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-971777608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971777608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1954170819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:30:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRXWjVMWDdib2VTSWF3M21ncVcxTkE9PSIsInZhbHVlIjoiQ1lYWWFZeDczQlRZa3o3SDN2ZGpGVTF3dGdpRVdWc3UzaHFEVE9tOHAvQ2wySVFkZ1luRm01RTNJZ2R3RzFXUFRwQTR5SlFoVnBIemtFRHk4bFFnKzFPTzh1bERIY0UwNVNjaklqRU8zVGxnUmNUellTMThMVVVoQ2JHMW9qWUJnb3lYTFBSdDFXMTlBR2pnQzZoWlduYVhXdFluc3prNFg0NFZZZEVZOXZKcXVPSUZtT1pWVzIrUXMrMThQeWNLd2ZJTEtYZjdCUVlIWXJrc2RtKzQ1Nm80ZnljQ1dPR3h0N1hOZXBEdFlUMmwveDVESWRYZDRtcHJTSmlKbC9KaVZWRWRVa0FVNjBYUVFHbHRyVWpzMDBpS1hzdWtTNStOS3B1L1hoWEpKS0I0RUtnck9HVWJvMzczZWorU21zOHZKWmEzb0w2MkdRL3dnZzUwN1F5TkxvK1FTenk2YWUyVERCaGJVTkZqSExCa2RLQU9PTE1Md29JbXhyNDhpRzZHbXpwTzRBOG9FdUpCT1lKUlgzK3FLTmxHbmk1RmhWYUplNHYveFVWQnB2eUFwMnpwSG1Vcy8vN0VrNThqTmdSeVhEck5nZHQ4NlNlUjZGQjYrdUE3ZlU2ZXZIcXoxTWhROW12SFZXam5Mam05dkMvTTF4U1cxVGJYaUJabytCZVMiLCJtYWMiOiI1MTUwMDc4OTViMzU5Y2YyZDdkMDZhNDZmMzYzZjY4NTlkOGZiZjExMGEyZDczMTJmNTM1MjcxM2E5ODI2YjBjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:30:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpuWGt4bVpNRHpHZXZoQnRMeHdDOWc9PSIsInZhbHVlIjoiMGJYV0YrRmZKWkU2MkN2dVdqbmIwQnVQSExUUmVJOVVDTkQ0dFZ0ZmpQQ2UweTJMd25jY2FlUG05alVTWmlpR1c3L0tLOHNWcjcxU1h0bE8wRFh2RGhydjNNczFKZWdzMVpCc1gzWWpCL0MzWmQ2ekg0WFlUaHhSak12VkljaGRLc0pGM2FBWlU3SFZCK3p4TThkZ2EvaW4ydUFpREFZeWt0d0hJcHdlSVRnekxzZTFKc0JRckt2VHhidGNINS9yeHNoVkNIbWx6M1pRcmFpVXdFQ3c2NVdpbXp5TlJpdnAxaFhkclhtU3BOVkx0RnFNZHY5QmJyOXErdnZoQTkrQmRhdGltR0M1ZEY2RnNGamtVWXEzNE1temxScDlzS3E0QzZ5UmQyVG1oUjFUbXdhQ3ZObmlPWjZTaVphMFdMWmJjTllaR2FsSmpYQVRYRit5TGR0b2xTdzNPNlFxVnZXa3VkY1hBamROSnFjRi84VUc3SnphTmhFazhOcmJwdFZKekdVMFljQndkS25VWE96MHZUeDl0MkhPQlBVUHJPM2tCdExDUExibWl3R2tLREcyenlUNjJ4YnVJMk05bTJJclZ0RHYyYVo2RFd5M3lxa29LdU9jeHlqb0pNR1RsM2tYS2pQZUN1T2prL0NtMjVCUG5uUk9UejRNVmlBeGE2UzMiLCJtYWMiOiJkYmM5MmI0MDg3N2ZlMzJjYmQ3N2I3M2NmYWVmOGI1MDM5NzcwYzc2ZmQ5YWM2MjMwODBiMWY4OTRjMzIxMjAzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:30:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRXWjVMWDdib2VTSWF3M21ncVcxTkE9PSIsInZhbHVlIjoiQ1lYWWFZeDczQlRZa3o3SDN2ZGpGVTF3dGdpRVdWc3UzaHFEVE9tOHAvQ2wySVFkZ1luRm01RTNJZ2R3RzFXUFRwQTR5SlFoVnBIemtFRHk4bFFnKzFPTzh1bERIY0UwNVNjaklqRU8zVGxnUmNUellTMThMVVVoQ2JHMW9qWUJnb3lYTFBSdDFXMTlBR2pnQzZoWlduYVhXdFluc3prNFg0NFZZZEVZOXZKcXVPSUZtT1pWVzIrUXMrMThQeWNLd2ZJTEtYZjdCUVlIWXJrc2RtKzQ1Nm80ZnljQ1dPR3h0N1hOZXBEdFlUMmwveDVESWRYZDRtcHJTSmlKbC9KaVZWRWRVa0FVNjBYUVFHbHRyVWpzMDBpS1hzdWtTNStOS3B1L1hoWEpKS0I0RUtnck9HVWJvMzczZWorU21zOHZKWmEzb0w2MkdRL3dnZzUwN1F5TkxvK1FTenk2YWUyVERCaGJVTkZqSExCa2RLQU9PTE1Md29JbXhyNDhpRzZHbXpwTzRBOG9FdUpCT1lKUlgzK3FLTmxHbmk1RmhWYUplNHYveFVWQnB2eUFwMnpwSG1Vcy8vN0VrNThqTmdSeVhEck5nZHQ4NlNlUjZGQjYrdUE3ZlU2ZXZIcXoxTWhROW12SFZXam5Mam05dkMvTTF4U1cxVGJYaUJabytCZVMiLCJtYWMiOiI1MTUwMDc4OTViMzU5Y2YyZDdkMDZhNDZmMzYzZjY4NTlkOGZiZjExMGEyZDczMTJmNTM1MjcxM2E5ODI2YjBjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:30:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpuWGt4bVpNRHpHZXZoQnRMeHdDOWc9PSIsInZhbHVlIjoiMGJYV0YrRmZKWkU2MkN2dVdqbmIwQnVQSExUUmVJOVVDTkQ0dFZ0ZmpQQ2UweTJMd25jY2FlUG05alVTWmlpR1c3L0tLOHNWcjcxU1h0bE8wRFh2RGhydjNNczFKZWdzMVpCc1gzWWpCL0MzWmQ2ekg0WFlUaHhSak12VkljaGRLc0pGM2FBWlU3SFZCK3p4TThkZ2EvaW4ydUFpREFZeWt0d0hJcHdlSVRnekxzZTFKc0JRckt2VHhidGNINS9yeHNoVkNIbWx6M1pRcmFpVXdFQ3c2NVdpbXp5TlJpdnAxaFhkclhtU3BOVkx0RnFNZHY5QmJyOXErdnZoQTkrQmRhdGltR0M1ZEY2RnNGamtVWXEzNE1temxScDlzS3E0QzZ5UmQyVG1oUjFUbXdhQ3ZObmlPWjZTaVphMFdMWmJjTllaR2FsSmpYQVRYRit5TGR0b2xTdzNPNlFxVnZXa3VkY1hBamROSnFjRi84VUc3SnphTmhFazhOcmJwdFZKekdVMFljQndkS25VWE96MHZUeDl0MkhPQlBVUHJPM2tCdExDUExibWl3R2tLREcyenlUNjJ4YnVJMk05bTJJclZ0RHYyYVo2RFd5M3lxa29LdU9jeHlqb0pNR1RsM2tYS2pQZUN1T2prL0NtMjVCUG5uUk9UejRNVmlBeGE2UzMiLCJtYWMiOiJkYmM5MmI0MDg3N2ZlMzJjYmQ3N2I3M2NmYWVmOGI1MDM5NzcwYzc2ZmQ5YWM2MjMwODBiMWY4OTRjMzIxMjAzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:30:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954170819\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-488442302 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UuWQbDV7WBTpCBbIy30YKNCCM8Vkj1fovZN063cs</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488442302\", {\"maxDepth\":0})</script>\n"}}