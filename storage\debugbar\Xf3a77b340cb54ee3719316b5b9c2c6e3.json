{"__meta": {"id": "Xf3a77b340cb54ee3719316b5b9c2c6e3", "datetime": "2025-07-30 08:02:37", "utime": **********.998167, "method": "GET", "uri": "/finance/sales/contacts/search?search=Parichay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862556.630529, "end": **********.998213, "duration": 1.3676841259002686, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1753862556.630529, "relative_start": 0, "end": **********.77392, "relative_end": **********.77392, "duration": 1.1433911323547363, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.773954, "relative_start": 1.1434249877929688, "end": **********.998218, "relative_end": 5.0067901611328125e-06, "duration": 0.22426414489746094, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665272, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.028399999999999995, "accumulated_duration_str": "28.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.878132, "duration": 0.023329999999999997, "duration_str": "23.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.148}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.938276, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.148, "width_percent": 6.761}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%Parichay%' or `email` like '%Parichay%' or `contact` like '%Parichay%')", "type": "query", "params": [], "bindings": ["79", "1", "%Parichay%", "%Parichay%", "%Parichay%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.953823, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 88.908, "width_percent": 5.563}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%Parichay%' or `email` like '%Parichay%' or `phone` like '%Parichay%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%Parichay%", "%Parichay%", "%Parichay%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.966249, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 94.472, "width_percent": 5.528}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-584131071 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-584131071\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1205850418 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Parichay</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205850418\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-930936785 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930936785\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-575624085 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImcvdXIwTVBWejM5SGMxV0dSWHlNblE9PSIsInZhbHVlIjoiVGZqRjQ0Z05OblNxeHJOSDBTVVhtS1FnR2ZNS1ZuVFZwKzAyQ1VxQTBrWjJUK1FsZXVNUlUzT0h0Tnl2MkNlMGpYSW1KcmxXT2R6eHZIdGoxRE0vdDVSUW1Ud3czdUczT0l4eUdwU2FrdEFuNmc5cUwyWTV6TzdtMmREYW5SU3orVXNPYXp3ZFBoSXFkQ2xRTVMvc3JSOU1PV1NtVzc5TzRFRm1NNTFaSlovWStva0hiMjVWNVZjSzkxc0hmZVZmQVdiaUxFbGwvWW9mNHhFbDZINTk0eU9keHhmdW1mbFdQTW5qeHo0cmlDdGFIeGtRcWpTMTVwYzhQR2tiVFhldnpwYkJYL09NZS93Y1ZhZXQzWkhsbmJDOVVUemFETEx5YS9TRWVjcUVQTmdyanZSajZmREtnMHNpa0J2U0Yyb1REL0VLbTVveDZlWjkzTXlrYnZCWHlLMnhvNy9GY0RYd0JzbWR6TjFIMngrN25oQ3FtSHJhSjVWQWN1VXlvNjdGTStqOCtsaVF2dHFqSnh3bjhyMXRlSnNTM0ZZRlI2bVdMaFJkbi9iKzA1YTlpUmZqMFAweW9NRStIeDdLSlQzeTQ2TlFJci9wemlDKzlmVkl0RlJUYlJPS3labEZtdGNhSHAzNThIZFV2UjlJR0s0NDJGQTZuamUvUkhhNmJFT28iLCJtYWMiOiJjNTcwNDI1MTk5MTg2ZjU5MzU1YzEwY2M1NmI0ZTQwNWY4YWFiZWViNTgxMTlkMDYwOTEyZmQ4ZmFkMTRmMzkyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImdndXpPY1F0ZnZ5aTJWSmo0UjQxQ0E9PSIsInZhbHVlIjoieFRzczQ5Y3dLSzFBcnlqUmJCMW9yenVzeWFid1I5QU9IbXpTZTZ4VkxBYlUrSisvOE02SE1tZ1lLK25WSm02c0pWMlZjbGxkelI5T1lrVXI5UUhmL1RlYTUxazJKeGtrZzY1NmZ3a3hMbU42OWcvcWxlZHpFZWltTDRrU09qb3J3ZkR4dlNMUlZrS3VVbjRMYWFlQmFIUVEzdk1lV2JLbWhMOHJLUFFla0xJSnRXMkhXYnJGQnVCWjFFRVIvaVRnQys0a3NMUGcxVlhOaWJKRFpXbHFMT1VFQWZNeTZGRGpwYnZuNnIvZDdidjFtR1JKM1Q3YzNzckxQVXEzZDJMMXFEd0IySDExSWVrUHlqNVhzbythSFdJZzlMNWdUWTFYTmhGVlNlUDRoYTJVTDhnQ2IwMHR1RFBHUU1yMXRYYVNCWVJyNU9Ea211OVFqL1dKdUE3SVBBcU5saFZWdWNZQlZXMFlsbFhPWHUzM2RjRG16TldMODlzdFVaSWRjT0VacHF0NWRieXF2VXZtZys5VDRnQ3NZdWxSMm5ITVV5SjN6SWJHRW0rb2drbHI3dUVhQ01ZRFlrK0VRVUM0WFlZRDhWYnlJeFZacm14b2tTTlFKT1FTWXBtRXM3OStvOFliNFJxQm8rRUU2cW1iQjdrbVRiVTIwRldvcmEvYTRtKzciLCJtYWMiOiIxMTM1ZDIxNmZhYTc5NDI4MzRmNjNkNGUyZTk1ZjAzMmZmY2FmYWIwYjdhMjg5ZWRiNDVlYTBkMGZkMTc3NmRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575624085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1119675032 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119675032\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1391897750 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFXM2dRbUlrV2hkdGxIYVpqVDV5dVE9PSIsInZhbHVlIjoiQjdFTXphR1FsOUE1TXZBdDNSMWFDNkcrQSs1aTFsSnh3ZGxjM0xQcWhibEpMWjZwb1dFc1YyajU4TmF5ZUtTbTY3VVhnTGswaFNTOWhQNUxoNkhkUG9iRzU2V1h1dnZvZkdWWEVtZnhFR3lMMWsxM1YzczZ4QW5tSklTMXJPaE52TUhjcWxCbnhlSFUvaFFCQ24zN1p3WmN6NGZpT1pIUC9YWTFWcHhVWE1BNDhMQXdLaUZlNDFkc205a1FIVzNDN1Rvek4rUGF5QnBsSVhNQ3M4TFpBbllxMXZ2aW9oNmxVczBBNnRoR29ia3VvWm5ORGFrdWd1R0RFeUxlcTFSeTZqUGJUVDBZRDhhUXlMcEszZE9CdTgwWXhpVjFIR3dhZ2todGxBZ0k2YWp5U25QYnBxN2hUalZyZE96ZlN0R1NFb3pQaEJFUy9yMkRtQk5NOU55Tjkwd2pmMlV5dkV1WjBob3dHZC9WU0RYUkg2UElDbm1BQ2JvWDZxS3NubjVueldHUFQ4ZWk3djRkRlRKajFUTUk5RGJSKzlnSm5PZ3NNWGxEWGJ4YUdsV2Y5R3J6SDRmZ1BtNWtRTUx3Y25iTmlxMFgwRzhqdW1mOHZOQVpIZHRUZTdZZFdIaDNJWi94RU1ZakpKVjNyNDEwUFljbGt0T2dmN29mMG5xWmNzQS8iLCJtYWMiOiIyZmMxNzkyM2ExOTM4OTFmY2FlNzQ0NDEzNWQ4YjJjOTM1MDJkYzU4ZjU1ZGJmZDZlZTJmNmYwODM0NWIwMTljIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:37 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpXalZ1NDJyZ1FCOEczaDZVZVJUK2c9PSIsInZhbHVlIjoiNERYWHNaaE5XZXk0MDlmbCtITFBEaFNPTG9ZRHVFNVVHcmk0ZzI4NUVmSWpDcHlhQisyMHdEb0ZlWVpzMG8rSC9yaisrZm1NdTRuV2NjOGJzR1RZQ3A2T3hyRk1qdk5XWDFvSXJTYUI2NHdJd1Y4NDZ0ejBBamF1L3JLS0dDbnFXZ0xWcDlKS0xYc0FLY1lIRFlPM0ZERnYrYkpvNm16bTFRRmgxMmYyRFBQRkNJekp5VC9mVm5mRG9YeUNadzJEV1lIdllocVkrVzZPeVFMaWZGL0N1ZFdWckNzdnVGSHkrT0JvVHpGSCtqUE9HM29nZ09LNm5aZDFzNCtjZlNiTFd5cXpjMnNKZWNMWGtpcW1wQ2RKRWFrby8yS1FES0JpS2R6V3BOZEFENzcwSEgxLzRaTDFqTnlrSGdOWXNuSjFuN3R6aWxqR1hOaXNzOFc1aGxNeXh0d3FWaXEvNlZkWUtlaFFjK2JVbWVGc0VVb3F0YkhLbnozUDk5bmdhKzNvR253SmZtT1hkQVd2TVF3S2ZNVnA2WXRoZVpxNmVuQzVtWHJrNHJOdVlWYndGNldQY1Y2akdDK2VKR0JTZWNQUi8zZDJpdzlwMWxpR3ZEN1B1UnBOS2Q4dXJnUGdJeGorVnViR3F4Rk5LUjlLTWVZdjdlQmY2SG8ydWZoTml1WnkiLCJtYWMiOiI5OTQ5ZDBlNDVhZjgwMjZmNWMzMjIyZjQxOTI3ZDczNTI5OWFjNDJiMGZlNTFlOTRmYjg0N2Y3ZmRiZTg4NDFkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:37 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFXM2dRbUlrV2hkdGxIYVpqVDV5dVE9PSIsInZhbHVlIjoiQjdFTXphR1FsOUE1TXZBdDNSMWFDNkcrQSs1aTFsSnh3ZGxjM0xQcWhibEpMWjZwb1dFc1YyajU4TmF5ZUtTbTY3VVhnTGswaFNTOWhQNUxoNkhkUG9iRzU2V1h1dnZvZkdWWEVtZnhFR3lMMWsxM1YzczZ4QW5tSklTMXJPaE52TUhjcWxCbnhlSFUvaFFCQ24zN1p3WmN6NGZpT1pIUC9YWTFWcHhVWE1BNDhMQXdLaUZlNDFkc205a1FIVzNDN1Rvek4rUGF5QnBsSVhNQ3M4TFpBbllxMXZ2aW9oNmxVczBBNnRoR29ia3VvWm5ORGFrdWd1R0RFeUxlcTFSeTZqUGJUVDBZRDhhUXlMcEszZE9CdTgwWXhpVjFIR3dhZ2todGxBZ0k2YWp5U25QYnBxN2hUalZyZE96ZlN0R1NFb3pQaEJFUy9yMkRtQk5NOU55Tjkwd2pmMlV5dkV1WjBob3dHZC9WU0RYUkg2UElDbm1BQ2JvWDZxS3NubjVueldHUFQ4ZWk3djRkRlRKajFUTUk5RGJSKzlnSm5PZ3NNWGxEWGJ4YUdsV2Y5R3J6SDRmZ1BtNWtRTUx3Y25iTmlxMFgwRzhqdW1mOHZOQVpIZHRUZTdZZFdIaDNJWi94RU1ZakpKVjNyNDEwUFljbGt0T2dmN29mMG5xWmNzQS8iLCJtYWMiOiIyZmMxNzkyM2ExOTM4OTFmY2FlNzQ0NDEzNWQ4YjJjOTM1MDJkYzU4ZjU1ZGJmZDZlZTJmNmYwODM0NWIwMTljIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpXalZ1NDJyZ1FCOEczaDZVZVJUK2c9PSIsInZhbHVlIjoiNERYWHNaaE5XZXk0MDlmbCtITFBEaFNPTG9ZRHVFNVVHcmk0ZzI4NUVmSWpDcHlhQisyMHdEb0ZlWVpzMG8rSC9yaisrZm1NdTRuV2NjOGJzR1RZQ3A2T3hyRk1qdk5XWDFvSXJTYUI2NHdJd1Y4NDZ0ejBBamF1L3JLS0dDbnFXZ0xWcDlKS0xYc0FLY1lIRFlPM0ZERnYrYkpvNm16bTFRRmgxMmYyRFBQRkNJekp5VC9mVm5mRG9YeUNadzJEV1lIdllocVkrVzZPeVFMaWZGL0N1ZFdWckNzdnVGSHkrT0JvVHpGSCtqUE9HM29nZ09LNm5aZDFzNCtjZlNiTFd5cXpjMnNKZWNMWGtpcW1wQ2RKRWFrby8yS1FES0JpS2R6V3BOZEFENzcwSEgxLzRaTDFqTnlrSGdOWXNuSjFuN3R6aWxqR1hOaXNzOFc1aGxNeXh0d3FWaXEvNlZkWUtlaFFjK2JVbWVGc0VVb3F0YkhLbnozUDk5bmdhKzNvR253SmZtT1hkQVd2TVF3S2ZNVnA2WXRoZVpxNmVuQzVtWHJrNHJOdVlWYndGNldQY1Y2akdDK2VKR0JTZWNQUi8zZDJpdzlwMWxpR3ZEN1B1UnBOS2Q4dXJnUGdJeGorVnViR3F4Rk5LUjlLTWVZdjdlQmY2SG8ydWZoTml1WnkiLCJtYWMiOiI5OTQ5ZDBlNDVhZjgwMjZmNWMzMjIyZjQxOTI3ZDczNTI5OWFjNDJiMGZlNTFlOTRmYjg0N2Y3ZmRiZTg4NDFkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391897750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-411665107 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411665107\", {\"maxDepth\":0})</script>\n"}}