{"__meta": {"id": "X0541ff7e3443ffd0175f1a6f59b0b5ff", "datetime": "2025-07-30 06:06:20", "utime": **********.753579, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855579.888773, "end": **********.753611, "duration": 0.8648381233215332, "duration_str": "865ms", "measures": [{"label": "Booting", "start": 1753855579.888773, "relative_start": 0, "end": **********.565753, "relative_end": **********.565753, "duration": 0.6769800186157227, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.565768, "relative_start": 0.676995038986206, "end": **********.753614, "relative_end": 2.86102294921875e-06, "duration": 0.18784594535827637, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46597368, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.663155, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.680929, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.730865, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.028510000000000004, "accumulated_duration_str": "28.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.611567, "duration": 0.01549, "duration_str": "15.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 54.332}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.631303, "duration": 0.0061600000000000005, "duration_str": "6.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 54.332, "width_percent": 21.606}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6432989, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 75.938, "width_percent": 2.631}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.664226, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 78.569, "width_percent": 3.718}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.682311, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 82.287, "width_percent": 3.753}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.702486, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 86.04, "width_percent": 3.472}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.712796, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 89.512, "width_percent": 5.156}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.721369, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 94.669, "width_percent": 5.331}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "neKcPpMjbly6We2Mj83xamILeAANzBF2pThVPzyw", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1579772053 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1579772053\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2036827706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2036827706\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1236701080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236701080\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1248256063 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248256063\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-556481479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-556481479\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1068555994 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:06:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9UN3JldHRReGpDMmR6L0IvNFlTWlE9PSIsInZhbHVlIjoiYnE5bXBkQjgwTXlyb21DYmFrOUxVbFBSWVhEZkxRU2VZdFljR1ZmcDR6Y2F2WDBrWG1ERzBhVW5UR2hUR2xVc21Tbm9mYVBQVUJNNGRYanErNUZ4bCtoeFdVMTNZWmVOZWVpd2NwQkNwRTVKVEdkTTFCN2d0anVOR1JpVUIremgyQmJRUTlabklwSEdTdDE3Z3NOMExWVDFEVFMwa3FaL0VPM0w5Z0RmdHliSXRNMnQ0dXpBbCtBa21taHloN3R4TkdKaFBBNFdHcCs4TTd4b2ZRb3pCeVdESkF5czlCbkM3V3VUazVEV3NOVm12SjhOcTZ4MHhocmEwUTRndjNveWxZb3AzUi9lSUhBOVhjTkorTXJxdnFibkR1eWJIN0syTjRBTjgzaEpQZXRWeXVFbW1xTUF1SE94Zzc5S2krMVRtSzB0TFV2bjdnRjFQeFlhSHFvK0JrRWJvWUZKK2tUVXVBTFRVZFJqR0pTbHdSOXN6Tlp6OHF0SHNvb3BCemlQUDNJcFJubnJLbmFxdVBPNDhLd3NjdjdHVXFYY3lJK1lmSGhYaUE5UEVTRFprbDk4LytkN0F6ZkN2U0ZqWHlNYWwzTjFFL1E0cHFSQ2l2d0NJbXJIOXFDVk5YZktTNE5nS2Z3cXcwM3JCUTgwaWd5V2MzMjBjc3VOcjFQdFRvUmQiLCJtYWMiOiJmYTFhMWUxZDBlNjFkNGNmZjliNTg0MWZhZTlkNDdmMTMzNjZlOTM5ODA3ZmQ1NjgxNjIwYjllOWMzOTVmNzIyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImdBVkJBQ3doekhaZVhpR0JncjlablE9PSIsInZhbHVlIjoid1FQSE1NZFk4ZUFVNHlKUFJNcnB5U0xLbGNyYzRoZ1BicnYvbUxSRWhtZTMxa2hoZ0FFM25HZmtmQy8wWmNDdER5ZC9ZL0x6VVFGMmkvcStPZlI0YmpzZE9KVHFaT2xld091RzlZYU1kV2MrcXh6RVNDZTY5QVRFU1ptU3VldEdMSjh2Zk9ZN3pWVjh1LzZSbVJPTW40WFFVeFJLenpXVUJWU1QrcmxTd3F0ekV3YVoyQ3dEVlFpYjZOUEs1MUc4Q0R6T0VlaU8yZ2MxY2xIcmg3QkZHZG10RXVRYU5lNExpaEpOWFlRVUFxdEc1clFBUkdaM0JPOExhMW1wNnNNbkppdFR5NkNGSjBTdWl6RjhlYkFaUWdRN1N0YlBobGlZU2JaMDZGejNIV1l6YzlXTFJyNXEybG1xR2xKSEZPckx6akVQVmcxV21NdlQ4WEVqQ0oyMWw3TEFOTEF0OVdYZVdmeXh6V0FSemdoVGYzb3oydkhlaUNaUkNkeHVyZXhiMjI3WDZZTG0rQTlYeWRCdmRGeWFVS3hHYUYrSi9EbEpoMFNFekJheklMcXZjWW5HZ1hYOFgyUUtISFdYUHJvYzIraEMySjNmdnV6Rng3MXUvMWlTdWJyNUo4ZXltOHZOOU9BTTNCcFVZSzRadHB0V2lYYTZpSjd2ZzRKUS8zckUiLCJtYWMiOiI1ODllM2Y2MWE0YTE5ODA4ODA0NTQ5MWJmYmI5YTIzODc2MTBlYzM3NmNkNzUwY2MzYjJiMzUzYmI5ZWQ4NDc4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9UN3JldHRReGpDMmR6L0IvNFlTWlE9PSIsInZhbHVlIjoiYnE5bXBkQjgwTXlyb21DYmFrOUxVbFBSWVhEZkxRU2VZdFljR1ZmcDR6Y2F2WDBrWG1ERzBhVW5UR2hUR2xVc21Tbm9mYVBQVUJNNGRYanErNUZ4bCtoeFdVMTNZWmVOZWVpd2NwQkNwRTVKVEdkTTFCN2d0anVOR1JpVUIremgyQmJRUTlabklwSEdTdDE3Z3NOMExWVDFEVFMwa3FaL0VPM0w5Z0RmdHliSXRNMnQ0dXpBbCtBa21taHloN3R4TkdKaFBBNFdHcCs4TTd4b2ZRb3pCeVdESkF5czlCbkM3V3VUazVEV3NOVm12SjhOcTZ4MHhocmEwUTRndjNveWxZb3AzUi9lSUhBOVhjTkorTXJxdnFibkR1eWJIN0syTjRBTjgzaEpQZXRWeXVFbW1xTUF1SE94Zzc5S2krMVRtSzB0TFV2bjdnRjFQeFlhSHFvK0JrRWJvWUZKK2tUVXVBTFRVZFJqR0pTbHdSOXN6Tlp6OHF0SHNvb3BCemlQUDNJcFJubnJLbmFxdVBPNDhLd3NjdjdHVXFYY3lJK1lmSGhYaUE5UEVTRFprbDk4LytkN0F6ZkN2U0ZqWHlNYWwzTjFFL1E0cHFSQ2l2d0NJbXJIOXFDVk5YZktTNE5nS2Z3cXcwM3JCUTgwaWd5V2MzMjBjc3VOcjFQdFRvUmQiLCJtYWMiOiJmYTFhMWUxZDBlNjFkNGNmZjliNTg0MWZhZTlkNDdmMTMzNjZlOTM5ODA3ZmQ1NjgxNjIwYjllOWMzOTVmNzIyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImdBVkJBQ3doekhaZVhpR0JncjlablE9PSIsInZhbHVlIjoid1FQSE1NZFk4ZUFVNHlKUFJNcnB5U0xLbGNyYzRoZ1BicnYvbUxSRWhtZTMxa2hoZ0FFM25HZmtmQy8wWmNDdER5ZC9ZL0x6VVFGMmkvcStPZlI0YmpzZE9KVHFaT2xld091RzlZYU1kV2MrcXh6RVNDZTY5QVRFU1ptU3VldEdMSjh2Zk9ZN3pWVjh1LzZSbVJPTW40WFFVeFJLenpXVUJWU1QrcmxTd3F0ekV3YVoyQ3dEVlFpYjZOUEs1MUc4Q0R6T0VlaU8yZ2MxY2xIcmg3QkZHZG10RXVRYU5lNExpaEpOWFlRVUFxdEc1clFBUkdaM0JPOExhMW1wNnNNbkppdFR5NkNGSjBTdWl6RjhlYkFaUWdRN1N0YlBobGlZU2JaMDZGejNIV1l6YzlXTFJyNXEybG1xR2xKSEZPckx6akVQVmcxV21NdlQ4WEVqQ0oyMWw3TEFOTEF0OVdYZVdmeXh6V0FSemdoVGYzb3oydkhlaUNaUkNkeHVyZXhiMjI3WDZZTG0rQTlYeWRCdmRGeWFVS3hHYUYrSi9EbEpoMFNFekJheklMcXZjWW5HZ1hYOFgyUUtISFdYUHJvYzIraEMySjNmdnV6Rng3MXUvMWlTdWJyNUo4ZXltOHZOOU9BTTNCcFVZSzRadHB0V2lYYTZpSjd2ZzRKUS8zckUiLCJtYWMiOiI1ODllM2Y2MWE0YTE5ODA4ODA0NTQ5MWJmYmI5YTIzODc2MTBlYzM3NmNkNzUwY2MzYjJiMzUzYmI5ZWQ4NDc4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068555994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1676576705 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">neKcPpMjbly6We2Mj83xamILeAANzBF2pThVPzyw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676576705\", {\"maxDepth\":0})</script>\n"}}