{"__meta": {"id": "X7863dcefe14c631c0070612b50d014de", "datetime": "2025-07-30 08:36:11", "utime": **********.076528, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753864569.287566, "end": **********.076582, "duration": 1.7890160083770752, "duration_str": "1.79s", "measures": [{"label": "Booting", "start": 1753864569.287566, "relative_start": 0, "end": 1753864570.940701, "relative_end": 1753864570.940701, "duration": 1.653135061264038, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753864570.940743, "relative_start": 1.**************, "end": **********.076589, "relative_end": 7.152557373046875e-06, "duration": 0.*****************, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Tqv626wEi0CX7JVLnyEtnEngeJF7R8vcRcPpX9TJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-831263497 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-831263497\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1038195001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038195001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1146021635 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146021635\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1451834960 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451834960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1501718367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1501718367\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:36:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklTTmlLSWpDRFdSOE1XL1R5S2M5ZHc9PSIsInZhbHVlIjoiS0J5bENJZ2FTNWhpQWRkVXl6S0UvTEZndHFiNTczUHhQS2pad3FkbEpDUnd4T0E4VTFIMjhCWlp2K01hU3A1YWp0U0oxOG5qalB0L2VCT29wWG94eVc5TUw0dm40anMrQlUxWjlsNlNXTVBId2FGOEtIeFZmc1VjQXZEQ0YzaWRDNm5YUUROMUFjdE1mT09MSFlkRU9tT2luSzU5NXh0VHI5amZrVXg0Z0RPbVVydzdOWFBiR3RNRkFzbm1zV0tJMzZrUEJUZkthWjB0elFSd1ZWOWU2ejNkVHBmSjlPTis1R2tnVzdNUnFZSEtFU0gyV3lLVHF2VHRSK0YzTzh2WWxNZDk3OEZKODNkL2xMbjBYa3BZblFsL3VYdUg0S0Y5dXdwL01vZHRqYmxWV0gxWElGZFoxajRPcGVJTE83R2hBSTVhbjJ6cFlWdDFjZC9BQ2xwRWZiSkZIUEpoNWMxZVFaZDY2Z2t0elN5T1d6dUhvR0tXVXM5NkNVSzFMdEp6MkV2TENhMGZaWndRQUlPM3FtQlJCd0JPUmhZSHFKY01UaWtqeXc3NlJkTUljZW9ZNHZlMmljb0V4M0JVV0dYbkk1NXJZNTAvU2JLYllXQUFjaGd5TkFTdmhuZmJSbXljZ0ZSVS82aktVY3pkeDJvTWpwc0Njbyt5QllvZFdFRVgiLCJtYWMiOiI5OGVlNmQ2ZDU4ODc0ZDFkNDczN2M1MDA2MGQ1ZDRiYWRmMGRhZDBhZjc0MzYwYzdmMDc1ZTk5OTAwMzNmMDQxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:36:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNBNVVHR0wwaEpWb3d4NWJyVTRQMUE9PSIsInZhbHVlIjoiYmVKZ3V1SlM2cU9LaGNFU054TjkwYmRGTEsyQjQrV1BQdjNtdlFaS0ViSnRjTVhHMDlKdjRkYTFXRDVWS0lKOTVmNmZLZVBYdy9jWFJpWitxaXpWM2ExdDNXVGJBNmlKQS9WdEJCSFNTR2d1MzJiTzIyZ2FCWDNkcE9wRHBGYUFNNVlrelRES0IrZlNLR0J4TDdEak5zYWZsWlVGWTdFVnhKMTJrSEZGUzY3SGlzVVlkcEZYTXZxMXpZL2pxaXVqekVjUFNRMVgyY3VCY0pqWm9rbFlncklha2VDSWh0V1BkcFV5R2svRHZWMjVPc2hKUlJieUhZeXVlZnVxR0ZrRmNlelpGVUVoc1VsNDgraytCaGdDZGdoNXgxREZxTjNMRnJKdCs4d2hDQUtRS2poRXluQ0pWc3VwelNNQUF6c201RExRVUp3OWlhV3FyWUlHTlJKSDRCVmZRWWlSaWUvRDhUbmxwVmRxRURWMUE3MVBGTFpycllPT2sxTWRsUFdTbTc2dDlhVkh4WEZjTjhnOCt2Kzh6SjVKNC9xRnhxbTRNRWF3SGw4a1NCMWVKY2JTTktvSHlQTitXQUdlbUJoWUltbnBzcHdKZzFoa2JTaGJ0YXdsNU1PTVRGQmk5UVpwOTVMQVVLTnRocmVMWVIwbklzeXk0Vzh5bGx4S245TlIiLCJtYWMiOiJlODJjYTk3YTg0MzllMzMzNDdiMzU3NjBhN2JiNTE2YzFhOTAxYjBiOGYxMjBhNjllNWU4MGQ0OTM4ZTIwOWY4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:36:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklTTmlLSWpDRFdSOE1XL1R5S2M5ZHc9PSIsInZhbHVlIjoiS0J5bENJZ2FTNWhpQWRkVXl6S0UvTEZndHFiNTczUHhQS2pad3FkbEpDUnd4T0E4VTFIMjhCWlp2K01hU3A1YWp0U0oxOG5qalB0L2VCT29wWG94eVc5TUw0dm40anMrQlUxWjlsNlNXTVBId2FGOEtIeFZmc1VjQXZEQ0YzaWRDNm5YUUROMUFjdE1mT09MSFlkRU9tT2luSzU5NXh0VHI5amZrVXg0Z0RPbVVydzdOWFBiR3RNRkFzbm1zV0tJMzZrUEJUZkthWjB0elFSd1ZWOWU2ejNkVHBmSjlPTis1R2tnVzdNUnFZSEtFU0gyV3lLVHF2VHRSK0YzTzh2WWxNZDk3OEZKODNkL2xMbjBYa3BZblFsL3VYdUg0S0Y5dXdwL01vZHRqYmxWV0gxWElGZFoxajRPcGVJTE83R2hBSTVhbjJ6cFlWdDFjZC9BQ2xwRWZiSkZIUEpoNWMxZVFaZDY2Z2t0elN5T1d6dUhvR0tXVXM5NkNVSzFMdEp6MkV2TENhMGZaWndRQUlPM3FtQlJCd0JPUmhZSHFKY01UaWtqeXc3NlJkTUljZW9ZNHZlMmljb0V4M0JVV0dYbkk1NXJZNTAvU2JLYllXQUFjaGd5TkFTdmhuZmJSbXljZ0ZSVS82aktVY3pkeDJvTWpwc0Njbyt5QllvZFdFRVgiLCJtYWMiOiI5OGVlNmQ2ZDU4ODc0ZDFkNDczN2M1MDA2MGQ1ZDRiYWRmMGRhZDBhZjc0MzYwYzdmMDc1ZTk5OTAwMzNmMDQxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:36:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNBNVVHR0wwaEpWb3d4NWJyVTRQMUE9PSIsInZhbHVlIjoiYmVKZ3V1SlM2cU9LaGNFU054TjkwYmRGTEsyQjQrV1BQdjNtdlFaS0ViSnRjTVhHMDlKdjRkYTFXRDVWS0lKOTVmNmZLZVBYdy9jWFJpWitxaXpWM2ExdDNXVGJBNmlKQS9WdEJCSFNTR2d1MzJiTzIyZ2FCWDNkcE9wRHBGYUFNNVlrelRES0IrZlNLR0J4TDdEak5zYWZsWlVGWTdFVnhKMTJrSEZGUzY3SGlzVVlkcEZYTXZxMXpZL2pxaXVqekVjUFNRMVgyY3VCY0pqWm9rbFlncklha2VDSWh0V1BkcFV5R2svRHZWMjVPc2hKUlJieUhZeXVlZnVxR0ZrRmNlelpGVUVoc1VsNDgraytCaGdDZGdoNXgxREZxTjNMRnJKdCs4d2hDQUtRS2poRXluQ0pWc3VwelNNQUF6c201RExRVUp3OWlhV3FyWUlHTlJKSDRCVmZRWWlSaWUvRDhUbmxwVmRxRURWMUE3MVBGTFpycllPT2sxTWRsUFdTbTc2dDlhVkh4WEZjTjhnOCt2Kzh6SjVKNC9xRnhxbTRNRWF3SGw4a1NCMWVKY2JTTktvSHlQTitXQUdlbUJoWUltbnBzcHdKZzFoa2JTaGJ0YXdsNU1PTVRGQmk5UVpwOTVMQVVLTnRocmVMWVIwbklzeXk0Vzh5bGx4S245TlIiLCJtYWMiOiJlODJjYTk3YTg0MzllMzMzNDdiMzU3NjBhN2JiNTE2YzFhOTAxYjBiOGYxMjBhNjllNWU4MGQ0OTM4ZTIwOWY4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:36:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1725460870 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Tqv626wEi0CX7JVLnyEtnEngeJF7R8vcRcPpX9TJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725460870\", {\"maxDepth\":0})</script>\n"}}