{"__meta": {"id": "Xa0839c57e771a03def3015d7f54dab64", "datetime": "2025-07-30 05:16:11", "utime": **********.318511, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753852570.162905, "end": **********.318543, "duration": 1.1556379795074463, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1753852570.162905, "relative_start": 0, "end": **********.214005, "relative_end": **********.214005, "duration": 1.0511000156402588, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.214024, "relative_start": 1.***************, "end": **********.318546, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uXsLutEI3cJvjAk7lSOIBN5Mb6Clf2nj85fU8ROj", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1963157849 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1963157849\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2116250498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116250498\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-780926558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-780926558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1261528495 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261528495\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1965396402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1965396402\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-713569260 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:16:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZCUkphd0wrV1lJeGNFZnRiSU5SMVE9PSIsInZhbHVlIjoiM2NUVnB4SGFJcWJmZm93c1FLSEJMbFc0ZmtWZ2xTTk93RjExNEljVFM3Q3M4cWFBcmQ4bTRTQjhqSkd6WHZKc0hockdMUDZZSFBFeDQ5c1FodzlnWTVqd0plbFlTRWdhZkxGc2ZnSkFpbmpXMjF0a1loNXRxeFQ0LzZzOExkYVhMTWVkWW5KSmhPTzcyTEQxWDYyajNEM3BmYTVKbU56S2REc09FOUQ0anRFdFRsSFUxNEdWNm92OE56NURZV25walc5SG9Nbjk0RytkRXlFWGFiUFBWUC93MVFxQ3lQcXNWbnlrNS9EaG9lOWVSUjlXbVJOaDlpcGFGRkNPVWdmbWlJcG5oekl2MXMxakhzK081dVRGVEdsbXM2OUY4cU5sSG40V0pNUDd1VlFXeGRzM1NQYzZGWVRqUVBOS25NQVJ4UFl1eUJyV09udkh0bHNsTzA0M3lGSGQ0QkNlazRpajJtT2srOHBmSUhYMUo4UmhvdzY3Z28rRHNlbjZXY281N0FNb3MyMVFBTURXM0QrR2RXYVBuQXZYNSt4dDYvTjNtSU5BWlI1bEVHaHdSak5odHoyM25yMnhrV0VDZWw1Y3YwMU9vRGxhQ1J1ODE1d1g0blNxVzM4QUk1bDl1bFN5K0cwTTlXMTNZNFE5amZuVjlObzJDSkRxTzhMNlhOWlkiLCJtYWMiOiIxNjk2M2U5NTY0ZGU0Njg2MjQzYjliODhkNjA3NjcyYTA3YTc4NzI3MDEwZmNjYTBlNzJmOWJlNTg1ZGMyYjFmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:16:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InFXa2tLdCt3OEs5QkhBdFNOVlYxNnc9PSIsInZhbHVlIjoiM0Q3V2tMS3V1VEVTSkNsYlFNYkMyTDZKK25LVnpSVjN3amt3eFFST3I0TjZtQndkY1VScmsrZXhvcXVIQzZJeTVmT0lraTBQM0F1UU5JaWVlNkx4OUVGMm8wdnBkN3VSa0tnWm5uMkp1Zys1eVovcWlldG1nc2hKbEpydGtpSTVwU3Q4YVNMWitQSFVpSVB2dWR1SnpTOW5ndzNibzA3QjczNjNzN3NMU1B4ZVJFT1hUQStidlV1RExzZ2U4U1lMeXNEdHdIZWgyWVdzYTdZZUkwK0VrdEpMbXBBVXhQcGsxMVZ1eGRJcDhSMGJSSHNOV2Q4SmdvMTlFcklkalR0MFZSS09DVko1ZDFxV3dUdnJ0MkxkUU9uUWFIQzZjQVY2cFROUUlTWjJhMGgzcHRwWEtCZEIxRTROQ24vYjNBN0FaSklhTjhxQ0ZGTVVaOGIxMUFvODJMenZRRXVxUHQzTWl5RWV3THRJK3lnZW4zbHZxVjQ3U0c0MFRyV1dYOVo5cm5ibm5hTFdpMHczNklCTXJ4dGtVNjBCczFoZy9ZKzNRak56dVBGTmVlUG1uSFBZUE5LYy90YUU2WTQ3YXh3WEJwUGh4aTZNRWorZVdGTVFROS9XSzJVU2UwTi9UcTBQcDRNYjZ5T2E0L20rYmRmVFZtVC82dTlPNEc1NTVJdVAiLCJtYWMiOiI1MTU3N2Y1MTdiOThhOGM0YThhMWE3OTI5MjIwZTlhNDRlZjViZTA4M2UyNTE5M2FkMGJjY2Q0ZDg5MjUxMjhjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:16:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZCUkphd0wrV1lJeGNFZnRiSU5SMVE9PSIsInZhbHVlIjoiM2NUVnB4SGFJcWJmZm93c1FLSEJMbFc0ZmtWZ2xTTk93RjExNEljVFM3Q3M4cWFBcmQ4bTRTQjhqSkd6WHZKc0hockdMUDZZSFBFeDQ5c1FodzlnWTVqd0plbFlTRWdhZkxGc2ZnSkFpbmpXMjF0a1loNXRxeFQ0LzZzOExkYVhMTWVkWW5KSmhPTzcyTEQxWDYyajNEM3BmYTVKbU56S2REc09FOUQ0anRFdFRsSFUxNEdWNm92OE56NURZV25walc5SG9Nbjk0RytkRXlFWGFiUFBWUC93MVFxQ3lQcXNWbnlrNS9EaG9lOWVSUjlXbVJOaDlpcGFGRkNPVWdmbWlJcG5oekl2MXMxakhzK081dVRGVEdsbXM2OUY4cU5sSG40V0pNUDd1VlFXeGRzM1NQYzZGWVRqUVBOS25NQVJ4UFl1eUJyV09udkh0bHNsTzA0M3lGSGQ0QkNlazRpajJtT2srOHBmSUhYMUo4UmhvdzY3Z28rRHNlbjZXY281N0FNb3MyMVFBTURXM0QrR2RXYVBuQXZYNSt4dDYvTjNtSU5BWlI1bEVHaHdSak5odHoyM25yMnhrV0VDZWw1Y3YwMU9vRGxhQ1J1ODE1d1g0blNxVzM4QUk1bDl1bFN5K0cwTTlXMTNZNFE5amZuVjlObzJDSkRxTzhMNlhOWlkiLCJtYWMiOiIxNjk2M2U5NTY0ZGU0Njg2MjQzYjliODhkNjA3NjcyYTA3YTc4NzI3MDEwZmNjYTBlNzJmOWJlNTg1ZGMyYjFmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:16:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InFXa2tLdCt3OEs5QkhBdFNOVlYxNnc9PSIsInZhbHVlIjoiM0Q3V2tMS3V1VEVTSkNsYlFNYkMyTDZKK25LVnpSVjN3amt3eFFST3I0TjZtQndkY1VScmsrZXhvcXVIQzZJeTVmT0lraTBQM0F1UU5JaWVlNkx4OUVGMm8wdnBkN3VSa0tnWm5uMkp1Zys1eVovcWlldG1nc2hKbEpydGtpSTVwU3Q4YVNMWitQSFVpSVB2dWR1SnpTOW5ndzNibzA3QjczNjNzN3NMU1B4ZVJFT1hUQStidlV1RExzZ2U4U1lMeXNEdHdIZWgyWVdzYTdZZUkwK0VrdEpMbXBBVXhQcGsxMVZ1eGRJcDhSMGJSSHNOV2Q4SmdvMTlFcklkalR0MFZSS09DVko1ZDFxV3dUdnJ0MkxkUU9uUWFIQzZjQVY2cFROUUlTWjJhMGgzcHRwWEtCZEIxRTROQ24vYjNBN0FaSklhTjhxQ0ZGTVVaOGIxMUFvODJMenZRRXVxUHQzTWl5RWV3THRJK3lnZW4zbHZxVjQ3U0c0MFRyV1dYOVo5cm5ibm5hTFdpMHczNklCTXJ4dGtVNjBCczFoZy9ZKzNRak56dVBGTmVlUG1uSFBZUE5LYy90YUU2WTQ3YXh3WEJwUGh4aTZNRWorZVdGTVFROS9XSzJVU2UwTi9UcTBQcDRNYjZ5T2E0L20rYmRmVFZtVC82dTlPNEc1NTVJdVAiLCJtYWMiOiI1MTU3N2Y1MTdiOThhOGM0YThhMWE3OTI5MjIwZTlhNDRlZjViZTA4M2UyNTE5M2FkMGJjY2Q0ZDg5MjUxMjhjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:16:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713569260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722054465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uXsLutEI3cJvjAk7lSOIBN5Mb6Clf2nj85fU8ROj</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722054465\", {\"maxDepth\":0})</script>\n"}}