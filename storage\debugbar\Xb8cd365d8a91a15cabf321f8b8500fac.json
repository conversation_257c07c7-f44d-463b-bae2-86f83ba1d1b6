{"__meta": {"id": "Xb8cd365d8a91a15cabf321f8b8500fac", "datetime": "2025-07-30 07:36:11", "utime": **********.242343, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753860969.228463, "end": **********.242491, "duration": 2.0140280723571777, "duration_str": "2.01s", "measures": [{"label": "Booting", "start": 1753860969.228463, "relative_start": 0, "end": **********.089355, "relative_end": **********.089355, "duration": 1.8608920574188232, "duration_str": "1.86s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.089383, "relative_start": 1.****************, "end": **********.242501, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XXYoNuef905JsvTNR1VtQrzp8P4cjVq3trtiJJhU", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-164018858 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-164018858\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-34569898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-34569898\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1259021474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1259021474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2039485383 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039485383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1779402232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1779402232\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1734300129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:36:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBwYWlXQy8rSHVMaEpHbHhpUkg2R0E9PSIsInZhbHVlIjoiMUFBejZBV3NndDBUV0ZXRTF5VU44MFQ1UjRWM0k1NzV6NWRtTC9xRFIrRGMvdnFaSldDVk0yaEJVdUNGdElmbERTV3BtYkpYTElOVjk4RFB3SXYySlVuQlVSY1ppOHlDaHBST05RcEtTZmcwQ3oyZUdKUlEwQVd3QlpFOVhRSjVmNlNheWk3QUY1ZGJidml6Nmc0NXJUeWdkL2xma05YMHFYRTBrYlJkanA0VS9IU3lhSHBUVWUzOGdySFljcjZsRE9VZVNFeGJZOFZrdFpobnpRQ091cm04eDZaMW01ZkRud0N4M285R25HY3dpMVpWZFdOdnZkMTg3SklQczg2UXpDRU04SmpsYVJDckZCSDFRMXZkZE0raFF3VmhCeHV2V0d4ZTFpUFdwVTJEZitRTlV1NGFwRkl2eS9GWlgrTXA4Q1hOaWU4WFI1ajVoUURFY3g1TFloWm1NU1VxUWtCYTY2M3VWU21CaEt4Qmxpd1haTG15NnF3cGdneGhPUkYvQmJVdjhERGFwRGx5OFlraTZxZ3VqeEpPcTZaUUtXc3ZCamJFNDhURUhYWnAwL0djdW9JY2tJemRQSzExZk5CYmZ3NkdaMVpWRDRpZmVLdS9va2IramM0bEl6UVQ5MjVWaUJJUkpxcnB6TUNPVW1ET2YvMVZGaXlvckx6K2U4dy8iLCJtYWMiOiI4YWVkMDFhYWUzOWQzMGEzOGI0ZGRjN2U1ODgyMmE2Y2EyYTg3YzFmZjNjNjA0NTlhMDVjNDE2MTQ0NDJlZTYwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:36:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJGRm1qRXY4c2lqZG1WK2d6TWdCdFE9PSIsInZhbHVlIjoiT3huK3dwSDM4MXRUdzJ1NEdvazRtVDd6MmV1a29VZUE0S0dkSE9qOWJFVXdBVElrVzcyMGFyeFNpSTN0RCt0Y3lKOUVNY3JaVFhvazg1TVM3VDQ3Zndmb1NzcnZOZVdTZGVxLzF3ZitIa2p4UnZBeWVzaFovOVF3MjdXNmhsVXdGdHhJN3AyT2kwTk9oQmFya2E5VHFubEU1aWlVYk9seko4SXUyZXBPaS9ySHhMcVhHcFpKVUgwT3RVZENNTmR1b1VsRlNyTWY0UkJkNWN5d2FKcFhrVHNQZVR6ZGcyZ1B4U3I2SjlOWUpBQWRCamhRREFLQzZZQXQ2TjE5RlZoYjlLWjJoZW40QmNDRE1Tb0JwLy9aWVVDZExFbXUyeXNpdktSbHJ5Z1FzMG1nRkc1b2ZmNldWa1o4QWd3alZiUGpmMVFTTThtTzZSRmxyK0dzQzZzcjM3STF6Y1VWNEtMRjJIWmxXZEhlWXFEcDVnYlI1RkRremNrRDhkTGtXdHMxVHhFM2YrUnN1NCtWellMMVhEdTQyODRVZ1N6bkZUdHA5ekxJWU8rUjh5V0pDTXFtd0FHRGU4a05BK1dpSHZzdCt1RXVpMHNodFRWUmRyWllpNkxNbUNVM1krR3QwTVNMR25MSHY1eUpOSWdNUmJlRjIvQVVjeGtJVkhBTkFUSlIiLCJtYWMiOiJiMWRjMmRhYjEyOTVkOGE1MzIxNzU5YzM2ZWFmNDc3ODRmNGVmOTYwNzhjMmZlZDVkOTBjYjI5MTYxODAwOTk5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:36:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBwYWlXQy8rSHVMaEpHbHhpUkg2R0E9PSIsInZhbHVlIjoiMUFBejZBV3NndDBUV0ZXRTF5VU44MFQ1UjRWM0k1NzV6NWRtTC9xRFIrRGMvdnFaSldDVk0yaEJVdUNGdElmbERTV3BtYkpYTElOVjk4RFB3SXYySlVuQlVSY1ppOHlDaHBST05RcEtTZmcwQ3oyZUdKUlEwQVd3QlpFOVhRSjVmNlNheWk3QUY1ZGJidml6Nmc0NXJUeWdkL2xma05YMHFYRTBrYlJkanA0VS9IU3lhSHBUVWUzOGdySFljcjZsRE9VZVNFeGJZOFZrdFpobnpRQ091cm04eDZaMW01ZkRud0N4M285R25HY3dpMVpWZFdOdnZkMTg3SklQczg2UXpDRU04SmpsYVJDckZCSDFRMXZkZE0raFF3VmhCeHV2V0d4ZTFpUFdwVTJEZitRTlV1NGFwRkl2eS9GWlgrTXA4Q1hOaWU4WFI1ajVoUURFY3g1TFloWm1NU1VxUWtCYTY2M3VWU21CaEt4Qmxpd1haTG15NnF3cGdneGhPUkYvQmJVdjhERGFwRGx5OFlraTZxZ3VqeEpPcTZaUUtXc3ZCamJFNDhURUhYWnAwL0djdW9JY2tJemRQSzExZk5CYmZ3NkdaMVpWRDRpZmVLdS9va2IramM0bEl6UVQ5MjVWaUJJUkpxcnB6TUNPVW1ET2YvMVZGaXlvckx6K2U4dy8iLCJtYWMiOiI4YWVkMDFhYWUzOWQzMGEzOGI0ZGRjN2U1ODgyMmE2Y2EyYTg3YzFmZjNjNjA0NTlhMDVjNDE2MTQ0NDJlZTYwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:36:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJGRm1qRXY4c2lqZG1WK2d6TWdCdFE9PSIsInZhbHVlIjoiT3huK3dwSDM4MXRUdzJ1NEdvazRtVDd6MmV1a29VZUE0S0dkSE9qOWJFVXdBVElrVzcyMGFyeFNpSTN0RCt0Y3lKOUVNY3JaVFhvazg1TVM3VDQ3Zndmb1NzcnZOZVdTZGVxLzF3ZitIa2p4UnZBeWVzaFovOVF3MjdXNmhsVXdGdHhJN3AyT2kwTk9oQmFya2E5VHFubEU1aWlVYk9seko4SXUyZXBPaS9ySHhMcVhHcFpKVUgwT3RVZENNTmR1b1VsRlNyTWY0UkJkNWN5d2FKcFhrVHNQZVR6ZGcyZ1B4U3I2SjlOWUpBQWRCamhRREFLQzZZQXQ2TjE5RlZoYjlLWjJoZW40QmNDRE1Tb0JwLy9aWVVDZExFbXUyeXNpdktSbHJ5Z1FzMG1nRkc1b2ZmNldWa1o4QWd3alZiUGpmMVFTTThtTzZSRmxyK0dzQzZzcjM3STF6Y1VWNEtMRjJIWmxXZEhlWXFEcDVnYlI1RkRremNrRDhkTGtXdHMxVHhFM2YrUnN1NCtWellMMVhEdTQyODRVZ1N6bkZUdHA5ekxJWU8rUjh5V0pDTXFtd0FHRGU4a05BK1dpSHZzdCt1RXVpMHNodFRWUmRyWllpNkxNbUNVM1krR3QwTVNMR25MSHY1eUpOSWdNUmJlRjIvQVVjeGtJVkhBTkFUSlIiLCJtYWMiOiJiMWRjMmRhYjEyOTVkOGE1MzIxNzU5YzM2ZWFmNDc3ODRmNGVmOTYwNzhjMmZlZDVkOTBjYjI5MTYxODAwOTk5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:36:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734300129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1212882554 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XXYoNuef905JsvTNR1VtQrzp8P4cjVq3trtiJJhU</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212882554\", {\"maxDepth\":0})</script>\n"}}