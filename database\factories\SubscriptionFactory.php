<?php

namespace Database\Factories;

use App\Models\Subscription;
use App\Models\Customer;
use App\Models\ProductService;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    public function definition()
    {
        $productPrice = $this->faker->randomFloat(2, 100, 5000);
        $downPayment = $this->faker->randomFloat(2, 0, $productPrice * 0.3);
        $discountAmount = $this->faker->randomFloat(2, 0, $productPrice * 0.1);
        $totalEmis = $this->faker->numberBetween(6, 24);
        $remainingAmount = $productPrice - $downPayment - $discountAmount;
        $emiAmount = $remainingAmount / $totalEmis;
        
        return [
            'subscription_id' => 'SUB-' . date('Y') . '-' . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT),
            'customer_id' => Customer::factory(),
            'customer_name' => $this->faker->name,
            'customer_email' => $this->faker->email,
            'customer_phone' => $this->faker->phoneNumber,
            'product_id' => ProductService::factory(),
            'product_name' => $this->faker->words(3, true),
            'product_price' => $productPrice,
            'down_payment' => $downPayment,
            'paid_amount' => $downPayment,
            'pending_amount' => $remainingAmount,
            'discount_amount' => $discountAmount,
            'status' => $this->faker->randomElement(['active', 'cancelled', 'paused', 'expired']),
            'next_emi_date' => $this->faker->dateTimeBetween('now', '+3 months'),
            'start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'end_date' => $this->faker->optional()->dateTimeBetween('+6 months', '+2 years'),
            'emi_count' => $this->faker->numberBetween(0, $totalEmis),
            'total_emis' => $totalEmis,
            'emi_amount' => $emiAmount,
            'billing_cycle' => $this->faker->randomElement(['monthly', 'quarterly', 'yearly']),
            'notes' => $this->faker->optional()->paragraph,
            'receipt_url' => $this->faker->optional()->url,
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the subscription is active.
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
            ];
        });
    }

    /**
     * Indicate that the subscription is cancelled.
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'cancelled',
            ];
        });
    }

    /**
     * Indicate that the subscription is paused.
     */
    public function paused()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'paused',
            ];
        });
    }

    /**
     * Indicate that the subscription is expired.
     */
    public function expired()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'expired',
            ];
        });
    }

    /**
     * Indicate that the subscription is overdue.
     */
    public function overdue()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'next_emi_date' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
            ];
        });
    }
}
