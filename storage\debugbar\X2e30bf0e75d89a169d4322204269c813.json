{"__meta": {"id": "X2e30bf0e75d89a169d4322204269c813", "datetime": "2025-07-30 08:01:27", "utime": **********.866657, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862486.625727, "end": **********.86669, "duration": 1.2409629821777344, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1753862486.625727, "relative_start": 0, "end": **********.784833, "relative_end": **********.784833, "duration": 1.1591060161590576, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.78485, "relative_start": 1.****************, "end": **********.866694, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "81.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JlqyX9JJ3a9nAJMfh8t0glxEdhEocgoItu1hRjAt", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1212757201 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1212757201\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1240460695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1240460695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1016584219 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1016584219\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-982648054 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982648054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1842681956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1842681956\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:01:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imh1dUhtOGZUNVpoR2hJU2E3ZkVhVEE9PSIsInZhbHVlIjoiSXJ2cElSdEppUksvZStFTUNEKzRnbFRRaEwvd1RGVjdsQnVnVGVlWWZUWHhXTC9oamY0WEJVSi92QjI5WVFiSlNWMTVOb202UUJwVTcxeUlqTkR1WTk4NzB4aTdORm81UkZWOEtKVnZQVVUzenhpYmprSFVUem1HTHRKTDFES04xaStIY2FBK2hPVk4vSzFtMzgxUVVHcmJ6TlZFYng3RjB4QXFPTnFubWxxMWZtTDVpeVRvMkZiU1JETVFwY2gyZk5SR3ZwNG9SUjJBZTNQS3IxUXJmNXdpbEVYa2lmbFZjWUZHV1pEc2d2b3VJY3BOeDMwSW9rQzdRNmNZaFNla3dHTkVqK2pWako2QTFxZ2ZSWVhYT2xVcWNiU05UYVdGSTdzbXNMT3pFOWNYS2piSi94YWxZcmxPTndOUnIvcU5sdFNadjR5S1p4MnkyL25RTFNodmdOaTF5bFhlWlJOZEM2anFqd0YvaVNkSnFJamdMbHA2ZHEzUDlGbVo3elc1cUJpcHNwZkpRU1hUaTJxQWxKc3ZqZ0Jyb2JIbzNFS0ZmRXZiYlZyaVA2YzR6VzAwSEVkeXYrTFl2R2VMUVlVMEdaR1oyUUF5d0Zyd28ra1JOSnM1T0piRHF1eTdCWlBpY3FJZkVqYXNjRnJPS3ZoSW1KbmprSU05WkFnR3hkNWoiLCJtYWMiOiJiMWM1NmQ5OWQwZTVmYTM1MjVlMmVlOWJjMDRkYmU0MWU3NzI2OTZhMDcxYjZkODJlOTRhNWVmOWY0MjQ1YWZhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ild2UXhOVmlTM0RUOE1pcVVRTm82VXc9PSIsInZhbHVlIjoiTGVkRXZCM3cwNUEyNklpMHBibTZMdk94TTBITzU3Vm9DZG5uTm1YWldFTGJ1bVJWZ2E5aU1KRWcrMTRmMzZMSHJhT01rSFRtR3hVMkVFV0xIOHhWS3BpVG9ENGNxdGNHU3VwVy9JN2N6dU5IMDIxSHBhdzM2bitXMnNlQWswdC9VK1BwMzdING5QTGZ6d1BiWXNsUjFlSGpsdThSTDdkcC8ybjBTNXJHbUl0SnAwaVVmTmZ2UVVBY1JLVVFvSHBrZ0JHMHRNbkVpVjg4RVVDVTRZYWRRTm9oWUV3VkxucnFYb25zUnA2d0ZzSit0VFduMVdndnBjWHdUMlZzQ1M0ZUpIZnFhbFdRL09OY1NMUy93dTZJeDhoN1hqTWVKeVdTTkFhMDh2cWlaTDI2TWcrK3pmNmJvZTV3TWZuRTRTc2FYTkVmM3N4S1czNUx4b0xLS3RJbGNBZWpCSWdRa2gvdmp0ZnZ6Ni9GeE9OTUlqU1NLNzVuL1djbXlsRytMZWxPOTRzRWZsZURaS1VJcHJFYnFHZGk5VldkbjRqYm1qY3FOMy9URHJKS21IK0JrR1U2NEJGaDFyK01nVk9kSHlQZ1ErdVRCQXpvODJWbjlTdFBpTmhzM21pV0VtVm51K3lGMURIOWRneDRqS0pick1rSFRiakZtdFUzNzFOTUUycTIiLCJtYWMiOiI4NjhiODBkNTdmZTBlMzNlMWQwMmEwMjEwOGU5YzA2MTBiNTdmMGYyY2FhMTM1OTc4NGQ3NjNmOGQxNDg4M2JhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imh1dUhtOGZUNVpoR2hJU2E3ZkVhVEE9PSIsInZhbHVlIjoiSXJ2cElSdEppUksvZStFTUNEKzRnbFRRaEwvd1RGVjdsQnVnVGVlWWZUWHhXTC9oamY0WEJVSi92QjI5WVFiSlNWMTVOb202UUJwVTcxeUlqTkR1WTk4NzB4aTdORm81UkZWOEtKVnZQVVUzenhpYmprSFVUem1HTHRKTDFES04xaStIY2FBK2hPVk4vSzFtMzgxUVVHcmJ6TlZFYng3RjB4QXFPTnFubWxxMWZtTDVpeVRvMkZiU1JETVFwY2gyZk5SR3ZwNG9SUjJBZTNQS3IxUXJmNXdpbEVYa2lmbFZjWUZHV1pEc2d2b3VJY3BOeDMwSW9rQzdRNmNZaFNla3dHTkVqK2pWako2QTFxZ2ZSWVhYT2xVcWNiU05UYVdGSTdzbXNMT3pFOWNYS2piSi94YWxZcmxPTndOUnIvcU5sdFNadjR5S1p4MnkyL25RTFNodmdOaTF5bFhlWlJOZEM2anFqd0YvaVNkSnFJamdMbHA2ZHEzUDlGbVo3elc1cUJpcHNwZkpRU1hUaTJxQWxKc3ZqZ0Jyb2JIbzNFS0ZmRXZiYlZyaVA2YzR6VzAwSEVkeXYrTFl2R2VMUVlVMEdaR1oyUUF5d0Zyd28ra1JOSnM1T0piRHF1eTdCWlBpY3FJZkVqYXNjRnJPS3ZoSW1KbmprSU05WkFnR3hkNWoiLCJtYWMiOiJiMWM1NmQ5OWQwZTVmYTM1MjVlMmVlOWJjMDRkYmU0MWU3NzI2OTZhMDcxYjZkODJlOTRhNWVmOWY0MjQ1YWZhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ild2UXhOVmlTM0RUOE1pcVVRTm82VXc9PSIsInZhbHVlIjoiTGVkRXZCM3cwNUEyNklpMHBibTZMdk94TTBITzU3Vm9DZG5uTm1YWldFTGJ1bVJWZ2E5aU1KRWcrMTRmMzZMSHJhT01rSFRtR3hVMkVFV0xIOHhWS3BpVG9ENGNxdGNHU3VwVy9JN2N6dU5IMDIxSHBhdzM2bitXMnNlQWswdC9VK1BwMzdING5QTGZ6d1BiWXNsUjFlSGpsdThSTDdkcC8ybjBTNXJHbUl0SnAwaVVmTmZ2UVVBY1JLVVFvSHBrZ0JHMHRNbkVpVjg4RVVDVTRZYWRRTm9oWUV3VkxucnFYb25zUnA2d0ZzSit0VFduMVdndnBjWHdUMlZzQ1M0ZUpIZnFhbFdRL09OY1NMUy93dTZJeDhoN1hqTWVKeVdTTkFhMDh2cWlaTDI2TWcrK3pmNmJvZTV3TWZuRTRTc2FYTkVmM3N4S1czNUx4b0xLS3RJbGNBZWpCSWdRa2gvdmp0ZnZ6Ni9GeE9OTUlqU1NLNzVuL1djbXlsRytMZWxPOTRzRWZsZURaS1VJcHJFYnFHZGk5VldkbjRqYm1qY3FOMy9URHJKS21IK0JrR1U2NEJGaDFyK01nVk9kSHlQZ1ErdVRCQXpvODJWbjlTdFBpTmhzM21pV0VtVm51K3lGMURIOWRneDRqS0pick1rSFRiakZtdFUzNzFOTUUycTIiLCJtYWMiOiI4NjhiODBkNTdmZTBlMzNlMWQwMmEwMjEwOGU5YzA2MTBiNTdmMGYyY2FhMTM1OTc4NGQ3NjNmOGQxNDg4M2JhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-573969823 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JlqyX9JJ3a9nAJMfh8t0glxEdhEocgoItu1hRjAt</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573969823\", {\"maxDepth\":0})</script>\n"}}