{"__meta": {"id": "X3d5a9bc641c06e93ea42b6810f2a579b", "datetime": "2025-07-30 06:05:01", "utime": **********.851245, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855500.731133, "end": **********.85129, "duration": 1.12015700340271, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753855500.731133, "relative_start": 0, "end": **********.772812, "relative_end": **********.772812, "duration": 1.0416789054870605, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.77283, "relative_start": 1.****************, "end": **********.851294, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "78.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7MOaqimYtkL5vQ3HzoXMW0mX11f48rNjFAis45Br", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1403833877 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1403833877\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-609932844 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-609932844\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2049275828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2049275828\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1518359281 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518359281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2124000003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2124000003\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1076084095 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9iWFRGT0VZaXhvQi9mVjVNTG5oT0E9PSIsInZhbHVlIjoiQThSSVpsKzIrVWM3VldCc1FadG9zY1pkUEZMSURNN3RnNWdON1dwRnNGMDk2YndLV1J4VmN6RTZNU001ak9HSGJGRXhTemRpZnltSkZkSWtFS2lndnB1clJDcVYvaGY1cDlzSHlFK0NjRFVRNXlrck1mOXpTTzR6OVVnZUZQUGtBSjB2Yk1GK1pWSGt2NGcwbUFUV1hJSjAxSHp4b0J6UGlNNE52SmlKMDVtU0twdjU2bWJJWG9VZzhlN21yYitsL3JkR1BGMXphUy9zUU1nc3IyQVFWZG5yeDRVaE84MURlaDZsVXJlejhhWCtDZ2V5UjRMRlQzVmpKazhIRGFOYzhEZ0wxZmZVYWpDZFl1MTNIdWdwU2k2Vlg5cjVpYlFQc01KR2N5WXRpa3llMkowR1kvaWlsU3Z1eFBzVVAwZDVEajZDMEMwN3BhQUhwajBoRU1NbFdRNWxsUzFVc0ZnK1lCMWtZLy9NRW1rM1Q1MUczdTJSVHppdENYRGVoR3BIeFE1NUNYcno5c3ZheEZKdFdJalRlWGdiQ0pQWEhOTGdML3libG9IV3hWTmszRUtCdHZJN25ya3NJZW5jRENHYnlOSExGbjR1NXhVV0dXak5WQ3MwNFVGdkdUekFDWGkxcThOZ2gxTWpBb3dYWW9EMlN6QmlnQm9iV01YdkZzL04iLCJtYWMiOiJmMDkxYjA2NmM2ZDQ0YTcxNWI3MTljYjhiMDc5ZTc0YTRjOGYzOGFlYmUxYWZmOWUwYjk2NDhkNmFlZGRjNjYzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkplZkpwdGpWZ2x3NEpqcmwyblJtMUE9PSIsInZhbHVlIjoiT1lHMVg1QTl5SFd1TFl0aEg0THFNZkZ6V2dlTkljN2pIbDFBNDlBWHRlN0FEa3M4N2hITTI2emtrRnVsb0NUMTJxWXJMY3J5U1FXa3RwSVlUZTlIQTR0V3FSdWZXeS9XT3AvYkpFbnpFREZkcDA0Ri9oTmhYNmhyOGlmT3BBKytFdGRJOHVQOC9wYmlISGVXUTd0V2c2UGNCZCtORDQ1OC9CbmVMY3I0TVZtZUF1NlpJVEc2T1AzSklDU1VBNmNQakZRaEgrZWVpc1RlT280UEs1VE1OUmtCQWxXNlVLRHBDNXZRdkRwRk4wQkp6aHYwQTc0NlZHWEhMZVJ4ZFJzSS9yRVZNLy9iMkt0WjVHb1RCZE5IWGtnemFhRUdRc0YvL2VUK3lqNGRGZjB6QkJVVDlzcTQvSU0wVWYzYmdkYzlTSWgybGpjRHRwMFovQjNKd0JMM1ZxSkU2a1h5c0ZxOVpYVlhacFZoU1ZpaU5GSkJ4clF4OUp5ZFUzR1lzOTZDN2lUOWFrUGR5M0xURmpXUG1TdkxmcVdwZ0x3Sk8wWFd1NVVIcTBjcGJpRVJsdjJlZys4VFlYa2lNQWNGR0RtVVBBbHFsM0cxcUs4UENOMWU4Um5CajFsVVJNaDRIbWE5MXg5MXNNbnNiOHRMT1VMTU5SYWc1b2plZFNiQ2hDZUQiLCJtYWMiOiJhM2Q1MDQ5MGQ1NDAzMjc3ZGNkZWNjNWRiZDUxOGJhZjNhNzVjMGYxOTkxNjA3NWM3NDFiZTE3ZjMzNzIyZjY4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9iWFRGT0VZaXhvQi9mVjVNTG5oT0E9PSIsInZhbHVlIjoiQThSSVpsKzIrVWM3VldCc1FadG9zY1pkUEZMSURNN3RnNWdON1dwRnNGMDk2YndLV1J4VmN6RTZNU001ak9HSGJGRXhTemRpZnltSkZkSWtFS2lndnB1clJDcVYvaGY1cDlzSHlFK0NjRFVRNXlrck1mOXpTTzR6OVVnZUZQUGtBSjB2Yk1GK1pWSGt2NGcwbUFUV1hJSjAxSHp4b0J6UGlNNE52SmlKMDVtU0twdjU2bWJJWG9VZzhlN21yYitsL3JkR1BGMXphUy9zUU1nc3IyQVFWZG5yeDRVaE84MURlaDZsVXJlejhhWCtDZ2V5UjRMRlQzVmpKazhIRGFOYzhEZ0wxZmZVYWpDZFl1MTNIdWdwU2k2Vlg5cjVpYlFQc01KR2N5WXRpa3llMkowR1kvaWlsU3Z1eFBzVVAwZDVEajZDMEMwN3BhQUhwajBoRU1NbFdRNWxsUzFVc0ZnK1lCMWtZLy9NRW1rM1Q1MUczdTJSVHppdENYRGVoR3BIeFE1NUNYcno5c3ZheEZKdFdJalRlWGdiQ0pQWEhOTGdML3libG9IV3hWTmszRUtCdHZJN25ya3NJZW5jRENHYnlOSExGbjR1NXhVV0dXak5WQ3MwNFVGdkdUekFDWGkxcThOZ2gxTWpBb3dYWW9EMlN6QmlnQm9iV01YdkZzL04iLCJtYWMiOiJmMDkxYjA2NmM2ZDQ0YTcxNWI3MTljYjhiMDc5ZTc0YTRjOGYzOGFlYmUxYWZmOWUwYjk2NDhkNmFlZGRjNjYzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkplZkpwdGpWZ2x3NEpqcmwyblJtMUE9PSIsInZhbHVlIjoiT1lHMVg1QTl5SFd1TFl0aEg0THFNZkZ6V2dlTkljN2pIbDFBNDlBWHRlN0FEa3M4N2hITTI2emtrRnVsb0NUMTJxWXJMY3J5U1FXa3RwSVlUZTlIQTR0V3FSdWZXeS9XT3AvYkpFbnpFREZkcDA0Ri9oTmhYNmhyOGlmT3BBKytFdGRJOHVQOC9wYmlISGVXUTd0V2c2UGNCZCtORDQ1OC9CbmVMY3I0TVZtZUF1NlpJVEc2T1AzSklDU1VBNmNQakZRaEgrZWVpc1RlT280UEs1VE1OUmtCQWxXNlVLRHBDNXZRdkRwRk4wQkp6aHYwQTc0NlZHWEhMZVJ4ZFJzSS9yRVZNLy9iMkt0WjVHb1RCZE5IWGtnemFhRUdRc0YvL2VUK3lqNGRGZjB6QkJVVDlzcTQvSU0wVWYzYmdkYzlTSWgybGpjRHRwMFovQjNKd0JMM1ZxSkU2a1h5c0ZxOVpYVlhacFZoU1ZpaU5GSkJ4clF4OUp5ZFUzR1lzOTZDN2lUOWFrUGR5M0xURmpXUG1TdkxmcVdwZ0x3Sk8wWFd1NVVIcTBjcGJpRVJsdjJlZys4VFlYa2lNQWNGR0RtVVBBbHFsM0cxcUs4UENOMWU4Um5CajFsVVJNaDRIbWE5MXg5MXNNbnNiOHRMT1VMTU5SYWc1b2plZFNiQ2hDZUQiLCJtYWMiOiJhM2Q1MDQ5MGQ1NDAzMjc3ZGNkZWNjNWRiZDUxOGJhZjNhNzVjMGYxOTkxNjA3NWM3NDFiZTE3ZjMzNzIyZjY4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076084095\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-547547997 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7MOaqimYtkL5vQ3HzoXMW0mX11f48rNjFAis45Br</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547547997\", {\"maxDepth\":0})</script>\n"}}