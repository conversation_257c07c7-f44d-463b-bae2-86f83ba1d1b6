{"__meta": {"id": "Xb9ca258b0a78dccf84db139c057b4e87", "datetime": "2025-07-30 08:06:24", "utime": **********.910537, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862782.059372, "end": **********.910712, "duration": 2.8513400554656982, "duration_str": "2.85s", "measures": [{"label": "Booting", "start": 1753862782.059372, "relative_start": 0, "end": **********.573044, "relative_end": **********.573044, "duration": 2.513672113418579, "duration_str": "2.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.57309, "relative_start": 2.****************, "end": **********.910724, "relative_end": 1.1920928955078125e-05, "duration": 0.****************, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CW270THh1TJgUpIVOBFvPZF6BzHihTymy5THeTmm", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1217959549 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1217959549\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1424947912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1424947912\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-885737528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-885737528\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1761646762 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761646762\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-337403535 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-337403535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2113432664 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:06:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFqSEQ0TlJEYVFHMHppa3pxdnV0RWc9PSIsInZhbHVlIjoiT0ZwZnBxaGlEcjFWZHhLbUZJVnU0a2ZkK25UK2NxYnhrYTQyT0tCWm9BMXZRckpZRDZDbGplRHZoTVVLVVhCcUVjUXFiNDNDVGFYSE83TUlOTE5ZWGZZWlc0dWRpclJLQWFWUCtUdUx4emp6eGtlZzVpY2VKd1k5Tnp3K2crWGh1dHBuTk5kUklybnR5WG9MQnIwMlJSSHhhcnljdkJJZ29ITkpodDhwQnRHYmNCVFRyYVRaYjJNWnJnODFzUWJRR1gzZlBBY1ZmdW9YL3lRdUZyTWJGeG8wcFI5YVgwQlZwTDAzbzlKd3dUUmRSOHR0ck1qNDNUb0g0dUZZUTBSa2wrN0FPYzg1bEhQWHhWSTluTWRRNmF0SkFiMzdhSEtmclRGb2hhdDRNVHRVRmdWWWI5WU14U0hOZHdvRmdpNjQxejNEVXA1Y0dIUjhZWThybXNURDVBZnZMVU9uZitqVmsySk16anI5K2x2NmFyWjFkN21DNmMvY1hNSDQ4TTRqa1N5MC9TUzQrTzA2RytFNkN0VWhvMC91YUlVOTkxdXBMR2xRVEJsZk5PTmY0czRsc0ZpNDQ1NkpXczFKbk1HZEpJYU96ZHYvNkI5QXJWb1VKUVg1bTl0YUpnYTJoK2lXUXNEZ0laTXM5WDdjNzVpREt1SkpPUjVaNjlsYTd6N2UiLCJtYWMiOiJjNzE4OTc5MmFhNWM2ODNlNzc5YTk1ZmJhOTUzNGQ2ZDFlOGNhODEzMzc4ZGVkNzEzZTY5NTg1YzM5ZWY0ZDBkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:06:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imp1QWJOSXlMVmpsdDkzbzJiN0JMMEE9PSIsInZhbHVlIjoieU5QUUgxU0Mybjc2YUFTMkpDNkZ3WEpsSFE4b2xtY3dESnpSM1JVRFNaT1I1eGlZQ0FuZ0lyd09aMlc4QmR0bGl6RmpuZnU5Ly9XTStzRGdNMUt1NE5tWkxzME5Sa0VQY1F3VFhBRFM2anFERDdkRmIvckxwUDhxSUF2blJqbGRockd0bENWSkFDTU91blp2bHBIWkhlcGNnUUNQYm8raCtVQStCQWpkYS9rUWphNVYrajJJSkx1aS85cmVxRllaekdPcXFzSmViam9Ud085aHlpb1VmQWpBOVVxZzdjUUZXWGNRaGNOeHBpRlBNVzBSYy9kalFZbWU3YWJZZUVsc0EwSnRLamdwVXl0WjFMbkF6OEplcU5tR2tvYUd5MFpVMzVxVGdyU3B1K0ZTK2taZTc0ZWR4am5SajQxOTl4U0RMbnlRWmFVcVVId01RNVdpZ2lNeHUzNy80U0VHaERFNXljeDNQek4wRlZEaEZlVzk4YnFhaHhEaGtvZ0dHNFpib0VKb1JwUVA5TDY3SUtPUGJmMk9DMkpDdVVKV2x5eVFqVlp4YzRpbi9nSGQrZXBRTHl0NjdabDNIZzZvcEdjNU5DSi81bGV3bGhDT1R1ZytNNkRyeTFPVkpZSEs2N2dGNmdFRGptMTJmaWJBdXU5d0EzYk02QkZpREZKVE05S0ciLCJtYWMiOiIzMjMzMTZiZDcyODE4MzQwOTQ3NGNkM2IxMzljNmM4MWE5ZDZmZWYwOWJiYTM2MmRlNGViODc5MGM4Yzk5YTMxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:06:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFqSEQ0TlJEYVFHMHppa3pxdnV0RWc9PSIsInZhbHVlIjoiT0ZwZnBxaGlEcjFWZHhLbUZJVnU0a2ZkK25UK2NxYnhrYTQyT0tCWm9BMXZRckpZRDZDbGplRHZoTVVLVVhCcUVjUXFiNDNDVGFYSE83TUlOTE5ZWGZZWlc0dWRpclJLQWFWUCtUdUx4emp6eGtlZzVpY2VKd1k5Tnp3K2crWGh1dHBuTk5kUklybnR5WG9MQnIwMlJSSHhhcnljdkJJZ29ITkpodDhwQnRHYmNCVFRyYVRaYjJNWnJnODFzUWJRR1gzZlBBY1ZmdW9YL3lRdUZyTWJGeG8wcFI5YVgwQlZwTDAzbzlKd3dUUmRSOHR0ck1qNDNUb0g0dUZZUTBSa2wrN0FPYzg1bEhQWHhWSTluTWRRNmF0SkFiMzdhSEtmclRGb2hhdDRNVHRVRmdWWWI5WU14U0hOZHdvRmdpNjQxejNEVXA1Y0dIUjhZWThybXNURDVBZnZMVU9uZitqVmsySk16anI5K2x2NmFyWjFkN21DNmMvY1hNSDQ4TTRqa1N5MC9TUzQrTzA2RytFNkN0VWhvMC91YUlVOTkxdXBMR2xRVEJsZk5PTmY0czRsc0ZpNDQ1NkpXczFKbk1HZEpJYU96ZHYvNkI5QXJWb1VKUVg1bTl0YUpnYTJoK2lXUXNEZ0laTXM5WDdjNzVpREt1SkpPUjVaNjlsYTd6N2UiLCJtYWMiOiJjNzE4OTc5MmFhNWM2ODNlNzc5YTk1ZmJhOTUzNGQ2ZDFlOGNhODEzMzc4ZGVkNzEzZTY5NTg1YzM5ZWY0ZDBkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:06:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imp1QWJOSXlMVmpsdDkzbzJiN0JMMEE9PSIsInZhbHVlIjoieU5QUUgxU0Mybjc2YUFTMkpDNkZ3WEpsSFE4b2xtY3dESnpSM1JVRFNaT1I1eGlZQ0FuZ0lyd09aMlc4QmR0bGl6RmpuZnU5Ly9XTStzRGdNMUt1NE5tWkxzME5Sa0VQY1F3VFhBRFM2anFERDdkRmIvckxwUDhxSUF2blJqbGRockd0bENWSkFDTU91blp2bHBIWkhlcGNnUUNQYm8raCtVQStCQWpkYS9rUWphNVYrajJJSkx1aS85cmVxRllaekdPcXFzSmViam9Ud085aHlpb1VmQWpBOVVxZzdjUUZXWGNRaGNOeHBpRlBNVzBSYy9kalFZbWU3YWJZZUVsc0EwSnRLamdwVXl0WjFMbkF6OEplcU5tR2tvYUd5MFpVMzVxVGdyU3B1K0ZTK2taZTc0ZWR4am5SajQxOTl4U0RMbnlRWmFVcVVId01RNVdpZ2lNeHUzNy80U0VHaERFNXljeDNQek4wRlZEaEZlVzk4YnFhaHhEaGtvZ0dHNFpib0VKb1JwUVA5TDY3SUtPUGJmMk9DMkpDdVVKV2x5eVFqVlp4YzRpbi9nSGQrZXBRTHl0NjdabDNIZzZvcEdjNU5DSi81bGV3bGhDT1R1ZytNNkRyeTFPVkpZSEs2N2dGNmdFRGptMTJmaWJBdXU5d0EzYk02QkZpREZKVE05S0ciLCJtYWMiOiIzMjMzMTZiZDcyODE4MzQwOTQ3NGNkM2IxMzljNmM4MWE5ZDZmZWYwOWJiYTM2MmRlNGViODc5MGM4Yzk5YTMxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:06:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113432664\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1623768869 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CW270THh1TJgUpIVOBFvPZF6BzHihTymy5THeTmm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623768869\", {\"maxDepth\":0})</script>\n"}}