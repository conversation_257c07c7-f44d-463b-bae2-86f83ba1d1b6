{"__meta": {"id": "Xe8d3be2fb1e60c194470d94fd068a4bf", "datetime": "2025-07-30 08:07:41", "utime": **********.484869, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862859.466359, "end": **********.48493, "duration": 2.018571138381958, "duration_str": "2.02s", "measures": [{"label": "Booting", "start": 1753862859.466359, "relative_start": 0, "end": **********.33267, "relative_end": **********.33267, "duration": 1.8663110733032227, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332701, "relative_start": 1.****************, "end": **********.484935, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8LU5szC8jweJKeAblFjpodBjTP10SfCAU69qcRYs", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-8091887 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-8091887\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1677347491 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1677347491\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1335766650 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1335766650\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-348023348 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348023348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-588527190 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-588527190\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-644674666 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:07:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ildra3dLQW8yUHMrQUdMTkFtY1V3VGc9PSIsInZhbHVlIjoiaFg3Z3F3UEk1THEwdTRRbXEza2FNZGFlT2FYVUxNYTcyczdTQ0pPMnI2VzFOcU9ZeEMra0hTeGJjZW5HY3hkNUk4UHdyTnMrUWxPNVFtR2F3dklxMmtOYmFmM0YxMThHUTZFSkZrS0ErMFdEUlJPZGNzNVV4RTJTRmUxRHo1UlA3SkNTRVVncHBIRlZmOE9JcmlwdDR5enRIM1pRUlVkK0hnYW9pR3hXMkU5R2hzVGtSVitmMmFKZW8wNVpTalVocmwwejNGMzl1emZjSWV5NktzeFg1UjlnVXhLby9Gd2VUTlJRZ0Q2WlhIVWc0SHMzQUVISnN4a01DNGszSE5VTWswMWh1WUtCTCt4UTNISHNkT3V3WlNlVFlUM3VqQmZCODluanJTMDBUenlFMVdiZHBHSWNzYUZzaE8vN3IyRnU4UjZoVXljT1ZaRDFoQmlJdnhneXBKY3hhVjc5SGtxK250Vlovam1vY2xVbjY1aXI2QzdGT1Fib0U1ZmlZcWZPMWI5eUJ4bVdxZHpuQ2Q3d1owOGpGQVpHZ2h6NmdKTldLZi9DY3NLc2RvMENiNGR5cjVaN3lMYnY5YitGRDVYZXNtSm5xUlJQaFlpdzVRV29KOGNDNS9wZ2dobWZqNU5nUzgvNGRSRy9mRmMzUGllL0ZCNWNsQkw2Sy9xRDVRTTEiLCJtYWMiOiJkODI3MTNhOWVlZTIzMjEyYWRiMTljNzVkZDdjZThhM2FhYjdkZWUzM2IwOWFlZTAwNDQ0YzZiMTdmNjY2MzRmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkJhZHJPRVg4YUQyR24xQkF6NER5WVE9PSIsInZhbHVlIjoiOWlFWGVuRmZKcHFEY1pJcDdBQWlMcDRORXp2cHBCQ3FYSXhycHZkV2xIYUd1UTd6MzRpbTA1dnFGcmlBVmhkb09yRDV4R2lHMFo4ZUVOSHZqK004VjdGMmhGN2RPaGZBb20yQTBvckxUbzlNbGZkLzQ1eEt3NnQ2UjBpWjhFLzg3N1dsdHp6ai9ocGhWUklNTnhBdkIvbGthbmpCN093V3I4UzN0dm5aRWJ6VmlpcWxEOFUyVXBPWU05R3ptSGYyNmF2TTNBdXBQSkV6TCtBSGlyU2cxRWVaeitCcW44Y0JNdFVsci9JUkVVcWpTNHVRMVYwUW9MMTFZMTkzdldIeWEvbU5ISHlScHhiUXNic2JRY2VtdWFJTjhFNm1RelJ6cnRMb0NKdHIxb21na2Q1alc5MTZiaTRFMkFmQlQ2Nnd4ejZYZnJ2YXUvbDk3YXhTMGcrczJMQkp5Q3AvMkhMZW9PdVpnUGdDODNpNlVNRHR2TEdtS3BzQnpPM2kzOVBiVE1iTVUrUDJrUXNCLzg5WlBjd2FiTU5jT2QzUTlNaVMyTjF6V1d6M0FGVklOOFdxSTBnQVZjVjhZODYrRFJBc016OVNFUTBweVJSZ0kyUDFaUmtvbXdUVERvZ0wrZ2hZWUVHS2tFOHRGcUFvb3o1NVBaNjhBa2VJeUsxaGR0S0YiLCJtYWMiOiI2Yjk0YjMxYWI2YTExNzcwODAxNzQyM2E3YmYyOTg3ODA2MzBkMGE5NjA3YzA1MGVhZmUxMmZiYmU3ZDRlODczIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ildra3dLQW8yUHMrQUdMTkFtY1V3VGc9PSIsInZhbHVlIjoiaFg3Z3F3UEk1THEwdTRRbXEza2FNZGFlT2FYVUxNYTcyczdTQ0pPMnI2VzFOcU9ZeEMra0hTeGJjZW5HY3hkNUk4UHdyTnMrUWxPNVFtR2F3dklxMmtOYmFmM0YxMThHUTZFSkZrS0ErMFdEUlJPZGNzNVV4RTJTRmUxRHo1UlA3SkNTRVVncHBIRlZmOE9JcmlwdDR5enRIM1pRUlVkK0hnYW9pR3hXMkU5R2hzVGtSVitmMmFKZW8wNVpTalVocmwwejNGMzl1emZjSWV5NktzeFg1UjlnVXhLby9Gd2VUTlJRZ0Q2WlhIVWc0SHMzQUVISnN4a01DNGszSE5VTWswMWh1WUtCTCt4UTNISHNkT3V3WlNlVFlUM3VqQmZCODluanJTMDBUenlFMVdiZHBHSWNzYUZzaE8vN3IyRnU4UjZoVXljT1ZaRDFoQmlJdnhneXBKY3hhVjc5SGtxK250Vlovam1vY2xVbjY1aXI2QzdGT1Fib0U1ZmlZcWZPMWI5eUJ4bVdxZHpuQ2Q3d1owOGpGQVpHZ2h6NmdKTldLZi9DY3NLc2RvMENiNGR5cjVaN3lMYnY5YitGRDVYZXNtSm5xUlJQaFlpdzVRV29KOGNDNS9wZ2dobWZqNU5nUzgvNGRSRy9mRmMzUGllL0ZCNWNsQkw2Sy9xRDVRTTEiLCJtYWMiOiJkODI3MTNhOWVlZTIzMjEyYWRiMTljNzVkZDdjZThhM2FhYjdkZWUzM2IwOWFlZTAwNDQ0YzZiMTdmNjY2MzRmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkJhZHJPRVg4YUQyR24xQkF6NER5WVE9PSIsInZhbHVlIjoiOWlFWGVuRmZKcHFEY1pJcDdBQWlMcDRORXp2cHBCQ3FYSXhycHZkV2xIYUd1UTd6MzRpbTA1dnFGcmlBVmhkb09yRDV4R2lHMFo4ZUVOSHZqK004VjdGMmhGN2RPaGZBb20yQTBvckxUbzlNbGZkLzQ1eEt3NnQ2UjBpWjhFLzg3N1dsdHp6ai9ocGhWUklNTnhBdkIvbGthbmpCN093V3I4UzN0dm5aRWJ6VmlpcWxEOFUyVXBPWU05R3ptSGYyNmF2TTNBdXBQSkV6TCtBSGlyU2cxRWVaeitCcW44Y0JNdFVsci9JUkVVcWpTNHVRMVYwUW9MMTFZMTkzdldIeWEvbU5ISHlScHhiUXNic2JRY2VtdWFJTjhFNm1RelJ6cnRMb0NKdHIxb21na2Q1alc5MTZiaTRFMkFmQlQ2Nnd4ejZYZnJ2YXUvbDk3YXhTMGcrczJMQkp5Q3AvMkhMZW9PdVpnUGdDODNpNlVNRHR2TEdtS3BzQnpPM2kzOVBiVE1iTVUrUDJrUXNCLzg5WlBjd2FiTU5jT2QzUTlNaVMyTjF6V1d6M0FGVklOOFdxSTBnQVZjVjhZODYrRFJBc016OVNFUTBweVJSZ0kyUDFaUmtvbXdUVERvZ0wrZ2hZWUVHS2tFOHRGcUFvb3o1NVBaNjhBa2VJeUsxaGR0S0YiLCJtYWMiOiI2Yjk0YjMxYWI2YTExNzcwODAxNzQyM2E3YmYyOTg3ODA2MzBkMGE5NjA3YzA1MGVhZmUxMmZiYmU3ZDRlODczIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644674666\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2104867777 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8LU5szC8jweJKeAblFjpodBjTP10SfCAU69qcRYs</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104867777\", {\"maxDepth\":0})</script>\n"}}