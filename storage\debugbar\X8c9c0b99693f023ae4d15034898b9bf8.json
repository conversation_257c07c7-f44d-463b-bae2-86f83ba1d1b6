{"__meta": {"id": "X8c9c0b99693f023ae4d15034898b9bf8", "datetime": "2025-07-30 07:40:27", "utime": **********.748518, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753861226.053277, "end": **********.748584, "duration": 1.6953070163726807, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1753861226.053277, "relative_start": 0, "end": **********.634222, "relative_end": **********.634222, "duration": 1.5809450149536133, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634249, "relative_start": 1.****************, "end": **********.748596, "relative_end": 1.1920928955078125e-05, "duration": 0.*****************, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "s22TCDveu3zXHRqSWvHze0y6zKc0HQP2pncMuNbw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1470191947 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1470191947\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-658125698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-658125698\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-984658138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-984658138\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1781600351 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781600351\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2031641298 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2031641298\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-352320201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:40:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNRaUFmMUJZNlJsaGNzN0F3Um1KRFE9PSIsInZhbHVlIjoiZ0R3bGxDalByMnBQUFFOcW1ic0czREZZKzR3OHRjM0tRVTVyMnJwZHhkSWsvckNKS29MbVdDWExxaS9za2JWd1lqb0tBYjlnTXRWZlpDM1dIRTAzdW55ZjhxMTliMFZpWVQrbHhnN1A4cUVQMVRiSldISjZxanlYR3Rjc3RCSDZBTFAyVHQ5cjA1VXVTSit5Y1BleWJDUE1LbGgrNDVsRlpXd3Z2Z0RKL3JhZEdibkhrNTU1UGRLRXJhWkNqeDJ5MWJTSnJWUTlmeDRKd0tzbEI4M0ZwUzRXR2JvN1UrK1hKUnhta2tXN2xrcys1dk1zNm1RRU5FaCtRZ0JmaTFoWGQ2a3h5KytrWjZJM0h0Yk9mMGUzVjVNSVhkQXBTRHNWbnVyQytGb25HaEY2NzZ5QU1RdTJ6bGNZbDFRWUtESlpVZGlxMWdSeFpnbGtBWDZ1bEtWRjJ2UlZSSGcwWUhXbDI4R01RWmdRTFM2Vy9xTzR5Ukh6NEVHcURPME1xWnBuTHNrVEUxZW1hNGh0UHRsOENacVhRVVkzM0thR0pGRCtheDhkUXQ5V2o0R3FWeEVsVXorclVlOHpIQklZY2I4dlhKcUNSKzFpL1RzbSswOU5zVHBhbDJQZWJMeFVSQWZGck1jRHpLT3hTRHV5VDFaV000aDFHUGxRYkJ6cjZ6WE4iLCJtYWMiOiJkNzkxODRhOWE3MjE1OTQxMjA5MTdkNThjNzRiZDg5NTUzMTU4NmU2YmFjOWZhOWM5ZmJlN2YzY2VjNzZlMzAyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:40:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikl4Zk1hYlpESWxXbWR0YzJCbWpZMVE9PSIsInZhbHVlIjoiRGJ4SGJSeGxrTmpKQS9lMDBSeW5maXE3d1krOCtzVytVRTVoUUx5UnQxSW5xTm5CK1ArYklPV1R2NFVYZmJFbnNkbDAxRDJmNnp4aWNPVjNmUHkxWWN5UFVxVUNvU0dPUHhkVWdJUUk3Vjkvd0pBUS8yUEZ1TWtlMnMrRTZuN3NqMm5oMkxoM0lsaDd6WjFyK0g0NUcxci9tKzFmYS80YzFxbW80V3FVWUxVcG5mNHcramE4TkQ0QnFkR3l6NlZDYWZudmFkdGM5SjRrWi9uUUNsTSthdCs0QmwrODhsRzIvMXcxbUtpR2k4TDZmMkQ1ZGpSUDR2TW5PSEZhdzRvd3RpZVZTemsvZENsck00UTdHdTExRlp0bkxPWm5sSlVWYURyYm1HNjdTaVVhc3dIZ2RIN2QxdmQ3YjhvRVNoY1RrZUdFRnVyMGJaa2ZTWG14czR5eUl6SmdvbUk4WEJmQVVXVVg5U0NXeUxkNmJwaG54UWcwbjE4ZkFNeFRRV0Z2MzFsTlBPUUxWK1Z2NmtVWno1djVPWVRCWlNQcE10SnRwMTB0bkcyREREV1pHc3BneFlPUTRCdEJBajM5bFpCblRwWVVWN3REQUsybnl1MGk1TTNhZ0ZNL3NBUFQrNkF5Y2FLSlhSL1NDUTJZN2l6WTNTUUJNeTNuUGxvSERma1kiLCJtYWMiOiI5ZTM5MjhmMDIyOGM2ZDk5MWI3M2RiMDFmMWVmNTBkODA1YjYzNDRjYmFiODNjMjJlNjEzNjhlZWRlYTk0YWVhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:40:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNRaUFmMUJZNlJsaGNzN0F3Um1KRFE9PSIsInZhbHVlIjoiZ0R3bGxDalByMnBQUFFOcW1ic0czREZZKzR3OHRjM0tRVTVyMnJwZHhkSWsvckNKS29MbVdDWExxaS9za2JWd1lqb0tBYjlnTXRWZlpDM1dIRTAzdW55ZjhxMTliMFZpWVQrbHhnN1A4cUVQMVRiSldISjZxanlYR3Rjc3RCSDZBTFAyVHQ5cjA1VXVTSit5Y1BleWJDUE1LbGgrNDVsRlpXd3Z2Z0RKL3JhZEdibkhrNTU1UGRLRXJhWkNqeDJ5MWJTSnJWUTlmeDRKd0tzbEI4M0ZwUzRXR2JvN1UrK1hKUnhta2tXN2xrcys1dk1zNm1RRU5FaCtRZ0JmaTFoWGQ2a3h5KytrWjZJM0h0Yk9mMGUzVjVNSVhkQXBTRHNWbnVyQytGb25HaEY2NzZ5QU1RdTJ6bGNZbDFRWUtESlpVZGlxMWdSeFpnbGtBWDZ1bEtWRjJ2UlZSSGcwWUhXbDI4R01RWmdRTFM2Vy9xTzR5Ukh6NEVHcURPME1xWnBuTHNrVEUxZW1hNGh0UHRsOENacVhRVVkzM0thR0pGRCtheDhkUXQ5V2o0R3FWeEVsVXorclVlOHpIQklZY2I4dlhKcUNSKzFpL1RzbSswOU5zVHBhbDJQZWJMeFVSQWZGck1jRHpLT3hTRHV5VDFaV000aDFHUGxRYkJ6cjZ6WE4iLCJtYWMiOiJkNzkxODRhOWE3MjE1OTQxMjA5MTdkNThjNzRiZDg5NTUzMTU4NmU2YmFjOWZhOWM5ZmJlN2YzY2VjNzZlMzAyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:40:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikl4Zk1hYlpESWxXbWR0YzJCbWpZMVE9PSIsInZhbHVlIjoiRGJ4SGJSeGxrTmpKQS9lMDBSeW5maXE3d1krOCtzVytVRTVoUUx5UnQxSW5xTm5CK1ArYklPV1R2NFVYZmJFbnNkbDAxRDJmNnp4aWNPVjNmUHkxWWN5UFVxVUNvU0dPUHhkVWdJUUk3Vjkvd0pBUS8yUEZ1TWtlMnMrRTZuN3NqMm5oMkxoM0lsaDd6WjFyK0g0NUcxci9tKzFmYS80YzFxbW80V3FVWUxVcG5mNHcramE4TkQ0QnFkR3l6NlZDYWZudmFkdGM5SjRrWi9uUUNsTSthdCs0QmwrODhsRzIvMXcxbUtpR2k4TDZmMkQ1ZGpSUDR2TW5PSEZhdzRvd3RpZVZTemsvZENsck00UTdHdTExRlp0bkxPWm5sSlVWYURyYm1HNjdTaVVhc3dIZ2RIN2QxdmQ3YjhvRVNoY1RrZUdFRnVyMGJaa2ZTWG14czR5eUl6SmdvbUk4WEJmQVVXVVg5U0NXeUxkNmJwaG54UWcwbjE4ZkFNeFRRV0Z2MzFsTlBPUUxWK1Z2NmtVWno1djVPWVRCWlNQcE10SnRwMTB0bkcyREREV1pHc3BneFlPUTRCdEJBajM5bFpCblRwWVVWN3REQUsybnl1MGk1TTNhZ0ZNL3NBUFQrNkF5Y2FLSlhSL1NDUTJZN2l6WTNTUUJNeTNuUGxvSERma1kiLCJtYWMiOiI5ZTM5MjhmMDIyOGM2ZDk5MWI3M2RiMDFmMWVmNTBkODA1YjYzNDRjYmFiODNjMjJlNjEzNjhlZWRlYTk0YWVhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:40:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352320201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1509131614 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s22TCDveu3zXHRqSWvHze0y6zKc0HQP2pncMuNbw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509131614\", {\"maxDepth\":0})</script>\n"}}