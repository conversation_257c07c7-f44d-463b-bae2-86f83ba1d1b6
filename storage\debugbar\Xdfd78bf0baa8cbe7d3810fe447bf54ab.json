{"__meta": {"id": "Xdfd78bf0baa8cbe7d3810fe447bf54ab", "datetime": "2025-07-30 07:10:15", "utime": **********.370711, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753859414.473671, "end": **********.370751, "duration": 0.8970799446105957, "duration_str": "897ms", "measures": [{"label": "Booting", "start": 1753859414.473671, "relative_start": 0, "end": **********.278995, "relative_end": **********.278995, "duration": 0.8053240776062012, "duration_str": "805ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.279035, "relative_start": 0.****************, "end": **********.370753, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "91.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2SIjJ3bmh4RVn6hGJkieHlmiiGzg9AUrmfFVUaTH", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1037194382 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1037194382\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1759247861 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1759247861\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1605359943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1605359943\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1827407498 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827407498\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-193195331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-193195331\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1066936700 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:10:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ild1TVFrWE8rU3JESUtkUzROWDAxU3c9PSIsInZhbHVlIjoiaFRlMC84dnFkelJNWkJwdjRZRjh5Q0diSzY1L0hMeitxcWplUzJ3R1oxZlIrMTdCVTRkWWhJNW1ZdWxWWE1rbzRlUzhjZEg4eGNBS3JGTlNpcWx1NUwwUGJzL1QrOUJ3b2dxMFNyZENPTGJIZGRwQ0J4S1R4Rm84YjJvaDN3TG9ZemFNamlDRzZLMGl2VnFCMVM0bStRaXFBWWJWcWpxRmROS2oyOXRubFNZRkpRb0ppN2htVVJ6TFBXUVBKY09UeG9WTmFDZFBQWjQ2dmZ6NTZCeUl6KzBqbjR3VGovTmhMWWN5VTM5M2ZoejV0S1NVdnBXSGlQcEk4Z0ZiS0ZRUm9RaWFOZHRPMUtnY3pqTEVmYkJUV0l1QnlPRWFGK2VFTVZFWGFaeFBFQTdiTmNXS0FJTlArNmxEbG4vTlVINjN6cDB2OHo3Y2hTUVlTNi9ZV0czakVBeElmclpSVFBQZDhsbFB2UVlSS015TVkwYVFYTmlXT0JCd3Q0QVJLWEd1ekpScm5xK214N2p1VkRoR21PUUI4ZktFVjY2bUx1Qm9tbnRXZlNnUEdSVDQvVVdoUmd0UTQyQllQN3d2bUJldnBCWVJWZXBGS0ZITFYvWTl5dmlVN2tKcWUycHlPVEYyRVZtOWpPQS9uOWhCaWgxZnVORFdZS1RqMWtvMnU5WHkiLCJtYWMiOiI1OTY2ZjJkYmRlNjk0NmIzMjM1MWZjZjFiNmE2MTRkYjUzM2M3MDA1MTZhMzViNjIxMzllYWVmYmY3ZjU5M2ZhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:10:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRXN2xIMDlRQ3M1azJBUUxraWpIQmc9PSIsInZhbHVlIjoiZHJoeDhEaTZRNkVPck5kamlZR3k1QTZvTkZJSFpTZ3ZpbUhEemdWS0xXdFBhdmMrQ29jVWJ5NUM4amR0ZlVNdVRPbEhFVkFFRjhIeTFhakxhNVdtRWxGTVlsQmd2NzdYYy9seVZVQ3JWcGlTL1ZNVkx5TFlVbHVxYTdKUXZQS1k0Q08rYWpiQzBHRldldDFGNUFBY1MzK0ZkV3JZL3J2NnpCeUtVK0pPZ1M4UFVUZDMyY1llRlpVK3g1MlVoZXNCcXNNVUlJM1RWMTJpNDZhR1VEa3hqbG9SdC9odDdjS3NBNXdEMHVKRVBNM1J0azFYUmFzNEJURVNaRUxwOStaSmJJTnpZdWd5UXJ1S3VTdU5tYy8wNkIwWHpGWkkvZnRSVnBpaWNaWXpFMllzU0pIWjJTYUpiZDZpYkJvaytKTkJrNnpGUFVCNTZPaUovVC9uQzAveWgrZ2dHS2FqMEliRDhDbStsaXExUDgvZzlrSXFlRjBUdlByRm1icUFBZ0NlNVhDU0tyOVZPSFp5d1B1YXY1NUhzLzlmM3VpYjQ1Zk85VGVlVDdjdDNteWxld3dzMjhmcjUvM0dkSUJTOWF4Z2ZaQThEVHp2OU1QR3BvRnpyRWp6Q2xFMnhwTy9MZHJZOC9QSzNEeWdXMDEzZHVkT3JZSjJyWUxNZ2FkL0hUcXAiLCJtYWMiOiI3NzVjODllMWM4NjE1ZjJmNjM2NGJjNzhmNTFlZmYxZmE1ODE3OTQwYTE3NzBmMWIzZGY0MmNiMWI0MjJjN2VhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:10:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ild1TVFrWE8rU3JESUtkUzROWDAxU3c9PSIsInZhbHVlIjoiaFRlMC84dnFkelJNWkJwdjRZRjh5Q0diSzY1L0hMeitxcWplUzJ3R1oxZlIrMTdCVTRkWWhJNW1ZdWxWWE1rbzRlUzhjZEg4eGNBS3JGTlNpcWx1NUwwUGJzL1QrOUJ3b2dxMFNyZENPTGJIZGRwQ0J4S1R4Rm84YjJvaDN3TG9ZemFNamlDRzZLMGl2VnFCMVM0bStRaXFBWWJWcWpxRmROS2oyOXRubFNZRkpRb0ppN2htVVJ6TFBXUVBKY09UeG9WTmFDZFBQWjQ2dmZ6NTZCeUl6KzBqbjR3VGovTmhMWWN5VTM5M2ZoejV0S1NVdnBXSGlQcEk4Z0ZiS0ZRUm9RaWFOZHRPMUtnY3pqTEVmYkJUV0l1QnlPRWFGK2VFTVZFWGFaeFBFQTdiTmNXS0FJTlArNmxEbG4vTlVINjN6cDB2OHo3Y2hTUVlTNi9ZV0czakVBeElmclpSVFBQZDhsbFB2UVlSS015TVkwYVFYTmlXT0JCd3Q0QVJLWEd1ekpScm5xK214N2p1VkRoR21PUUI4ZktFVjY2bUx1Qm9tbnRXZlNnUEdSVDQvVVdoUmd0UTQyQllQN3d2bUJldnBCWVJWZXBGS0ZITFYvWTl5dmlVN2tKcWUycHlPVEYyRVZtOWpPQS9uOWhCaWgxZnVORFdZS1RqMWtvMnU5WHkiLCJtYWMiOiI1OTY2ZjJkYmRlNjk0NmIzMjM1MWZjZjFiNmE2MTRkYjUzM2M3MDA1MTZhMzViNjIxMzllYWVmYmY3ZjU5M2ZhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:10:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRXN2xIMDlRQ3M1azJBUUxraWpIQmc9PSIsInZhbHVlIjoiZHJoeDhEaTZRNkVPck5kamlZR3k1QTZvTkZJSFpTZ3ZpbUhEemdWS0xXdFBhdmMrQ29jVWJ5NUM4amR0ZlVNdVRPbEhFVkFFRjhIeTFhakxhNVdtRWxGTVlsQmd2NzdYYy9seVZVQ3JWcGlTL1ZNVkx5TFlVbHVxYTdKUXZQS1k0Q08rYWpiQzBHRldldDFGNUFBY1MzK0ZkV3JZL3J2NnpCeUtVK0pPZ1M4UFVUZDMyY1llRlpVK3g1MlVoZXNCcXNNVUlJM1RWMTJpNDZhR1VEa3hqbG9SdC9odDdjS3NBNXdEMHVKRVBNM1J0azFYUmFzNEJURVNaRUxwOStaSmJJTnpZdWd5UXJ1S3VTdU5tYy8wNkIwWHpGWkkvZnRSVnBpaWNaWXpFMllzU0pIWjJTYUpiZDZpYkJvaytKTkJrNnpGUFVCNTZPaUovVC9uQzAveWgrZ2dHS2FqMEliRDhDbStsaXExUDgvZzlrSXFlRjBUdlByRm1icUFBZ0NlNVhDU0tyOVZPSFp5d1B1YXY1NUhzLzlmM3VpYjQ1Zk85VGVlVDdjdDNteWxld3dzMjhmcjUvM0dkSUJTOWF4Z2ZaQThEVHp2OU1QR3BvRnpyRWp6Q2xFMnhwTy9MZHJZOC9QSzNEeWdXMDEzZHVkT3JZSjJyWUxNZ2FkL0hUcXAiLCJtYWMiOiI3NzVjODllMWM4NjE1ZjJmNjM2NGJjNzhmNTFlZmYxZmE1ODE3OTQwYTE3NzBmMWIzZGY0MmNiMWI0MjJjN2VhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:10:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066936700\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747001289 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2SIjJ3bmh4RVn6hGJkieHlmiiGzg9AUrmfFVUaTH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747001289\", {\"maxDepth\":0})</script>\n"}}