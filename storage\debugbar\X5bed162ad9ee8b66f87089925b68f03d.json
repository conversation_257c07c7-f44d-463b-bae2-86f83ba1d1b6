{"__meta": {"id": "X5bed162ad9ee8b66f87089925b68f03d", "datetime": "2025-07-30 06:05:46", "utime": **********.075545, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855545.114976, "end": **********.075576, "duration": 0.9606001377105713, "duration_str": "961ms", "measures": [{"label": "Booting", "start": 1753855545.114976, "relative_start": 0, "end": 1753855545.992656, "relative_end": 1753855545.992656, "duration": 0.8776800632476807, "duration_str": "878ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753855545.992687, "relative_start": 0.****************, "end": **********.075579, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "82.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dgeYQ3RbgqBfpEf2qnfinvxngulHdHN9Kd3L3pPu", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-501220423 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-501220423\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1436177489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1436177489\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-615177369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-615177369\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-363625579 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363625579\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-169489285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-169489285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2044277511 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIwQ0RtRmFackJtdU1Rd2t0cmxTeVE9PSIsInZhbHVlIjoiWnpuak8vTDJsRlR3U1pnbHgwekFnWUdtKzZxYVN6L3hrbTAvSkpnU3hQcFRDQVZ4UW5KVmg4dHJWT2hPbVRjMmJERU5wQjZQLzA0NFc4KzM2eGxCRkpwMXlyaVpmSCs5cGpUZHpxTERJUEI4U0tPalpFMm5qMk1tdnArQ1lrVzJ5bFBPelVyMWpudW5yTlpueVhjSGdRZ3VKSWxvdnNUQWpoaGRCMnd0KzVwNW1EcDczQzdvWmFvV0c3eEk5c0VYUjF1YytISUZEMklpaXZOTm5uWVVpWHdyNktDa0w4SDNtNm9QOS9QUE94QVVEOWZiOGdNS1BDK3VxaUVFWE5nQXpPc3o2S0tzSjFKNEQrS2x6V1RrYmN1SVNwQ0JNZ3UwS0FVRUxYZkxqQUQyZFpiQmY2K3o1cWxRR0FSTWtURW51b2RUR0hOcTYvYnRQU1Z4WmMzYkcrZTFZK3hsdjNyRTI4QUFiZ2RremVYc1o0ZmZaZG15Rlg0M0JUeFd1OXNmL3ZPVjVPZUk4TE9INzFsSENJUE8rbDlCYnYwUzlHTXZNZEd5eUt1QU8ydVVwMXJXV3BXM3E3KzRpWDZ0TDVDSnk1UWNrMS9tcGtkL0JvK1g1YkJ2WlZ2TlRaNms5QTFjK3g1VzZVZG5neXcvOXdzR2NZSENpTElSZjRCdnRTSG4iLCJtYWMiOiI3NWZkYWVmMWZmYzcxYjdhY2ZiNGY3ZWE1NTg4NGI3NTdhZDgxZGYyMzAxZjM1YjhkNDY2NTBkNGVkODljNGM0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkU4Y1J4NDVBTkI4Y094NUNjeVFqRUE9PSIsInZhbHVlIjoieTk2TWNCcTh5Uk1hSDhxZUEzVzVLbXJqMHRKb3BoM3hZRGNyRlVsd3lHVi9rWFNyR1JPRjREbERVd1hlZGFjWW5xOFpKVWxraEZON3pUVXZkRklhcDUzRThObU9ELzdJa3c5UzJzRUppNmJKaEoyOVFNZ0JROVIvZkRGQ2RobnJOMzltNTg4b2RrYy90SEdvWFVVZHc1OG9PUW1zU1VSa3pTSW5hZzhiYTBqR0lZaVlHcWtybHZFMkRzZy9kMnYyWkl0djJ1UkswemMyUGxEZjFCZi9td0llcDN0RDJZci9ZU1RXY2crSWZXaitzMGdEa0lOWEdWYWVWZU9xSUYrVFNFU0FwVitRdmtXVVZ3cjcxTjgrN3haVzUxc0FVSXl5bGlDU1RKdG5KMllvSGFuTEVMUnJTYW01dXd2Nlc4TTVqSjNMMGkxcXQvN2t1UFBuZVFxQXUyQ0hYdTN1d1pkeWpRRWxtb1IyRjdQeG9kSXppNnZqckVlTnBkeFB3a25HeWVkdkpvMzZvTlVIc0d1bXdUN1MyRWYxVGhJS0xyMUFRbE9EUFRMRDh3bW1ubERxVU1JTjVPZWpwd0ZpVkhKby9icXBHdFprWXIvQm14ZmpVYVNjdzlCVDJUMXhvdmg4aGRKcHBuNmFEdXdjQkwrUjY5d1Z0UjU4azMwbTVIL3QiLCJtYWMiOiI2NWM0YzNiZjhlZWYzMzA0YWIzMmQyYzNmYmFmNzdhNjQ2MzgwYTM4M2E4ZjQwZjZkMjg4MDRlZGQ4NDc2YzUzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIwQ0RtRmFackJtdU1Rd2t0cmxTeVE9PSIsInZhbHVlIjoiWnpuak8vTDJsRlR3U1pnbHgwekFnWUdtKzZxYVN6L3hrbTAvSkpnU3hQcFRDQVZ4UW5KVmg4dHJWT2hPbVRjMmJERU5wQjZQLzA0NFc4KzM2eGxCRkpwMXlyaVpmSCs5cGpUZHpxTERJUEI4U0tPalpFMm5qMk1tdnArQ1lrVzJ5bFBPelVyMWpudW5yTlpueVhjSGdRZ3VKSWxvdnNUQWpoaGRCMnd0KzVwNW1EcDczQzdvWmFvV0c3eEk5c0VYUjF1YytISUZEMklpaXZOTm5uWVVpWHdyNktDa0w4SDNtNm9QOS9QUE94QVVEOWZiOGdNS1BDK3VxaUVFWE5nQXpPc3o2S0tzSjFKNEQrS2x6V1RrYmN1SVNwQ0JNZ3UwS0FVRUxYZkxqQUQyZFpiQmY2K3o1cWxRR0FSTWtURW51b2RUR0hOcTYvYnRQU1Z4WmMzYkcrZTFZK3hsdjNyRTI4QUFiZ2RremVYc1o0ZmZaZG15Rlg0M0JUeFd1OXNmL3ZPVjVPZUk4TE9INzFsSENJUE8rbDlCYnYwUzlHTXZNZEd5eUt1QU8ydVVwMXJXV3BXM3E3KzRpWDZ0TDVDSnk1UWNrMS9tcGtkL0JvK1g1YkJ2WlZ2TlRaNms5QTFjK3g1VzZVZG5neXcvOXdzR2NZSENpTElSZjRCdnRTSG4iLCJtYWMiOiI3NWZkYWVmMWZmYzcxYjdhY2ZiNGY3ZWE1NTg4NGI3NTdhZDgxZGYyMzAxZjM1YjhkNDY2NTBkNGVkODljNGM0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkU4Y1J4NDVBTkI4Y094NUNjeVFqRUE9PSIsInZhbHVlIjoieTk2TWNCcTh5Uk1hSDhxZUEzVzVLbXJqMHRKb3BoM3hZRGNyRlVsd3lHVi9rWFNyR1JPRjREbERVd1hlZGFjWW5xOFpKVWxraEZON3pUVXZkRklhcDUzRThObU9ELzdJa3c5UzJzRUppNmJKaEoyOVFNZ0JROVIvZkRGQ2RobnJOMzltNTg4b2RrYy90SEdvWFVVZHc1OG9PUW1zU1VSa3pTSW5hZzhiYTBqR0lZaVlHcWtybHZFMkRzZy9kMnYyWkl0djJ1UkswemMyUGxEZjFCZi9td0llcDN0RDJZci9ZU1RXY2crSWZXaitzMGdEa0lOWEdWYWVWZU9xSUYrVFNFU0FwVitRdmtXVVZ3cjcxTjgrN3haVzUxc0FVSXl5bGlDU1RKdG5KMllvSGFuTEVMUnJTYW01dXd2Nlc4TTVqSjNMMGkxcXQvN2t1UFBuZVFxQXUyQ0hYdTN1d1pkeWpRRWxtb1IyRjdQeG9kSXppNnZqckVlTnBkeFB3a25HeWVkdkpvMzZvTlVIc0d1bXdUN1MyRWYxVGhJS0xyMUFRbE9EUFRMRDh3bW1ubERxVU1JTjVPZWpwd0ZpVkhKby9icXBHdFprWXIvQm14ZmpVYVNjdzlCVDJUMXhvdmg4aGRKcHBuNmFEdXdjQkwrUjY5d1Z0UjU4azMwbTVIL3QiLCJtYWMiOiI2NWM0YzNiZjhlZWYzMzA0YWIzMmQyYzNmYmFmNzdhNjQ2MzgwYTM4M2E4ZjQwZjZkMjg4MDRlZGQ4NDc2YzUzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044277511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2013361219 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dgeYQ3RbgqBfpEf2qnfinvxngulHdHN9Kd3L3pPu</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013361219\", {\"maxDepth\":0})</script>\n"}}