{"__meta": {"id": "X9eeac4a309f9980f1ba1f0b284125867", "datetime": "2025-07-30 06:05:42", "utime": **********.667565, "method": "DELETE", "uri": "/contact-groups/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855541.822994, "end": **********.667589, "duration": 0.8445949554443359, "duration_str": "845ms", "measures": [{"label": "Booting", "start": 1753855541.822994, "relative_start": 0, "end": **********.564327, "relative_end": **********.564327, "duration": 0.7413330078125, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56434, "relative_start": 0.7413461208343506, "end": **********.667592, "relative_end": 3.0994415283203125e-06, "duration": 0.10325193405151367, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE contact-groups/{contact_group}", "middleware": "web, auth, XSS, revalidate", "as": "contact-groups.destroy", "controller": "App\\Http\\Controllers\\ContactGroupController@destroy", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=424\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:424-460</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.027659999999999997, "accumulated_duration_str": "27.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.606187, "duration": 0.016300000000000002, "duration_str": "16.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 58.93}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.6333418, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 58.93, "width_percent": 3.435}, {"sql": "select * from `contact_groups` where `id` = '1' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["1", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 429}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.63851, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:429", "source": "app/Http/Controllers/ContactGroupController.php:429", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=429", "ajax": false, "filename": "ContactGroupController.php", "line": "429"}, "connection": "radhe_same", "start_percent": 62.364, "width_percent": 2.495}, {"sql": "update `leads` set `contact_group_id` = '', `leads`.`updated_at` = '2025-07-30 06:05:42' where `contact_group_id` = '1'", "type": "query", "params": [], "bindings": ["", "2025-07-30 06:05:42", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 439}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.642781, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:439", "source": "app/Http/Controllers/ContactGroupController.php:439", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=439", "ajax": false, "filename": "ContactGroupController.php", "line": "439"}, "connection": "radhe_same", "start_percent": 64.859, "width_percent": 13.377}, {"sql": "delete from `contact_groups` where `id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 441}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.65023, "duration": 0.006019999999999999, "duration_str": "6.02ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:441", "source": "app/Http/Controllers/ContactGroupController.php:441", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=441", "ajax": false, "filename": "ContactGroupController.php", "line": "441"}, "connection": "radhe_same", "start_percent": 78.236, "width_percent": 21.764}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/contact-groups/1", "status_code": "<pre class=sf-dump id=sf-dump-136624381 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-136624381\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1393761812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1393761812\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1324230257 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324230257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1188311439 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjdMK0RmdmNlcExDTTU5M3FuVHhOM1E9PSIsInZhbHVlIjoiWXZlekY5WVp6Q3hraE0rRzZoNTdiN2lPZ1NlQWNhSm1FOUhhZnhsdUVZbDZpa2U3MTNjSjZRcDlaMHN6Umlkc2VibnF1Z2trc3AwWUptRDNsbUF6NWl0Y2gxcmZDMjRlVHNiUTNaY2hWUjRrZE9VN29mNG1jWENKWE9KNnNQa2FkK3lrTkJwTGovZWhFYjNwOEplc3NHYitoaTk5Y290UmtIM0t1Q3NPd1ljSUwwbDd6enhSS3NkblNqd2ZiNERnNWNWOTFGc3NZQkVReWIreDVpSmxUYUNqT1dyUFR2c2FoS3Y5S29ySU9PRVBYaFduSHN2THlCWXlCQnZtRWJRNlV3bUU5M21WSGpGdWpsSUFmZEpQazJGTlJoTzJGbGxKdWhWSit4eWZaQytQK3ljVlB3R0lHOHUvQW81Y29ra3haK0FNYy9JM05SQUNMZmREL2hYMXFidzRBb0YybEpaTy9Ld1pnU1loc1pIOW8yY0lMeWUrVm54eVovUXJ6M09HUE8zbTNTSEJBWWZ4dlFIcTBwa0RHRXNodnRRNlZNNjhLNUE1T3RQbXpOL1JMQXd0NlVjTFAwdEZOVG1BQjRtMm5rMmpaODVNRHJwRzNOc2ZXUGNWQXUyTzZQWktIRWZneUg5cW9lOHVUYzg4dDdITTlkQzU1akNYbWJiQTFOWCsiLCJtYWMiOiIyODNiOTUwOGE5YjJkODZmNTllZjBhNjBkN2M3OWE5MzljZTY2ODYxNWQ2NDM4M2Q5YmM1ZTVhZWM5MTVhZjJiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlM5LzdzNnNybVhjYzBZYWFvNG42M0E9PSIsInZhbHVlIjoiN2d4YzZOcGJ6WnhKaWJYdmZvbGtKQVZLMmJLclRpNGRTeVZnRHAyemZTbFVOU0E2VTVsZ09tUVNZeHAvdFA5alFraVNLSytvcWdYNngwaU5Pcmd3UUdyQmJRclg5dnRCdHJFTlZqeFdlZlZEcUF3V0VnaFpCMkwzQk1tOWRoc0Y4ZmNxTHlpRW1Za25MWW4yQ3JuSzdJdktBY0ZneU0zdDNMbWM2b0FuTkIzc2dkckUyNGl3a3F3N1k2TFArdVVxK2V5R25YK282L0N5KzRUcmxpMzc2NEVDZklBYjYyRWRFcjJReXlmUzhNVUVFbXRMbEVlQVdzYVVTTTVQZW4zVEY4ZmpPV243bjRmRkZzWWthbnRnZkJKcWxZcGxJQTN5Vkc1MXhSNUhhNUlQeG16MmFodHdRTkZsaWpVTlNEaGgvYWE2dlBCcHZ6KzVJV1FVWnhuNVJjREU1YjFTaGtEZHJMdmV0MXM3NDdzdDlmMDRIbWI5VzV6elI4Y09tRjdiaThmalNIaG9uOEVKUEk3eHEzazdEZDBlYUcvYVVPdERJbDlMZzJZMnpWSk5PVHFnTzV1RHU5TmJjdmxsTmJablJIdjdtcVl0eTNCeXdKRnFDYTQ3VVh6UFB3ejQrN1hncm1EMEo1QTJ6ZXZLb1pCYzV2UElvd2VFM3l4UFI0YU8iLCJtYWMiOiIwNWM2M2UzNjM1YjNlMGFlZmU5OTg1YWMyYTQ4NzIxYzI1M2I0NGFkYzc1OGE1YTJjYTIwNzk3NGY3NzM2NGE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188311439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-619953384 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InI1dmZTMG0ybVRaM01ydVBZdDJZU1E9PSIsInZhbHVlIjoiNGFERzEzT3lWYWorQTR3aU0zbUMrVVU4N0JJeGZ1QU12cnhnSlpYUHZnbHBzUGZycVhVUHZoK2RlWndVRzN3dE42V2VUWXNNTDhKeXRpbjNaMEVUbEZtYVE4a0twMHR3Z1dnSHFDc1BEMVdESFkySkR2bEtVQS9ZTEdVb004N2F5MXVIS1AyOVRGd29ybmQzWU9nNWJiZ2cwcTcxaE9BaU5DaEN0RzQ3STZvUVh5TUxialA1KzU4ZElPbWJnditieW1EdSsxR0FybDlYV2tkZ3J0SVN0bndCQ3JLSklrbktuZmkwUnd6REhDUmZnT1NndkVmTTVIQXJ4OFhHUHlGdHE2cnUyKzExK2Y3aW5rd2c0Q0dpU20vUy81dEIwa1JuMUFaeVV6UXR2UmlxMUliMTNxVkdqMEF0TFVmWlA1NXVpb2RFeGY1NGJXZG5WcDVsRnp0cjNQdnNyRTVFQUMzRGxSSk9KTzlhSG8vZXgyWEFLWk4rbTRma211eURQaUJCWmJHZ2tXTzhaM2N4Qnl5VlQvcFZFay8vemFPbU5IRUNVMEVtUE45REJrVHlDMlllRWpzOW5FUU9SeTBES0xwTFFhRUI1S3NQUEQwVGtLVWtSUUxBZm1HeGltRWI5eisvUWRtVVg3SFBQWHl2Vk9BaS9DZ3gvdjJhNkRpdHM3ZTEiLCJtYWMiOiI4MDVkYmYwMmQxOGJjMjdlMTM4ZGE2YTk0MjlhMDZlOTgwZDQyYjE4ZjVmMGY1OGZhYWVjMzc0NmM2ZWIzM2VkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZjTlhlS2ovYzZLR0lvVFFUUENMRmc9PSIsInZhbHVlIjoiRmV1blpadlREY0hqNkZXVm0wVUdFanpwMDJCRzlZbFFVaG0wUkdIVHN2NDh0UTZub2VzUlA0cXNoT041Y214Nll5cUpvTkduaEExNWlQVUNYU3o1MTRDS3UyU2NXdTJ3MVluR3UrL0VVazJBK2Z2MXJuaEVuNDRSenU1SnZNN1VlOU0rMlduOU43UlJqbXNXOUgwMUkydDB6QnRjUnhQeVJYbFluVFFMditDWFREbW5tRnRobm1YNitCZkFWeHdNbjFwdDg4ZXIrNUFZWHlHQ3FsYUZCSFFnNUZyemFiMjBZWlZOeXAxbVFRN1FMRUVqa1RuenlFSmhoLzVYQkNjMVA3amV3ckdqQmwxU3VOMHk1SWlhVmxnT3Mrc3ZnVk9lUm8yMUtOM050QmV3YWdKcFJLUkUrYWhTdnhOaUNxbzVnTlFXN1d0d1Ivc1h1dXB5TXp6YTZtaitYRHBoZkdFN0dscEd5K1I1WUVtbmQrWXMvMVR5MXlwV1R4bTV0eFhOZ0xISU9nN0dJSFY4RmNjT1RWcjdEbXd0aG84Rm5RNURqbVhFY29LY3lESFhSU1grRTlYanc3RFRHSHIyYTRHWFRRVlV6S2VyUlY4Y3g2Z09SMDI3OGNxYmhVTVlGc1hIZHhWZGdqdE15UjNkVXl2bmNWWWE3OHRJNE1zSGJ6UGUiLCJtYWMiOiI2NTZkMzMyY2FmYThkZTllNDkxOGU4NGU4NGEyZTc4MDRkNmE4NGRmZGEyZjhiY2Y0NDUyZTc0NmU2ZDM4ODhmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InI1dmZTMG0ybVRaM01ydVBZdDJZU1E9PSIsInZhbHVlIjoiNGFERzEzT3lWYWorQTR3aU0zbUMrVVU4N0JJeGZ1QU12cnhnSlpYUHZnbHBzUGZycVhVUHZoK2RlWndVRzN3dE42V2VUWXNNTDhKeXRpbjNaMEVUbEZtYVE4a0twMHR3Z1dnSHFDc1BEMVdESFkySkR2bEtVQS9ZTEdVb004N2F5MXVIS1AyOVRGd29ybmQzWU9nNWJiZ2cwcTcxaE9BaU5DaEN0RzQ3STZvUVh5TUxialA1KzU4ZElPbWJnditieW1EdSsxR0FybDlYV2tkZ3J0SVN0bndCQ3JLSklrbktuZmkwUnd6REhDUmZnT1NndkVmTTVIQXJ4OFhHUHlGdHE2cnUyKzExK2Y3aW5rd2c0Q0dpU20vUy81dEIwa1JuMUFaeVV6UXR2UmlxMUliMTNxVkdqMEF0TFVmWlA1NXVpb2RFeGY1NGJXZG5WcDVsRnp0cjNQdnNyRTVFQUMzRGxSSk9KTzlhSG8vZXgyWEFLWk4rbTRma211eURQaUJCWmJHZ2tXTzhaM2N4Qnl5VlQvcFZFay8vemFPbU5IRUNVMEVtUE45REJrVHlDMlllRWpzOW5FUU9SeTBES0xwTFFhRUI1S3NQUEQwVGtLVWtSUUxBZm1HeGltRWI5eisvUWRtVVg3SFBQWHl2Vk9BaS9DZ3gvdjJhNkRpdHM3ZTEiLCJtYWMiOiI4MDVkYmYwMmQxOGJjMjdlMTM4ZGE2YTk0MjlhMDZlOTgwZDQyYjE4ZjVmMGY1OGZhYWVjMzc0NmM2ZWIzM2VkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZjTlhlS2ovYzZLR0lvVFFUUENMRmc9PSIsInZhbHVlIjoiRmV1blpadlREY0hqNkZXVm0wVUdFanpwMDJCRzlZbFFVaG0wUkdIVHN2NDh0UTZub2VzUlA0cXNoT041Y214Nll5cUpvTkduaEExNWlQVUNYU3o1MTRDS3UyU2NXdTJ3MVluR3UrL0VVazJBK2Z2MXJuaEVuNDRSenU1SnZNN1VlOU0rMlduOU43UlJqbXNXOUgwMUkydDB6QnRjUnhQeVJYbFluVFFMditDWFREbW5tRnRobm1YNitCZkFWeHdNbjFwdDg4ZXIrNUFZWHlHQ3FsYUZCSFFnNUZyemFiMjBZWlZOeXAxbVFRN1FMRUVqa1RuenlFSmhoLzVYQkNjMVA3amV3ckdqQmwxU3VOMHk1SWlhVmxnT3Mrc3ZnVk9lUm8yMUtOM050QmV3YWdKcFJLUkUrYWhTdnhOaUNxbzVnTlFXN1d0d1Ivc1h1dXB5TXp6YTZtaitYRHBoZkdFN0dscEd5K1I1WUVtbmQrWXMvMVR5MXlwV1R4bTV0eFhOZ0xISU9nN0dJSFY4RmNjT1RWcjdEbXd0aG84Rm5RNURqbVhFY29LY3lESFhSU1grRTlYanc3RFRHSHIyYTRHWFRRVlV6S2VyUlY4Y3g2Z09SMDI3OGNxYmhVTVlGc1hIZHhWZGdqdE15UjNkVXl2bmNWWWE3OHRJNE1zSGJ6UGUiLCJtYWMiOiI2NTZkMzMyY2FmYThkZTllNDkxOGU4NGU4NGEyZTc4MDRkNmE4NGRmZGEyZjhiY2Y0NDUyZTc0NmU2ZDM4ODhmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619953384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-540217135 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540217135\", {\"maxDepth\":0})</script>\n"}}