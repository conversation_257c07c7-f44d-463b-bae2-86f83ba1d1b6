{"__meta": {"id": "Xaf1772b9b43a4714ee063a1c75c174bf", "datetime": "2025-07-30 02:37:24", "utime": **********.506982, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843041.662828, "end": **********.507037, "duration": 2.8442089557647705, "duration_str": "2.84s", "measures": [{"label": "Booting", "start": 1753843041.662828, "relative_start": 0, "end": 1753843043.969155, "relative_end": 1753843043.969155, "duration": 2.3063271045684814, "duration_str": "2.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753843043.969181, "relative_start": 2.3063530921936035, "end": **********.507042, "relative_end": 5.0067901611328125e-06, "duration": 0.5378608703613281, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44662624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02094, "accumulated_duration_str": "20.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.171227, "duration": 0.01877, "duration_str": "18.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 89.637}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2099679, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 89.637, "width_percent": 10.363}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-57634885 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-57634885\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1270660358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1270660358\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1596376559 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im1PMDlSZjUzLzJVeWJSZWI2UUFBdmc9PSIsInZhbHVlIjoiSzRDUXpDQjIxNlJZWGZNS0FDRmMzcC9GNVlPWVRxdzg0N1VQWHByS1M5aUFmSW1XUnJVYmVyeTVzTVhpWlkyRmpkRnQ4VUJHbGZBekNKcHEySkJJbEJILzBLQUJUYVFHUVBoTEpYaU0wYlladzhoT2xYamRYeEEzcHorMmwzTU1aekZseVdnaEdIU25NelRSUGEreHk3UnFzdmN3T0o4dWIrUUVUUEZRUjVBYjRSWm5rL2lMaDRLVUVVV1hxeGROWWZjT0lPQ3dsWFIxRzZtQWU5STVFRjRZZWVabWFuL3N5bXc1ZWVxS1R4QThrTzdOVEozZWpvalUrL0M0a2dSRmlHQS9NbDU2VFc2M3FWazlDZVp2VWxiaFJ3Q2JDb2tzWGdhSWlGeU1FdU9pekpRZHViL3A5aFdJQTd3MSt6NitBOS9sNnlVKzg5MkgxUGwrYWJYYUM1bUFGSkZOU1kvTnBhZ091TnNBNzdWTG5mUkFaRisvZFZFZ1pHWjVyVTZWc2hSMXcyNkVJbG50SlN1N25JbXBvUmdBQko1cW5OeHMzbVJlcUczYk9BQW5aWUo1cE9UclZ0TEJSaHhiR0loQVJ2WFZTdGtzTThTMkNuVmptdEljOG5GVDd2VFRTK0ZwQ3Q3N2lVais0U3djSzlYUzYrMkhhZDBWa3NmczBCaG4iLCJtYWMiOiI0ZWQyYmIxMWNlMjc1YzIzYzgxNTY5YzNlY2NkZDc4ODQ2OWI4ZTdjMWRlOTc1NGM0OTMxZDYzMWE1ZThhMjQxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlBPcm5IdGw0Y1crQlJYazRlWE5RYXc9PSIsInZhbHVlIjoiZEdsaXdCWTlZTDJxdVR1Umw1czdXb3g3RlZEMEJrVDgwRVlTekFGTDVWRlVZMUluSHE3R3dFSmpmdk1WUWxGTEYvZkVGeEdNdHc2dUVJcDlVTTZ4MTZYcHBFNHIxT2UwalFRQ2J1M3ZONUs0dmJqZys0RXd5VUY3RlVFdWdsekxFeXFIR2Z4eHMvVS8xV1RKTDJwV2RGQ0dFR1hGQ1ZwbFd3YjJTZXVYK1M5YjZ3VzIydC9OMmRaYjk0MDlzZEF0ajNWem9xQjFZeUswd2pNQnE2TTNySzZOa3VtOGlFMGRSYmJnM3hCQ3NrRkcrdEtOT3g2cWxSMTNlaGpEV0lkczJ6OU5GbzNEU3ZZYmhpRXNGZ2R1elJ5VlpOK3R0bXdhbC9XT3k1MUFNK1BFalVUd01waThEWjFJeGd1cnNYa2l6emJudEswZHB5ZE52UTFMeHRqY2Q5THFrK2doSlNTNWh0RU9vUXNKWVZaZGF3M0NLSElFTkp2NEphd2tIajJVRFFhNmJLL1hlOEJKK0lhbmF2a2E3TVBUUmEvaER0bnArV0kvMkY1QVFvMlNQNm5PZlk0TDBhRFl0Mk91RnY3MnNWV2NyclNtTDAxZkMxK3RoV0hHZmxNUXBjN2hRMjhSWlpVaHNYY2s2SmFROWFuc0JWSjZWZVdNdkxWTlZQZFAiLCJtYWMiOiI1YWUzNzgzNjNjNjEwODI5ZDQ1NWEzZjM0NTcyNWZiMTNmNjAyNjA4OGVjNzRkNmQzMTNhMzQ1YTQ5NjFmMGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596376559\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-940376732 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XRhqfNhIljuWkeh2CYPLg15rfgYjPgg1Mschh0S6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940376732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-79787789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:37:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkvbklMZW1Ta3F6OHZRV05mVGc3WFE9PSIsInZhbHVlIjoiUFNZQWZQRytmWDN6bXhDblFnYzBudExKQ0h2anRoRDVyRkdiZE0wRTNXWmhiVDA2Vi9JM09EUzdVaHBJMk9GZ2s3ZjVBZWVHckNML2VLUW4rK3BqTXQ1MlRlSHYxbUJuQXJ2dk1WQlVjRUlBR0haNGcyNERVdkNkWmI3NWVkOGZ4b1FMZ0U1Q3NoajV2MmppdkY4L28rZGNmb3hkVlUxbjZrUjltN3oxU2Uzb25aZXU3dHVKQmtPTThXbWtEQTZmR1NObUR2Nm9UQkRuU0R2cTF5VnZ4R2l2NElnR2pjbUtUSTlCL1lFYzFCdzZ6bm1EU1ltUVBXM2xMY1NQSlY4OFRCNXZQMlFQcXE5Ym1RdmhlMVlBRmZDY0R0blUrekNjQ2xxUXRwenB5Y1RrSVJYdFlhUzJkRTlrb1FDSG1NeWRUbTBCeC9KejNRY1hkTE9wMGN3ZXhWQ1U4dmhGN0NRRUNYS09GU3FIMHRoWjhTWTBHYlJSbXFpVUU0a3V0bzBKWHFubE5sc3Z3dlRGakdqS1ozc0k5NG9mTzh4aks1U1lNYk9SS2kxcTNOd08rKzdlaUZKMGdrYXMxdEMwOVBvaFZLVVZwRXFuN01QeC84QWVLc3R5Sm4xL0prb0dZN1BobVpoeGhXbDFSMVBUQ3YyTlB4K29ja29mQXM4eW5SeFMiLCJtYWMiOiI0Y2M4MzZmZmY2MzdiYWJkOGI5YjAwMGQ1ZDkyYTk0YzcxOWJlNjhiMTY1ZmM0ZGRiYmNjMzdiZTlhNjdjYjc4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9BaVNkTnpETnNDbHdjbGZuYWdPeWc9PSIsInZhbHVlIjoicVJzZitqYjNhVU1NOVpFZndiaVhZb3BtMjhGUUxmOXV5dEowSjhrNWxxWFFzZk5lK3pocnFITmkwVEt2OXlvaFNJdURMZDNMYnFESkVnaDl0bjRPOFBFUzFpTUd3cmNBdjBnT0Z5K0VDV2hHMzdDTVhCSlg0emZIdlZaZ25FRjFxS0VOU01RdVJramRBVnJFWDc2NTQ2ejVOUHVyaXZzWTZKSE1zWmh5Y2ZhUTFGY0NlWkh5STNYNE9IZzBRZWsxMmlDRVNZb1lUS3RseUlaaFZUb0RkQXN1b2lORkxEOEwwdnZ3M083cUQ4cHJiQ1VrNzB6NDRRb0poRUpmQWNqcUZLZUM4b3ZvdVhQcnZsSGE1WldmaEpSOXEzOU1HODl5VUcyLzVKWDJkU2JxVGlqT2JrcjhqSFlibURMNHdhNC82S1JQUXFmNDRkeUJ2d3g1OWtCMTk3Tkp4WElwcWNyWEcrRjROMG8xLzlNU3RuUDFsb1JWY3JYc0lLYnBRV0lZcy9la1pyTzRwbHc4VUp0anRoV2NFTGlsTDlzZXkrcWlYMW1HalF1OU5vMVJiOTFsOWs2RitSSXdPbkxmUW81b0JEMzBOaXhPTSt2cFd2cDMydjVaRWhPZG1CSFZaeWc3TjJSN0h1dUxMKy9XZGRTLytrVlk5Y05OR1NPbVArdloiLCJtYWMiOiI1MzY1ZDgzZGMwODIxNDEyMTA5NWIwZGU5Mzc1NGMyY2QzMjY0YTU4MTEwMzdkZDQ5YTQyNjMwZmFiNDhkYjk1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkvbklMZW1Ta3F6OHZRV05mVGc3WFE9PSIsInZhbHVlIjoiUFNZQWZQRytmWDN6bXhDblFnYzBudExKQ0h2anRoRDVyRkdiZE0wRTNXWmhiVDA2Vi9JM09EUzdVaHBJMk9GZ2s3ZjVBZWVHckNML2VLUW4rK3BqTXQ1MlRlSHYxbUJuQXJ2dk1WQlVjRUlBR0haNGcyNERVdkNkWmI3NWVkOGZ4b1FMZ0U1Q3NoajV2MmppdkY4L28rZGNmb3hkVlUxbjZrUjltN3oxU2Uzb25aZXU3dHVKQmtPTThXbWtEQTZmR1NObUR2Nm9UQkRuU0R2cTF5VnZ4R2l2NElnR2pjbUtUSTlCL1lFYzFCdzZ6bm1EU1ltUVBXM2xMY1NQSlY4OFRCNXZQMlFQcXE5Ym1RdmhlMVlBRmZDY0R0blUrekNjQ2xxUXRwenB5Y1RrSVJYdFlhUzJkRTlrb1FDSG1NeWRUbTBCeC9KejNRY1hkTE9wMGN3ZXhWQ1U4dmhGN0NRRUNYS09GU3FIMHRoWjhTWTBHYlJSbXFpVUU0a3V0bzBKWHFubE5sc3Z3dlRGakdqS1ozc0k5NG9mTzh4aks1U1lNYk9SS2kxcTNOd08rKzdlaUZKMGdrYXMxdEMwOVBvaFZLVVZwRXFuN01QeC84QWVLc3R5Sm4xL0prb0dZN1BobVpoeGhXbDFSMVBUQ3YyTlB4K29ja29mQXM4eW5SeFMiLCJtYWMiOiI0Y2M4MzZmZmY2MzdiYWJkOGI5YjAwMGQ1ZDkyYTk0YzcxOWJlNjhiMTY1ZmM0ZGRiYmNjMzdiZTlhNjdjYjc4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9BaVNkTnpETnNDbHdjbGZuYWdPeWc9PSIsInZhbHVlIjoicVJzZitqYjNhVU1NOVpFZndiaVhZb3BtMjhGUUxmOXV5dEowSjhrNWxxWFFzZk5lK3pocnFITmkwVEt2OXlvaFNJdURMZDNMYnFESkVnaDl0bjRPOFBFUzFpTUd3cmNBdjBnT0Z5K0VDV2hHMzdDTVhCSlg0emZIdlZaZ25FRjFxS0VOU01RdVJramRBVnJFWDc2NTQ2ejVOUHVyaXZzWTZKSE1zWmh5Y2ZhUTFGY0NlWkh5STNYNE9IZzBRZWsxMmlDRVNZb1lUS3RseUlaaFZUb0RkQXN1b2lORkxEOEwwdnZ3M083cUQ4cHJiQ1VrNzB6NDRRb0poRUpmQWNqcUZLZUM4b3ZvdVhQcnZsSGE1WldmaEpSOXEzOU1HODl5VUcyLzVKWDJkU2JxVGlqT2JrcjhqSFlibURMNHdhNC82S1JQUXFmNDRkeUJ2d3g1OWtCMTk3Tkp4WElwcWNyWEcrRjROMG8xLzlNU3RuUDFsb1JWY3JYc0lLYnBRV0lZcy9la1pyTzRwbHc4VUp0anRoV2NFTGlsTDlzZXkrcWlYMW1HalF1OU5vMVJiOTFsOWs2RitSSXdPbkxmUW81b0JEMzBOaXhPTSt2cFd2cDMydjVaRWhPZG1CSFZaeWc3TjJSN0h1dUxMKy9XZGRTLytrVlk5Y05OR1NPbVArdloiLCJtYWMiOiI1MzY1ZDgzZGMwODIxNDEyMTA5NWIwZGU5Mzc1NGMyY2QzMjY0YTU4MTEwMzdkZDQ5YTQyNjMwZmFiNDhkYjk1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79787789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1132456103 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132456103\", {\"maxDepth\":0})</script>\n"}}