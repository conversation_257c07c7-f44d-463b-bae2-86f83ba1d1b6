{"__meta": {"id": "X3c21aaeb23531bd7eef53585109c0291", "datetime": "2025-07-30 06:05:21", "utime": **********.797564, "method": "POST", "uri": "/contact-groups", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:05:21] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/contact-groups\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.793099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753855520.884304, "end": **********.797605, "duration": 0.9133009910583496, "duration_str": "913ms", "measures": [{"label": "Booting", "start": 1753855520.884304, "relative_start": 0, "end": **********.583312, "relative_end": **********.583312, "duration": 0.6990079879760742, "duration_str": "699ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.583325, "relative_start": 0.6990208625793457, "end": **********.797607, "relative_end": 1.9073486328125e-06, "duration": 0.21428203582763672, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52527280, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST contact-groups", "middleware": "web, auth, XSS, revalidate", "as": "contact-groups.store", "controller": "App\\Http\\Controllers\\ContactGroupController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=29\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:29-128</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.035070000000000004, "accumulated_duration_str": "35.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.633692, "duration": 0.02502, "duration_str": "25.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 71.343}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.6762412, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 71.343, "width_percent": 2.88}, {"sql": "insert into `contact_groups` (`name`, `description`, `created_by`, `updated_at`, `created_at`) values ('Test group', '', 79, '2025-07-30 06:05:21', '2025-07-30 06:05:21')", "type": "query", "params": [], "bindings": ["Test group", "", "79", "2025-07-30 06:05:21", "2025-07-30 06:05:21"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 64}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6974, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:64", "source": "app/Http/Controllers/ContactGroupController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=64", "ajax": false, "filename": "ContactGroupController.php", "line": "64"}, "connection": "radhe_same", "start_percent": 74.223, "width_percent": 6.958}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.7198029, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 81.18, "width_percent": 4.106}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.732296, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 85.287, "width_percent": 2.88}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.737797, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 88.167, "width_percent": 2.31}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.746185, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 90.476, "width_percent": 9.524}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/contact-groups", "status_code": "<pre class=sf-dump id=sf-dump-312298431 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-312298431\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2018592875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2018592875\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1394504193 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Test group</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394504193\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1063719920 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">278</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryjHvdNfVyoF1r2bxS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IllZNjd6TGdIMjNIanE1VnAyZ1RVU2c9PSIsInZhbHVlIjoiN2NjU3Q1aW5YamJsZUNvYmdWSGhuYVltYW5POTBuekpzUkJKN2hlTWdHekcxWXRjMFlmQzVlNU9pK1VLNVc5MldUdE1GMURVZ2g1K25UMGJPbENTV0Jqb0Zid3NzWHFUdnRUaWVWZGNlTW9YOTlIRHUvUXFqRTVkUmh5R1JJTkRQWjhDMzF0bmJiV2FLSHdUbFBqWGkrRjZNZmo5ekxCOTF4QjJsYnBNUS8xdXdQQnFBbUNwdmpYN0NzajRGVnVuKzdNUmhuRXl4dWtudVFESE43a01EKzhYSU5GeTIrWDg5anBQSWYydVZlaHl2WHg0N08xVXZocFhuNW5uc2tJL3hnQm5EaFovT2duZWRORkM0dVE0dGNaL0lDL0ZOMW1qbExWaXI5cngvSWhNN2phUFJveWlzdVJzWVhTM2ZzQ2hJWVMzSDF5TFN1S1hiSFNhYWdmRjMxc1JFSVRKQk5wWTlaYWhLYS9pU3FqblZ5STBCR3V3ck0wYWlYL0hmTzZ5WUYvOHErUkQ3Tkp4Z2J0bFUxREorOG5QRmpqZGMwblVmZm0vcVJ1Z0RkWTVkUVdnUUdYMVQ1VWNDZ3grR2FpR0FqSEk5eHRUZ2RaTkQ5YTd5WllodkM1d3QvWUdQMUVBd0RYb21KbENQanpGYVJJVmxpVm9keWNidmRvWFc3K1YiLCJtYWMiOiJmYWViYmFkZTI5ZjRhMjQxZDRlM2E4ZDczNTQyMGMyNGU1ZTZmZWZiMjM4ZjAzMzM4ZDRlYWZmY2VkNGJmZTQ0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkNTamtJSjVJL0RSeklHdW9KZ1R6c0E9PSIsInZhbHVlIjoiaDNWdTdSMDVsbDVHOEMxMjBUa2FBazQ3V1FRSE8zdzU4cGFTVkVBWVppZVNHL01jZ0VNbWhrN0NKK0lCOFMybi8vVm1lOTlrS3U4aEtmc0QzdXB1NHZuZWQzK0lqVFBFNHVGZFpWNDZPSVJvenIrTlpVS3pyUDVTTm80S1MvVmFyU2ZxbnpZK2hHS2dGejRVdmRvM2VpcWJpdU1TZTRiZEdBaENFWjhIcDFyZ1BxdlJtTllhT2dXRit2WnhtMkIvTHBsZlBObVZkMDhkdWI4MHZreW5id09DQkNqSkcwOFNUem05SjZPRCtkak5NSUZVT3NHVDJWZ3N3UTE5cTBXL0pDaEJzTjJoZ3pCVFJoRE9KSC9FVUw1a3FzYXRiSk80S2hjUWsxNnlZTjU2TFpVdk5NS2MzVmtzUjNSNGtuQWQxMm5TSW9tbFlINTN2YmFEQkRwR0JBODduV0xPajFBUlJwNG85Wm10U0wzT094SzFWTHFOMWtQV1NBNmxmYnZzdUEvdGNuQ0Vid2tzV1o0ZDFtalNrRjdPYm9iRzdmMEQzVE9OYndJZmNkeEVZSHpBOGs3UnAwTmZ5d3JLd3F1a0hEbTRSSzQ5cGRTZlVxTTJNSUc0dnI3NWdzY1VIR2o5K1VQbzBFcmpUdXBOMW4wajhBMjNOVGNZMEZyUU5oZksiLCJtYWMiOiJlNGRhMzExNzYwNDE4NjI0Y2NjNWM0ODM2YWUyMWEwOGM1YzM5NmM4OGViZDlhMWJmNzI2OWYwNDZjNzk3YWU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063719920\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1314716592 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314716592\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-723750080 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJkb0h0TmhoWUpWOEVOZXBZY0hjWUE9PSIsInZhbHVlIjoiYVUyMGJZV3lqQWUxOUZsTEQwSkpEVHlMR05sYUt3dFhDNHJJYkhEWGFFd0Y0M0czdHpKSzFMVm5UZ2JaYkl5L1N2SEJFYkNlWFI3THN6eE9EY25POTJNVHV2UHVydEFaUTIwMktoS2JNVXVLbWgrUGVsMHBmRlFxeUNvMURIc0x6MjkzcEpvYXplQjhxcVJ1NGZDMVlzNkgvNkhpV0JidjNkVlFFeDB5alNNRDM5aFhLVktGbmF5Qy9YTkhvNnVqaWhSLytTYlBOd3VCbXd1VXRqQ2V3dURFNEtSRDV5aFV4S0RkTFIvWS9JY2lGdDRIZHp4NW9KbFIrWnlNRm0wR2l3Z3dDQktPYjk4WXE5ZkprUEFwVC9nZWZqTHlWbURFMjdFRzdMVTh4L3F6SkM2dy94NkpjY1VtRm54RU9sMXRRdXBNeTI1SWlQVWN3T3FQdmxUWXg1N0JhVkJIT2M4NEg4aFdiQXpxRTI2a1E4U0FxVnhrVzltWVZBdTdTVEtINnA1MlhOaXg2SHZxVTFlclQ5OGQrbEJ6SzdGMWM3OFpTTzVmYnhKRDg4dzg5bUYxN1p0Y2dRaTlYRjcrWXJLOG0yazBDS21tOFJxWmNEU0toSlJKdlpHNDlOcHc1Vm9EdzM5cng0b1pVdGFsVlduNFUxSEFHT0V2UkVobXFHVUUiLCJtYWMiOiJjZjE1OTFjZTYyMDA0ODZkMGRmNzNiMGY5NjQ2MzBlNWZiMzVjOTM0ODhiNWUxYjVmMTg3MjZkNjc1MWI1YmExIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllYRmlJTVhJOWlsWWE2TlRhTk1BRmc9PSIsInZhbHVlIjoiejFiMUlla1d3UVowZGhUL0JES3RpazNZQ25aM0pHemxXUStabHQrNjJ0NjVhWGNRMzQzOEFiUFcydnMzNnMrNFp5Qm5rVGphd2tTbDFzSlJ6eWNxYS93T2VCcEFkTEhhVnc4TEpSYUJ5WHlSdzA2SUV1YWttRm1sSUhBcXlIQk54MElPK2JiS2VPOUY0czc4VTBlYjU1S1FjVnc3N01sZGlJWnhBQ0owNFhCM0c2em9xRVlabXJPR1VpQzFYem90eWNhRW1HRUZja2hlVVlLT1FwQldpbE9HVnNUWE1CZTFuV2RsNzkwRFl5dnUyMlRueWh5ZERsYkhvMzM2WElHQjNsVXJONWhJeWh1aXBIcnR4cm5BZ3owZkdzaFFmcDM1Qjh2Q2Npd1ZjTVV4TmhGYkFoN2dOQjMzT0RVTmpLOC9iWjZMMVZMTTNIdXFSM1M3RDQxY1BIQmxkOVFFTG90WUVORVFjU3dLVTBzeWIxZlZva0RlOStLZ0I1M0Q5Q0plVjNXSythbFdqRFBPYzYrUDI3M1hxOVd3UGt1dUtuMGZmUG1sRE1Sb2wyU3J3MDJPbkQ4WUpLeEJwUm1UdTJTcUFlSklRRndFVlBtN3B2TGFndEU3SkU3ajNnQ242U2dadFp3a1dDVlQvVG5lQ1U5ODFza0l6UnU5bXp6WTJFR3EiLCJtYWMiOiIwYzY2MjI0MmRmNGE4NmE1ZWM0NmQyOTY4YTMzNTNjOWNhOTkyMzkwMDZkNzBhZWU1MjUwOTI0ZGNmMDc2ZTdiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJkb0h0TmhoWUpWOEVOZXBZY0hjWUE9PSIsInZhbHVlIjoiYVUyMGJZV3lqQWUxOUZsTEQwSkpEVHlMR05sYUt3dFhDNHJJYkhEWGFFd0Y0M0czdHpKSzFMVm5UZ2JaYkl5L1N2SEJFYkNlWFI3THN6eE9EY25POTJNVHV2UHVydEFaUTIwMktoS2JNVXVLbWgrUGVsMHBmRlFxeUNvMURIc0x6MjkzcEpvYXplQjhxcVJ1NGZDMVlzNkgvNkhpV0JidjNkVlFFeDB5alNNRDM5aFhLVktGbmF5Qy9YTkhvNnVqaWhSLytTYlBOd3VCbXd1VXRqQ2V3dURFNEtSRDV5aFV4S0RkTFIvWS9JY2lGdDRIZHp4NW9KbFIrWnlNRm0wR2l3Z3dDQktPYjk4WXE5ZkprUEFwVC9nZWZqTHlWbURFMjdFRzdMVTh4L3F6SkM2dy94NkpjY1VtRm54RU9sMXRRdXBNeTI1SWlQVWN3T3FQdmxUWXg1N0JhVkJIT2M4NEg4aFdiQXpxRTI2a1E4U0FxVnhrVzltWVZBdTdTVEtINnA1MlhOaXg2SHZxVTFlclQ5OGQrbEJ6SzdGMWM3OFpTTzVmYnhKRDg4dzg5bUYxN1p0Y2dRaTlYRjcrWXJLOG0yazBDS21tOFJxWmNEU0toSlJKdlpHNDlOcHc1Vm9EdzM5cng0b1pVdGFsVlduNFUxSEFHT0V2UkVobXFHVUUiLCJtYWMiOiJjZjE1OTFjZTYyMDA0ODZkMGRmNzNiMGY5NjQ2MzBlNWZiMzVjOTM0ODhiNWUxYjVmMTg3MjZkNjc1MWI1YmExIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllYRmlJTVhJOWlsWWE2TlRhTk1BRmc9PSIsInZhbHVlIjoiejFiMUlla1d3UVowZGhUL0JES3RpazNZQ25aM0pHemxXUStabHQrNjJ0NjVhWGNRMzQzOEFiUFcydnMzNnMrNFp5Qm5rVGphd2tTbDFzSlJ6eWNxYS93T2VCcEFkTEhhVnc4TEpSYUJ5WHlSdzA2SUV1YWttRm1sSUhBcXlIQk54MElPK2JiS2VPOUY0czc4VTBlYjU1S1FjVnc3N01sZGlJWnhBQ0owNFhCM0c2em9xRVlabXJPR1VpQzFYem90eWNhRW1HRUZja2hlVVlLT1FwQldpbE9HVnNUWE1CZTFuV2RsNzkwRFl5dnUyMlRueWh5ZERsYkhvMzM2WElHQjNsVXJONWhJeWh1aXBIcnR4cm5BZ3owZkdzaFFmcDM1Qjh2Q2Npd1ZjTVV4TmhGYkFoN2dOQjMzT0RVTmpLOC9iWjZMMVZMTTNIdXFSM1M3RDQxY1BIQmxkOVFFTG90WUVORVFjU3dLVTBzeWIxZlZva0RlOStLZ0I1M0Q5Q0plVjNXSythbFdqRFBPYzYrUDI3M1hxOVd3UGt1dUtuMGZmUG1sRE1Sb2wyU3J3MDJPbkQ4WUpLeEJwUm1UdTJTcUFlSklRRndFVlBtN3B2TGFndEU3SkU3ajNnQ242U2dadFp3a1dDVlQvVG5lQ1U5ODFza0l6UnU5bXp6WTJFR3EiLCJtYWMiOiIwYzY2MjI0MmRmNGE4NmE1ZWM0NmQyOTY4YTMzNTNjOWNhOTkyMzkwMDZkNzBhZWU1MjUwOTI0ZGNmMDc2ZTdiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723750080\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-315775369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315775369\", {\"maxDepth\":0})</script>\n"}}