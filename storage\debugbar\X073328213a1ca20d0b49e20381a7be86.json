{"__meta": {"id": "X073328213a1ca20d0b49e20381a7be86", "datetime": "2025-07-30 08:14:28", "utime": **********.813432, "method": "GET", "uri": "/finance/sales/contacts/lead/13", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863267.379334, "end": **********.813468, "duration": 1.4341340065002441, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753863267.379334, "relative_start": 0, "end": **********.617355, "relative_end": **********.617355, "duration": 1.2380211353302002, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.617379, "relative_start": 1.2380449771881104, "end": **********.813471, "relative_end": 3.0994415283203125e-06, "duration": 0.1960921287536621, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025560000000000003, "accumulated_duration_str": "25.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7270179, "duration": 0.02265, "duration_str": "22.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 88.615}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.780076, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 88.615, "width_percent": 6.847}, {"sql": "select * from `leads` where `id` = '13' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["13", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7901502, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 95.462, "width_percent": 4.538}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/13", "status_code": "<pre class=sf-dump id=sf-dump-261919678 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-261919678\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-70482521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-70482521\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-895239483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-895239483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-406791022 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjBOb1d5VWVLNEdQVDZORlNwQ29MTXc9PSIsInZhbHVlIjoiYUlwT3hsZTArb3ZybEFaV2cydHRyZGl3Sk1qZisyRWpVUER4VzRIVlV4S1RZSEwzL1hscHFDYmJTc1ZGYnFvTXRJbnhzZWJRc3lEaEd2S3JORndWVmNpSVl5ckk0N3M4ckJYS2F3UklMQ21reWdGMEw0d2JjazRUNzlLbVVwZDNPMUNWV0tYTWNVSUVWTmgybjRKMWlNSWU2bHlHVUhSQkluZzhzOGUwOVR2a0pRTkYvajRTdDhvSHZ4M3Bid0F4eSsydUZRSC8wSEQwaFlURlpNRUE5cTlPUWJjc3puRmFnK0oxT29sTFBxaVJFdXlibzhzdndMRXdwSVBHd1d2eEx4Q0x6dXA2QWFTbFNOeUZVaGhqYmhCdnF0aDhXWmdlcjN0UDNHMEJNVnh4bktiRHo2aUZZdTN1WVU5dE5Tcy9qQ3hjOW4wSW5ONU1oQWVPeW5pY1hMdmhMOWJLOWhKMkNVM0dxOWdPMHlzM1NMNGZPT0pFeUZtVDFnUmVIaThEZjVrMUk4a3V1L2hrQzVLbWVzbGZlU2lremJOS0l5TlpqbTVuOHY0c2t3TkFkN2JyL2dYOG9pSzM4em0vZEJiYWZHMDNnSWxZMEtKbTd4MXhNYjhnQ3cxZzZHUlJxUDRjY3hXTGVRbmNUcElmQjdaUXhId3ljNG1FZXNUOUY0UEwiLCJtYWMiOiJiOGY3Y2Q4NGUwMjlhOGU4ODdhNDk2N2ZiZDlmNDUzMGNhOWYzYzM2MjY4ZmVlNmY4Yzk2NDZlNTkxYmQ4YzgyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InNETlV3ajF6T0NBVVVENmdZODZwOWc9PSIsInZhbHVlIjoieDFZUkFPTmpDcTBSQWErc2pZNWsweUp4bTJlTExLMTQ3eUtreW1YeFJCaDh0WEJaMG5EWmR3SkpEdDlHUFpSYm13ZkUyOTZyMlg4UlAxT2FsNjNVN2t1bzFmRG50WS9yekExOEZDR05Ea2duRTFIYTVSYlBCaXNRRFNkcXh2S0lKM2Z3STFjTmlNaTd5UVVTb05Lb3hHcCtEb3ZvYWFWTTdua2xCcjlMaFh1cWp4eVRpMGhvZTdKeTlPNHJBOExlYTAvYUJ1SXJPZlNJZXUvdWVHZmZ3eDVNT2ZTeEhDWVJSbjV4c2hWek9Vd1ZFTi9iZ2M2dy9EYjlnUGNndUhJem5MckN3SzRUaDhyajVVbHNPTjE5dkVJRnAxT3d3UVBMN2x2Zm1HdUlhbU12UDYwSlIwNnhIa3RjZS8vVG9NV2pLdTJNcnN3Y0tkZXZFcm9MUDkvcmh3aUQyNUxuUHhiblhsQUNXRjlTUkh4OU1rLzZHM3N5MmJIN2pybXUrYVZlYUlRdHlvRVYwZm13aGdMemJhY3NkdVdnUTdCbFVFb01oU3hGblE5ZUF1MmtlOE53TW1nOTI5WG83NG1kUW04R0drNnUyS0FPWW13Yy9mcnhmc29xS2JjbkJyblh6aDc2L3U1SHltOU9pa1l5RFZEWnlmWmVOUGorOVhadDV6YmsiLCJtYWMiOiI4YzVhODRjNGIyYjg2MDYyMWM4MjNkZDc4NWUxOWFlODZkYWY0ZmRkYTQ5MGMwMzFjMTUxYzIwZTk2YTZkZGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406791022\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1210728105 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210728105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-874447447 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:14:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtwYW0xNzR6RWpQY2pSVDlpc0Vkb3c9PSIsInZhbHVlIjoiUXNvdlZjYUNMTU1LRHowMHVBSHZ2VDI5M0JwbGJ0RnJ1dzFicURJSGZCTTJPM0VQVUhWSHpza3N0L3FUdHRQSHhCNHJ3NC8ySmxSMlh4R2FQeFhzcndwR0tmQmRsVXpiNng1Z2t5eWRPMEpORXdRUXRTY3lRdVN6RURMdkVDQUZxWUZSNEl5LzlXc3VQT3ZTMHQ5Q0trU1RyZHZGK2huZmJzb1JzOENZdUJpZVhiemZLNXQ5L2oyemtjOFB3K0lpdkVYbDVjbWZ5a2pOdjBwaVYvSUh1ZDE5MkxnVjZENDFkSnZWQUQvS3R0R1VYNS9UdWFHQ2E3bExNeXE5dGlTV0Y4U2s1KzZGd0R4a2FvODV2Q1lQUWlCb1hBeVdHQXRBd1dWQmVQRWdWSkZGdU9XMWNvN1REdHF4a201dlQ0bFBRU2lDRDMzSWJ5VlpDdGdPbTFWWXF3R3RwY043YlFxTnFDbXIrTmFxZ3VIME1ub2xld0FSQlhLN0UrTmhEdzUxY1Z3dnRpaUJMNmlCTEsxcWduQk9DSXlyZFk2V2FtM1g4eE5QbUx1WExZcndLYmxqb3AzU0U5Wk9ad2REdGkxeDdaUmNOR3ZFYnZZUm04dEtuQmJuMTFnaGxvZEFmWHQxS3E3V0dNYUVDdllFVVdVTDZOZG9SQTkxb3QxdWE5TGciLCJtYWMiOiJhOTJjY2QwZmVjNDczMDE5NGU3Y2ZlNDNiZmViMjQ5MDlmODllY2VlNzMzYzhlYTA1ODJlMmUzY2Y4YmM5NWZiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:14:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkV4WmRzQ3FWR25XWjlFeTRDUFdzRUE9PSIsInZhbHVlIjoiSjgwMHliaWwxamY1WnhONW1OM0ZTZDQyVWNCZE8zaTFRaEkyVGttSUtIK3FlQUlNZlhqbU5JOTZ2ZmdHUnJscmM1ckZEZEdycEd3N3N3eDhIK2ExTG95UUZXOU9xMkVCWTZyamcwSnZ3M3o4T1d4YVB0YndtUXJVanVvWlBiYmRLUGZHWUgzQ2s4b2pCWm0xdmlBVHNodXpmc0Y3RUxFMFhtQmxraE45SnNJYVpIc0ErRjgyUnV3eFFNRnh3bXAwVllvZkVHK0luM294OW5FYWo0eU03N1J3UXZyNzJYVk1pRlVxaTZVSUQvaUhWTnIrOTRKRlZ2dFVnVDJLS2htUE9RNDFVeDJ2WWYySm0rL0pwM2VucVdXMkh4U3M0OXpqSHYvU2Yrc09YL2ZoQkY5L2kwempSeVZkYmhDUUZXSFdDMHdmeVpGbWJLUkZRU1NYTDg4dVNucXdIV2VpbHRGRkJPbXluYVRoOStUcFA1b3lHVUFjV0JkWnY3TWtMSzAwTEFDSU52TGpQZzNNa0kwWXVmUytBS2pHYVdvZHBuTWhnaDVkcStzV3lsZkpwL04zN0lCdjVDTzNYWGdpcjk4c0Q5NktDZXZzVVgzR1R2MWNPTDduWUU1YktNZ29TRTJjOHNreUlIL21jSmlQMG1jMkhoYkVlTC9NUU5nV2I1akciLCJtYWMiOiI5MWFjNGJkZjllZWNiZmJlNmM5NTQ1ZjM1MzUwODVjNTk0YTUxODgwZWYyOTY0OTYzYjk5MTUxNTVkMTg0MGIzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:14:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtwYW0xNzR6RWpQY2pSVDlpc0Vkb3c9PSIsInZhbHVlIjoiUXNvdlZjYUNMTU1LRHowMHVBSHZ2VDI5M0JwbGJ0RnJ1dzFicURJSGZCTTJPM0VQVUhWSHpza3N0L3FUdHRQSHhCNHJ3NC8ySmxSMlh4R2FQeFhzcndwR0tmQmRsVXpiNng1Z2t5eWRPMEpORXdRUXRTY3lRdVN6RURMdkVDQUZxWUZSNEl5LzlXc3VQT3ZTMHQ5Q0trU1RyZHZGK2huZmJzb1JzOENZdUJpZVhiemZLNXQ5L2oyemtjOFB3K0lpdkVYbDVjbWZ5a2pOdjBwaVYvSUh1ZDE5MkxnVjZENDFkSnZWQUQvS3R0R1VYNS9UdWFHQ2E3bExNeXE5dGlTV0Y4U2s1KzZGd0R4a2FvODV2Q1lQUWlCb1hBeVdHQXRBd1dWQmVQRWdWSkZGdU9XMWNvN1REdHF4a201dlQ0bFBRU2lDRDMzSWJ5VlpDdGdPbTFWWXF3R3RwY043YlFxTnFDbXIrTmFxZ3VIME1ub2xld0FSQlhLN0UrTmhEdzUxY1Z3dnRpaUJMNmlCTEsxcWduQk9DSXlyZFk2V2FtM1g4eE5QbUx1WExZcndLYmxqb3AzU0U5Wk9ad2REdGkxeDdaUmNOR3ZFYnZZUm04dEtuQmJuMTFnaGxvZEFmWHQxS3E3V0dNYUVDdllFVVdVTDZOZG9SQTkxb3QxdWE5TGciLCJtYWMiOiJhOTJjY2QwZmVjNDczMDE5NGU3Y2ZlNDNiZmViMjQ5MDlmODllY2VlNzMzYzhlYTA1ODJlMmUzY2Y4YmM5NWZiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:14:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkV4WmRzQ3FWR25XWjlFeTRDUFdzRUE9PSIsInZhbHVlIjoiSjgwMHliaWwxamY1WnhONW1OM0ZTZDQyVWNCZE8zaTFRaEkyVGttSUtIK3FlQUlNZlhqbU5JOTZ2ZmdHUnJscmM1ckZEZEdycEd3N3N3eDhIK2ExTG95UUZXOU9xMkVCWTZyamcwSnZ3M3o4T1d4YVB0YndtUXJVanVvWlBiYmRLUGZHWUgzQ2s4b2pCWm0xdmlBVHNodXpmc0Y3RUxFMFhtQmxraE45SnNJYVpIc0ErRjgyUnV3eFFNRnh3bXAwVllvZkVHK0luM294OW5FYWo0eU03N1J3UXZyNzJYVk1pRlVxaTZVSUQvaUhWTnIrOTRKRlZ2dFVnVDJLS2htUE9RNDFVeDJ2WWYySm0rL0pwM2VucVdXMkh4U3M0OXpqSHYvU2Yrc09YL2ZoQkY5L2kwempSeVZkYmhDUUZXSFdDMHdmeVpGbWJLUkZRU1NYTDg4dVNucXdIV2VpbHRGRkJPbXluYVRoOStUcFA1b3lHVUFjV0JkWnY3TWtMSzAwTEFDSU52TGpQZzNNa0kwWXVmUytBS2pHYVdvZHBuTWhnaDVkcStzV3lsZkpwL04zN0lCdjVDTzNYWGdpcjk4c0Q5NktDZXZzVVgzR1R2MWNPTDduWUU1YktNZ29TRTJjOHNreUlIL21jSmlQMG1jMkhoYkVlTC9NUU5nV2I1akciLCJtYWMiOiI5MWFjNGJkZjllZWNiZmJlNmM5NTQ1ZjM1MzUwODVjNTk0YTUxODgwZWYyOTY0OTYzYjk5MTUxNTVkMTg0MGIzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:14:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874447447\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1664252915 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664252915\", {\"maxDepth\":0})</script>\n"}}