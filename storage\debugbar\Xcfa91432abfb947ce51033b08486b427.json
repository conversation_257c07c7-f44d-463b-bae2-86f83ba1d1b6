{"__meta": {"id": "Xcfa91432abfb947ce51033b08486b427", "datetime": "2025-07-30 08:13:58", "utime": **********.715777, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863236.843138, "end": **********.715827, "duration": 1.8726890087127686, "duration_str": "1.87s", "measures": [{"label": "Booting", "start": 1753863236.843138, "relative_start": 0, "end": **********.574722, "relative_end": **********.574722, "duration": 1.731584072113037, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.574752, "relative_start": 1.***************, "end": **********.715832, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "h4toIsXlemUCz0hzWf6X7TUXlsQBGzHapMGpPpV6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1042095497 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1042095497\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-876224641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-876224641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-410219065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-410219065\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1136860183 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136860183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1496714930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1496714930\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1722359615 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:13:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5iaGd6cUFkVzdwL0tBMDNlN3c4Mmc9PSIsInZhbHVlIjoiVHJOSzVWWWxJNjBramRrdTUwczFWaGhxaTZpaE9jazltd2lNcFpqOTN0ZTJOanlnZDRxeDlQZElkc0JkV2ovVnBwb1Z4OFpaRWlEeEJhNTFuY01wdmdXTDVaa2ZYazVjUzlsME1CMHk1Ym1uUkVkQzVQU3p5am9xb21kZDlpU2FEVnR0dEZ1dWFvMWg0WHorRXNIYjJwTURYbjh2Q0tvYjR3dHViZmNCSjZYMVBvenlxYXVBcXl6NVBzVFMyQjNTN1M1Mk14K0hvUlFJNW0zaXZmWmsrK2dEeG5RZzdHNkppcjY1aTBGKzgrejI3MWJRSE9IZHpyTlpFaHVVbmg0UUtKam5lRGZsNS9ycEJKQVVvQkhBS0RUaDFVL3krYUh4cnpncFpWdUJsdmdwbk1KTWVJRmJWVDdWZWVBMndYb3hYSDFpdHhVd3R3MjE4aEJuaGNoZkZtanpjamJ1RkxGZ2tjbHpUZWNNSmw2ZndHU0xEeE1TZk5wd0c0bjVaUjgxa3NPNHJlbjV1cDIvcUVQNDNSaVc2OHZwczgyd2ZtenU5dHZxNnlFZmljU3pubW9iQU1OWnc5NWt1UjJHOVB0R1BJOTlRZllsakljRUJNNG9hMEU5RHVaL1BrZi9mSWRUODVyeFBDY0VzWStHTlBGMFNxcnJJVHhJM0ExcTEwMjkiLCJtYWMiOiIzOWQwZjU5MzQ1NzcyYWVmMDc0MjY3YWYyNGU4Yjk5NDA3YjhhMTc3MjBmNDg1MzQ5ZDA4ODY4ZGYyOGFmMDJlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:13:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZUaUtCZkp2WHRmWWI2b1lPOTdRakE9PSIsInZhbHVlIjoiZXA0QU04MThIMHAwTWNWMVh3MDVMZFBxMTduSzlZNDdSRFlDRWErS3VES3dHNlcwVU0zeXU0MW93SHFvYVJ2TklMMDJRYkdrMXlMWmJUd083ZC9RRGNSRDc0UDc2ZDZkTDllODBEVFBuY2N6T3FmbEpRM0RYOWVCUFpnT1lwRWlJZWpydFEyckRmNVZNVWxMVkNlOVhjdXN0UFcvTFVFaVFGUER2ZmpiVERhRENhUkFUUFdYeHZDSkZWQTkxeENlcTdCZW80ZzRaTCs4R3hUQ0g1L3Zzbm9adm1XeGVqTlRDNUlabStldW9ZN1pDeDJITDNXMTRTeUJNYmNKdDNlZ3MrLzcwUU9BRTBqS3ZZd2hHRGgwZW9HY1FqazN5VUwxSUlCNGMzZ3IwOVpVWGFTdXl3VlMrUEEvcHF0N0E3QmJUTGh0QjNsNnhiRElXd0xEdUtBTEh5OUU0cGNORUpzUThhSDh1V0R2YlZ6RlNNZkYwR2RGUUg4akdZVUFJeWs5RmhjQzJnS0J1T0tNTHhmZ21KalAzeGxweG1zck84ZGRtd2JaZU1TNkg1WkowZm9ZVlg2cDRzSzBKbGFTOGRCTElEOGFJdTNqRVdta1pBcFh3Y0hHbEVFQWFCQS82SWJ5aVQ5Ny8zbmlhU3JaTlkvVlhEeUpqTkRaZWJQbFFGQXQiLCJtYWMiOiJhY2I5M2Y0ZWEyY2I2ZTFmNThhNjM4ZDE1MjY0ZjgzMWU1ZmU1ZWRlMTRiOGNlZmIzZjRjYzE0YjMyZTZmYjkyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:13:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5iaGd6cUFkVzdwL0tBMDNlN3c4Mmc9PSIsInZhbHVlIjoiVHJOSzVWWWxJNjBramRrdTUwczFWaGhxaTZpaE9jazltd2lNcFpqOTN0ZTJOanlnZDRxeDlQZElkc0JkV2ovVnBwb1Z4OFpaRWlEeEJhNTFuY01wdmdXTDVaa2ZYazVjUzlsME1CMHk1Ym1uUkVkQzVQU3p5am9xb21kZDlpU2FEVnR0dEZ1dWFvMWg0WHorRXNIYjJwTURYbjh2Q0tvYjR3dHViZmNCSjZYMVBvenlxYXVBcXl6NVBzVFMyQjNTN1M1Mk14K0hvUlFJNW0zaXZmWmsrK2dEeG5RZzdHNkppcjY1aTBGKzgrejI3MWJRSE9IZHpyTlpFaHVVbmg0UUtKam5lRGZsNS9ycEJKQVVvQkhBS0RUaDFVL3krYUh4cnpncFpWdUJsdmdwbk1KTWVJRmJWVDdWZWVBMndYb3hYSDFpdHhVd3R3MjE4aEJuaGNoZkZtanpjamJ1RkxGZ2tjbHpUZWNNSmw2ZndHU0xEeE1TZk5wd0c0bjVaUjgxa3NPNHJlbjV1cDIvcUVQNDNSaVc2OHZwczgyd2ZtenU5dHZxNnlFZmljU3pubW9iQU1OWnc5NWt1UjJHOVB0R1BJOTlRZllsakljRUJNNG9hMEU5RHVaL1BrZi9mSWRUODVyeFBDY0VzWStHTlBGMFNxcnJJVHhJM0ExcTEwMjkiLCJtYWMiOiIzOWQwZjU5MzQ1NzcyYWVmMDc0MjY3YWYyNGU4Yjk5NDA3YjhhMTc3MjBmNDg1MzQ5ZDA4ODY4ZGYyOGFmMDJlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:13:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZUaUtCZkp2WHRmWWI2b1lPOTdRakE9PSIsInZhbHVlIjoiZXA0QU04MThIMHAwTWNWMVh3MDVMZFBxMTduSzlZNDdSRFlDRWErS3VES3dHNlcwVU0zeXU0MW93SHFvYVJ2TklMMDJRYkdrMXlMWmJUd083ZC9RRGNSRDc0UDc2ZDZkTDllODBEVFBuY2N6T3FmbEpRM0RYOWVCUFpnT1lwRWlJZWpydFEyckRmNVZNVWxMVkNlOVhjdXN0UFcvTFVFaVFGUER2ZmpiVERhRENhUkFUUFdYeHZDSkZWQTkxeENlcTdCZW80ZzRaTCs4R3hUQ0g1L3Zzbm9adm1XeGVqTlRDNUlabStldW9ZN1pDeDJITDNXMTRTeUJNYmNKdDNlZ3MrLzcwUU9BRTBqS3ZZd2hHRGgwZW9HY1FqazN5VUwxSUlCNGMzZ3IwOVpVWGFTdXl3VlMrUEEvcHF0N0E3QmJUTGh0QjNsNnhiRElXd0xEdUtBTEh5OUU0cGNORUpzUThhSDh1V0R2YlZ6RlNNZkYwR2RGUUg4akdZVUFJeWs5RmhjQzJnS0J1T0tNTHhmZ21KalAzeGxweG1zck84ZGRtd2JaZU1TNkg1WkowZm9ZVlg2cDRzSzBKbGFTOGRCTElEOGFJdTNqRVdta1pBcFh3Y0hHbEVFQWFCQS82SWJ5aVQ5Ny8zbmlhU3JaTlkvVlhEeUpqTkRaZWJQbFFGQXQiLCJtYWMiOiJhY2I5M2Y0ZWEyY2I2ZTFmNThhNjM4ZDE1MjY0ZjgzMWU1ZmU1ZWRlMTRiOGNlZmIzZjRjYzE0YjMyZTZmYjkyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:13:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722359615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1977232746 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4toIsXlemUCz0hzWf6X7TUXlsQBGzHapMGpPpV6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977232746\", {\"maxDepth\":0})</script>\n"}}