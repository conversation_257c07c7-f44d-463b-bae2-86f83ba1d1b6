{"__meta": {"id": "Xd1d17a6a819ccb5fca0fb3869180ab96", "datetime": "2025-07-30 02:44:27", "utime": **********.382803, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843464.032409, "end": **********.382859, "duration": 3.350450038909912, "duration_str": "3.35s", "measures": [{"label": "Booting", "start": 1753843464.032409, "relative_start": 0, "end": **********.05236, "relative_end": **********.05236, "duration": 3.019951105117798, "duration_str": "3.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.052428, "relative_start": 3.***************, "end": **********.382864, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hsLVBu0RqOAZGERAkB4ovjMjDATOFtUdiToGZrZ1", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2087204662 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2087204662\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1245174651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1245174651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-784913132 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784913132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2069106905 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069106905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1741103658 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741103658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-881942191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:44:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldQY2kzbXFSaVdsZHF3b1QvOVB1d2c9PSIsInZhbHVlIjoicjE3aDBqSXhoRDlwN3lBYUR0cTFFS0NZQXI3Qk9OdWpmVnM1V09WZHRGcDd4ankyRWROTVBWaXREcHBDTWNDZHdNSUdmczJqeWFWc2NiZW9hSDBvR21iZXdjRVJYeWxRNmZvdTdwNXlSU1pJKzdISnlIYWF2NWVEOEtEVmMydmxMZk1vYzdCd0oxejBybnA4YVQzQ1RVVmdMdS9CcU80L3p1WGtoNWRseWhzV0trSXRzczlESDBrNENMNjFSbEpqbTZlaXBkdXZNWjJmWk9HQTg1SUpQREZUcTFQRlMyVmwrN2pMTWRVdC9PK0pBSkRGZkFBYlUwMmhSVDlnRFFlZGk5S01xcWtMdE92MHdsazlORC9zaUI1VGJMbHptYUxPU1VLeXJ5bmtleGNDY1BZc3BhRXBJWHFaSTFiQTdJY2lMUGllaUsyZHZ3Z3E3ZEY5Z2lBQlpyOC9Zek4zVXdISCtvOUVIQ0FxQUlzQ3VWYWo0RW0wVDdNWklJeVJ4aGVLQnMxM1VrdlQwSzVmRytPbDdYVmxrR255dFJZM2h0Wk5qMDVqeHJLd1YvWlFjcHlPZDhOU2E4MmpCR3pQaWJPQTBNZURCV1daZGQzdGIvU01yTkxQVUx6SjhKbDJuc3M2Sk94ZmlkQ3pDWXFzMUVqZHMwMkJuNStDaTZFVUhKalYiLCJtYWMiOiIzNjViMTg5NTAyOWJiMzQwOGE1YzQ5YzI5YzcwZjUyNWIzZGYzZmZlYWFhY2YzNzU5NWJmNDQwN2MyNmViM2ZhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:44:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5FZzhSa2NmdStMc3FJek1XOGgzZlE9PSIsInZhbHVlIjoiR3g2MjY5VVl4MGluRDZGbE91cUZIaFRCa0c4eDFEMWMzOVVDcHRpQXA1OENBTlBwVDJsaVFjRUkxMzRhZ1UzWHc2Q1VtNjVtNTJhZTZHeGtzb1NoM1N3SHFSc2xRdXVheEYxK0VxekJQLzZiUXhUOG42L0JsU2UxaUgyTEV1K2c1cmpRWXU3VUdQbG9xUDJHY1l3YzI4NkU5TTNXQWxXV3Q0cFBYdW04RTdZLzRpQndlRVFMVXhmOUtkNWUrNUwvOVBBaExHTGVVN1pWVEpFWGVLSGJEYzRyZk94Wi83Q3FJbWV1cXo3QVFISjdxcWxoUWFXdmg5K1Zjcjc0Z3BiVVBJcjJoTkhveDFxMVRkY3VBaW1oVUl6ZnRpKzFpWHZMY3piVEFlR0RvbE9RR1FXbmN3eHczNVYwRjJnTEszd3NEbHZhVnBFd1dTMlM0aWdFay93R0hPWE1YZTBtOVBZaTYvRTVVWTdhTDRxaFlnMFZhZ3ZHTHNtNzdjejl2dFRnOElQaEwwdGd6eFNDMDlPVTdFMXVmQ1JxZHNlS0VpMWlLWWQyUkM2MTAwSzB4V05FOXpWQUhyZ3Exb1Z4bjFLMjRERVRMZ3NVcWdYQ2lMbUtJMkI5ZE8waTF5QmgwYm9BbUFILzFmNlQ3M0o1amF2d0hzMTBVd0wrZWNBTWVvOHUiLCJtYWMiOiIzZWYzMWNiMDdiNDgzOGUwM2QzNDZmZmVkMDhhYmFkOTU0Yjg5MThjODBhNTkxNWUxZjg4YTE2MDU4Mzc2NTkwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:44:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldQY2kzbXFSaVdsZHF3b1QvOVB1d2c9PSIsInZhbHVlIjoicjE3aDBqSXhoRDlwN3lBYUR0cTFFS0NZQXI3Qk9OdWpmVnM1V09WZHRGcDd4ankyRWROTVBWaXREcHBDTWNDZHdNSUdmczJqeWFWc2NiZW9hSDBvR21iZXdjRVJYeWxRNmZvdTdwNXlSU1pJKzdISnlIYWF2NWVEOEtEVmMydmxMZk1vYzdCd0oxejBybnA4YVQzQ1RVVmdMdS9CcU80L3p1WGtoNWRseWhzV0trSXRzczlESDBrNENMNjFSbEpqbTZlaXBkdXZNWjJmWk9HQTg1SUpQREZUcTFQRlMyVmwrN2pMTWRVdC9PK0pBSkRGZkFBYlUwMmhSVDlnRFFlZGk5S01xcWtMdE92MHdsazlORC9zaUI1VGJMbHptYUxPU1VLeXJ5bmtleGNDY1BZc3BhRXBJWHFaSTFiQTdJY2lMUGllaUsyZHZ3Z3E3ZEY5Z2lBQlpyOC9Zek4zVXdISCtvOUVIQ0FxQUlzQ3VWYWo0RW0wVDdNWklJeVJ4aGVLQnMxM1VrdlQwSzVmRytPbDdYVmxrR255dFJZM2h0Wk5qMDVqeHJLd1YvWlFjcHlPZDhOU2E4MmpCR3pQaWJPQTBNZURCV1daZGQzdGIvU01yTkxQVUx6SjhKbDJuc3M2Sk94ZmlkQ3pDWXFzMUVqZHMwMkJuNStDaTZFVUhKalYiLCJtYWMiOiIzNjViMTg5NTAyOWJiMzQwOGE1YzQ5YzI5YzcwZjUyNWIzZGYzZmZlYWFhY2YzNzU5NWJmNDQwN2MyNmViM2ZhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:44:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5FZzhSa2NmdStMc3FJek1XOGgzZlE9PSIsInZhbHVlIjoiR3g2MjY5VVl4MGluRDZGbE91cUZIaFRCa0c4eDFEMWMzOVVDcHRpQXA1OENBTlBwVDJsaVFjRUkxMzRhZ1UzWHc2Q1VtNjVtNTJhZTZHeGtzb1NoM1N3SHFSc2xRdXVheEYxK0VxekJQLzZiUXhUOG42L0JsU2UxaUgyTEV1K2c1cmpRWXU3VUdQbG9xUDJHY1l3YzI4NkU5TTNXQWxXV3Q0cFBYdW04RTdZLzRpQndlRVFMVXhmOUtkNWUrNUwvOVBBaExHTGVVN1pWVEpFWGVLSGJEYzRyZk94Wi83Q3FJbWV1cXo3QVFISjdxcWxoUWFXdmg5K1Zjcjc0Z3BiVVBJcjJoTkhveDFxMVRkY3VBaW1oVUl6ZnRpKzFpWHZMY3piVEFlR0RvbE9RR1FXbmN3eHczNVYwRjJnTEszd3NEbHZhVnBFd1dTMlM0aWdFay93R0hPWE1YZTBtOVBZaTYvRTVVWTdhTDRxaFlnMFZhZ3ZHTHNtNzdjejl2dFRnOElQaEwwdGd6eFNDMDlPVTdFMXVmQ1JxZHNlS0VpMWlLWWQyUkM2MTAwSzB4V05FOXpWQUhyZ3Exb1Z4bjFLMjRERVRMZ3NVcWdYQ2lMbUtJMkI5ZE8waTF5QmgwYm9BbUFILzFmNlQ3M0o1amF2d0hzMTBVd0wrZWNBTWVvOHUiLCJtYWMiOiIzZWYzMWNiMDdiNDgzOGUwM2QzNDZmZmVkMDhhYmFkOTU0Yjg5MThjODBhNTkxNWUxZjg4YTE2MDU4Mzc2NTkwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:44:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881942191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-277291432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hsLVBu0RqOAZGERAkB4ovjMjDATOFtUdiToGZrZ1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277291432\", {\"maxDepth\":0})</script>\n"}}