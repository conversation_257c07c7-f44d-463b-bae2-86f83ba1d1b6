{"__meta": {"id": "X3692ca973fa259b4559a15d30e50f74b", "datetime": "2025-07-30 08:02:33", "utime": **********.670151, "method": "GET", "uri": "/finance/sales/contacts/search?search=Par", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862552.276216, "end": **********.670178, "duration": 1.3939619064331055, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1753862552.276216, "relative_start": 0, "end": **********.478882, "relative_end": **********.478882, "duration": 1.2026660442352295, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.478906, "relative_start": 1.2026898860931396, "end": **********.670181, "relative_end": 3.0994415283203125e-06, "duration": 0.19127511978149414, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665256, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.007819999999999999, "accumulated_duration_str": "7.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.614925, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 59.207}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6394432, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 59.207, "width_percent": 12.404}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%Par%' or `email` like '%Par%' or `contact` like '%Par%')", "type": "query", "params": [], "bindings": ["79", "1", "%Par%", "%Par%", "%Par%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6474168, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 71.611, "width_percent": 16.624}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%Par%' or `email` like '%Par%' or `phone` like '%Par%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%Par%", "%Par%", "%Par%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.653563, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 88.235, "width_percent": 11.765}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1063865198 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1063865198\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-860032303 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Par</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860032303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-960717196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960717196\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-72875312 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjMxK0I5ajR0SjJZTE9GMitFUE04SXc9PSIsInZhbHVlIjoiempTYkVCWk8wSVJMVzdEVFoxSXBsM3REdk9wM29HVTFiVnlqVTY0L1lvNGZhOHVTVzdIQnlIczM3VjZBQ2R5bXdTeEthYzBHSGRsZENrYVMxYXh3N3VFL1ZjcERTUzV0RzkvMjd0TUtGKzVONmVYMUVrTTN6N1RJOXpId2REU2x1V3FvaHlQenlUbHVYbGxrUlZIcHgwVFVuU3VRNGpVT3Nhd3RoaGo5eG1ObkdxTHA3RmZ4dVplUmxJRURTZ1ZyWGFYaHNYdXBXZXVxT3R1Nm5WS2ZFTlQzRFVxK292OG9oQU83TEpHSTlGRHdCYkVvWVdZSUQ1YjgvRXNnbFNzdU55V3liQUdCZHZFQ3pjTzJWeEY5Qjh5QTh3U1VQU3IvQUhneXZLcTJpZ01vM0FnaElNendZL21CMmszL1ZyQVBvaFZnRlVRdU93dXdjWjBzQStoaldSRWRBaWg5VzFrUVZSL3JQYlJpbWx2QXBPN0VhcUdjS0xVQlovTm1DTUZYbFNDOXU1Tmd4aDVlWXFrSTBuTUs1RUtUMURqSE4rTXlmNzR2Y0xLV2t1d0JLNXFCSkJhNWExQ2NNNkQveTNJQ2dQTGtCMGttVjZSbEhmS3N6UnRiRWFmeUdKZmZIQVQxYk9qSE93cHdNSGhVc2FYbVlVMjBscG40aUIwdStnNDkiLCJtYWMiOiI2MmJiZmM5YTZjOTYyZWQ3ZmI4ODE4MmMxYmFjMDZhY2VlZjQyZmMyNTBjNTBkMTliMGZlN2U1MTFkNjBlMDQ0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik9tcmltOHlGYVpLL1lHUWg5cURxWVE9PSIsInZhbHVlIjoiOVM5Mi90RVFpcVE4T0EvWE82aFNJMWpqRGdKNzJHYVNVS0xqSUwyQ2FxR3ZuS2tFQ3F2dDFOcTNDbnBVYjdGWENyYUJZNUkrdHhMQzVVUVRJVmhBSmFNYnR0NXpzay84MXAreWFiOVhYdVBXK3ZMZnBtSDA2NVFkaktBL1RJVlB2NHRFSllaQ05STXJwM2haSjNINjBsYmxQa1JRVjBWTEhWL3ZacEZSejMraDArcnZtSzczdkg0V0NFNHU4L0tBemtQZTRhT2FwZnhha29BMDB4K09nZHVaRHdnNlBEVHRmQVNzUUJQQ1pDNHJZNENHWEZaWHF1dkh6cDQxcytGbndkb2lSalNrTzFxUTNqdU1YY2lkVDMwNUU2TmRmYUVGbFptWkFId01BY3NjUnZScHdNOTBsWXV5RGJJNHg3dUF1aE56emtlL1hkNmUyU0hSbGlXSmFxeUgyc3J0eXZMSGFHSjU4SktFM1AyNk5rYWUzWWNMUnhDVkxUcWMzQTFTbWYrbFFPdDh2SWtVYVhSc2lUYnh2cmdSSmhpNnRqalJZcEovWkF5VW5iU2FKa1V1WXVoM0xrakVpUkJVN0ZqbmQ4RSt1ei9keWVSaFRJSkgwU2dOMlV5Z2pERG5YZlpmOFowRW5tcTlOWmp3UkNuTmV5NGl6UUtMV1Z0RDZxdlIiLCJtYWMiOiI5NDMzZGQ1NTk1NzA1YjMyYTgyOGI4MTRjMzgyMTc0Njg0YTZjMGZjMDEwOTdkMmQxYmFlOTUxZGFjMDY4NDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72875312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1961080154 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961080154\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-811796334 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjU4WWEzNWZuZWE2V1o4QUNjWk1vQ3c9PSIsInZhbHVlIjoiY3dreUhVR0dIcnhrdHM0T1lkdHRLVkRlK2FuM0hsNHpleExzc0djc2s2NFJtSG5IaFA2VytiamZKNG1uNENjejFLWjJPOW9oaTV2ZkNQWlBwRXZaalhjUExFNThuNlRLdXU2T05oaEFIVmFUL3RkSjlrN2JIS29Xb3JUL0p5RlNzMlRNUVh2TDUvdU5hbGJBWVFFam9VWERjRG1qSXM0TEp6YkM1QVh5RlNnVUVCWDJvK1FDTWdHSDBZZTYza09oQU5GbDJvemRncllXUVBpWXJrNXpEMnNvN1FmNWVEMmwyQlRzQmFWVlhRRllKQ09qWENGaXBoUlcxdEo4OFNGRERQMWhXSnVLUURQWU40YUtlWGk0bjhHMXNrVGpQTVZDeS9zQXBJZ2pNazZyM2lpMm9tWjl6Tis1WU5XNVJRMGpFUVdhOWFMcUFEN2hkTDhSdHcya0c1NkQxaUtQS0JpTDc2TXR5L3ZlU0JnZktKcWtxWC9lWEtnYXV1MFZEamFhYVpDSHdRTGM0S2xQemh5NTUyZFFaWDd3bGViS3pQM1VNMFFkRjdxUkcvc2xKdXhkK3dyNDNMY3A2MlVoUFA2MkwwMEhoeXJLNU9BemtvQ1lBSHFGeHFCQzA3UEY4K0Q3UFdVcW02YUNqTExVQldYRkpMWGdpZnJMVlZObi9UYzMiLCJtYWMiOiI5OGE4ZmE1OWE2NWIxYmJhYzQ4NzI4ZTI0ZDY0NTQyNWQ2MDNmOGNmYzdhZGM3ZjQzOGMzN2ZlMTBkNGEyYWQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVvNnNlWjZpb3R6UGZSUXd2cGNoNlE9PSIsInZhbHVlIjoiRU5YSHpPczFFWTZxQXhDVFIwUHJpWmZlYS9za2FMVVpHRDU4SUlRNHBlZ0lMUzNmOUhlRE9Uc3I4R2JsL09oU0g1ZXFCQ05JK0pXVE1yazAwUytVVk14dElHRlltWHUzRGVrMWlXclExNzVSOWpLdm9UK1E2dzgzWVRWbFNQQ2pZdkxCbGFEb01PME1qUUR0MERvNUpjVUI3VVYzSXZvU3FjSWZEWG0xWTdnTlpNVG4rM3AyTHNPUC93QTMyM2RRRkRyQkNEcVJPQzBGenBPN3JpZU9zQktOOExCbHY4S2U2WGNWdkluMlV4S0M4aW5jdG50aFFuNUErS3JyOVlFUHRtN3AyYU5XVjFMQ0dQWU56dnQrRmFZQk8rbTRIakJlUFZuVUVTMmZyQTVQT1RTd2pXbmY5L2oyWGphVU1kL2pQdVhhUUpHL3pkbk4yQy8vdXRHL3FzUDJQRkhkZmlZMWVBRVVacE54b2RRSkxyVkVCMDZEempiR09TVkdCUHF1VWhlOC9sL294azN3UHpKV1lxNnJyYk9vUmo1WitROGdscUhxYVRiOFE2eDJBdnR6bVZQZUd4N3ZCZHF2YUhsRkFVU1JFMW9sZU9KV0RtT3ZtdTRNOXZ6UjR0ZFVzR3o0Um9qMERZZSt6UWRHVzNBaDZjZmwwR2Q4MGIyQmNha1IiLCJtYWMiOiJkYjkzYzZjODJhMjFlMDFlYTZkNzliOGFlZGQ4ZGIwMWMwOWIxNWY4MmJhNjY1YzRhMjAzODMzMTAwNTFjODQ1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjU4WWEzNWZuZWE2V1o4QUNjWk1vQ3c9PSIsInZhbHVlIjoiY3dreUhVR0dIcnhrdHM0T1lkdHRLVkRlK2FuM0hsNHpleExzc0djc2s2NFJtSG5IaFA2VytiamZKNG1uNENjejFLWjJPOW9oaTV2ZkNQWlBwRXZaalhjUExFNThuNlRLdXU2T05oaEFIVmFUL3RkSjlrN2JIS29Xb3JUL0p5RlNzMlRNUVh2TDUvdU5hbGJBWVFFam9VWERjRG1qSXM0TEp6YkM1QVh5RlNnVUVCWDJvK1FDTWdHSDBZZTYza09oQU5GbDJvemRncllXUVBpWXJrNXpEMnNvN1FmNWVEMmwyQlRzQmFWVlhRRllKQ09qWENGaXBoUlcxdEo4OFNGRERQMWhXSnVLUURQWU40YUtlWGk0bjhHMXNrVGpQTVZDeS9zQXBJZ2pNazZyM2lpMm9tWjl6Tis1WU5XNVJRMGpFUVdhOWFMcUFEN2hkTDhSdHcya0c1NkQxaUtQS0JpTDc2TXR5L3ZlU0JnZktKcWtxWC9lWEtnYXV1MFZEamFhYVpDSHdRTGM0S2xQemh5NTUyZFFaWDd3bGViS3pQM1VNMFFkRjdxUkcvc2xKdXhkK3dyNDNMY3A2MlVoUFA2MkwwMEhoeXJLNU9BemtvQ1lBSHFGeHFCQzA3UEY4K0Q3UFdVcW02YUNqTExVQldYRkpMWGdpZnJMVlZObi9UYzMiLCJtYWMiOiI5OGE4ZmE1OWE2NWIxYmJhYzQ4NzI4ZTI0ZDY0NTQyNWQ2MDNmOGNmYzdhZGM3ZjQzOGMzN2ZlMTBkNGEyYWQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVvNnNlWjZpb3R6UGZSUXd2cGNoNlE9PSIsInZhbHVlIjoiRU5YSHpPczFFWTZxQXhDVFIwUHJpWmZlYS9za2FMVVpHRDU4SUlRNHBlZ0lMUzNmOUhlRE9Uc3I4R2JsL09oU0g1ZXFCQ05JK0pXVE1yazAwUytVVk14dElHRlltWHUzRGVrMWlXclExNzVSOWpLdm9UK1E2dzgzWVRWbFNQQ2pZdkxCbGFEb01PME1qUUR0MERvNUpjVUI3VVYzSXZvU3FjSWZEWG0xWTdnTlpNVG4rM3AyTHNPUC93QTMyM2RRRkRyQkNEcVJPQzBGenBPN3JpZU9zQktOOExCbHY4S2U2WGNWdkluMlV4S0M4aW5jdG50aFFuNUErS3JyOVlFUHRtN3AyYU5XVjFMQ0dQWU56dnQrRmFZQk8rbTRIakJlUFZuVUVTMmZyQTVQT1RTd2pXbmY5L2oyWGphVU1kL2pQdVhhUUpHL3pkbk4yQy8vdXRHL3FzUDJQRkhkZmlZMWVBRVVacE54b2RRSkxyVkVCMDZEempiR09TVkdCUHF1VWhlOC9sL294azN3UHpKV1lxNnJyYk9vUmo1WitROGdscUhxYVRiOFE2eDJBdnR6bVZQZUd4N3ZCZHF2YUhsRkFVU1JFMW9sZU9KV0RtT3ZtdTRNOXZ6UjR0ZFVzR3o0Um9qMERZZSt6UWRHVzNBaDZjZmwwR2Q4MGIyQmNha1IiLCJtYWMiOiJkYjkzYzZjODJhMjFlMDFlYTZkNzliOGFlZGQ4ZGIwMWMwOWIxNWY4MmJhNjY1YzRhMjAzODMzMTAwNTFjODQ1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811796334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2026636999 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026636999\", {\"maxDepth\":0})</script>\n"}}