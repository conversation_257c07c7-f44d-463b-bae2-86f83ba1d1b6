{"__meta": {"id": "Xa13061ddc53ab8ceb1046574e383ea2e", "datetime": "2025-07-30 07:36:42", "utime": **********.13537, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753861000.633868, "end": **********.135422, "duration": 1.501554012298584, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1753861000.633868, "relative_start": 0, "end": 1753861001.983639, "relative_end": 1753861001.983639, "duration": 1.3497710227966309, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753861001.983688, "relative_start": 1.****************, "end": **********.135428, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UdqrFsnj1cxshGy05j2ubgosK8OiJEKoipSxEVO4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1712487714 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1712487714\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1904209759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1904209759\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1760434283 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1760434283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-208765876 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208765876\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2055075884 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2055075884\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-376449717 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:36:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdRMmRGR29YVStvNVJ2UURsalJrVFE9PSIsInZhbHVlIjoiQVpOM0FsbTZibzF5V0FEMEw1VDg4R0g0ZEMwazZORHBFaDRibThickxxeG4vcU9HZjhURXIwTTVJL0ZKMzM1UUh3TGwzU0xRS1VIdWxROEU1OE1xRlpZU29DaVV5dENqaTdJL0xqbU8xaHpUOVduZkFUYTc5alk5a0txMlQ1SXdRVkUzc1R4M1o3YzFDYy9VN3lTQkdlNkZEVWVlUkxRZFdGcC9qUG5RcHlPY3VVQitaaS9QVklXKzdsV3psTml6Rk41cWVFOXJFSFZxczRvVzA3NjBObEFUaTg3bTZPMFRQRWxwUTh5eXZOOXJ3MjEzV0pjd0tCSFBrTG1ZYTg2Z2VQdW8zMW1jMTlnTjJsblgwRUIxcDRva1BaQzlGWEJjVmpNYis1RWFYcHlaVmhCN2syM2Z4NUlzSjBTdWoxYktEM3V3akF5M240QjdqYzE4RTVPZVlsUlIvZ1NSNlByV1kybXZ2N3E0bTcxNUtaTkk4WmVtRnhHYThrQ1dseEZTdStNWno4U0F3cmhDcnhWbHN5dDd5R2x6Y3YxL21yV0FUK2xoZEdVNFJNSERRVHFZRjNzdHNscm1wVitPT1Uxc0RMTXZIS29nK01YNDhORGp0cGdEVG5XRkRXb1FzV2h6K1AzL3NoZzRSOWlIaUZJcUl5RGhRN2xLNm9VNXd3MFQiLCJtYWMiOiIyMzQ5NzY0ODA4NjM3ZjJmMDQ3NDE3NTNjZTMwNzYwNTYzZmFiN2NkNjZkYzEwZTUyMTRlOWQ5MTI3Y2ZlOTYyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:36:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii8vM2QyQ3d4MVZqMVM5dDZTemZScGc9PSIsInZhbHVlIjoiODFoY2J0TWlRUUxDYnZGNVQ2ZEwrWjl5SjJSRXU5bnhUY3F5TmM4ZDI2aitCOHdkbTZWV3p1cXpMQmE0ZDVGdlEwWDVVb1hCKzZ4Nm94ZlV4bTRNTzJTd3lkR2dEM2hvS0FNbDB5UGduai90bFZKSUZzTlJTelR1ZXdtVE0yc00wKzFaMEN5NVR5UHFTT3ZNSXlLbTFrbk52eHNha0YxL0ttd1J4b2U1cldGQ2xDUHpPS0x5UjJ1VGd6VFMwdDZiczJweTBOcEpMaUpPUDY2YlBaOW9VTkpUcUlkV3JLVjBMQ0laM1ZlZmRxTXRLT1RDRE5xR1VEUWhDTGJKbWFTSnZPbUk5N0haNFBmMUs2YStCeGUvbHMwVUFxSlFBOFk1ZUdLL0UyU0R2a2JQd0RKWEJpd2xQdzB2YUpRVjZKTVZWWnJ5N3FqMmJBNGdFMUN6Rm5IL1UyQkgvYUd5YWVSeVh3TzBRaDdsQTRVSGJUekF6NWlOS29aTElaQlpYS3ozbmJKWHJnZDdMblYzSmdENWx5TVk2aUJJakRaZ05GSW9TL0VQcUlyZFFVajRhSzI0cklhR1lWV3RpNVJRMitQcVZUc0pOMnV6QUtUR1BwdkFZL0YzUUhTcFBxMU5mYlkrSWdyZGtlaDllaFpwZitycWNrOWh6Z1JpK0dRbHFwV28iLCJtYWMiOiJjNTM4ZTc4ZDZiNDcyMmJiZjQyMTFlNGFlMTdmNTA0MWNjODBhZGNmZWJkNWRlMmVlZjkwYTM4MGNmMDM1NWYwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:36:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdRMmRGR29YVStvNVJ2UURsalJrVFE9PSIsInZhbHVlIjoiQVpOM0FsbTZibzF5V0FEMEw1VDg4R0g0ZEMwazZORHBFaDRibThickxxeG4vcU9HZjhURXIwTTVJL0ZKMzM1UUh3TGwzU0xRS1VIdWxROEU1OE1xRlpZU29DaVV5dENqaTdJL0xqbU8xaHpUOVduZkFUYTc5alk5a0txMlQ1SXdRVkUzc1R4M1o3YzFDYy9VN3lTQkdlNkZEVWVlUkxRZFdGcC9qUG5RcHlPY3VVQitaaS9QVklXKzdsV3psTml6Rk41cWVFOXJFSFZxczRvVzA3NjBObEFUaTg3bTZPMFRQRWxwUTh5eXZOOXJ3MjEzV0pjd0tCSFBrTG1ZYTg2Z2VQdW8zMW1jMTlnTjJsblgwRUIxcDRva1BaQzlGWEJjVmpNYis1RWFYcHlaVmhCN2syM2Z4NUlzSjBTdWoxYktEM3V3akF5M240QjdqYzE4RTVPZVlsUlIvZ1NSNlByV1kybXZ2N3E0bTcxNUtaTkk4WmVtRnhHYThrQ1dseEZTdStNWno4U0F3cmhDcnhWbHN5dDd5R2x6Y3YxL21yV0FUK2xoZEdVNFJNSERRVHFZRjNzdHNscm1wVitPT1Uxc0RMTXZIS29nK01YNDhORGp0cGdEVG5XRkRXb1FzV2h6K1AzL3NoZzRSOWlIaUZJcUl5RGhRN2xLNm9VNXd3MFQiLCJtYWMiOiIyMzQ5NzY0ODA4NjM3ZjJmMDQ3NDE3NTNjZTMwNzYwNTYzZmFiN2NkNjZkYzEwZTUyMTRlOWQ5MTI3Y2ZlOTYyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:36:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii8vM2QyQ3d4MVZqMVM5dDZTemZScGc9PSIsInZhbHVlIjoiODFoY2J0TWlRUUxDYnZGNVQ2ZEwrWjl5SjJSRXU5bnhUY3F5TmM4ZDI2aitCOHdkbTZWV3p1cXpMQmE0ZDVGdlEwWDVVb1hCKzZ4Nm94ZlV4bTRNTzJTd3lkR2dEM2hvS0FNbDB5UGduai90bFZKSUZzTlJTelR1ZXdtVE0yc00wKzFaMEN5NVR5UHFTT3ZNSXlLbTFrbk52eHNha0YxL0ttd1J4b2U1cldGQ2xDUHpPS0x5UjJ1VGd6VFMwdDZiczJweTBOcEpMaUpPUDY2YlBaOW9VTkpUcUlkV3JLVjBMQ0laM1ZlZmRxTXRLT1RDRE5xR1VEUWhDTGJKbWFTSnZPbUk5N0haNFBmMUs2YStCeGUvbHMwVUFxSlFBOFk1ZUdLL0UyU0R2a2JQd0RKWEJpd2xQdzB2YUpRVjZKTVZWWnJ5N3FqMmJBNGdFMUN6Rm5IL1UyQkgvYUd5YWVSeVh3TzBRaDdsQTRVSGJUekF6NWlOS29aTElaQlpYS3ozbmJKWHJnZDdMblYzSmdENWx5TVk2aUJJakRaZ05GSW9TL0VQcUlyZFFVajRhSzI0cklhR1lWV3RpNVJRMitQcVZUc0pOMnV6QUtUR1BwdkFZL0YzUUhTcFBxMU5mYlkrSWdyZGtlaDllaFpwZitycWNrOWh6Z1JpK0dRbHFwV28iLCJtYWMiOiJjNTM4ZTc4ZDZiNDcyMmJiZjQyMTFlNGFlMTdmNTA0MWNjODBhZGNmZWJkNWRlMmVlZjkwYTM4MGNmMDM1NWYwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:36:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376449717\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400562539 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UdqrFsnj1cxshGy05j2ubgosK8OiJEKoipSxEVO4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400562539\", {\"maxDepth\":0})</script>\n"}}