{"__meta": {"id": "Xb01ae26461624455cf54ce4c031f986f", "datetime": "2025-07-30 05:43:58", "utime": **********.637627, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854237.798835, "end": **********.637671, "duration": 0.8388359546661377, "duration_str": "839ms", "measures": [{"label": "Booting", "start": 1753854237.798835, "relative_start": 0, "end": **********.556281, "relative_end": **********.556281, "duration": 0.7574460506439209, "duration_str": "757ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556296, "relative_start": 0.****************, "end": **********.637674, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "81.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hSEYDHArClO6It5pErXsFZwdpTXsyBkiswne4vBi", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-851954432 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-851954432\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-309703128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-309703128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-209495285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-209495285\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-24583411 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24583411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-229029231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229029231\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-583987357 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:43:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJRNlJOYmw1dStJeU5Jek1Uc3V5TFE9PSIsInZhbHVlIjoibGxSSXpLemlEc05uZ3RlYmdZZno3SkVKTkJaR3R0TFJWU1pEWG45ZzVPaWpkSkIyL1VaWmVCb2luekZpa0VONGFFY1NlSS9NMjZRRVBOc2hUMHpHM3hhcTYvYlhRY21KbDFlTHB4VG1KTlB3cFZVZkVIRkUzSXFYQ3lPeWMweVFsR243aFMxL2g1SFNHWVBENUs5WFU5SDVncFNxNFpKZG9BaWR1WWZMTnlmZ0pFMUlxVStWbkFXblgrMVppYXF3ZzQ3Y0dSK3BDcFRaWlY5OC9QMFhUZG1sNENYc2RuOUFoVGJSemdxYVpWZGVQMC9KMVhDMnczcnFOdmRaUk55RFJmN1FRZy9EYWhmRE9hUEdRSWRqNERQZWF6VFIvOXNOK2c0RmlKcTZnN3NnZ1U5Y3F5L2FPWXZybDJQOEw3SEpHZ0Q0RDBUUG5GZklzVi9qSk0wUHNGeW5xSzkrL0p6UHRTazM2TGJOcmFucFFtOTNrVUpGbzA0MVE0Y21xb3FFS3QrMHYveTZLdGlpelF2MWdXY3RYcjVOenE5TkRPaFhBS0YvUkhtNVlvb1V4Z29FazVzVUozZVFYbkR1eHJ4UXYva0xobm0yNzhXeWRjVGJXdjFXa0RUS042cVlDVkpFWGJQR1R4eG1qVnpaTElGazU1cmRLQXVDdk5mOU8zejUiLCJtYWMiOiIxYjM4MjBmMTg4NzVmMmJkZDc5YjVhMDAzMmViZTNiYTAwZDYxNTQ3MDBiODY0OGUzZDdlZWU1YjQzNzg3NDg2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:43:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVTRnpLWWxGRXluTXFCeGJKU1FnV1E9PSIsInZhbHVlIjoibUQybktUcm5jbWJKYk5ZSUcxU1h1T1lON1ZjMjRpMmRPbjB4OS8zZHdFTkRTUG4ySnBUZnVXWVp4bXlYS1QxRmpubm84RENVQXNpR3hDYjBCN3BvYTRNUnVpaWtnK0hIWFAyRWpaRzFSZ29FSjZGU2JuM01xOFZkckFVRTV5Y2lwWkZ5empjQjc5bWExUDF5MVJ0dEpEcUFSR0hrZW9UMFlWbzVTelB5RUd4K1plWXhNMVdaR0hkZ01DWnBIN1dGeWRJVWR2K0QzQXI5N3ZUaTM0ODZoVisyc2J5bHNDK2VNU01aOVpqM1dXQjlQVGNQa2crT1E5Z1dYYnhwZkJqbWl5MFZrRU45eXViRm95MXFQQVgzcndsMEt0SmQ2em91dW9YUVZlTWtVVzlRVkIvckV1MmtlaWJ0YmZwRjljaXBRcDZrd3FZcVc3VmxZQUg5aU80K2JuSmc2dWZkYzZYTDV6V3Mxa2JIR25KSGVGRmlFTHJGTS9KV3RUUFRaNnc5M05Zb0cwZVpTV0h2cWE1TE1KWHhITWFTQ0tKbXhmaHZpemNYQnRCZWM5NTBKdVp1NndZcHJIVWhiMU1kM3UwTDZuSlZPUUJGSVhWanpodXA4alF6TlhVWXhBRE15bi9qWUx5QllXMjhmQVFZblV0S3ZVR3gvbld3UFdqVzhTQzAiLCJtYWMiOiI1YmEzM2Y5MWJiZmI5Y2VhNjIxODUxZjY2M2EzZDQ2Zjg5MTEyNGFkYzZkMDEwOTk5ZDI5NGQ2NTY0MmE1MGM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:43:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJRNlJOYmw1dStJeU5Jek1Uc3V5TFE9PSIsInZhbHVlIjoibGxSSXpLemlEc05uZ3RlYmdZZno3SkVKTkJaR3R0TFJWU1pEWG45ZzVPaWpkSkIyL1VaWmVCb2luekZpa0VONGFFY1NlSS9NMjZRRVBOc2hUMHpHM3hhcTYvYlhRY21KbDFlTHB4VG1KTlB3cFZVZkVIRkUzSXFYQ3lPeWMweVFsR243aFMxL2g1SFNHWVBENUs5WFU5SDVncFNxNFpKZG9BaWR1WWZMTnlmZ0pFMUlxVStWbkFXblgrMVppYXF3ZzQ3Y0dSK3BDcFRaWlY5OC9QMFhUZG1sNENYc2RuOUFoVGJSemdxYVpWZGVQMC9KMVhDMnczcnFOdmRaUk55RFJmN1FRZy9EYWhmRE9hUEdRSWRqNERQZWF6VFIvOXNOK2c0RmlKcTZnN3NnZ1U5Y3F5L2FPWXZybDJQOEw3SEpHZ0Q0RDBUUG5GZklzVi9qSk0wUHNGeW5xSzkrL0p6UHRTazM2TGJOcmFucFFtOTNrVUpGbzA0MVE0Y21xb3FFS3QrMHYveTZLdGlpelF2MWdXY3RYcjVOenE5TkRPaFhBS0YvUkhtNVlvb1V4Z29FazVzVUozZVFYbkR1eHJ4UXYva0xobm0yNzhXeWRjVGJXdjFXa0RUS042cVlDVkpFWGJQR1R4eG1qVnpaTElGazU1cmRLQXVDdk5mOU8zejUiLCJtYWMiOiIxYjM4MjBmMTg4NzVmMmJkZDc5YjVhMDAzMmViZTNiYTAwZDYxNTQ3MDBiODY0OGUzZDdlZWU1YjQzNzg3NDg2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:43:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVTRnpLWWxGRXluTXFCeGJKU1FnV1E9PSIsInZhbHVlIjoibUQybktUcm5jbWJKYk5ZSUcxU1h1T1lON1ZjMjRpMmRPbjB4OS8zZHdFTkRTUG4ySnBUZnVXWVp4bXlYS1QxRmpubm84RENVQXNpR3hDYjBCN3BvYTRNUnVpaWtnK0hIWFAyRWpaRzFSZ29FSjZGU2JuM01xOFZkckFVRTV5Y2lwWkZ5empjQjc5bWExUDF5MVJ0dEpEcUFSR0hrZW9UMFlWbzVTelB5RUd4K1plWXhNMVdaR0hkZ01DWnBIN1dGeWRJVWR2K0QzQXI5N3ZUaTM0ODZoVisyc2J5bHNDK2VNU01aOVpqM1dXQjlQVGNQa2crT1E5Z1dYYnhwZkJqbWl5MFZrRU45eXViRm95MXFQQVgzcndsMEt0SmQ2em91dW9YUVZlTWtVVzlRVkIvckV1MmtlaWJ0YmZwRjljaXBRcDZrd3FZcVc3VmxZQUg5aU80K2JuSmc2dWZkYzZYTDV6V3Mxa2JIR25KSGVGRmlFTHJGTS9KV3RUUFRaNnc5M05Zb0cwZVpTV0h2cWE1TE1KWHhITWFTQ0tKbXhmaHZpemNYQnRCZWM5NTBKdVp1NndZcHJIVWhiMU1kM3UwTDZuSlZPUUJGSVhWanpodXA4alF6TlhVWXhBRE15bi9qWUx5QllXMjhmQVFZblV0S3ZVR3gvbld3UFdqVzhTQzAiLCJtYWMiOiI1YmEzM2Y5MWJiZmI5Y2VhNjIxODUxZjY2M2EzZDQ2Zjg5MTEyNGFkYzZkMDEwOTk5ZDI5NGQ2NTY0MmE1MGM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:43:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583987357\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-745536595 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hSEYDHArClO6It5pErXsFZwdpTXsyBkiswne4vBi</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745536595\", {\"maxDepth\":0})</script>\n"}}