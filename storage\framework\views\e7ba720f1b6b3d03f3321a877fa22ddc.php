<!-- Plan Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Plan Management')); ?></h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active" data-plan-view="products">
                    <i class="ti ti-box me-1"></i><?php echo e(__('Products')); ?>

                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="coupons">
                    <i class="ti ti-ticket me-1"></i><?php echo e(__('Coupons')); ?>

                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="links">
                    <i class="ti ti-link me-1"></i><?php echo e(__('Links')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products View -->
<div id="products-view" class="plan-view active">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Products Table')); ?></h5>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create product & service')): ?>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Add Product')); ?>

                            </button>
                        <?php endif; ?>
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i><?php echo e(__('Refresh')); ?></a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i><?php echo e(__('Filter')); ?></a></li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <?php echo e(__('10')); ?>

                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">10</a></li>
                                <li><a class="dropdown-item" href="#">25</a></li>
                                <li><a class="dropdown-item" href="#">50</a></li>
                                <li><a class="dropdown-item" href="#">100</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;">#</th>
                                    <th><?php echo e(__('Name')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Image')); ?></th>
                                    <th><?php echo e(__('Price')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Down Payment')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Tax')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Total Sales')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e($product->name); ?>" readonly>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="ti ti-package text-muted"></i>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e(\Auth::user()->priceFormat($product->sale_price)); ?>" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="0.00" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e($product->tax ? $product->tax->rate : '0.00'); ?>" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="0" readonly>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" <?php echo e($product->is_active ? 'checked' : ''); ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" title="<?php echo e(__('Edit')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="<?php echo e(__('Duplicate')); ?>">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="<?php echo e(__('Share')); ?>">
                                                <i class="ti ti-share"></i>
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="<?php echo e(route('productservice.edit', $product->id)); ?>"><i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?></a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="ti ti-copy me-2"></i><?php echo e(__('Duplicate')); ?></a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#"><i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?></a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-package" style="font-size: 3rem;"></i>
                                            <p class="mt-2"><?php echo e(__('No products found')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create product & service')): ?>
                                                <a href="<?php echo e(route('productservice.create')); ?>" class="btn btn-primary btn-sm">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add First Product')); ?>

                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Coupons View -->
<div id="coupons-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Coupons Management')); ?></h5>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create coupon')): ?>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Coupon')); ?>

                            </button>
                        <?php endif; ?>
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i><?php echo e(__('Refresh')); ?></a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i><?php echo e(__('Filter')); ?></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Name')); ?></th>
                                    <th><?php echo e(__('Code')); ?></th>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('Start`s From')); ?></th>
                                    <th><?php echo e(__('End`s at')); ?></th>
                                    <th><?php echo e(__('Amount')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $coupons = \App\Models\Coupon::where('created_by', \Auth::user()->creatorId())->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $coupons = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($coupon->name); ?></td>
                                    <td><code><?php echo e($coupon->code); ?></code></td>
                                    <td><?php echo e($coupon->product_name ?? __('All Products')); ?></td>
                                    <td><?php echo e($coupon->start_date ? \Carbon\Carbon::parse($coupon->start_date)->format('M d, Y') : __('No Start Date')); ?></td>
                                    <td><?php echo e($coupon->end_date ? \Carbon\Carbon::parse($coupon->end_date)->format('M d, Y') : __('No End Date')); ?></td>
                                    <td><?php echo e($coupon->discount); ?><?php echo e(($coupon->discount_type ?? 'percentage') == 'percentage' ? '%' : \Auth::user()->currencySymbol()); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($coupon->is_active ? 'success' : 'danger'); ?>">
                                            <?php echo e($coupon->is_active ? __('Active') : __('Inactive')); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" title="<?php echo e(__('Edit')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" title="<?php echo e(__('Delete')); ?>">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-ticket" style="font-size: 3rem;"></i>
                                            <p class="mt-2"><?php echo e(__('No coupons found')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create coupon')): ?>
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add First Coupon')); ?>

                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Links View -->
<div id="links-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Payment Links')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Link')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="ti ti-link text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3"><?php echo e(__('Payment Links')); ?></h5>
                        <p class="text-muted"><?php echo e(__('Create shareable payment links for quick transactions')); ?></p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                            <i class="ti ti-plus me-1"></i><?php echo e(__('Create Your First Link')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Plan view switching
    const planViewButtons = document.querySelectorAll('[data-plan-view]');
    const planViews = document.querySelectorAll('.plan-view');
    
    planViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-plan-view');
            
            // Remove active class from all buttons
            planViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            planViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });
});
</script>

<style>
#addProductModal .toolbar button {
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#addProductModal .toolbar button:hover {
    background: #e9ecef;
}

#addProductModal .form-select-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

#addProductModal #imagePlaceholder {
    border: 2px dashed #dee2e6;
    background: #f8f9fa;
}

#addProductModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addProductModal .modal-xl {
    max-width: 1200px;
}

@media (max-width: 1200px) {
    #addProductModal .modal-xl {
        max-width: 95%;
    }
}

#addCouponModal .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#addCouponModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addCouponModal .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}
</style>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel"><?php echo e(__('Add Product')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addProductForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row">
                        <!-- Name and Nickname -->
                        <div class="col-md-6 mb-3">
                            <label for="productName" class="form-label"><?php echo e(__('Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="productName" name="name" placeholder="Product Name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productNickname" class="form-label"><?php echo e(__('Nickname (For internal use only)')); ?></label>
                            <input type="text" class="form-control" id="productNickname" name="nickname" placeholder="Product Nickname">
                        </div>

                        <!-- Checkboxes Row 1 -->
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restrictOneTime" name="restrict_one_time">
                                <label class="form-check-label" for="restrictOneTime">
                                    <?php echo e(__('Restrict to one time?')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isFreeTrial" name="is_free_trial"
                                       onchange="handleFreeTrialChange(this)">
                                <label class="form-check-label" for="isFreeTrial">
                                    <?php echo e(__('Is it a free trial?')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isSubscription" name="is_subscription"
                                       onchange="handleSubscriptionChange(this)">
                                <label class="form-check-label" for="isSubscription">
                                    <?php echo e(__('Is it a subscription?')); ?>

                                </label>
                            </div>
                        </div>

                        <!-- Price Row -->
                        <div class="col-md-3 mb-3">
                            <label for="productPrice" class="form-label"><?php echo e(__('Price')); ?> <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="productPrice" name="price" placeholder="Product Price" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="strikedPrice" class="form-label"><?php echo e(__('Striked Price')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="strikedPrice" name="striked_price" placeholder="Striked Price">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="taxSlab" class="form-label"><?php echo e(__('Tax Slab')); ?></label>
                            <select class="form-select" id="taxSlab" name="tax_slab">
                                <option value=""><?php echo e(__('Select')); ?></option>
                                <?php if(isset($tax)): ?>
                                    <?php $__currentLoopData = $tax; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($id); ?>"><?php echo e($name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="taxRate" class="form-label"><?php echo e(__('Tax')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="taxRate" name="tax_rate" placeholder="0">
                        </div>

                        <!-- Checkboxes Row 2 -->
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emiOptions" name="emi_options">
                                <label class="form-check-label" for="emiOptions">
                                    <?php echo e(__('Emi Options')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showShippingField" name="show_shipping_field">
                                <label class="form-check-label" for="showShippingField">
                                    <?php echo e(__('Show shipping field')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="paymentGateway" name="payment_gateway">
                                <label class="form-check-label" for="paymentGateway">
                                    <?php echo e(__('Payment Gateway')); ?>

                                </label>
                            </div>
                        </div>

                        <!-- Bump up and Image -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><?php echo e(__('Bump up')); ?></label>
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-success btn-sm">
                                    <i class="ti ti-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productImage" class="form-label"><?php echo e(__('Image')); ?></label>
                            <input type="file" class="form-control" id="productImage" name="pro_image" accept="image/*">
                            <small class="text-muted">
                                <?php echo e(__('Supported formats are jpg, jpeg and png')); ?>

                                <a href="https://www.canva.com" target="_blank" class="text-primary"><?php echo e(__('Create Product Image from Canva')); ?></a>
                            </small>
                            <div class="mt-2">
                                <img id="imagePreview" src="#" alt="Image Preview" style="max-width: 150px; max-height: 150px; display: none;" class="img-thumbnail">
                                <div id="imagePlaceholder" class="bg-light border rounded d-flex align-items-center justify-content-center" style="width: 150px; height: 100px;">
                                    <i class="ti ti-photo text-muted" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Skip GST and HSN/SAC -->
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skipGstForm" name="skip_gst_form">
                                <label class="form-check-label" for="skipGstForm">
                                    <?php echo e(__('Skip GST form?')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="hsnSacNo" class="form-label"><?php echo e(__('HSN/SAC No.')); ?></label>
                            <input type="text" class="form-control" id="hsnSacNo" name="hsn_sac_no" placeholder="HSN/SAC No.">
                        </div>

                        <!-- Redirect URL -->
                        <div class="col-12 mb-3">
                            <label for="redirectUrl" class="form-label"><?php echo e(__('Redirect Url')); ?></label>
                            <input type="url" class="form-control" id="redirectUrl" name="redirect_url" placeholder="Redirect Url">
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label"><?php echo e(__('Description')); ?></label>
                            <div class="border rounded">
                                <div class="toolbar border-bottom p-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><strong>B</strong></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><em>I</em></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><u>U</u></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">S</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">"</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">&lt;/&gt;</button>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                        <option>H1</option>
                                        <option>H2</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto">
                                        <option>Sans Serif</option>
                                    </select>
                                </div>
                                <textarea class="form-control border-0" id="description" name="description" rows="6" placeholder="Enter your content..." style="resize: none;"></textarea>
                            </div>
                        </div>

                        <!-- Invoice Footer -->
                        <div class="col-12 mb-3">
                            <label for="invoiceFooter" class="form-label"><?php echo e(__('Invoice Footer')); ?></label>
                            <div class="border rounded">
                                <div class="toolbar border-bottom p-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><strong>B</strong></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><em>I</em></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><u>U</u></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">S</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">"</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">&lt;/&gt;</button>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                        <option>H1</option>
                                        <option>H2</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto">
                                        <option>Sans Serif</option>
                                    </select>
                                </div>
                                <textarea class="form-control border-0" id="invoiceFooter" name="invoice_footer" rows="6" placeholder="Enter your bank details, policies or anything else" style="resize: none;"></textarea>
                            </div>
                        </div>

                        <!-- Trial Fields (shown when "Is it a free trial?" is checked) -->
                        <div id="trialFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="trialDurationType" class="form-label"><?php echo e(__('Trial Duration Type')); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select" id="trialDurationType" name="trial_duration_type">
                                        <option value=""><?php echo e(__('Select')); ?></option>
                                        <option value="days"><?php echo e(__('Days')); ?></option>
                                        <option value="months"><?php echo e(__('Months')); ?></option>
                                        <option value="years"><?php echo e(__('Years')); ?></option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="trialDuration" class="form-label"><?php echo e(__('Trial Duration')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="trialDuration" name="trial_duration" placeholder="Trial Duration">
                                    <small class="text-muted"><?php echo e(__('No of days, months or years of trial period')); ?></small>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="trialPrice" class="form-label"><?php echo e(__('Trial Price')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" class="form-control" id="trialPrice" name="trial_price" value="0">
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                        </div>

                        <!-- Subscription Fields (shown when "Is it a subscription?" is checked) -->
                        <div id="subscriptionFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isCancellable" name="is_cancellable">
                                        <label class="form-check-label" for="isCancellable">
                                            <?php echo e(__('Is Cancellable?')); ?>

                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="billedEvery" class="form-label"><?php echo e(__('Billed Every')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="billedEvery" name="billed_every" value="1">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <select class="form-select" name="billing_cycle_type">
                                        <option value="monthly"><?php echo e(__('Monthly')); ?></option>
                                        <option value="yearly"><?php echo e(__('Yearly')); ?></option>
                                        <option value="weekly"><?php echo e(__('Weekly')); ?></option>
                                    </select>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="billingCycle" class="form-label"><?php echo e(__('Billing Cycle')); ?> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="billingCycle" name="billing_cycle_display" value="Forever" readonly>
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Submit')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Coupon Modal -->
<div class="modal fade" id="addCouponModal" tabindex="-1" aria-labelledby="addCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCouponModalLabel"><?php echo e(__('Create Coupon')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCouponForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row">
                        <!-- Coupon Name -->
                        <div class="col-md-6 mb-3">
                            <label for="couponName" class="form-label"><?php echo e(__('Coupon Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="couponName" name="name" placeholder="<?php echo e(__('Enter coupon name')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Coupon Code -->
                        <div class="col-md-6 mb-3">
                            <label for="couponCode" class="form-label"><?php echo e(__('Coupon Code')); ?> <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="couponCode" name="code" placeholder="<?php echo e(__('Enter coupon code')); ?>" required>
                                <button type="button" class="btn btn-outline-secondary" id="generateCodeBtn">
                                    <i class="ti ti-refresh"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Product Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="couponProduct" class="form-label"><?php echo e(__('Product')); ?></label>
                            <select class="form-select" id="couponProduct" name="product_id">
                                <option value=""><?php echo e(__('All Products')); ?></option>
                                <?php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                ?>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($product->id); ?>"><?php echo e($product->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Discount Type -->
                        <div class="col-md-6 mb-3">
                            <label for="discountType" class="form-label"><?php echo e(__('Discount Type')); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="discountType" name="discount_type" required>
                                <option value="percentage"><?php echo e(__('Percentage')); ?></option>
                                <option value="fixed"><?php echo e(__('Fixed Amount')); ?></option>
                            </select>
                        </div>

                        <!-- Discount Amount -->
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label"><?php echo e(__('Discount Amount')); ?> <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount" placeholder="<?php echo e(__('Enter discount amount')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Usage Limit -->
                        <div class="col-md-6 mb-3">
                            <label for="usageLimit" class="form-label"><?php echo e(__('Usage Limit')); ?> <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="usageLimit" name="limit" placeholder="<?php echo e(__('Enter usage limit')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Start Date -->
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label"><?php echo e(__('Start Date')); ?></label>
                            <input type="date" class="form-control" id="startDate" name="start_date">
                        </div>

                        <!-- End Date -->
                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label"><?php echo e(__('End Date')); ?></label>
                            <input type="date" class="form-control" id="endDate" name="end_date">
                        </div>

                        <!-- Status -->
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    <?php echo e(__('Active')); ?>

                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="couponDescription" class="form-label"><?php echo e(__('Description')); ?></label>
                            <textarea class="form-control" id="couponDescription" name="description" rows="3" placeholder="<?php echo e(__('Enter coupon description (optional)')); ?>"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Create Coupon')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Inline functions for immediate execution
function handleFreeTrialChange(checkbox) {
    console.log('Free trial changed (inline):', checkbox.checked);
    if (checkbox.checked) {
        document.getElementById('trialFields').style.display = 'block';
        document.getElementById('subscriptionFields').style.display = 'none';
        document.getElementById('isSubscription').checked = false;

        // Set required attributes
        document.getElementById('trialDurationType').required = true;
        document.getElementById('trialDuration').required = true;
        document.getElementById('trialPrice').required = true;
        document.getElementById('billedEvery').required = false;
    } else {
        document.getElementById('trialFields').style.display = 'none';

        // Remove required attributes
        document.getElementById('trialDurationType').required = false;
        document.getElementById('trialDuration').required = false;
        document.getElementById('trialPrice').required = false;
    }
}

function handleSubscriptionChange(checkbox) {
    console.log('Subscription changed (inline):', checkbox.checked);
    if (checkbox.checked) {
        document.getElementById('subscriptionFields').style.display = 'block';
        document.getElementById('trialFields').style.display = 'none';
        document.getElementById('isFreeTrial').checked = false;

        // Set required attributes
        document.getElementById('billedEvery').required = true;
        document.getElementById('trialDurationType').required = false;
        document.getElementById('trialDuration').required = false;
        document.getElementById('trialPrice').required = false;
    } else {
        document.getElementById('subscriptionFields').style.display = 'none';
        document.getElementById('billedEvery').required = false;
    }
}

$(document).ready(function() {
    console.log('Product modal script loaded');

    // Handle image preview
    $('#productImage').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result).show();
                $('#imagePlaceholder').hide();
            };
            reader.readAsDataURL(file);
        } else {
            $('#imagePreview').hide();
            $('#imagePlaceholder').show();
        }
    });

    // Handle free trial checkbox
    $('#isFreeTrial').on('change', function() {
        console.log('Free trial checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#subscriptionFields').hide();
            $('#isSubscription').prop('checked', false);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
            $('#billedEvery').prop('required', false);
            console.log('Trial fields shown');
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
            console.log('Trial fields hidden');
        }
    });

    // Handle subscription checkbox
    $('#isSubscription').on('change', function() {
        console.log('Subscription checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#trialFields').hide();
            $('#isFreeTrial').prop('checked', false);
            $('#billedEvery').prop('required', true);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
            console.log('Subscription fields shown');
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
            console.log('Subscription fields hidden');
        }
    });

    // Handle form submission
    $('#addProductForm').on('submit', function(e) {
        e.preventDefault();

        // Custom validation
        let isValid = true;

        // Check if free trial is selected and required fields are filled
        if ($('#isFreeTrial').is(':checked')) {
            if (!$('#trialDurationType').val()) {
                $('#trialDurationType').addClass('is-invalid');
                $('#trialDurationType').siblings('.invalid-feedback').text('<?php echo e(__("Trial Duration Type is required")); ?>');
                isValid = false;
            }
            if (!$('#trialDuration').val()) {
                $('#trialDuration').addClass('is-invalid');
                $('#trialDuration').siblings('.invalid-feedback').text('<?php echo e(__("Trial Duration is required")); ?>');
                isValid = false;
            }
        }

        // Check if subscription is selected and required fields are filled
        if ($('#isSubscription').is(':checked')) {
            if (!$('#billedEvery').val()) {
                $('#billedEvery').addClass('is-invalid');
                $('#billedEvery').siblings('.invalid-feedback').text('<?php echo e(__("Billed Every is required")); ?>');
                isValid = false;
            }
        }

        if (!isValid) {
            show_toastr('error', '<?php echo e(__("Please fill in all required fields")); ?>');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.plan.store-product")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    show_toastr('success', response.message);

                    // Close modal
                    $('#addProductModal').modal('hide');

                    // Reset form
                    $('#addProductForm')[0].reset();
                    $('#imagePreview').hide();
                    $('#imagePlaceholder').show();
                    $('#trialFields').hide();
                    $('#subscriptionFields').hide();

                    // Reload the page or update the products table
                    location.reload();
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                // Clear previous validation errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Handle validation errors
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Clear validation errors when user starts typing
    $('#addProductForm input, #addProductForm select, #addProductForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addProductModal').on('hidden.bs.modal', function() {
        $('#addProductForm')[0].reset();
        $('#imagePreview').hide();
        $('#imagePlaceholder').show();
        $('#trialFields').hide();
        $('#subscriptionFields').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    });

    // Debug when modal is shown
    $('#addProductModal').on('shown.bs.modal', function() {
        console.log('Modal shown');
        console.log('Free trial checkbox exists:', $('#isFreeTrial').length);
        console.log('Subscription checkbox exists:', $('#isSubscription').length);
        console.log('Trial fields exists:', $('#trialFields').length);
        console.log('Subscription fields exists:', $('#subscriptionFields').length);
    });

    // Alternative event delegation approach in case elements are not ready
    $(document).on('change', '#isFreeTrial', function() {
        console.log('Free trial checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#subscriptionFields').hide();
            $('#isSubscription').prop('checked', false);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
            $('#billedEvery').prop('required', false);
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
        }
    });

    $(document).on('change', '#isSubscription', function() {
        console.log('Subscription checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#trialFields').hide();
            $('#isFreeTrial').prop('checked', false);
            $('#billedEvery').prop('required', true);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
        }
    });

    // Coupon Modal Functionality
    // Generate random coupon code
    $('#generateCodeBtn').on('click', function() {
        const randomCode = 'COUPON' + Math.random().toString(36).substr(2, 6).toUpperCase();
        $('#couponCode').val(randomCode);
    });

    // Validate discount based on type
    $('#discountType, #discountAmount').on('change input', function() {
        const discountType = $('#discountType').val();
        const discountAmount = parseFloat($('#discountAmount').val());

        if (discountType === 'percentage' && discountAmount > 100) {
            $('#discountAmount').addClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('<?php echo e(__("Percentage discount cannot exceed 100%")); ?>');
        } else {
            $('#discountAmount').removeClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('');
        }
    });

    // Validate date range
    $('#startDate, #endDate').on('change', function() {
        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            $('#endDate').addClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('<?php echo e(__("End date must be after start date")); ?>');
        } else {
            $('#endDate').removeClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('');
        }
    });

    // Handle coupon form submission
    $('#addCouponForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.plan.store-coupon")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    show_toastr('success', response.message);

                    // Close modal
                    $('#addCouponModal').modal('hide');

                    // Reset form
                    $('#addCouponForm')[0].reset();

                    // Reload the page or update the coupons table
                    location.reload();
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                // Clear previous validation errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Handle validation errors
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Clear validation errors when user starts typing
    $('#addCouponForm input, #addCouponForm select, #addCouponForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addCouponModal').on('hidden.bs.modal', function() {
        $('#addCouponForm')[0].reset();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/plan.blade.php ENDPATH**/ ?>