<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Bill;
use App\Models\Revenue;
use App\Models\Expense;
use App\Models\Payment;
use App\Models\Transaction;
use App\Models\Customer;
use App\Models\ProductService;
use App\Models\Coupon;

class FinanceController extends Controller
{
    /**
     * Display the Finance dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        if (!Gate::check('manage customer') && !Gate::check('manage vender') && !Gate::check('manage invoice')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get data for the product modal in plan tab
        $category = \App\Models\ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())
            ->where('type', '=', 'product & service')->get()->pluck('name', 'id');
        $unit = \App\Models\ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');
        $tax = \App\Models\Tax::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');

        return view('finance.dashboard', compact('category', 'unit', 'tax'));
    }

    /**
     * Display the Plan section (Products, Coupons, Links).
     *
     * @return \Illuminate\Http\Response
     */
    public function plan()
    {
        if (!Gate::check('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get data for the product modal
        $category = \App\Models\ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())
            ->where('type', '=', 'product & service')->get()->pluck('name', 'id');
        $unit = \App\Models\ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');
        $tax = \App\Models\Tax::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');

        return view('finance.plan.index', compact('category', 'unit', 'tax'));
    }

    /**
     * Store a new product from the modal form
     */
    public function storeProduct(Request $request)
    {
        if (!\Auth::user()->can('create product & service')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        $rules = [
            'name' => 'required',
            'nickname' => 'nullable|string',
            'price' => 'required|numeric',
            'striked_price' => 'nullable|numeric',
            'tax_slab' => 'nullable|string',
            'tax_rate' => 'nullable|numeric',
            'hsn_sac_no' => 'nullable|string',
            'redirect_url' => 'nullable|url',
            'description' => 'nullable|string',
            'invoice_footer' => 'nullable|string',
            'is_free_trial' => 'nullable|boolean',
            'is_subscription' => 'nullable|boolean',
            'trial_duration_type' => 'nullable|string',
            'trial_duration' => 'nullable|integer',
            'trial_price' => 'nullable|numeric',
            'is_cancellable' => 'nullable|boolean',
            'billed_every' => 'nullable|integer',
            'billing_cycle_type' => 'nullable|string',
            'billing_cycle_display' => 'nullable|string',
            'restrict_one_time' => 'nullable|boolean',
            'emi_options' => 'nullable|boolean',
            'show_shipping_field' => 'nullable|boolean',
            'payment_gateway' => 'nullable|boolean',
            'skip_gst_form' => 'nullable|boolean',
            'pro_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 422);
        }

        try {
            // Generate unique SKU
            $sku = $request->nickname ?: Str::slug($request->name);
            $originalSku = $sku;
            $counter = 1;

            while (\App\Models\ProductService::where('sku', $sku)
                ->where('created_by', \Auth::user()->creatorId())->exists()) {
                $sku = $originalSku . '-' . $counter;
                $counter++;
            }

            $productService = new \App\Models\ProductService();
            $productService->name = $request->name;
            $productService->sku = $sku;
            $productService->description = $request->description;
            $productService->sale_price = $request->price;
            $productService->purchase_price = $request->striked_price ?: 0;
            $productService->tax_id = $request->tax_slab ?: '';
            $productService->type = 'product';
            $productService->category_id = 0;
            $productService->unit_id = 0;
            $productService->sale_chartaccount_id = 0;
            $productService->expense_chartaccount_id = 0;
            $productService->created_by = \Auth::user()->creatorId();

            // Handle image upload
            if ($request->hasFile('pro_image')) {
                $image = $request->file('pro_image');
                $fileName = time() . '_' . $image->getClientOriginalName();
                $dir = 'uploads/pro_image';
                $path = \App\Models\Utility::upload_file($request, 'pro_image', $fileName, $dir, []);
                if ($path) {
                    $productService->pro_image = $fileName;
                }
            }

            $productService->save();

            return response()->json([
                'success' => true,
                'message' => __('Product successfully created.'),
                'product' => $productService
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => __('Something went wrong. Please try again.')], 500);
        }
    }

    /**
     * Display the Sales section (Subscription, Installment).
     *
     * @return \Illuminate\Http\Response
     */
    public function sales()
    {
        if (!Gate::check('manage customer')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('finance.sales.index');
    }

    /**
     * Display the Invoices section.
     *
     * @return \Illuminate\Http\Response
     */
    public function invoices()
    {
        if (!Gate::check('manage invoice')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('invoice.index');
    }

    /**
     * Display the Transactions section.
     *
     * @return \Illuminate\Http\Response
     */
    public function transactions()
    {
        if (!Gate::check('manage transaction')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('transaction.index');
    }

    /**
     * Display the Expenses section.
     *
     * @return \Illuminate\Http\Response
     */
    public function expenses()
    {
        if (!Gate::check('manage expense')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('expense.index');
    }

    /**
     * Display the Reports section.
     *
     * @return \Illuminate\Http\Response
     */
    public function reports()
    {
        if (!Gate::check('income vs expense report')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('finance.reports.index');
    }

    /**
     * Display the Payment Gateways section.
     *
     * @return \Illuminate\Http\Response
     */
    public function paymentGateways()
    {
        if (!Gate::check('manage company settings')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('settings')->with('tab', 'payment');
    }

    /**
     * Display the Business Info section.
     *
     * @return \Illuminate\Http\Response
     */
    public function businessInfo()
    {
        if (!Gate::check('manage company settings')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $businessInfo = \App\Models\BusinessInfo::getByUser(Auth::user()->creatorId());

        return view('finance.dashboard', compact('businessInfo'));
    }

    /**
     * Update business information.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBusinessInfo(Request $request)
    {
        if (!Gate::check('manage company settings')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }



        $rules = [
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|max:255',
            'company_phone' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'currency' => 'required|string|max:10',
            'business_gst' => 'nullable|string|max:50',
            'business_state' => 'required|string|max:100',
            'business_logo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'agree_gst_change' => 'nullable|in:0,1',
            'street_address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'pincode' => 'required|string|max:20',
            'state_prov_region' => 'required|string|max:100',
            'address_country' => 'required|string|max:100',
            'time_zone' => 'required|string|max:100',
            'contact_person_name' => 'required|string|max:255',
            'job_position' => 'required|string|max:100',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $data = $request->except(['_token', 'business_logo']);

            // Handle checkbox properly - ensure it's 0 or 1
            $data['agree_gst_change'] = $request->input('agree_gst_change', 0);

            // Filter out empty values (except for checkbox fields)
            $filteredData = [];
            foreach ($data as $key => $value) {
                if ($key === 'agree_gst_change' || (!empty($value) && trim($value) !== '')) {
                    $filteredData[$key] = $value;
                }
            }
            $data = $filteredData;

            // Handle business logo upload
            if ($request->hasFile('business_logo')) {
                $file = $request->file('business_logo');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs('business_logos', $fileName, 'public');
                $data['business_logo'] = $filePath;
            }

            $businessInfo = \App\Models\BusinessInfo::updateOrCreateForUser(
                Auth::user()->creatorId(),
                $data
            );

            return response()->json([
                'success' => true,
                'message' => __('Profile updated successfully'),
                'data' => $businessInfo,
                'saved_fields' => count($data)
            ]);

        } catch (\Exception $e) {
            Log::error('Business Info Update Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Get total revenue for the creator.
     *
     * @param int $creatorId
     * @return float
     */
    private function getTotalRevenue($creatorId)
    {
        // Calculate invoice total by getting all invoices and summing their totals
        $invoices = Invoice::where('created_by', $creatorId)
            ->where('status', '!=', 'Draft')
            ->get();

        $invoiceTotal = 0;
        foreach ($invoices as $invoice) {
            $invoiceTotal += $invoice->getTotal();
        }

        $revenueTotal = Revenue::where('created_by', $creatorId)->sum('amount');

        return $invoiceTotal + $revenueTotal;
    }

    /**
     * Get total expenses for the creator.
     *
     * @param int $creatorId
     * @return float
     */
    private function getTotalExpenses($creatorId)
    {
        // Calculate bill total by getting all bills and summing their totals
        $bills = Bill::where('created_by', $creatorId)->get();

        $billTotal = 0;
        foreach ($bills as $bill) {
            $billTotal += $bill->getTotal();
        }

        $expenseTotal = Expense::where('created_by', $creatorId)->sum('amount');

        return $billTotal + $expenseTotal;
    }

    /**
     * Get total number of invoices for the creator.
     *
     * @param int $creatorId
     * @return int
     */
    private function getTotalInvoices($creatorId)
    {
        return Invoice::where('created_by', $creatorId)->count();
    }

    /**
     * Get recent transactions for the creator.
     *
     * @param int $creatorId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecentTransactions($creatorId)
    {
        // Get recent revenue and payment transactions
        $revenues = Revenue::where('created_by', $creatorId)
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($revenue) {
                return [
                    'type' => 'income',
                    'description' => $revenue->description ?? 'Revenue',
                    'amount' => $revenue->amount,
                    'date' => $revenue->date,
                ];
            });

        $payments = Payment::where('created_by', $creatorId)
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($payment) {
                return [
                    'type' => 'expense',
                    'description' => $payment->description ?? 'Payment',
                    'amount' => $payment->amount,
                    'date' => $payment->date,
                ];
            });

        return $revenues->merge($payments)->sortByDesc('date')->take(10);
    }

    /**
     * Get monthly financial data for charts.
     *
     * @param int $creatorId
     * @return array
     */
    private function getMonthlyFinancialData($creatorId)
    {
        $months = [];
        $revenues = [];
        $expenses = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $months[] = $month->format('M Y');

            // Calculate monthly revenue
            $monthlyRevenue = Invoice::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->where('status', '!=', 'Draft')
                ->sum('getTotal');

            $monthlyRevenue += Revenue::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('amount');

            $revenues[] = $monthlyRevenue;

            // Calculate monthly expenses
            $monthlyExpense = Bill::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('getTotal');

            $monthlyExpense += Expense::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('amount');

            $expenses[] = $monthlyExpense;
        }

        return [
            'months' => $months,
            'revenues' => $revenues,
            'expenses' => $expenses,
        ];
    }

    /**
     * Get finance overview data for API/AJAX requests.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFinanceOverview()
    {
        $user = Auth::user();
        $creatorId = $user->creatorId();

        $data = [
            'total_revenue' => $this->getTotalRevenue($creatorId),
            'total_expenses' => $this->getTotalExpenses($creatorId),
            'total_invoices' => $this->getTotalInvoices($creatorId),
            'net_profit' => $this->getTotalRevenue($creatorId) - $this->getTotalExpenses($creatorId),
            'monthly_data' => $this->getMonthlyFinancialData($creatorId),
        ];

        return response()->json($data);
    }

    /**
     * Store a new coupon from the modal form
     */
    public function storeCoupon(Request $request)
    {
        // Temporarily disable permission check for testing
        // if (!\Auth::user()->can('create coupon')) {
        //     return response()->json(['error' => __('Permission denied.')], 403);
        // }

        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:coupons,code',
            'discount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:percentage,fixed',
            'limit' => 'required|integer|min:1',
            'product_id' => 'nullable|exists:product_services,id',
            'start_date' => 'nullable|date|after_or_equal:today',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'description' => 'nullable|string|max:1000',
        ];

        // Additional validation for percentage discount
        if ($request->discount_type === 'percentage' && $request->discount > 100) {
            return response()->json([
                'success' => false,
                'errors' => ['discount' => [__('Percentage discount cannot exceed 100%.')]]
            ], 422);
        }

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get product name if product_id is provided
            $productName = null;
            if ($request->product_id) {
                $product = \App\Models\ProductService::find($request->product_id);
                $productName = $product ? $product->name : null;
            }

            $coupon = new \App\Models\Coupon();
            $coupon->name = $request->name;
            $coupon->code = strtoupper($request->code);
            $coupon->product_id = $request->product_id;
            $coupon->product_name = $productName;
            $coupon->discount = $request->discount;
            $coupon->discount_type = $request->discount_type;
            $coupon->limit = $request->limit;
            $coupon->start_date = $request->start_date;
            $coupon->end_date = $request->end_date;
            $coupon->description = $request->description;
            $coupon->is_active = $request->has('is_active') ? 1 : 0;
            $coupon->created_by = \Auth::user()->creatorId();
            $coupon->save();

            return response()->json([
                'success' => true,
                'message' => __('Coupon created successfully.'),
                'coupon' => $coupon
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Store a new subscription
     */
    public function storeSubscription(Request $request)
    {
        $rules = [
            'customer_id' => 'required',
            'customer_type' => 'required|in:customer,lead',
            'customer_email' => 'required|email',
            'product_id' => 'required|exists:product_services,id',
            'start_date' => 'required|date',
            'product_price' => 'required|numeric|min:0',
            'down_payment' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'total_emis' => 'required|integer|min:1',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly',
            'payment_method' => 'required|in:offline,online',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get customer/lead and product details
            $customer = null;
            $customerName = '';

            if ($request->customer_type === 'customer') {
                $customer = \App\Models\Customer::where('id', $request->customer_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();
                $customerName = $customer ? $customer->name : '';
            } elseif ($request->customer_type === 'lead') {
                $customer = \App\Models\Lead::where('id', $request->customer_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();
                $customerName = $customer ? $customer->name : '';
            }

            $product = \App\Models\ProductService::find($request->product_id);

            if (!$customer || !$product) {
                return response()->json([
                    'success' => false,
                    'message' => __('Customer/Lead or Product not found.')
                ], 404);
            }

            // Calculate amounts
            $productPrice = $request->product_price;
            $downPayment = $request->down_payment ?? 0;
            $discountAmount = $request->discount_amount ?? 0;
            $totalEmis = $request->total_emis;

            $remainingAmount = $productPrice - $downPayment - $discountAmount;
            $emiAmount = $remainingAmount / $totalEmis;
            $pendingAmount = $remainingAmount;

            // Calculate next EMI date
            $startDate = \Carbon\Carbon::parse($request->start_date);
            $nextEmiDate = $startDate->copy();

            switch ($request->billing_cycle) {
                case 'monthly':
                    $nextEmiDate->addMonth();
                    break;
                case 'quarterly':
                    $nextEmiDate->addMonths(3);
                    break;
                case 'yearly':
                    $nextEmiDate->addYear();
                    break;
            }

            // Create subscription
            $subscription = new \App\Models\Subscription();
            $subscription->subscription_id = \App\Models\Subscription::generateSubscriptionId();
            $subscription->customer_id = $customer->id;
            $subscription->customer_name = $customerName;
            $subscription->customer_email = $request->customer_email;
            $subscription->customer_phone = $request->customer_phone;
            $subscription->product_id = $product->id;
            $subscription->product_name = $product->name;
            $subscription->product_price = $productPrice;
            $subscription->down_payment = $downPayment;
            $subscription->paid_amount = $downPayment; // Initially only down payment is paid
            $subscription->pending_amount = $pendingAmount;
            $subscription->discount_amount = $discountAmount;
            $subscription->status = 'active';
            $subscription->next_emi_date = $nextEmiDate;
            $subscription->start_date = $startDate;
            $subscription->emi_count = 0;
            $subscription->total_emis = $totalEmis;
            $subscription->emi_amount = $emiAmount;
            $subscription->billing_cycle = $request->billing_cycle;
            $subscription->notes = $request->description;
            $subscription->created_by = \Auth::user()->creatorId();
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription created successfully.'),
                'subscription' => $subscription
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Get subscription details
     */
    public function getSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'subscription' => $subscription
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong.')
            ], 500);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->status = 'cancelled';
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription cancelled successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to cancel subscription.')
            ], 500);
        }
    }

    /**
     * Update subscription notes
     */
    public function updateSubscriptionNotes(Request $request, $id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->notes = $request->notes;
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Notes updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update notes.')
            ], 500);
        }
    }

    /**
     * Update subscription
     */
    public function updateSubscription(Request $request, $id)
    {
        $rules = [
            'status' => 'required|in:active,cancelled,paused,expired',
            'next_emi_date' => 'nullable|date',
            'paid_amount' => 'nullable|numeric|min:0',
            'pending_amount' => 'nullable|numeric|min:0',
            'receipt_url' => 'nullable|url',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->status = $request->status;
            if ($request->next_emi_date) {
                $subscription->next_emi_date = $request->next_emi_date;
            }
            if ($request->paid_amount !== null) {
                $subscription->paid_amount = $request->paid_amount;
            }
            if ($request->pending_amount !== null) {
                $subscription->pending_amount = $request->pending_amount;
            }
            if ($request->receipt_url) {
                $subscription->receipt_url = $request->receipt_url;
            }
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update subscription.')
            ], 500);
        }
    }

    /**
     * Delete subscription
     */
    public function deleteSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->delete();

            return response()->json([
                'success' => true,
                'message' => __('Subscription deleted successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to delete subscription.')
            ], 500);
        }
    }

    /**
     * Search contacts from both Leads and Customers tables
     */
    public function searchContacts(Request $request)
    {
        try {
            $search = $request->get('search', '');
            $contacts = collect();

            // Search in Customers table
            $customers = \App\Models\Customer::where('created_by', \Auth::user()->creatorId())
                ->where('is_active', 1)
                ->when($search, function ($query, $search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('contact', 'like', "%{$search}%");
                    });
                })
                ->get()
                ->map(function ($customer) {
                    return [
                        'id' => $customer->id,
                        'type' => 'customer',
                        'name' => $customer->name,
                        'email' => $customer->email,
                        'phone' => $customer->contact,
                        'display_name' => $customer->name . ' (Customer)',
                        'display_info' => $customer->email . ($customer->contact ? ' | ' . $customer->contact : ''),
                    ];
                });

            // Search in Leads table
            $leads = \App\Models\Lead::where('created_by', \Auth::user()->creatorId())
                ->where('is_active', 1)
                ->where('is_converted', 0) // Only non-converted leads
                ->when($search, function ($query, $search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%");
                    });
                })
                ->get()
                ->map(function ($lead) {
                    return [
                        'id' => $lead->id,
                        'type' => 'lead',
                        'name' => $lead->name,
                        'email' => $lead->email,
                        'phone' => $lead->phone,
                        'display_name' => $lead->name . ' (Lead)',
                        'display_info' => $lead->email . ($lead->phone ? ' | ' . $lead->phone : ''),
                    ];
                });

            // Combine and sort results
            $contacts = $customers->concat($leads)->sortBy('name');

            return response()->json([
                'success' => true,
                'contacts' => $contacts->values()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to fetch contacts.')
            ], 500);
        }
    }

    /**
     * Get specific contact details by type and ID
     */
    public function getContactDetails($type, $id)
    {
        try {
            $contact = null;

            if ($type === 'customer') {
                $contact = \App\Models\Customer::where('id', $id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();

                if ($contact) {
                    $contactData = [
                        'id' => $contact->id,
                        'type' => 'customer',
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'phone' => $contact->contact,
                        'emails' => array_filter([$contact->email]), // Can be extended for multiple emails
                        'phones' => array_filter([$contact->contact]), // Can be extended for multiple phones
                    ];
                }
            } elseif ($type === 'lead') {
                $contact = \App\Models\Lead::where('id', $id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();

                if ($contact) {
                    $contactData = [
                        'id' => $contact->id,
                        'type' => 'lead',
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'phone' => $contact->phone,
                        'emails' => array_filter([$contact->email]), // Can be extended for multiple emails
                        'phones' => array_filter([$contact->phone]), // Can be extended for multiple phones
                    ];
                }
            }

            if (!$contact) {
                return response()->json([
                    'success' => false,
                    'message' => __('Contact not found.')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'contact' => $contactData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to fetch contact details.')
            ], 500);
        }
    }
}
