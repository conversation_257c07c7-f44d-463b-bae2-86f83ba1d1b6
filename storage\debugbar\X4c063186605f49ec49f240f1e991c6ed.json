{"__meta": {"id": "X4c063186605f49ec49f240f1e991c6ed", "datetime": "2025-07-30 08:38:46", "utime": **********.686736, "method": "GET", "uri": "/finance/sales/products/search", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753864724.393832, "end": **********.686779, "duration": 2.2929470539093018, "duration_str": "2.29s", "measures": [{"label": "Booting", "start": 1753864724.393832, "relative_start": 0, "end": **********.409657, "relative_end": **********.409657, "duration": 2.015825033187866, "duration_str": "2.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409702, "relative_start": 2.0158700942993164, "end": **********.686783, "relative_end": 4.0531158447265625e-06, "duration": 0.2770810127258301, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686776, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1041\" onclick=\"\">app/Http/Controllers/FinanceController.php:1041-1084</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00925, "accumulated_duration_str": "9.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6057448, "duration": 0.00674, "duration_str": "6.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 72.865}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.647722, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 72.865, "width_percent": 15.676}, {"sql": "select * from `product_services` where `created_by` = 79 order by `name` asc", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1055}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.661584, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1055", "source": "app/Http/Controllers/FinanceController.php:1055", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1055", "ajax": false, "filename": "FinanceController.php", "line": "1055"}, "connection": "radhe_same", "start_percent": 88.541, "width_percent": 11.459}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-108169547 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-108169547\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-170788433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-170788433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-541303053 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-541303053\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1486929922 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjlWWDExZEEySnE1eGxKRldZV0s3RHc9PSIsInZhbHVlIjoieXNRczY3Lzg0eXh3THhqKzVnMnFYTGZ6Y3VYUmZmaW9mbHo4U3p6UU9Pd1NCeDY0NjFPN1J3Z2dpcVhSZUpoMU9vUTVELzBIMlRCNUpDY1JsUk1pS1BySzBabHVaV01sNG13dnZMdWVqS2E1NnRIV3hmSCtydmU1UEpLRklzQWN4UmdIWDkzdW81NmtBQVF3bGJCcS9Pc1VlK1lZdStBVzRlOUJRSWx5aVk5VUVYek5WdUJ6cTlEcWhNUStxaGYzWmFWV3VOMEN1VmxLdnFsS1FYbnR1cW56a1BtOUErVnRTN1A3d0Nvc0Y2SGF1TW1PVVZPelB4NmxRRHB1OGZnTTdjS2o2SE1PWFAxRXNleEdGQ2k5V2hYT1NSME43YzJnUGxpVnJ2eXo4eGRxbjNIRjZ4UnJOZzVwR0V0b2Y3WXpWWlJIdDFxSTMvL0hnZ0g2RHZCOEZ2ZGVGcDZ6a0lSNTJ6OWdVaGdRWk55eE1ObjdTaS9RVnRlVkgveE9zTjBpM3BUdWtYaHlpMVhUUkVGWGQ4ZGxMbG5sL0tjSVBXWWh4QVNqOVlNTmorS0Y2OWpQY282RjhraVFQM3JuZ2l3WmYrNnI0SEpsajltNm45dDY1V2NTcDJDNWFmcGxVUklDcnUyTmJvZXlSNXIwTWl3RFVacE5Ya2JKZzF1QXJoUk4iLCJtYWMiOiJmYmMwYzBmNGM4ZTFlMTQ1OTIwNzRiMGMyYmNhOTdmNmYyMTlkOTIzMzY1NjhkN2QzYjAyYTJmZTU2MGYzODEzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImlBY0dmcFUxUEFrTjRZWjVCTDdEckE9PSIsInZhbHVlIjoiT1psOWxFc0pVS0xVd0tidVZEb3VuTzdGd3I2OW5tQ003WHZlVWt6eVU3Q1NQMjBoL0hqTDdyVHhKeGljWG50aFByK2piQnF4RkNFejJ6OFhtQmhEZm5pVVBuZTdacEZaVHVpcVhQd2ZxaUNvL3B1cW9kMnVjMHJYc1hYaG5LSlhLYjlpZTV4TkNFRERSRUU5WGtmRSs5dVRBMTBwVVl0N2R5R0hYZHVzb1ZGSXBtSVN4ZEltazV6VVdCS1VwN3FSR04wTEFvNW1WRHlXQ085dTVvaGNFYnJUU0RCd2dwazdjMStzZUYwbnFTeE1vOGt5eWEyREFFTzdHMk53aW1qUzZSS0VwN0UyNGIzaFVVU0FWZTE4YldPcHlSdkxuUUZNMTRwR2NWKzN6emRLRVh5SEtRNXFPeW03ZzZUeTJnQ1lQVWRueUk1UEpNUVNEZUdQd1pWOEtxS2RuNWhHZjVCRWNYbERMLzNnbGNNNFIwK3dwcG55YnlSSHVzNTNsQmlTY25ReWtRWlM1N2ZsWkZqZmxvOFRKRC9MRG50SjdMU09tdWp6MG44TTE1Yk1LSzlFNWpWSGZKdHBhaEJWc2gvQUxwZDNjSlN1cnF6bU5LV0hVRXJpUEN3K3ZrcjRmbFM2L2R2emhoMVFtUWoxeFhPYXZ5SUJHVWVtT2VZVFdpNEsiLCJtYWMiOiI1YjljNWE5NTgxMGQwY2JjNGFhNTY1YzJmMzRkMWJlYWQ2NjU3YzMyOTI0NWVmYjM0NTYzN2Q5MzQzMWViZDI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486929922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-730650793 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730650793\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2129807389 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:38:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcyWkh2WnJqUjBoOGUvYnBadjU0VGc9PSIsInZhbHVlIjoiTXB6MC9KQ1B3RDFWVUNWQ3JENjFLMW5Fc1kwUzFEYSsrbFBhdHRtb3h2SFd0WTBvWmxQbGpPY1lXR1NYUEFRMyswMkRCYS9CUDl3NlNpZFNwbVY2TENmSktiS0JXOGVXNVBva1FSOUN1U3drWVJReWsyeHA4SjliY2x1N1JDN3RFSXVMcWV3d2RXNDI2OTgzbE5wK1NhbzB2SE1NZFFoUy9GSENCTjk5bEVKRTRuTzdlTGtCbEtENG1tTmNEWm92SzN3MG8zaFNPVmNVdHNCUUtwNjZ0RlErRXIzM3RxbS9RT29odnhBT3VZVXoySnBwK2xCbnNkajVnZHJCeTRtZUFsaE1FWEkzLzJZSHJzWXo1QTdkUEw2VXVJMXA4aHNkUWNFZlZzeWlRRFlKNjlQMk85ckwvRWJKSThhYTdzemFsQk43Vis5RFJQUkRqb1ZnWlF3dlB4c1BXdXZkTjJDOHlESXhsTjljNFZ6ckNwMHNFZ1NIQ01obHgrWVAzb1BLcllGNjV2QnhaRHo1ZjdRa3BqUm5BaTVJejBWM1ZDeEh4dE5uWVlCbVhFcDYxbFplY20rYzhWMHc5ZHFoQ0VYVnQ2WmtIWFBCcDhOSlpYSW91V1BzYmJWd2FrQWhMNnUyYXZtYmJ0cjFsZURxRUJaOUh6WEd1U1FwOFU0cnZTUjciLCJtYWMiOiJhNTU2Njg1NWFkZmVjNTZjMzM1N2JkY2I5ODlmNGJlYzFhZWZmMjEzZTE2NDIyYTAxMzdiOTY0ZGI1NzMwMmU2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:38:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVaQW4xcE9LTnlSbjR0MFlsMVJDNWc9PSIsInZhbHVlIjoidWtvb29VUEVXNG5LZlFCN0FqUHhIU0ZrNGo1TVNVdzFFTzh4VjVaZ1RkcjdJUUkwM3hoamc3SExYVFJoQ1R0QzRtK1NyWE1hRXJKSFYrTFVlZjF2L3VyV0tBVGZuN3ZFWDlEMlp4T24vbGFFdjd4TWMrTE8wR2NFQ01iSng5OVZ3d2FBWThDc1gvaUkrSEF4SHh1alJGclFleWRmVE9vM3pvYVZOc1RSekhDN1ZmaWJmd0RVaWg2bFcwRmtTbERSY3orUE44S1lZYUo2b29VUWlkaTlyQWd4c1V5RFo2UVBweXpSNXpxYWhiZUVON2lZYWdjcUpLOWxsaVI3ZHNkWElMQmMxMEtjbjd4NGpEMXExMTM4RFM2KzNqVVlqZC9GRU8rU2ZHb0hJQ1VWUzJNclI3STJBZWk2T1lwSHB0cXpVMVJkWlRiSlpGRWpZdTZ1YXZqSnkwTUJxSStoVGNESDN5U295S09yOHcyN1prZVMxMXdSTFJRZWhvMmdvTmgzZEQvSUNuam1QOGtTZWRDSGJoYVBKZHhkWmNoSjdkV1NjaGxIS0hXdlFHYkVwTTVDWDFPTk5XTlphVXZYTkFmQkxaTitRcnVNQitVRTdBNytXOW1OcG1kR2R3UWxCanNOVWFGU1RIV2JwaG1TWUpnTzkwN2FySnltanZvRTAvaWMiLCJtYWMiOiI2ZjVmOTY0ZjAzY2E0ZWE0ZmFlNTA2MDRkYmU0MTlmOWI4M2UzYTBiMmJiNWQ3NjRjMjA0MzJhNzA5NDExNzM0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:38:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcyWkh2WnJqUjBoOGUvYnBadjU0VGc9PSIsInZhbHVlIjoiTXB6MC9KQ1B3RDFWVUNWQ3JENjFLMW5Fc1kwUzFEYSsrbFBhdHRtb3h2SFd0WTBvWmxQbGpPY1lXR1NYUEFRMyswMkRCYS9CUDl3NlNpZFNwbVY2TENmSktiS0JXOGVXNVBva1FSOUN1U3drWVJReWsyeHA4SjliY2x1N1JDN3RFSXVMcWV3d2RXNDI2OTgzbE5wK1NhbzB2SE1NZFFoUy9GSENCTjk5bEVKRTRuTzdlTGtCbEtENG1tTmNEWm92SzN3MG8zaFNPVmNVdHNCUUtwNjZ0RlErRXIzM3RxbS9RT29odnhBT3VZVXoySnBwK2xCbnNkajVnZHJCeTRtZUFsaE1FWEkzLzJZSHJzWXo1QTdkUEw2VXVJMXA4aHNkUWNFZlZzeWlRRFlKNjlQMk85ckwvRWJKSThhYTdzemFsQk43Vis5RFJQUkRqb1ZnWlF3dlB4c1BXdXZkTjJDOHlESXhsTjljNFZ6ckNwMHNFZ1NIQ01obHgrWVAzb1BLcllGNjV2QnhaRHo1ZjdRa3BqUm5BaTVJejBWM1ZDeEh4dE5uWVlCbVhFcDYxbFplY20rYzhWMHc5ZHFoQ0VYVnQ2WmtIWFBCcDhOSlpYSW91V1BzYmJWd2FrQWhMNnUyYXZtYmJ0cjFsZURxRUJaOUh6WEd1U1FwOFU0cnZTUjciLCJtYWMiOiJhNTU2Njg1NWFkZmVjNTZjMzM1N2JkY2I5ODlmNGJlYzFhZWZmMjEzZTE2NDIyYTAxMzdiOTY0ZGI1NzMwMmU2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:38:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVaQW4xcE9LTnlSbjR0MFlsMVJDNWc9PSIsInZhbHVlIjoidWtvb29VUEVXNG5LZlFCN0FqUHhIU0ZrNGo1TVNVdzFFTzh4VjVaZ1RkcjdJUUkwM3hoamc3SExYVFJoQ1R0QzRtK1NyWE1hRXJKSFYrTFVlZjF2L3VyV0tBVGZuN3ZFWDlEMlp4T24vbGFFdjd4TWMrTE8wR2NFQ01iSng5OVZ3d2FBWThDc1gvaUkrSEF4SHh1alJGclFleWRmVE9vM3pvYVZOc1RSekhDN1ZmaWJmd0RVaWg2bFcwRmtTbERSY3orUE44S1lZYUo2b29VUWlkaTlyQWd4c1V5RFo2UVBweXpSNXpxYWhiZUVON2lZYWdjcUpLOWxsaVI3ZHNkWElMQmMxMEtjbjd4NGpEMXExMTM4RFM2KzNqVVlqZC9GRU8rU2ZHb0hJQ1VWUzJNclI3STJBZWk2T1lwSHB0cXpVMVJkWlRiSlpGRWpZdTZ1YXZqSnkwTUJxSStoVGNESDN5U295S09yOHcyN1prZVMxMXdSTFJRZWhvMmdvTmgzZEQvSUNuam1QOGtTZWRDSGJoYVBKZHhkWmNoSjdkV1NjaGxIS0hXdlFHYkVwTTVDWDFPTk5XTlphVXZYTkFmQkxaTitRcnVNQitVRTdBNytXOW1OcG1kR2R3UWxCanNOVWFGU1RIV2JwaG1TWUpnTzkwN2FySnltanZvRTAvaWMiLCJtYWMiOiI2ZjVmOTY0ZjAzY2E0ZWE0ZmFlNTA2MDRkYmU0MTlmOWI4M2UzYTBiMmJiNWQ3NjRjMjA0MzJhNzA5NDExNzM0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:38:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129807389\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}