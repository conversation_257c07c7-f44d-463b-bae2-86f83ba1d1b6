{"__meta": {"id": "X3234b60df51ffe122e5d600f429f8e15", "datetime": "2025-07-30 05:43:39", "utime": **********.945187, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854218.974565, "end": **********.945232, "duration": 0.9706668853759766, "duration_str": "971ms", "measures": [{"label": "Booting", "start": 1753854218.974565, "relative_start": 0, "end": **********.871012, "relative_end": **********.871012, "duration": 0.896446943283081, "duration_str": "896ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.871029, "relative_start": 0.****************, "end": **********.945235, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "74.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gk1nrZq1kQfXRHCVZcFEpShYD1UffbF0ErFHlhlk", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-652971119 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-652971119\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1666574848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1666574848\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1667359192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667359192\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1619233400 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619233400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770206476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-770206476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1786575896 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:43:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9hMnNlNUlDbVgyNDdFczRENkJ2MXc9PSIsInZhbHVlIjoiNDZ6S1FjeWJ3cTg2VFJPR0FtWkpwZmJPU3dVckhKUUErVzRxWlRaUG5MNlhJRllqRE9zQUZON1g4UFBhSFEvMmFFeXlHTkY3ZWNVZVp1SXJxSkQwb0JXb0hXRGJUM3pqbTk5bEl4TEFRaERCaTdSdEhVR1JHc21ST3RtaFNOVk9hSEhTOUFoUnlra0lOTWZGTzlzbjg2MU1nZTM5TEFTdS9lRUlpVkRhdVoyZUtmUmVuVEYyaWorVGltemRObFJlTTAzckZqc1RFdWlvYzdLT25udTN3aS8xRWU2a0s3OWFTYzFuNjJQdFR5NXR0UTBBdEgra0Z1NWlQU3ppL2tPUHhROUxwZG96eTJVdURycVNXTUF2Y25jeGp6czNlc1BkSXlGU1dkdk1xK096TXRqMHV5bDRCZzBBRTl3c2dodkVFclE0ZG1aSUQ4dzVxdkZJc1hNVi9CRkRSWEl3MGdkMXZDLy9zL0hzNWlhbC9ldzNleUZ2aVhLNmFxU0pkM2FCQXNTdFowRU56NXRkUmlrczgyZU9yRElMTjFGeE5uZDdmR3dDWHUxempQNmZMRlhNVzdWc2NpbzJPVmhxSzNmWWVDMkpobWFXTVFMenRMeHBCeHJGV3o1b2x1N0FuZTFPUkVCRGNZZjFZUGJmMnlLaUFHckFsandrYzRHVlJwZ1giLCJtYWMiOiJkN2QwYjZhN2UxNmYxYjFhMzNjY2EwMzBiNjlkZjllNTIyMmVhZDliOGE3NWQzYjUzMmM1YzYzM2E3NmEzZTViIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:43:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVzTmYwamtBcmE4MmpsbDVsY3dHOHc9PSIsInZhbHVlIjoianZRZGw1cmdwNWtBa2VBRW8zVmNZWmY3c2VPc0JOS0ZkQnFrZi80MjMxN0dzWmdOcFExL2N4ZzY2ZGNhaFRuZkEvZ3RMNi9PNHBLR3NtcHdvUUJhVWgrTlJON0xQWTlzdmp6VWpaSW5VVHY3d1dEWG9rcXN3WlllSnZrRmRxTW9LS0tyV2tyVk94VWswRXdqQy9MVXBiemk1ZEU5WUJvNVFOellXSUtoUGF6eTJZZVdZbmFrbkJTQllCdTBmOGxRWE45bVRvWVFBOXJCMTJuTHpuelR5djZUZXRhRXRJRXFTZjlocnFXaUh2MmFVcXhuVDl0Y0NyR2E3YVFWREcwajcxOVhHZWFWdVFtczlwWGxkZGZmTFcvbUoyQStMOGpMU2VoL3ZRMlFYWld2azVxNWV2cUgxMndpWHg5TllDY2N4a3hRWXR1UElqdU41SlZZVzBNZDVSSU82bGJvVzh2UGFwRE1rY05CK2xqTCtvQ0l3bmZJZ0ZaQldXc0xwa2V2V0xFYWtQZ1RudUZNTnVjUENuR0Q2UTdmOElHVnhaN1BjN1Rqczk5N3lZaGh5VzBUcTVPbjd3OFJkWnRva3hvZ2lhTXBKSTQ1VnNISnpvZmR5Ymk2ajd1d3BwZlFKYUM1UFpEdk15Q294aGQ2KzdlbksrMURVYWI5U3VTZkIyS1kiLCJtYWMiOiJhOWEyNzRkY2UyYzAwNWRkYjg5NWIzYzczNjIwMThiZThhODEyMmMwZjY4ZjgwOGY2OTY5ZTcxZWZlZjA3MGEyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:43:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9hMnNlNUlDbVgyNDdFczRENkJ2MXc9PSIsInZhbHVlIjoiNDZ6S1FjeWJ3cTg2VFJPR0FtWkpwZmJPU3dVckhKUUErVzRxWlRaUG5MNlhJRllqRE9zQUZON1g4UFBhSFEvMmFFeXlHTkY3ZWNVZVp1SXJxSkQwb0JXb0hXRGJUM3pqbTk5bEl4TEFRaERCaTdSdEhVR1JHc21ST3RtaFNOVk9hSEhTOUFoUnlra0lOTWZGTzlzbjg2MU1nZTM5TEFTdS9lRUlpVkRhdVoyZUtmUmVuVEYyaWorVGltemRObFJlTTAzckZqc1RFdWlvYzdLT25udTN3aS8xRWU2a0s3OWFTYzFuNjJQdFR5NXR0UTBBdEgra0Z1NWlQU3ppL2tPUHhROUxwZG96eTJVdURycVNXTUF2Y25jeGp6czNlc1BkSXlGU1dkdk1xK096TXRqMHV5bDRCZzBBRTl3c2dodkVFclE0ZG1aSUQ4dzVxdkZJc1hNVi9CRkRSWEl3MGdkMXZDLy9zL0hzNWlhbC9ldzNleUZ2aVhLNmFxU0pkM2FCQXNTdFowRU56NXRkUmlrczgyZU9yRElMTjFGeE5uZDdmR3dDWHUxempQNmZMRlhNVzdWc2NpbzJPVmhxSzNmWWVDMkpobWFXTVFMenRMeHBCeHJGV3o1b2x1N0FuZTFPUkVCRGNZZjFZUGJmMnlLaUFHckFsandrYzRHVlJwZ1giLCJtYWMiOiJkN2QwYjZhN2UxNmYxYjFhMzNjY2EwMzBiNjlkZjllNTIyMmVhZDliOGE3NWQzYjUzMmM1YzYzM2E3NmEzZTViIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:43:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVzTmYwamtBcmE4MmpsbDVsY3dHOHc9PSIsInZhbHVlIjoianZRZGw1cmdwNWtBa2VBRW8zVmNZWmY3c2VPc0JOS0ZkQnFrZi80MjMxN0dzWmdOcFExL2N4ZzY2ZGNhaFRuZkEvZ3RMNi9PNHBLR3NtcHdvUUJhVWgrTlJON0xQWTlzdmp6VWpaSW5VVHY3d1dEWG9rcXN3WlllSnZrRmRxTW9LS0tyV2tyVk94VWswRXdqQy9MVXBiemk1ZEU5WUJvNVFOellXSUtoUGF6eTJZZVdZbmFrbkJTQllCdTBmOGxRWE45bVRvWVFBOXJCMTJuTHpuelR5djZUZXRhRXRJRXFTZjlocnFXaUh2MmFVcXhuVDl0Y0NyR2E3YVFWREcwajcxOVhHZWFWdVFtczlwWGxkZGZmTFcvbUoyQStMOGpMU2VoL3ZRMlFYWld2azVxNWV2cUgxMndpWHg5TllDY2N4a3hRWXR1UElqdU41SlZZVzBNZDVSSU82bGJvVzh2UGFwRE1rY05CK2xqTCtvQ0l3bmZJZ0ZaQldXc0xwa2V2V0xFYWtQZ1RudUZNTnVjUENuR0Q2UTdmOElHVnhaN1BjN1Rqczk5N3lZaGh5VzBUcTVPbjd3OFJkWnRva3hvZ2lhTXBKSTQ1VnNISnpvZmR5Ymk2ajd1d3BwZlFKYUM1UFpEdk15Q294aGQ2KzdlbksrMURVYWI5U3VTZkIyS1kiLCJtYWMiOiJhOWEyNzRkY2UyYzAwNWRkYjg5NWIzYzczNjIwMThiZThhODEyMmMwZjY4ZjgwOGY2OTY5ZTcxZWZlZjA3MGEyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:43:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786575896\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1148982349 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gk1nrZq1kQfXRHCVZcFEpShYD1UffbF0ErFHlhlk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148982349\", {\"maxDepth\":0})</script>\n"}}