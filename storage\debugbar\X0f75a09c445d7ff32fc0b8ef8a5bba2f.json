{"__meta": {"id": "X0f75a09c445d7ff32fc0b8ef8a5bba2f", "datetime": "2025-07-30 02:43:55", "utime": **********.578648, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843432.381633, "end": **********.578732, "duration": 3.197098970413208, "duration_str": "3.2s", "measures": [{"label": "Booting", "start": 1753843432.381633, "relative_start": 0, "end": **********.371154, "relative_end": **********.371154, "duration": 2.989521026611328, "duration_str": "2.99s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.371207, "relative_start": 2.****************, "end": **********.578739, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1BvaOuqK9ORYg0qb4xeBLHau9ziYh8sM2YhIQaJD", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1518699342 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1518699342\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1140137537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1140137537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1783159632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1783159632\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1113172227 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113172227\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1146512017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146512017\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-490078481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:43:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN3c1A3eHgzWnJNY2pURHFyQlR6b1E9PSIsInZhbHVlIjoiTEtEdExMNExTcDIyQ1hnUWlPT0IrblJZMXZKOVErWWNod3RhaCtLRXgzSDRqd1ppUVVNbTBBa0dWbDl3S3RGbmpNL2ViWDFEbFVzRmo0VUVwYm1FR2ljZ2JvVlVzWkRiWnhmUGtKSW1Cd0JsZFlNY2ZhZldadGJsckhJZTFtVEhJcGZocEVJajhGRkxIZVd5TkhHeDhOZ2hJOHB3R1VGcE1XWUM5Tm5aYVBoandaQ0ZkU29kNG1kQzFKWitzZkpZVmxpMzMxKzhPZTFKVDltbzdOa0ZYNkJiQzZYdi9hT0kySDdqaUtxNGZ6VlJlMngrOFN2eWJFeW9aQUl1N0lHVzRFZnlvSlR0dTFKaEZMTk1oQWk2cDNMWVhON05JVkltdUxlTVN3R3IvYkh1SnNrZmJkUFAyMVZDOVM3YkNzdktZQ01tT2RzMXd6azJZQnE2cUh4aGJCSWxUWU9OOXFsRVNBdTB3ZldPTVpKd29TVVhlQklZVzJTYklyOVpuWDNPcEFvTElnWnMyUUhnSUM2dUJJL21vbFpsdlJsY29walNjckdqK1k3V0VWekk5aFdqVkxoVXF3VjR0T2N6SU93K2w1Q214dkdvWittcEM1K01DKzQrazJ1SFhaT2tKU0s3ZXpqUXNmdTlSKzhseVZCbEtrKzdSV2kvcE1mMWlrZ2YiLCJtYWMiOiJhYTBjZjE3ZWM5YTE5MDEyMGYwNWY1YTk2MzU1ZjM2MTAwMDYzZjBiNmJjNDlmNjhlNmQ1N2U5MDE2MjlkYjUzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:43:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVqaGpibE9zZkRlMGYvQTFiTGJyZ2c9PSIsInZhbHVlIjoiM3pxa3F5d0NnUlhxOHZFUU40dFFDMEVvbVhya05vdXNxa2ZYTzNCYkphUElSMndoUjFlTFBQT01vaWdhbG1uV01sNVBnQmdvL254Vkw3Q21ScjZ3NTJnZVcxdjFXQnJ6TmJpWUp5VDBESXpFYjlURE5hK1ZEUllGVk5iU29XZ091Mis3V2hnL1ZhRHF0ckRQWFA5THExZTNzbjdxUkJOTGxQSzNmbzBBY2JtbVhQYkNyUEpDUi96QTlwVnRrQXhpSkVZdEtwR3V1U1RwOXVaVmNVRGJTSkNUN1BZOEg3WG5EcXJ5R3hkRytrSHJlVDdlZVZoL1Z2cVpZcTRjdDA4YTF1aHRYc1hsMllueEk0MzNoeTVkWm5XNnZhcW81VUNudS80UjdVVEVKdCtKNWtxMHpJaEZ3dllvYkU1MllaS3FObTdsNG1zeG5lUElCUDM4VjlzQWY1NW1FS0tSL3dwR0V0ZGJmTkZ0Y1lhZElTTncvYkZtRTkrV0hGcVB6cExDZXo1OW5kZUtnTTd2MnQ0OGJ2SUtubHhjeGtuYnBWZzRqSzA2N1hDY09QRnJYN0ZRYUZwQ3NJcld2QTNrSEFMQmtialo1RjRkL2lOT0JlQXNnWVNDaHZOQnR2ZFFDdzQ4YzFHaVdETG5oLzJyRFhTSXE4Rlh0SHVNVnp5UVNXdVkiLCJtYWMiOiJkOGI0OTdjMDVhZWUxMmExYzU3ZDY5MWY4YzZiMmE1OGY3OTdkZDBjOWQ4MDVjMGUzNDM2ZWVkY2MwMGFjZGQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:43:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN3c1A3eHgzWnJNY2pURHFyQlR6b1E9PSIsInZhbHVlIjoiTEtEdExMNExTcDIyQ1hnUWlPT0IrblJZMXZKOVErWWNod3RhaCtLRXgzSDRqd1ppUVVNbTBBa0dWbDl3S3RGbmpNL2ViWDFEbFVzRmo0VUVwYm1FR2ljZ2JvVlVzWkRiWnhmUGtKSW1Cd0JsZFlNY2ZhZldadGJsckhJZTFtVEhJcGZocEVJajhGRkxIZVd5TkhHeDhOZ2hJOHB3R1VGcE1XWUM5Tm5aYVBoandaQ0ZkU29kNG1kQzFKWitzZkpZVmxpMzMxKzhPZTFKVDltbzdOa0ZYNkJiQzZYdi9hT0kySDdqaUtxNGZ6VlJlMngrOFN2eWJFeW9aQUl1N0lHVzRFZnlvSlR0dTFKaEZMTk1oQWk2cDNMWVhON05JVkltdUxlTVN3R3IvYkh1SnNrZmJkUFAyMVZDOVM3YkNzdktZQ01tT2RzMXd6azJZQnE2cUh4aGJCSWxUWU9OOXFsRVNBdTB3ZldPTVpKd29TVVhlQklZVzJTYklyOVpuWDNPcEFvTElnWnMyUUhnSUM2dUJJL21vbFpsdlJsY29walNjckdqK1k3V0VWekk5aFdqVkxoVXF3VjR0T2N6SU93K2w1Q214dkdvWittcEM1K01DKzQrazJ1SFhaT2tKU0s3ZXpqUXNmdTlSKzhseVZCbEtrKzdSV2kvcE1mMWlrZ2YiLCJtYWMiOiJhYTBjZjE3ZWM5YTE5MDEyMGYwNWY1YTk2MzU1ZjM2MTAwMDYzZjBiNmJjNDlmNjhlNmQ1N2U5MDE2MjlkYjUzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:43:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVqaGpibE9zZkRlMGYvQTFiTGJyZ2c9PSIsInZhbHVlIjoiM3pxa3F5d0NnUlhxOHZFUU40dFFDMEVvbVhya05vdXNxa2ZYTzNCYkphUElSMndoUjFlTFBQT01vaWdhbG1uV01sNVBnQmdvL254Vkw3Q21ScjZ3NTJnZVcxdjFXQnJ6TmJpWUp5VDBESXpFYjlURE5hK1ZEUllGVk5iU29XZ091Mis3V2hnL1ZhRHF0ckRQWFA5THExZTNzbjdxUkJOTGxQSzNmbzBBY2JtbVhQYkNyUEpDUi96QTlwVnRrQXhpSkVZdEtwR3V1U1RwOXVaVmNVRGJTSkNUN1BZOEg3WG5EcXJ5R3hkRytrSHJlVDdlZVZoL1Z2cVpZcTRjdDA4YTF1aHRYc1hsMllueEk0MzNoeTVkWm5XNnZhcW81VUNudS80UjdVVEVKdCtKNWtxMHpJaEZ3dllvYkU1MllaS3FObTdsNG1zeG5lUElCUDM4VjlzQWY1NW1FS0tSL3dwR0V0ZGJmTkZ0Y1lhZElTTncvYkZtRTkrV0hGcVB6cExDZXo1OW5kZUtnTTd2MnQ0OGJ2SUtubHhjeGtuYnBWZzRqSzA2N1hDY09QRnJYN0ZRYUZwQ3NJcld2QTNrSEFMQmtialo1RjRkL2lOT0JlQXNnWVNDaHZOQnR2ZFFDdzQ4YzFHaVdETG5oLzJyRFhTSXE4Rlh0SHVNVnp5UVNXdVkiLCJtYWMiOiJkOGI0OTdjMDVhZWUxMmExYzU3ZDY5MWY4YzZiMmE1OGY3OTdkZDBjOWQ4MDVjMGUzNDM2ZWVkY2MwMGFjZGQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:43:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490078481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-722322745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1BvaOuqK9ORYg0qb4xeBLHau9ziYh8sM2YhIQaJD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722322745\", {\"maxDepth\":0})</script>\n"}}