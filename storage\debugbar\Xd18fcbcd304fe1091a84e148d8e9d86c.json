{"__meta": {"id": "Xd18fcbcd304fe1091a84e148d8e9d86c", "datetime": "2025-07-30 08:07:20", "utime": **********.595529, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862838.662761, "end": **********.595634, "duration": 1.932873010635376, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": 1753862838.662761, "relative_start": 0, "end": **********.297409, "relative_end": **********.297409, "duration": 1.634648084640503, "duration_str": "1.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297473, "relative_start": 1.****************, "end": **********.595643, "relative_end": 9.059906005859375e-06, "duration": 0.****************, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gRcw75ygtWDVdPt6pSdOeEKUZBOgH04Mg29c0uYx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-517626528 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-517626528\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-623261930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623261930\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914666329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914666329\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1294619805 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294619805\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-920003012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-920003012\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1692985990 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:07:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZLZTA4UTVXZGFpQ093T2N0dUFadGc9PSIsInZhbHVlIjoiZ1VIZXJ3N00zbkEwRzAvYW4wY3Z6bmZYSjdJalV2ekRwSG5aYjN4MXZzSWxTL2VkWDVncmptUEMwTDJxbGJIb1VWKzJpM1Rwbms2am5LeHhIRWd1VzhzU1RNWFZkTzFvMUZBRWtqSWwybjNGS3hBWjNPYmNFWXVZMVQzUU50OVY0NFR5NG4xc05RQlJhaTNUYmM3eWRKTldvMisrTTVQK1ZZT1BJVmNNUlI0UUhtQlNvWE9YSGZ3Rzc3VER3Njc1Sk0wTGpCVlgzQVJmMnRWbTU3REozOXFjMllLWFNZQmNVNE40MVZydEdRdjVzZ3h3dGwzWTliRmI0eDVtMWE1VHdyY0cxV3hkZ3R3RWJ4eFZVK1o2RGxUUFZnTGVBdm9TTGh5aSsxVEdMMnRTb3Urd0tLZDZvRDlGR0xCRE44K0V3QnBET2NtekxYK1I0TE1NRXl1bE42SzRUMEtHQ1pwVFVTeGFGSU44UUN3QVU0Qjk5ZmNPQlFzdDhrTzQrbzJMc0d2SWFuUiszRVMxYlBiSmI2ZGg3cjRDZmNYK0pJS0xzMFhKQ29YeElXWlZucFhxOG1abXZrVWxkU0hHUXBQM0gvRCsyYjVYdW1IbnNvMmEyVXVvSkdBYWdLVVYrSlRwd1JCd0xsSXQ3NkZ3d3djaG1rT3lKUlV1NkJtU1hnQ1UiLCJtYWMiOiJhZWZmNmM5OWY5NjVhNjcyZDU1MGE4ZjA4ZmViYTA5OWYzYjQ2NGRlMWYwOTU1M2IyODk3OTMwZjUzZWU5MWEzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNQbDhhTnkzZHAxeVpIQnVVZjJEeEE9PSIsInZhbHVlIjoiS2plcFhzd1p6OFc0UzdpMDA3Zkt6UXhsK1gzRWdwYXVvRUZKQWo3VjhlelFBdjlxNGpVQ0RsL0V0SlUrMzBEN08rZUhKblhENmpKb3pScWwvU2ZZUHNuWXFJcUtGdHZxU0hEbGo4WXN2eFpQQjRwUStzTkVYbDZpYzRDUnA3RjB5TXlqZ0h0N2VoNlk5aW5ZUEgrR0ptYXFid0l1NUVYbmowaHNXZ0pUYlNMOGlGZnR0ME1Ndk1EVWdyYXhMRUI4anZsakZvZmdWbG5IajRnc2FQaVYrK09aV3hTNEVmM09xWWFSemNHZ0txdlZ6cWE3REJ1Z1Jtb0d3bzRsQ3lxZVdESTV4cGczRlpxaWxXQm14a3F5WXBGSktLWDRBS09hZ2ZlNStDMTRLSWtWd3dpSWRSTmluVGNLRjRZN3BPYWxQWHR6QjRTc1dhck1zaHI4ZEh5L2JtUVVHZEVYZUZnMjdTR1gzZ0oya2lqQURkNUJ1bzg2aDR0aWY3eVFaZlVDN0xnM1BwclRvcFlIT1I4TTlESW1ISjdlWFEvZUxyMyszWWZ2V1k5QkZaWDAvV3lRV0NocFZMQkd1OExJaEtsalUrRzZUakhKeGEySWNFbWp0aU9DVW1WMGJ6emIvS3N1OWErU0l1akFiY3Y3VE5DcFZtUXFrUkJQWlVkbDJxQjAiLCJtYWMiOiIwZDA1ZjAwOWQwNzA4NmNkMzJlNGExZWMyZjlmN2M0OTQ5MjEzZjUwMzZlYWVkMzAyOWRjOWYxN2E4OTkzMGY4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZLZTA4UTVXZGFpQ093T2N0dUFadGc9PSIsInZhbHVlIjoiZ1VIZXJ3N00zbkEwRzAvYW4wY3Z6bmZYSjdJalV2ekRwSG5aYjN4MXZzSWxTL2VkWDVncmptUEMwTDJxbGJIb1VWKzJpM1Rwbms2am5LeHhIRWd1VzhzU1RNWFZkTzFvMUZBRWtqSWwybjNGS3hBWjNPYmNFWXVZMVQzUU50OVY0NFR5NG4xc05RQlJhaTNUYmM3eWRKTldvMisrTTVQK1ZZT1BJVmNNUlI0UUhtQlNvWE9YSGZ3Rzc3VER3Njc1Sk0wTGpCVlgzQVJmMnRWbTU3REozOXFjMllLWFNZQmNVNE40MVZydEdRdjVzZ3h3dGwzWTliRmI0eDVtMWE1VHdyY0cxV3hkZ3R3RWJ4eFZVK1o2RGxUUFZnTGVBdm9TTGh5aSsxVEdMMnRTb3Urd0tLZDZvRDlGR0xCRE44K0V3QnBET2NtekxYK1I0TE1NRXl1bE42SzRUMEtHQ1pwVFVTeGFGSU44UUN3QVU0Qjk5ZmNPQlFzdDhrTzQrbzJMc0d2SWFuUiszRVMxYlBiSmI2ZGg3cjRDZmNYK0pJS0xzMFhKQ29YeElXWlZucFhxOG1abXZrVWxkU0hHUXBQM0gvRCsyYjVYdW1IbnNvMmEyVXVvSkdBYWdLVVYrSlRwd1JCd0xsSXQ3NkZ3d3djaG1rT3lKUlV1NkJtU1hnQ1UiLCJtYWMiOiJhZWZmNmM5OWY5NjVhNjcyZDU1MGE4ZjA4ZmViYTA5OWYzYjQ2NGRlMWYwOTU1M2IyODk3OTMwZjUzZWU5MWEzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNQbDhhTnkzZHAxeVpIQnVVZjJEeEE9PSIsInZhbHVlIjoiS2plcFhzd1p6OFc0UzdpMDA3Zkt6UXhsK1gzRWdwYXVvRUZKQWo3VjhlelFBdjlxNGpVQ0RsL0V0SlUrMzBEN08rZUhKblhENmpKb3pScWwvU2ZZUHNuWXFJcUtGdHZxU0hEbGo4WXN2eFpQQjRwUStzTkVYbDZpYzRDUnA3RjB5TXlqZ0h0N2VoNlk5aW5ZUEgrR0ptYXFid0l1NUVYbmowaHNXZ0pUYlNMOGlGZnR0ME1Ndk1EVWdyYXhMRUI4anZsakZvZmdWbG5IajRnc2FQaVYrK09aV3hTNEVmM09xWWFSemNHZ0txdlZ6cWE3REJ1Z1Jtb0d3bzRsQ3lxZVdESTV4cGczRlpxaWxXQm14a3F5WXBGSktLWDRBS09hZ2ZlNStDMTRLSWtWd3dpSWRSTmluVGNLRjRZN3BPYWxQWHR6QjRTc1dhck1zaHI4ZEh5L2JtUVVHZEVYZUZnMjdTR1gzZ0oya2lqQURkNUJ1bzg2aDR0aWY3eVFaZlVDN0xnM1BwclRvcFlIT1I4TTlESW1ISjdlWFEvZUxyMyszWWZ2V1k5QkZaWDAvV3lRV0NocFZMQkd1OExJaEtsalUrRzZUakhKeGEySWNFbWp0aU9DVW1WMGJ6emIvS3N1OWErU0l1akFiY3Y3VE5DcFZtUXFrUkJQWlVkbDJxQjAiLCJtYWMiOiIwZDA1ZjAwOWQwNzA4NmNkMzJlNGExZWMyZjlmN2M0OTQ5MjEzZjUwMzZlYWVkMzAyOWRjOWYxN2E4OTkzMGY4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692985990\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023664448 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gRcw75ygtWDVdPt6pSdOeEKUZBOgH04Mg29c0uYx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023664448\", {\"maxDepth\":0})</script>\n"}}