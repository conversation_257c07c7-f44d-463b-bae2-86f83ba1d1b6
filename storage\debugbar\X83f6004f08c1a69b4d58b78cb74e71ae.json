{"__meta": {"id": "X83f6004f08c1a69b4d58b78cb74e71ae", "datetime": "2025-07-30 06:34:43", "utime": **********.148899, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753857282.501587, "end": **********.148932, "duration": 0.6473450660705566, "duration_str": "647ms", "measures": [{"label": "Booting", "start": 1753857282.501587, "relative_start": 0, "end": **********.076537, "relative_end": **********.076537, "duration": 0.5749499797821045, "duration_str": "575ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076563, "relative_start": 0.****************, "end": **********.14895, "relative_end": 1.811981201171875e-05, "duration": 0.****************, "duration_str": "72.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G2In03tSQFxRBzWtjgpZEnrJgNp6vtsp1OtPxbZq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-545365539 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-545365539\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1876509750 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1876509750\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1740431511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1740431511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1142101952 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142101952\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-707436045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-707436045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1001127526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFnSDdaRWIwTllvRlhKRkFRaVc2Wnc9PSIsInZhbHVlIjoiKzBsdWZoMlFHblptT2JOLy9zYVpzeXN3RjNGbDFxN0EyVzhpZlU5RTFIb0VFSWRVRHJRUzJuT2dPV3Z6bHpodk1mTmxWSkxrSGtYem9mdFRpbXJxd0F2RS9keG1VYmRUbXFXVHNHQmpTUmtWaExjaHJpYzkwMmhQenlGWnEwWms0eUNqMmZwNS9qZzN0Z0JFUHNha2UwWGU2QlExL0JJdVZFYWZMcjUzNFdtZmtWMDl5enBGejVrZll4Tm9idkZyS3IraWVpVk81NVNqMlpWc3JEd3RKN3FuK09LaEFXbVZNQ2hDYVpJWFVrYkNGeVU4NWRwc2krU2NpVGIvNkludkxrZzBMdEs2UFNqaGpvQWtJM2cyU3hOS041Q0hrTGVlWFQya25mUjJJTGVaemtma1pvTnZOYWRKZkRmMXFHeGZzWTJOTnVZUzFVQjZUT3YvM2hKVEdQbkxMUTZLWVM1a3RUeU9HWk1hNEZXazcrVDFtVG5CQTFobkE0V1dDNm1BRWtOdG96Z1o4enFtbWxHbkR1RWlxV2lZWDVEU0dEQUxSS082U1NzTFRkY2ErS3ZxMW1CS0dqSnF1Y0NuMitSOWhLeWlDTFZWVGl0ZjEwMFZOQU83Y2h5MVIxelNKMWgxVW5VTThvb0NVcHhTd3Y0SnlHcmtSMUxJQ05SeURHZGgiLCJtYWMiOiJlMDYxMWFiOGE1OTQ5MTQzNTk5MGUxZWVkMTZkNTVlOWExMmJlNWRjOTk0MmMyNTcwYjRiNWI1YmM5NDJmNmE3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVFbUZTQjEvQWhjZ3pDbWxsTjVGeVE9PSIsInZhbHVlIjoid2diSnIrK1p0M094MnlpOUdvYzh1ZWdTNHF4UU8zTFYrTzZrb3VTU1gzeGdMdTJ1Z1g4TnJTQUpkSzNIY1JoWlhqZFgvblByR2YySW1ZMDg5Y1hLM1pMdUJ0Ym13NGJpTHY5cS9nS1doVHoyMDZtbElOemxSOU1icndQK21iaVRqTkcyb0RXdVE2R09RbWJlcldUY29vK0lQbFNBOUpONzFKbmdtVmhTeW4yQzk1R2pPMDRza3pCQkVKRXE1Y0lTclA0WXczcDVQdzd6RTgyQzVxU05RaFpJUVM5bU5PNW5rbERzaWpVTXhuYXBZYVJ0NDFLcFFWeGNWTm9XVVlXais1aEVFNzVrUisxNmRtbTNXdlFFSktBdEY2N1FDSFVVTlZMQ0pzMWdkYlZKVU13Qk5WN094emxHZjAxbDEzbDZ6SlNPTHE1T1dkbVpudTE3RjBySitaNXYrSWU4MnFteWpEQVRPck1hNkVSdVVPQVpJZUVqT1BUSXNONExHNnp0c2lJT05JM2xwanBlalMybFM5UTEwZHlhREc3QkZxVFV4b0ZwcUN6NW1ZWGd0L0wxcGZNbnlEbjhiajc2aWFkSG5YVjU0VC9mRkhhVm5nK1NBSUhCc2JDNkNzekVHTDVxU0JYaVlpNGFsaEdFbFVRWStIbXpkeEp6Z0FEK2tMeGEiLCJtYWMiOiJmNTViNzA5ZTA5YzhmYjU4Mjg4ZWJiMThkZGY0YjViZmYwYzdiZWRkY2Q5OGQxMmZkM2U0NTA5MTNmMjBhMDEwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFnSDdaRWIwTllvRlhKRkFRaVc2Wnc9PSIsInZhbHVlIjoiKzBsdWZoMlFHblptT2JOLy9zYVpzeXN3RjNGbDFxN0EyVzhpZlU5RTFIb0VFSWRVRHJRUzJuT2dPV3Z6bHpodk1mTmxWSkxrSGtYem9mdFRpbXJxd0F2RS9keG1VYmRUbXFXVHNHQmpTUmtWaExjaHJpYzkwMmhQenlGWnEwWms0eUNqMmZwNS9qZzN0Z0JFUHNha2UwWGU2QlExL0JJdVZFYWZMcjUzNFdtZmtWMDl5enBGejVrZll4Tm9idkZyS3IraWVpVk81NVNqMlpWc3JEd3RKN3FuK09LaEFXbVZNQ2hDYVpJWFVrYkNGeVU4NWRwc2krU2NpVGIvNkludkxrZzBMdEs2UFNqaGpvQWtJM2cyU3hOS041Q0hrTGVlWFQya25mUjJJTGVaemtma1pvTnZOYWRKZkRmMXFHeGZzWTJOTnVZUzFVQjZUT3YvM2hKVEdQbkxMUTZLWVM1a3RUeU9HWk1hNEZXazcrVDFtVG5CQTFobkE0V1dDNm1BRWtOdG96Z1o4enFtbWxHbkR1RWlxV2lZWDVEU0dEQUxSS082U1NzTFRkY2ErS3ZxMW1CS0dqSnF1Y0NuMitSOWhLeWlDTFZWVGl0ZjEwMFZOQU83Y2h5MVIxelNKMWgxVW5VTThvb0NVcHhTd3Y0SnlHcmtSMUxJQ05SeURHZGgiLCJtYWMiOiJlMDYxMWFiOGE1OTQ5MTQzNTk5MGUxZWVkMTZkNTVlOWExMmJlNWRjOTk0MmMyNTcwYjRiNWI1YmM5NDJmNmE3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVFbUZTQjEvQWhjZ3pDbWxsTjVGeVE9PSIsInZhbHVlIjoid2diSnIrK1p0M094MnlpOUdvYzh1ZWdTNHF4UU8zTFYrTzZrb3VTU1gzeGdMdTJ1Z1g4TnJTQUpkSzNIY1JoWlhqZFgvblByR2YySW1ZMDg5Y1hLM1pMdUJ0Ym13NGJpTHY5cS9nS1doVHoyMDZtbElOemxSOU1icndQK21iaVRqTkcyb0RXdVE2R09RbWJlcldUY29vK0lQbFNBOUpONzFKbmdtVmhTeW4yQzk1R2pPMDRza3pCQkVKRXE1Y0lTclA0WXczcDVQdzd6RTgyQzVxU05RaFpJUVM5bU5PNW5rbERzaWpVTXhuYXBZYVJ0NDFLcFFWeGNWTm9XVVlXais1aEVFNzVrUisxNmRtbTNXdlFFSktBdEY2N1FDSFVVTlZMQ0pzMWdkYlZKVU13Qk5WN094emxHZjAxbDEzbDZ6SlNPTHE1T1dkbVpudTE3RjBySitaNXYrSWU4MnFteWpEQVRPck1hNkVSdVVPQVpJZUVqT1BUSXNONExHNnp0c2lJT05JM2xwanBlalMybFM5UTEwZHlhREc3QkZxVFV4b0ZwcUN6NW1ZWGd0L0wxcGZNbnlEbjhiajc2aWFkSG5YVjU0VC9mRkhhVm5nK1NBSUhCc2JDNkNzekVHTDVxU0JYaVlpNGFsaEdFbFVRWStIbXpkeEp6Z0FEK2tMeGEiLCJtYWMiOiJmNTViNzA5ZTA5YzhmYjU4Mjg4ZWJiMThkZGY0YjViZmYwYzdiZWRkY2Q5OGQxMmZkM2U0NTA5MTNmMjBhMDEwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001127526\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1728696674 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2In03tSQFxRBzWtjgpZEnrJgNp6vtsp1OtPxbZq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728696674\", {\"maxDepth\":0})</script>\n"}}