{"__meta": {"id": "Xcc0c9379a5a1834ced64211181f64fa7", "datetime": "2025-07-30 08:00:37", "utime": **********.499263, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862435.695443, "end": **********.499311, "duration": 1.803868055343628, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1753862435.695443, "relative_start": 0, "end": **********.372304, "relative_end": **********.372304, "duration": 1.676861047744751, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.372337, "relative_start": 1.***************, "end": **********.499316, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kantwgMGQAJPfHWE7j0m5m3X7xYQpwQm4JiUyplL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1135242900 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1135242900\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-847807385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-847807385\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1770210815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1770210815\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1557385513 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557385513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1108370172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1108370172\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1830965888 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:00:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ2Q0NVTWZObHZGZnI3ZGJIdk5sK3c9PSIsInZhbHVlIjoiY0pLV05VZldCdStVN0UwUHR2b1ZvRnY3YTQvK1FxVStwSjk0cnRrT1hoYlUwZlBnMzVMMGdlSE1DQklBWkpZcFlMVi9BdG56RlF5VVBwTkx6YnZLODZMNXlWdVF5UHNwdm9jcy9KcnFYTkJlbkd1QWF5TWhoWTJqS29Kc0FlRWhoZWk1ZXlLeDc4ZStlZ0ptUWF4aFVyRERGUHZBSGJ1Uzl2a05lcEFyb1NXZFhWUVorUFc0OHkzcGlXK1Y2SVBkM3oxcHN5bm1yNTdMK0tnbk9tMXFjWUtINUVzZ1NQdzF5VVplWjQ2NlB2RmpoQnZ2MFJRQXdiV0pDSmFOY2c1UmRUZDFwNzlYSEY4ZVkyeC82ZTJTUWlKVkZ4VHRwTzY0MGNqdmp6QXpBVVhRd3BTeE5ybkRFdUhpMWJyZnhUellYdDJUR0t0dThHTWtNUDgvYTFCRUFHQnU4RDJ3bkQ3U25UVDFPRDFxMStPcmlJS0FWL1U4VHpZUFUrbm5rM0wxcTRuc054K1VpQlljNTh1aksxaTBWRnZ1S2FzamY2NkdsYkVnM2YxekFQdU4xeG54QW1CVjluSWd4TUhUSTFUTXBRWVdhYTI3ZERTR0t5a3RYbFhveWNFeTUycTFCbkhJZi9hd04yaDhtY2JOdlkzNEg3cDNvNDNWcVJackFnREoiLCJtYWMiOiIxNWNiYWIyNTkwYmQ0ODMyMmIzY2FlNjVmNDc4OGNlNTQzY2E4ODUwYzBjMjFjNjE4NmJiYTY1N2Q2OWZlMDQ1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:00:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRXN2l6VzNaM3V4ZWtKRFRUNm1tYVE9PSIsInZhbHVlIjoiVjFHVVJuWmNwcFJjRE5VOCtWcm9uVjdBWXNPZTJJZUw5Q2doUkJQT0U0SHhTeGVFTkc3UGhlQmNSTDVUUG94dVhaWmUrbGZmSzlpRnBOUUpVU2pYVUdmU0Y4bjhaaGdzK2NlbnVTSkk1S3BkSHlBNVc5ek5PWW43VTVaQjZYdHNhL0JNaHhzZTdXSmxhcGgrWGxkSksvcDB0WlRUVUFFREJWL0E4L0d1MFNGaVpBcDNJMlRVZjdBT053cnI0K3hBUlFsUm9vVlhYTXBFT3NSUkZkR1hhWTNnbEpzeWp6SnRadkphTFYvN1oyKzBEUXpQY3lVUkFUQ3d2TkhWNHdKa2xlNWVaMXYzRDJUdkVhTDA2ZCtlSThDRlQxbmJQS2tsVUNGaUVlRC9wbUtOajIrUlAzZ3AzQWQzdTBmUjQyYlUvVnJ3Ukw1YzlTSWRrcEJIZEhXdVhTMlpTUmkzVGZxMWtvYWU1NWZHVjBSUU5UQlY3VkplSTFza2ZKK0lPUlNhU1NKeWdvbVFMMTUrTThxTVd1V0cyUFh5WWttMHhibUxmdUxaVFVZVEpOajBsTklMb25aTWxDZXkyNUVYanVTUnQ1V3FNZWFSSWZZdjVtbVJxWUZGSFB5YVYrOUdTQ0lQazdTai9kWEVEa1FrNU9kK0hzalEzWkdtN3ZVMno0ODgiLCJtYWMiOiI0MzI4MDY2YTc0OTlkNGU4OGYxZGRlMzZhZmEzNDBjNWUwZjUzYTI0ZGYxMmQ2Njg0MGM5NDgxMjhmNGRjNzIzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:00:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ2Q0NVTWZObHZGZnI3ZGJIdk5sK3c9PSIsInZhbHVlIjoiY0pLV05VZldCdStVN0UwUHR2b1ZvRnY3YTQvK1FxVStwSjk0cnRrT1hoYlUwZlBnMzVMMGdlSE1DQklBWkpZcFlMVi9BdG56RlF5VVBwTkx6YnZLODZMNXlWdVF5UHNwdm9jcy9KcnFYTkJlbkd1QWF5TWhoWTJqS29Kc0FlRWhoZWk1ZXlLeDc4ZStlZ0ptUWF4aFVyRERGUHZBSGJ1Uzl2a05lcEFyb1NXZFhWUVorUFc0OHkzcGlXK1Y2SVBkM3oxcHN5bm1yNTdMK0tnbk9tMXFjWUtINUVzZ1NQdzF5VVplWjQ2NlB2RmpoQnZ2MFJRQXdiV0pDSmFOY2c1UmRUZDFwNzlYSEY4ZVkyeC82ZTJTUWlKVkZ4VHRwTzY0MGNqdmp6QXpBVVhRd3BTeE5ybkRFdUhpMWJyZnhUellYdDJUR0t0dThHTWtNUDgvYTFCRUFHQnU4RDJ3bkQ3U25UVDFPRDFxMStPcmlJS0FWL1U4VHpZUFUrbm5rM0wxcTRuc054K1VpQlljNTh1aksxaTBWRnZ1S2FzamY2NkdsYkVnM2YxekFQdU4xeG54QW1CVjluSWd4TUhUSTFUTXBRWVdhYTI3ZERTR0t5a3RYbFhveWNFeTUycTFCbkhJZi9hd04yaDhtY2JOdlkzNEg3cDNvNDNWcVJackFnREoiLCJtYWMiOiIxNWNiYWIyNTkwYmQ0ODMyMmIzY2FlNjVmNDc4OGNlNTQzY2E4ODUwYzBjMjFjNjE4NmJiYTY1N2Q2OWZlMDQ1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:00:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRXN2l6VzNaM3V4ZWtKRFRUNm1tYVE9PSIsInZhbHVlIjoiVjFHVVJuWmNwcFJjRE5VOCtWcm9uVjdBWXNPZTJJZUw5Q2doUkJQT0U0SHhTeGVFTkc3UGhlQmNSTDVUUG94dVhaWmUrbGZmSzlpRnBOUUpVU2pYVUdmU0Y4bjhaaGdzK2NlbnVTSkk1S3BkSHlBNVc5ek5PWW43VTVaQjZYdHNhL0JNaHhzZTdXSmxhcGgrWGxkSksvcDB0WlRUVUFFREJWL0E4L0d1MFNGaVpBcDNJMlRVZjdBT053cnI0K3hBUlFsUm9vVlhYTXBFT3NSUkZkR1hhWTNnbEpzeWp6SnRadkphTFYvN1oyKzBEUXpQY3lVUkFUQ3d2TkhWNHdKa2xlNWVaMXYzRDJUdkVhTDA2ZCtlSThDRlQxbmJQS2tsVUNGaUVlRC9wbUtOajIrUlAzZ3AzQWQzdTBmUjQyYlUvVnJ3Ukw1YzlTSWRrcEJIZEhXdVhTMlpTUmkzVGZxMWtvYWU1NWZHVjBSUU5UQlY3VkplSTFza2ZKK0lPUlNhU1NKeWdvbVFMMTUrTThxTVd1V0cyUFh5WWttMHhibUxmdUxaVFVZVEpOajBsTklMb25aTWxDZXkyNUVYanVTUnQ1V3FNZWFSSWZZdjVtbVJxWUZGSFB5YVYrOUdTQ0lQazdTai9kWEVEa1FrNU9kK0hzalEzWkdtN3ZVMno0ODgiLCJtYWMiOiI0MzI4MDY2YTc0OTlkNGU4OGYxZGRlMzZhZmEzNDBjNWUwZjUzYTI0ZGYxMmQ2Njg0MGM5NDgxMjhmNGRjNzIzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:00:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830965888\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1135921430 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kantwgMGQAJPfHWE7j0m5m3X7xYQpwQm4JiUyplL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135921430\", {\"maxDepth\":0})</script>\n"}}