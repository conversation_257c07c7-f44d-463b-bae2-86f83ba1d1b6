{"__meta": {"id": "X3246f105f06d8ad0b34ff4b1a14148fe", "datetime": "2025-07-30 06:35:06", "utime": **********.379181, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753857305.475022, "end": **********.379226, "duration": 0.9042038917541504, "duration_str": "904ms", "measures": [{"label": "Booting", "start": 1753857305.475022, "relative_start": 0, "end": **********.217078, "relative_end": **********.217078, "duration": 0.7420558929443359, "duration_str": "742ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.217101, "relative_start": 0.****************, "end": **********.37923, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aoMvhFdVkr0ZwSV3gPDdCdVlRr6jJnpAzeu63kEs", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-991458576 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-991458576\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1562419233 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1562419233\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1664407578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1664407578\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-660846487 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660846487\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1207361801 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1207361801\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486313824 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVFOWg4WGk4ZGlRUHJKOE45UmZzVHc9PSIsInZhbHVlIjoiUTN4OFNnd1B1N3VaVklrYzlNMSttM2diQTRBc3VrK0FaMkVTQUdmdFhSejcxOWFjU0JPeC9kS014RFE3VStwWWpsd2pHaG1nck5GN05sYlp2UDdYaU12bkdzTWsvUmxUTFgwTzNwQnRxWUpXUmhuWnh1ZzJnYXQrSXlRSm9rS0IyRGFMeXNnTGNPSmZMMnV1ZXlEZlJ4dTQweit4VGhzcXRXRWpqSU9QYkJQNHNBWHphM2dXWW42MU1xbE1KcjhMSmN3aWwzTEduVGlLelJFOCtjeHYwSEEzbTV6ODE5OUNDbU56SStwOFNNc2EzV2V4eFdURWpsWlUwMEpEZUZtMTdNR08yMHd6dHUva2J2Q0RSUDIwWTVqamo0N3REaWh3NDNRZC9EVDNWYUxRcW5VNUpLQml1WnUyK3BOUzVhYUNId0dDQldQZmNwVUg3VDRaSXdVc0VrYkFTY2FJZGUvSUlFeEZUdFVFcHJ3aEt1T2pJbTlaa005MUdMMDBUeTRwMTJVQ3krNGpPN2h1NVhuRkgvb0g4ZUdrZ05xWUszS1JZV2h2TU5ha3NEbXdScnhCWGhmdDdSNGxjWmJyazVHOHhsOTQ3eUxKaXByajVld3g2dm9icWthQTlGQ1Y5RWl0SUxoU3hTSHBCT0grYUNpQjUrY3JEYWF5YUxWWkpnQ3UiLCJtYWMiOiI3N2E2OGMxNDM4ZThkOTgyOTM3MjVhZDM5MmIxYzE4ZTk3YzFmZGZlMzMxMTRjY2UwOTkxZDkyMzQwMTZmNTAxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:35:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjQzc0hVWUZqRjlrdmRyMldQRE9ralE9PSIsInZhbHVlIjoiMjFQaUdoaElzYUZWTkpJeHVDTkZKcHltNktlcFJGTDEyQkhodDBicHAxNjJGTGtWenVsc1hldlBtVjdDaXRFeWN6MnprcXlrMG5oMVlYNWZjRGZ1VVBaTldSU1ZucEY2VHc0SnUvWVEvZmtqWWRwS24rcndhaXZ0bTkreU9MNlhvQWgrakNpbmV2Z3c5T2xVREJITE5XTkJkTTFMcW1TS0c3cXA3VzZxMExyTkZ3V3FQM0dHYTEvbktxUkZCTDBWUDIzV1l2OVUwU1Q3UmZXWmxyS3NXczlLRnVYZWtVcXhsd3labnB2dVcyOHlXMzBpd013Qit5QldUU0dOUlNkM2JacTg5TXREL2VCa09TeVZPNm5abkN6UzFDNG1rMWZxVk10VWFxSlJnbGs3V2xET3pRb0pmbmhTWVZaVTY5VnpsVTlnTytWd0pBN3pQV0t6NWRvMWZIem5YMzhkMDRQNVpILy9HUTZ3Q01sU2ZiWFNpNFhWdFAySEpza2VFV3Y0Wll5ZlRTcVVDS1hmU2hGSXVGTDdpb2F1Yjl0VXpvbTBsQThUUjBadWdmSVd1U3RicDZSZXZBVmZrbjYyQXdxRzNBZFA0OVlLeHFONFc2UUdhV2NGejM5RENTVENGdDd5THozdUszb0RkNVhiMDdINXNXczBydWY0OWJ6U2l5UHEiLCJtYWMiOiJjMDc1YTc5OGJlYTJkNzVhNjMxZWZkMjMxNzM2YjA0ZDI5NjE5YTJmM2E0YWRkZjNkZjI1ZjRiMjkwNTdkNjVkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:35:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVFOWg4WGk4ZGlRUHJKOE45UmZzVHc9PSIsInZhbHVlIjoiUTN4OFNnd1B1N3VaVklrYzlNMSttM2diQTRBc3VrK0FaMkVTQUdmdFhSejcxOWFjU0JPeC9kS014RFE3VStwWWpsd2pHaG1nck5GN05sYlp2UDdYaU12bkdzTWsvUmxUTFgwTzNwQnRxWUpXUmhuWnh1ZzJnYXQrSXlRSm9rS0IyRGFMeXNnTGNPSmZMMnV1ZXlEZlJ4dTQweit4VGhzcXRXRWpqSU9QYkJQNHNBWHphM2dXWW42MU1xbE1KcjhMSmN3aWwzTEduVGlLelJFOCtjeHYwSEEzbTV6ODE5OUNDbU56SStwOFNNc2EzV2V4eFdURWpsWlUwMEpEZUZtMTdNR08yMHd6dHUva2J2Q0RSUDIwWTVqamo0N3REaWh3NDNRZC9EVDNWYUxRcW5VNUpLQml1WnUyK3BOUzVhYUNId0dDQldQZmNwVUg3VDRaSXdVc0VrYkFTY2FJZGUvSUlFeEZUdFVFcHJ3aEt1T2pJbTlaa005MUdMMDBUeTRwMTJVQ3krNGpPN2h1NVhuRkgvb0g4ZUdrZ05xWUszS1JZV2h2TU5ha3NEbXdScnhCWGhmdDdSNGxjWmJyazVHOHhsOTQ3eUxKaXByajVld3g2dm9icWthQTlGQ1Y5RWl0SUxoU3hTSHBCT0grYUNpQjUrY3JEYWF5YUxWWkpnQ3UiLCJtYWMiOiI3N2E2OGMxNDM4ZThkOTgyOTM3MjVhZDM5MmIxYzE4ZTk3YzFmZGZlMzMxMTRjY2UwOTkxZDkyMzQwMTZmNTAxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:35:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjQzc0hVWUZqRjlrdmRyMldQRE9ralE9PSIsInZhbHVlIjoiMjFQaUdoaElzYUZWTkpJeHVDTkZKcHltNktlcFJGTDEyQkhodDBicHAxNjJGTGtWenVsc1hldlBtVjdDaXRFeWN6MnprcXlrMG5oMVlYNWZjRGZ1VVBaTldSU1ZucEY2VHc0SnUvWVEvZmtqWWRwS24rcndhaXZ0bTkreU9MNlhvQWgrakNpbmV2Z3c5T2xVREJITE5XTkJkTTFMcW1TS0c3cXA3VzZxMExyTkZ3V3FQM0dHYTEvbktxUkZCTDBWUDIzV1l2OVUwU1Q3UmZXWmxyS3NXczlLRnVYZWtVcXhsd3labnB2dVcyOHlXMzBpd013Qit5QldUU0dOUlNkM2JacTg5TXREL2VCa09TeVZPNm5abkN6UzFDNG1rMWZxVk10VWFxSlJnbGs3V2xET3pRb0pmbmhTWVZaVTY5VnpsVTlnTytWd0pBN3pQV0t6NWRvMWZIem5YMzhkMDRQNVpILy9HUTZ3Q01sU2ZiWFNpNFhWdFAySEpza2VFV3Y0Wll5ZlRTcVVDS1hmU2hGSXVGTDdpb2F1Yjl0VXpvbTBsQThUUjBadWdmSVd1U3RicDZSZXZBVmZrbjYyQXdxRzNBZFA0OVlLeHFONFc2UUdhV2NGejM5RENTVENGdDd5THozdUszb0RkNVhiMDdINXNXczBydWY0OWJ6U2l5UHEiLCJtYWMiOiJjMDc1YTc5OGJlYTJkNzVhNjMxZWZkMjMxNzM2YjA0ZDI5NjE5YTJmM2E0YWRkZjNkZjI1ZjRiMjkwNTdkNjVkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:35:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486313824\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aoMvhFdVkr0ZwSV3gPDdCdVlRr6jJnpAzeu63kEs</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}