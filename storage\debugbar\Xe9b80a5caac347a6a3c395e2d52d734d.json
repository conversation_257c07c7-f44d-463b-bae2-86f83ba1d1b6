{"__meta": {"id": "Xe9b80a5caac347a6a3c395e2d52d734d", "datetime": "2025-07-30 08:02:42", "utime": **********.812403, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862560.672576, "end": **********.812443, "duration": 2.139867067337036, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1753862560.672576, "relative_start": 0, "end": **********.620299, "relative_end": **********.620299, "duration": 1.947723150253296, "duration_str": "1.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.620352, "relative_start": 1.9477760791778564, "end": **********.812447, "relative_end": 4.0531158447265625e-06, "duration": 0.19209504127502441, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00725, "accumulated_duration_str": "7.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7547622, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 69.379}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.782595, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 69.379, "width_percent": 15.724}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.791806, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 85.103, "width_percent": 14.897}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-2083092604 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2083092604\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-557111178 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-557111178\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1369877698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1369877698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1103311251 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ik1SUk9CVXd2VWE1NlpXVFRmRUlxQ3c9PSIsInZhbHVlIjoiK3cvSGtzbnlWR2J2QVpFcW04ZkRGd3ZJRXVoMDRzQ2kvbTdaMnNpaGp3TWlweVRmMjRiRHNZaW8yUFlEcjhGMlQyK0pSaisva0V3RjZjVVpiNzhtMjBVSVFCMmduY3VhL0VhT2t5Tno3WGRndGlBOGppbTZKQ25EZTlHZzI0b3gvV3FPMlhYMjFrTUF1RFhNbjJXallwVEVnNnZBWGtXM2hwTHRLelVUR1ZaejFhMlJGUEh1dU5ZSVRkQW8xUUN2VWt2ZWovNXlLbnUyUkY2VXE0dTZOaVFVWjhlL3NleExjb2ozWUhxa1lTWWtJaklXWlNDYlUydnFDYjlhTStrdit6M1ZkR1lUbmhqYll5OWxPd2dYODExdnMrQXB5NEV1dlhHUmRxUm1HV3Q5eE1oSWtyUnhCc20yM1lQK3QyZUdYUmNPRy8zNkh3ZGY4SGJHYjFYZGNNa1Qrd1A4aFY1RXBaN3NRNzNkQWFiTHlrSmRoazQrYi9Cd21ZTXNSbG11QzdrbHhaaFdQOEEzclBpRTlEV21ldHE2YU9GRVl4U2xsd2hlN2lIcWIxSGNEdDNZL01JR0FTNm9BN2h2ZFpsV09kZ0pOQ24zVnBWRVJiVTNyYUNMMkxoR3dQeHB3V0kwZ1RKV1NmSWNCVjY2c0VFeTVJdWVTblFxOEplcmp4THQiLCJtYWMiOiJiN2VlNjNkOGZlY2VkMDdlY2FiOGEwMTRlYzExZGFjN2FmMzA0NjdmNmY2NzBlMTgyZmEzYzViMzdkMjc4ZGMzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImhqL3RyL0lBc3VpS1BlNWtJbDBWR3c9PSIsInZhbHVlIjoiRXNjbzRjNi9hS2JIUEVnSFZIakZSd1Vzeko0LzFjYnByUWhXT042dmd0YnFPMG1QQytiSXAySzZGQlNWbk5SaFh5NHpKSHFxL2owSGlzVlJjVEE5d0tvYXdZVWdvYjdTbzZjWWswZW1KNVhFZG5HQWlpNVArMTJpODlJREJFcHQyNnM5dkx2VGcxWHJodzFzSEt2bjBGRjRpRlM0UHljbWRUSDFMOTJVRnJ1bVNKRlpUUWpjeUZyaHl4YXorQUZmR2tqWmdFT240L0ErcGxtV3kwNGkwSWpIc1BaSUdTcUdrOWhCamNieWE3T3BsN1lGb1BJUzBRaXBRWWFRd0J0cjBwZm0wUHZJOExwL3BzaFo5aEk5eUcxeWN4dTlCZGdNRk03RkNNMEpKQkNDZXpPS2hZb3QrTEV2YXozRE5DYmNTVERFM2NyeWtDeGFHUGxpYWZ5enFrQU9McUtEVnZ6c3BNWElScHR2M3RDTlJScHVWNWg5RFVtYUJOc0Vyby9VbHFkVmRQQ2UxNW1lbDFrM3poMFhlUGRNZjZicmxEdHJ3QnFUcHRCdmRsTWpHRVA2TVlreU8yRkxqMklabnpWSDdEaFczUVRKaXRVU3JYS0s3Z3FIcXpZRHVlZHNTMjNad3Rvb3NJRW9LZERpNkRJMkhKZXBBUFZ5RU9Cc0NhMU8iLCJtYWMiOiI5YmYyNmFhOGQ0YThjZTZjZjRiNzAxOTE4ZTEwODNjZWNmMThjNmI4YWMzY2NjN2I3YTM1NjMwNmYxOWVjYWQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103311251\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1018417019 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018417019\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9Ma0dNOWJTbmxwbGRnWm9wdWl1bVE9PSIsInZhbHVlIjoidkZXUFpWYmtRYzhRNmdYYzVLeDh0aVRoT1grSm1yUk1LNkxseUxTQTBOYVNzNEdEY2hXTzNXMW5hVHZMd25CZzZ5c2lEZmhMUXNSVHRwaU5tV0x6L3ZaWTd3WVZzTzdSVXQ4bkJMUkJYMnBINVBkcmQvcE0zMFN4aGZDcnZMaWp2Y0tjdHd0UTlPNnlFdjRjSHlrVmNvTGJRaC82dFFpV0dtejdnNFFQdlRlK1dVVDhOZllOMWIzaXVhZnZ3VTkxb3BwZjY3V0ZGL3BRVG5IVHpwYythbWwwTGpNbnFFR09LbGlEMnR6Y2hicHdRS1B2eklvYk5TT242aklIWk5Wb3R3cWQrREhrU1o1SytqTkZua29mTURzKzJhSHNwVmV6VWtxc2JUU2QzMFJRMHZNL0RVdXpJSFByemRLYk5NaENDVWh4YUFSRWV4RFpaNjVXanVQVjZFSXJFc0orMVFLQU9rRVNPN1NHSWdFcGd2UXBsSlJLRGVpdU0rWW1uaVJMMHpkZ0FyNzNFUzVINGF5WlRiK0YySVJ6WVh4N1JjSC9lNGRqMEZjYnR4Z3J5M0dJY2VUQmZmV2tBY3MxcjlIcno0Wlp2NHlQcmJVTUF5b05iY0lZR050Zmtsd1V0ZkJrTENLSnhEUHMydlN1NXdBRFlGSlBvcEdIK0lKVzE3ZzAiLCJtYWMiOiJhMmRlYWRlM2FhYmRjNzk1MDJkY2ZiY2RkYzAwZGYzNjdkNzE3NjNjMmM4OTQxY2Y3YmMyZThjNjRlZjQ2MjlkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBLTk5yMG1MNTRhN2l2VG51WktiOGc9PSIsInZhbHVlIjoiSm9zZXdoQWphR2FiVW0yL3RHMVN1OHFZTjhiNUhVbW5salJNK2svWnc4S3FBQlFENWR2TWM3TU5jUnR6b2FsWGw5dFlYL2o2MzBPQ0QySXRGTjBMaWxOTDFHSzh3dmZleldPSXFCMHk1eVlGaDZjTUNFWDFuMVp3NllSNnhGOGJMSHZFUDEvL3IxcXU0SVFaeTB1OFNyNmhRNG12TjJZckdndldYSHVTRzl4aWlZK3F3NDBYQzB1WU1ZWkwrREdRRkhBMDFKZXprWVpnbDhxSDFoSG00L2ZsWW15WFNsYUc3VmZOZElJMnpDdmVDdFFLSjJJaHJKS3NNaENWbENzV1cydHRRMUVGK0JnOUtkTGlnZndvcEVhVDY2bzExamVaZ25YUSttaHlZbUx5ZEZHdk1CMTVCTTYxelN4M09qTTgrWjBvbWpHVVNxMGU5ZnFBYWtMUzhkNzZDK2xkMk93YVFsa2ZXYi93KytMSUN6MExMZUpGQ3FaN01sT1JuVWt1bVFiOTBXZWMreUY2azZpdE5ZRWUzbVI3cEFsd0x1T3dFYXA4RUFieWpEcHN0eGNBbUJodkNXOWk1K2ZHaWtQemVwTmgvSXNYOWFQT3JJekNtaURaTC9LRDlSSUtRUk9JZ3BBZTY2elZIQ1owWWoyV1NERlNJSTBFY2hMKzJ3MVgiLCJtYWMiOiJjZWQxYWQ5OTJkNDg4MTIwNmU0NTVjOGQ5MjliMmRiYjFkOTlmMTFhYzhkYTVjMDI4NWE3NTliMTViYTE2MmYyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9Ma0dNOWJTbmxwbGRnWm9wdWl1bVE9PSIsInZhbHVlIjoidkZXUFpWYmtRYzhRNmdYYzVLeDh0aVRoT1grSm1yUk1LNkxseUxTQTBOYVNzNEdEY2hXTzNXMW5hVHZMd25CZzZ5c2lEZmhMUXNSVHRwaU5tV0x6L3ZaWTd3WVZzTzdSVXQ4bkJMUkJYMnBINVBkcmQvcE0zMFN4aGZDcnZMaWp2Y0tjdHd0UTlPNnlFdjRjSHlrVmNvTGJRaC82dFFpV0dtejdnNFFQdlRlK1dVVDhOZllOMWIzaXVhZnZ3VTkxb3BwZjY3V0ZGL3BRVG5IVHpwYythbWwwTGpNbnFFR09LbGlEMnR6Y2hicHdRS1B2eklvYk5TT242aklIWk5Wb3R3cWQrREhrU1o1SytqTkZua29mTURzKzJhSHNwVmV6VWtxc2JUU2QzMFJRMHZNL0RVdXpJSFByemRLYk5NaENDVWh4YUFSRWV4RFpaNjVXanVQVjZFSXJFc0orMVFLQU9rRVNPN1NHSWdFcGd2UXBsSlJLRGVpdU0rWW1uaVJMMHpkZ0FyNzNFUzVINGF5WlRiK0YySVJ6WVh4N1JjSC9lNGRqMEZjYnR4Z3J5M0dJY2VUQmZmV2tBY3MxcjlIcno0Wlp2NHlQcmJVTUF5b05iY0lZR050Zmtsd1V0ZkJrTENLSnhEUHMydlN1NXdBRFlGSlBvcEdIK0lKVzE3ZzAiLCJtYWMiOiJhMmRlYWRlM2FhYmRjNzk1MDJkY2ZiY2RkYzAwZGYzNjdkNzE3NjNjMmM4OTQxY2Y3YmMyZThjNjRlZjQ2MjlkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBLTk5yMG1MNTRhN2l2VG51WktiOGc9PSIsInZhbHVlIjoiSm9zZXdoQWphR2FiVW0yL3RHMVN1OHFZTjhiNUhVbW5salJNK2svWnc4S3FBQlFENWR2TWM3TU5jUnR6b2FsWGw5dFlYL2o2MzBPQ0QySXRGTjBMaWxOTDFHSzh3dmZleldPSXFCMHk1eVlGaDZjTUNFWDFuMVp3NllSNnhGOGJMSHZFUDEvL3IxcXU0SVFaeTB1OFNyNmhRNG12TjJZckdndldYSHVTRzl4aWlZK3F3NDBYQzB1WU1ZWkwrREdRRkhBMDFKZXprWVpnbDhxSDFoSG00L2ZsWW15WFNsYUc3VmZOZElJMnpDdmVDdFFLSjJJaHJKS3NNaENWbENzV1cydHRRMUVGK0JnOUtkTGlnZndvcEVhVDY2bzExamVaZ25YUSttaHlZbUx5ZEZHdk1CMTVCTTYxelN4M09qTTgrWjBvbWpHVVNxMGU5ZnFBYWtMUzhkNzZDK2xkMk93YVFsa2ZXYi93KytMSUN6MExMZUpGQ3FaN01sT1JuVWt1bVFiOTBXZWMreUY2azZpdE5ZRWUzbVI3cEFsd0x1T3dFYXA4RUFieWpEcHN0eGNBbUJodkNXOWk1K2ZHaWtQemVwTmgvSXNYOWFQT3JJekNtaURaTC9LRDlSSUtRUk9JZ3BBZTY2elZIQ1owWWoyV1NERlNJSTBFY2hMKzJ3MVgiLCJtYWMiOiJjZWQxYWQ5OTJkNDg4MTIwNmU0NTVjOGQ5MjliMmRiYjFkOTlmMTFhYzhkYTVjMDI4NWE3NTliMTViYTE2MmYyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1679466796 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679466796\", {\"maxDepth\":0})</script>\n"}}