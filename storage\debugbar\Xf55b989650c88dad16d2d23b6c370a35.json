{"__meta": {"id": "Xf55b989650c88dad16d2d23b6c370a35", "datetime": "2025-07-30 07:56:59", "utime": **********.083066, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862216.222504, "end": **********.08313, "duration": 2.860625982284546, "duration_str": "2.86s", "measures": [{"label": "Booting", "start": 1753862216.222504, "relative_start": 0, "end": 1753862218.875927, "relative_end": 1753862218.875927, "duration": 2.6534230709075928, "duration_str": "2.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862218.875974, "relative_start": 2.***************, "end": **********.083135, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Z3hd5ZecCpP01pX1JrK57pDTx8NCBi7r8bZ5yoaN", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-969937572 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-969937572\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1504406982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1504406982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1262402699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1262402699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921481075 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921481075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1119603676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1119603676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2103303336 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:56:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNuYm5zZGhWL05YTS9XaDlvc0k1K2c9PSIsInZhbHVlIjoidW1FMTZOTjM0TFh1bHdObHc4WDRXci9HZXhRejdOQlhBMDlleDM4bzdXNjh1MVZvelFNcW9ySEY5TzVTMWU3bkgvTFdFUjZSMDFnZkIwbzdrZnJyR0tmUU9hSlROWTJtZ0hXMmZiT1pjN1hyRi9ybVFCL1VkVXZXWkRzL01BZVE4T1UrMnN1VlEyT25tNThsQ0xKQ1EwaGlnQlJxa2RtdkszcjNBS1JKOXA2WmVsL2ViYkxxNWZoSWI2ZmlFOFNXWCt5YS9lVjFkKzhIMDNxNzFYWnRuK2xLcG5vV2lsTGV5ZUFnQ1lwTkNuVkFjR0VvcGQ2WnRsY1lPdGNXU1AyckcyT0NvMmlzcEZRVzRhWmdka3ZvVjFOdytRVnU4Mnp4c0VQZndvb01HaWJhZkZmaGR5NDh4VmN4Y0FBRkVOUU9aS3RhbktoZWFGeGQxWnB2Rno0RDlaczI5aXhmVFhxY1lMNW9MNW43bzVYRUptNUY1RTFBTVNlcXFVeVEvbmlJOWZpcTEyVVJWb3h4RGRXcitTU1FpVVVsOS9JOFZscG5XWVVSdWU0aUxVNGxCT01PM2JJZ2dVS0V0SnJmOHBkMVpBdGpuakFmeDd6YWYvU3AxTFFsdmliMVhNNVpjR3daNnlua3Z5WjVIdVJZanBuR25jWkpsYzZ3c0Z4dDViL0IiLCJtYWMiOiJlNTc1NTg4NTg5ZTcxNzhmZWFjYzQ2NTRhZGVjYzkwMDkwYTRkZjc4YmM0ZmM3MDJmMmQ3ZTdmODcwY2FjNjQzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:56:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFPTFVMSm02Qld5SUo3SElMT1NOelE9PSIsInZhbHVlIjoiQkdIejRHaGcyeHQwZHU4aXpYSk12bzNob3NYcVI2RWZrcW9YZEg4R2xjOXJRd2E1UTJZL0pCZ0dxRzVtL3I4aFpXdlg5cXFQK1ZQazV4T3ZaVzUwaFNmazJCSkQyb3M2NkszRlcvVWtBb2lnYlZRalpQNzROR0VxT29DRS9jdmNWSWZqK0taWFphM0JrTThZSG5IaDY1THgzWVhRNHoyY1RoVHJ3MHZINHgrbHBaSHFnWXd3eVZQRmpxMWNhaFlSOTd6TWVFSTVPaDR6MVRtZ0YyYmZVZkdSNUlhbmpGVkV0c2s5cUhqN0lKU3pLRWU0SXVPRzFPV3g4MjAwc1pRZTU5cFE3QU9vVXBQaE9tNmdnWDlMaE12WjE5N2RjWmtqMUJiNlAyTDZOZ3F0cS80QmcrOTJ1U1VQNTE5bUdVTXprQ09EZmhyWlNiV01XWHpnbUZhSXh4QUF6QlU0TFpLZXU5Y2xoWllpSnZUUWU1clJmR1BtRk9HcVhlQmtNVnJFMkxhWS9EcFFKSzc1bVZVUlp4ZGkrZW9ORkNzWXU5dVBPa1ozV3NDRzZCTkF6V1Z2SjR3Tk9samo1ZE9DdTRiVDZwRnJsN1F4NzdTS2pzWCtmQjJYNzVHV3NRZ1lTYUhBVnJob1puWG55azE3OE9YNlFsK3VrcWdxa2M1dWJwbmkiLCJtYWMiOiI1ODlmOTRjZmM2NTkyODZiMjZiYjQyNzNlYjhhZWI1OTY5NmQyNjk0MmRlYjI4MDBhNjI4ZmMzZTJlY2JmN2U3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:56:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNuYm5zZGhWL05YTS9XaDlvc0k1K2c9PSIsInZhbHVlIjoidW1FMTZOTjM0TFh1bHdObHc4WDRXci9HZXhRejdOQlhBMDlleDM4bzdXNjh1MVZvelFNcW9ySEY5TzVTMWU3bkgvTFdFUjZSMDFnZkIwbzdrZnJyR0tmUU9hSlROWTJtZ0hXMmZiT1pjN1hyRi9ybVFCL1VkVXZXWkRzL01BZVE4T1UrMnN1VlEyT25tNThsQ0xKQ1EwaGlnQlJxa2RtdkszcjNBS1JKOXA2WmVsL2ViYkxxNWZoSWI2ZmlFOFNXWCt5YS9lVjFkKzhIMDNxNzFYWnRuK2xLcG5vV2lsTGV5ZUFnQ1lwTkNuVkFjR0VvcGQ2WnRsY1lPdGNXU1AyckcyT0NvMmlzcEZRVzRhWmdka3ZvVjFOdytRVnU4Mnp4c0VQZndvb01HaWJhZkZmaGR5NDh4VmN4Y0FBRkVOUU9aS3RhbktoZWFGeGQxWnB2Rno0RDlaczI5aXhmVFhxY1lMNW9MNW43bzVYRUptNUY1RTFBTVNlcXFVeVEvbmlJOWZpcTEyVVJWb3h4RGRXcitTU1FpVVVsOS9JOFZscG5XWVVSdWU0aUxVNGxCT01PM2JJZ2dVS0V0SnJmOHBkMVpBdGpuakFmeDd6YWYvU3AxTFFsdmliMVhNNVpjR3daNnlua3Z5WjVIdVJZanBuR25jWkpsYzZ3c0Z4dDViL0IiLCJtYWMiOiJlNTc1NTg4NTg5ZTcxNzhmZWFjYzQ2NTRhZGVjYzkwMDkwYTRkZjc4YmM0ZmM3MDJmMmQ3ZTdmODcwY2FjNjQzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:56:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFPTFVMSm02Qld5SUo3SElMT1NOelE9PSIsInZhbHVlIjoiQkdIejRHaGcyeHQwZHU4aXpYSk12bzNob3NYcVI2RWZrcW9YZEg4R2xjOXJRd2E1UTJZL0pCZ0dxRzVtL3I4aFpXdlg5cXFQK1ZQazV4T3ZaVzUwaFNmazJCSkQyb3M2NkszRlcvVWtBb2lnYlZRalpQNzROR0VxT29DRS9jdmNWSWZqK0taWFphM0JrTThZSG5IaDY1THgzWVhRNHoyY1RoVHJ3MHZINHgrbHBaSHFnWXd3eVZQRmpxMWNhaFlSOTd6TWVFSTVPaDR6MVRtZ0YyYmZVZkdSNUlhbmpGVkV0c2s5cUhqN0lKU3pLRWU0SXVPRzFPV3g4MjAwc1pRZTU5cFE3QU9vVXBQaE9tNmdnWDlMaE12WjE5N2RjWmtqMUJiNlAyTDZOZ3F0cS80QmcrOTJ1U1VQNTE5bUdVTXprQ09EZmhyWlNiV01XWHpnbUZhSXh4QUF6QlU0TFpLZXU5Y2xoWllpSnZUUWU1clJmR1BtRk9HcVhlQmtNVnJFMkxhWS9EcFFKSzc1bVZVUlp4ZGkrZW9ORkNzWXU5dVBPa1ozV3NDRzZCTkF6V1Z2SjR3Tk9samo1ZE9DdTRiVDZwRnJsN1F4NzdTS2pzWCtmQjJYNzVHV3NRZ1lTYUhBVnJob1puWG55azE3OE9YNlFsK3VrcWdxa2M1dWJwbmkiLCJtYWMiOiI1ODlmOTRjZmM2NTkyODZiMjZiYjQyNzNlYjhhZWI1OTY5NmQyNjk0MmRlYjI4MDBhNjI4ZmMzZTJlY2JmN2U3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:56:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103303336\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-186007138 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Z3hd5ZecCpP01pX1JrK57pDTx8NCBi7r8bZ5yoaN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186007138\", {\"maxDepth\":0})</script>\n"}}