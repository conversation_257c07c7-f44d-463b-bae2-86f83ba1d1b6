
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Form Builder')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).ready(function () {
            $('.cp_link').on('click', function () {
                var value = $(this).attr('data-link');
                var $temp = $("<input>");
                $("body").append($temp);
                $temp.val(value).select();
                document.execCommand("copy");
                $temp.remove();
                show_toastr('success', '<?php echo e(__('Link Copy on Clipboard')); ?>')
            });
        });

        $(document).ready(function () {
            $('.iframe_link').on('click', function () {
                var value = $(this).attr('data-link');
                var $temp = $("<input>");
                $("body").append($temp);
                $temp.val(value).select();
                document.execCommand("copy");
                $temp.remove();
                show_toastr('success', '<?php echo e(__('Link Copy on Clipboard')); ?>')
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Form Builder')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="#" data-size="md" data-url="<?php echo e(route('form_builder.create')); ?>" data-ajax-popup="true" data-bs-toggle="tooltip" title="<?php echo e(__('Create New Form')); ?>" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
    
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                            <tr>
                                <th><?php echo e(__('Name')); ?></th>
                                <th><?php echo e(__('Response')); ?></th>
                                <?php if(\Auth::user()->type == 'company' || (\Auth::user()->can('manage form field') || \Auth::user()->can('view form response') || \Auth::user()->can('edit form builder') || \Auth::user()->can('delete form builder'))): ?>
                                    <th class="text-end" width="200px"><?php echo e(__('Action')); ?></th>
                                <?php endif; ?>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $forms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($form->name); ?></td>
                                    <td>
                                        <?php echo e($form->response->count()); ?>

                                    </td>
                                        <td class="text-end">

                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center cp_link bg-warning-subtle" data-link="<iframe width=&quot;100%&quot; height=&quot;100%&quot; src='<?php echo e(url('/form/'.$form->code)); ?>' title='<?php echo e($form->name); ?>'></iframe>" data-bs-toggle="tooltip" title="<?php echo e(__('Click to copy iframe link')); ?>"><i class="ti ti-frame text-white"></i></a>
                                            </div>

                                            <?php if(\Auth::user()->type=='company' || \Auth::user()->type=='accountant'): ?>
                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center bg-light-blue-subtitle" data-url="<?php echo e(route('form.field.bind',$form->id)); ?>" data-ajax-popup="true" data-size="md" data-bs-toggle="tooltip" title="<?php echo e(__('Convert into Lead Setting')); ?>" data-title="<?php echo e(__('Convert into Lead Setting')); ?>">
                                                    <i class="ti ti-exchange text-white"></i>
                                                </a>
                                            </div>
                                            <?php endif; ?>

                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center cp_link bg-primary" data-link="<?php echo e(url('/form/'.$form->code)); ?>" data-bs-toggle="tooltip" title="<?php echo e(__('Click to copy link')); ?>"><i class="ti ti-copy text-white"></i></a>
                                            </div>

                                            
                                            

                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage form field')): ?>
                                                <div class="action-btn me-2">
                                                    <a href="<?php echo e(route('form_builder.customize',$form->id)); ?>" class="mx-3 btn btn-sm align-items-center bg-danger" data-bs-toggle="tooltip" title="<?php echo e(__('Form Customization')); ?>"><i class="ti ti-palette text-white"></i></a>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Three-dot context menu for View Response, Edit, Delete -->
                                            <div class="action-btn me-2">
                                                <div class="dropdown">
                                                    <a href="#" class="mx-3 btn btn-sm align-items-center bg-info" data-bs-toggle="dropdown" aria-expanded="false" data-bs-toggle="tooltip" title="<?php echo e(__('More Actions')); ?>">
                                                        <i class="ti ti-dots-vertical text-white"></i>
                                                    </a>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton-<?php echo e($form->id); ?>">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view form response')): ?>
                                                        <li>
                                                            <a class="dropdown-item" href="<?php echo e(route('form.response',$form->id)); ?>">
                                                                <i class="ti ti-eye me-2"></i><?php echo e(__('View Response')); ?>

                                                            </a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit form builder')): ?>
                                                        <li>
                                                            <a class="dropdown-item" href="#" data-url="<?php echo e(route('form_builder.edit',$form->id)); ?>" data-ajax-popup="true" data-size="md" data-title="<?php echo e(__('Form Builder Edit')); ?>">
                                                                <i class="ti ti-pencil me-2"></i><?php echo e(__('Edit')); ?>

                                                            </a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete form builder')): ?>
                                                        <li>
                                                            <?php echo Form::open(['method' => 'DELETE', 'route' => ['form_builder.destroy', $form->id],'id'=>'delete-form-'.$form->id, 'style' => 'display: inline;']); ?>

                                                            <a class="dropdown-item text-danger bs-pass-para" href="#">
                                                                <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                                            </a>
                                                            <?php echo Form::close(); ?>

                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                                </div>
                                            </div>
                                        </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/form_builder/index.blade.php ENDPATH**/ ?>