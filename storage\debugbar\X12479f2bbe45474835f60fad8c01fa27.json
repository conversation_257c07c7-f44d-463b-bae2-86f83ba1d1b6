{"__meta": {"id": "X12479f2bbe45474835f60fad8c01fa27", "datetime": "2025-07-30 08:02:02", "utime": **********.036921, "method": "GET", "uri": "/finance/sales/contacts/search?search=paruchay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862519.409726, "end": **********.036959, "duration": 2.6272330284118652, "duration_str": "2.63s", "measures": [{"label": "Booting", "start": 1753862519.409726, "relative_start": 0, "end": **********.73127, "relative_end": **********.73127, "duration": 2.3215441703796387, "duration_str": "2.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.731297, "relative_start": 2.321571111679077, "end": **********.036963, "relative_end": 4.0531158447265625e-06, "duration": 0.3056659698486328, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665272, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01883, "accumulated_duration_str": "18.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9459698, "duration": 0.00939, "duration_str": "9.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 49.867}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.987511, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 49.867, "width_percent": 11.577}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%paruchay%' or `email` like '%paruchay%' or `contact` like '%paruchay%')", "type": "query", "params": [], "bindings": ["79", "1", "%paruchay%", "%paruchay%", "%paruchay%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.000556, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 61.445, "width_percent": 5.682}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%paruchay%' or `email` like '%paruchay%' or `phone` like '%paruchay%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%paruchay%", "%paruchay%", "%paruchay%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0102298, "duration": 0.00619, "duration_str": "6.19ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 67.127, "width_percent": 32.873}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-723630175 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-723630175\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1467827509 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paruchay</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467827509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1156587117 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1156587117\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1466117535 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImtKSTF6ZmIwb2dOQndqTnU5a1FrTkE9PSIsInZhbHVlIjoiY1kwU1hiUGNjczhTRHZlamF0VVpkejViTzRQVEE3UTFQWnA1bG5nTDJyN1Q0YXU4a2VjcnlMWWFYbk9YMFgxbURwSjV6S0t2Q1c3dVBYSGFrRFQ5Wll6SUF3MkN4UVlCNDNOMXVNNzU4RlBFSDZqMU5TWDBDSWhuUlNVczJBM3BFNytuQ0hXYU5hRE9CbFdPcHgrSVhYRGh5WFZOb2lYcFZwcmJkWEJ2bDN2azR1em9SczEzZkVycXBRNjRFZFRXZm9QNWE2eVJ1ZVlRcWtpeHozYWVqeXhjMTd3WjlnMHdxVzZxa1lNU3Byd3o2TUoyZ2NKVGZTQWg0SnNEN3Y2NVg0dlNGb0h2S0VKS2ZwWnpIeHIxVkMzT2JkVkpiUDc5ZHMybmVMd1lLWC83NlprSm5HNDlyVXhGK2FDMlQzdlMvVGtTallmMWpmREsrU2x3Wjd3aXZvT2xYVXpra0JxS1Eyc29Kd0NsYmd2RXRPYXViUVJzOUtTQVI1NWtPMTNQanlOczVQMHVKVU5OUkdVa0x1dHMzcjlVNmxEL1BXemhEYnZWRGZaNSttc1NBclJiZng3MkxOeCtaM2w0VVdOcGtjWkpJTndjZWxQdzVNRnFaeEoyWHdkdkhGVUJkN0sxcUEwS3Z6NjhlQ0tuUnV4dVd4UGJIZFkxRWplVnIrTFgiLCJtYWMiOiI0MTRjZTcxZWI1ZGVhN2U5YWMwZTc2NGQ0ODM3NmUzMjhkMTZlYzQ1MzY2NDFlNTM4YzcwYTNkMWIwYjcxNmQ5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpEMkxBU2RqTnozVTBVakhiL1BaZkE9PSIsInZhbHVlIjoiL3M1NTJrYzNkZllSbmNPN2VLWDk2RVBaY0RTUXhYR000UFpwdkhYR1p1bDlsblpxdFJRajNhNmZ4YXVlM1pCbnR2dmM5b3F5ajlZU3FxWTdsTGwzeHVsRlR4ZkZ5TWp4R3dxNmc5VDgxN3JTbEtyRk9Fd1ZQZzR6R1NYR3NpYW82YXp1OE5UTHpMMVpZb2xHbEROTHRTaDc5Z3ZVTlVqL1MvMithQWZFM2RVUlJrVHBZK0tUMSt5Y21BZk0zL29saEJpWFdRaDNRZ2dPVm00QkM5WkdYZzBEclYxYldDc3ZoZ1VhTzJKQmZBZmpIOG9vT2NGNTdqd0NJNFRsSmwyTE9LZ05ncm5vQW1QSWludy9FYW9VeWJld1Y1eTFNb1hVSUxsM1BQTjlSbjd3VGlublVSWDVVNzQ2K3RMRHh6aTlxcUY2eXZ3cjVvdVlUQjEwd0h6dWFUYUtaYitCYXdTWlNGSVJEVVZ4cjgyTXlqdytHUis0YTlsUVlpM3RvM3pESGlzeHBGdHJQeGkrV3NmRzNiNUFNR29tRjFaVFQ5MWhvdUtaNmJVWUxTMHBsK1g2RGMzQnNQTmR0WWRRNEtSRnhqcDNTSWc3NzJZc0YwOFRhODRMNWhMdG9veG9Ha2NGQTVhZVg5Vlc4ejJvQVREd25aZk41bG1tUlcvdGlTVGwiLCJtYWMiOiIxZmU3ZmFjN2Q5OGVmMjE1OTc2YWEwMWUxNWUwMzcyM2NhYWU2ZDA2NTJjNmMwYjM0MmM4OTkwMWI1ZDIzNTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466117535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250693842 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250693842\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFwcGVmcFBTQ3ZEd2tPSld1WVJSQlE9PSIsInZhbHVlIjoiWTliT0hSdStUN1lnRkg0NHIxaDJaYjkrQUQ3MHViV0JKeVJ5dkhZRXA4S0QxaXFScXVqcXlvOEtEZDk4N3lybUYxRHhOMERuTS94LzQyazJzVFVOQmZEVEFzSm1OS3MwK0tod2w4NEN3RTVCbmN4ck9YdkRJL3FzZmtjSkFtamgzeE13WFI3dnhXZlcvVXVrdENXZmRYaEdHd3dBY3JtZ0M2NXhhMG5OYmlCYTZFYldRSFhIdlRCUlZNdzF3VGQ1SlpjbEFwRlpkZFdBOEVhU2tYRjcxam9MZmNCWmlFTFpHY0hKWFUwMjlNYWN2U08rbWxnYnl4ellVRXlyRG5oeXhPQXBDZmFCeXdERGZsTEZKdElqK1pudjVJKzFUNkpJQ1ZYM2x2YUV4WVVHM0dSbURVcTFUOUdRc0hlMGpQSmxmbUtUK2h0bzNJcGx6TU54cHgrV0FLNVhLUW4yMnJ6Wkw0bUxwRnQ1ckNFUFhmR0JKUFJEanZhNGwrT1RNQS9rOFY3UllMcUFQVW14UUVWQTZIdVBpb2pZeDJEaVVHT0FKZjFZV3JiNHVEZ0o1cVloS3ZwMDE4czRCb1ZqbnZqbkx2RjZpdU1nWDdRbjhZUXYrYzdCV2IwL1I0cm5vU3lqKzhwWmVBTFo5S3JwWGlpNWRjNytVZ0tDNW5Ha1pyRUYiLCJtYWMiOiIyMzdkMGZmMjUyMzBiMWI4OWU2ZDI0NGEyMGI4YzdjMDc2NTE2NTU5MmQzMTA3ODk1MjJjZWU2ZGU0ODM4MTVjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRKVWFnOSs4Zk0yYk9uRXpCZmR6Zmc9PSIsInZhbHVlIjoiUldHTEJBVkFkOXhHcXpMVkhUd21NdVluTmtLSk10MENUZUJWOUJTT1hkTUM4ZXV0bWNzK1plYk9RSEs0SWpqNUxhUWY3Vkd3bFlxQXNNcWM0TEhLV0hiRFMyeHZzZkJMUnp4S215bmR0cXpyWWNmdFd2LzAwVjVtc0FrNTUyZE8wM0dGcmxhWTZCVjVlVkRvUmh6NXo4WCszdTdjOUYyMWo5TlhuQ05NeldzOTB2ZnRWN2F0WTJkSVd1aU9YeWE0SUd2NWhCaXpaeE1SMU8zOWROWHBlS2p6UjJWSDFVdFNCN3dRcHc2RHYwcE11T3IzSGE0RHJzUlVKS21KN21Yc1dvdWlsWW1vR1dVUXZ2TkJvQVBwQ21FQ0Y5S1hMU0dXZTQwNDZZNVc0WERSRUEyZ3FOeEorTnh2WEhRaGkvbm1mZ1JxN0JQM3lnendaaCtLV0JNeHpLQzF4cTZJM3FHcEl1QWhQVVIwOElUaXAzRkxKVVJ6WG44c29XNGhsVHJjU1hCTTlQSWhZYzROQnZZZTEreWpjd3BuT0JScTJpY2N1VTNuZlVsQm5IU2c0OU4wSWxBTWd3dUNRVFFnc1k5ZlhIZVZJbnBoYkhqQ0s2OGh6ZE02eE1tSjFmRmNiNnpxVjhpck1lblB6WTBZUGRoVzZwb1Z0ekJ6Q2FqenBFTFUiLCJtYWMiOiJlMjE3YWU0MjZlMTFjYzBmODdkNzQ1YWUzMDAwMTE0ZWNmMWVkZTk4ZGRkZGY4Mzg5ODllZmVjYWM3NmQyMzE3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFwcGVmcFBTQ3ZEd2tPSld1WVJSQlE9PSIsInZhbHVlIjoiWTliT0hSdStUN1lnRkg0NHIxaDJaYjkrQUQ3MHViV0JKeVJ5dkhZRXA4S0QxaXFScXVqcXlvOEtEZDk4N3lybUYxRHhOMERuTS94LzQyazJzVFVOQmZEVEFzSm1OS3MwK0tod2w4NEN3RTVCbmN4ck9YdkRJL3FzZmtjSkFtamgzeE13WFI3dnhXZlcvVXVrdENXZmRYaEdHd3dBY3JtZ0M2NXhhMG5OYmlCYTZFYldRSFhIdlRCUlZNdzF3VGQ1SlpjbEFwRlpkZFdBOEVhU2tYRjcxam9MZmNCWmlFTFpHY0hKWFUwMjlNYWN2U08rbWxnYnl4ellVRXlyRG5oeXhPQXBDZmFCeXdERGZsTEZKdElqK1pudjVJKzFUNkpJQ1ZYM2x2YUV4WVVHM0dSbURVcTFUOUdRc0hlMGpQSmxmbUtUK2h0bzNJcGx6TU54cHgrV0FLNVhLUW4yMnJ6Wkw0bUxwRnQ1ckNFUFhmR0JKUFJEanZhNGwrT1RNQS9rOFY3UllMcUFQVW14UUVWQTZIdVBpb2pZeDJEaVVHT0FKZjFZV3JiNHVEZ0o1cVloS3ZwMDE4czRCb1ZqbnZqbkx2RjZpdU1nWDdRbjhZUXYrYzdCV2IwL1I0cm5vU3lqKzhwWmVBTFo5S3JwWGlpNWRjNytVZ0tDNW5Ha1pyRUYiLCJtYWMiOiIyMzdkMGZmMjUyMzBiMWI4OWU2ZDI0NGEyMGI4YzdjMDc2NTE2NTU5MmQzMTA3ODk1MjJjZWU2ZGU0ODM4MTVjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRKVWFnOSs4Zk0yYk9uRXpCZmR6Zmc9PSIsInZhbHVlIjoiUldHTEJBVkFkOXhHcXpMVkhUd21NdVluTmtLSk10MENUZUJWOUJTT1hkTUM4ZXV0bWNzK1plYk9RSEs0SWpqNUxhUWY3Vkd3bFlxQXNNcWM0TEhLV0hiRFMyeHZzZkJMUnp4S215bmR0cXpyWWNmdFd2LzAwVjVtc0FrNTUyZE8wM0dGcmxhWTZCVjVlVkRvUmh6NXo4WCszdTdjOUYyMWo5TlhuQ05NeldzOTB2ZnRWN2F0WTJkSVd1aU9YeWE0SUd2NWhCaXpaeE1SMU8zOWROWHBlS2p6UjJWSDFVdFNCN3dRcHc2RHYwcE11T3IzSGE0RHJzUlVKS21KN21Yc1dvdWlsWW1vR1dVUXZ2TkJvQVBwQ21FQ0Y5S1hMU0dXZTQwNDZZNVc0WERSRUEyZ3FOeEorTnh2WEhRaGkvbm1mZ1JxN0JQM3lnendaaCtLV0JNeHpLQzF4cTZJM3FHcEl1QWhQVVIwOElUaXAzRkxKVVJ6WG44c29XNGhsVHJjU1hCTTlQSWhZYzROQnZZZTEreWpjd3BuT0JScTJpY2N1VTNuZlVsQm5IU2c0OU4wSWxBTWd3dUNRVFFnc1k5ZlhIZVZJbnBoYkhqQ0s2OGh6ZE02eE1tSjFmRmNiNnpxVjhpck1lblB6WTBZUGRoVzZwb1Z0ekJ6Q2FqenBFTFUiLCJtYWMiOiJlMjE3YWU0MjZlMTFjYzBmODdkNzQ1YWUzMDAwMTE0ZWNmMWVkZTk4ZGRkZGY4Mzg5ODllZmVjYWM3NmQyMzE3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}