<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    protected $fillable = [
        'name',
        'code',
        'product_id',
        'product_name',
        'discount',
        'discount_type',
        'limit',
        'start_date',
        'end_date',
        'description',
        'is_active',
        'created_by',
    ];


    public function used_coupon()
    {
        return $this->hasMany('App\Models\UserCoupon', 'coupon', 'id')->count();
    }
}
