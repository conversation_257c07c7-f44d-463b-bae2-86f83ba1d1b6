<!-- Expenses Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Expense Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-warning active" data-expense-view="manage">
                    <i class="ti ti-list me-1"></i>{{ __('Manage') }}
                </button>
                <button type="button" class="btn btn-outline-warning" data-expense-view="categories">
                    <i class="ti ti-category me-1"></i>{{ __('Categories') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Manage View -->
<div id="manage-view" class="expense-view active">
    <div class="row mb-4">
        <!-- Expense Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-danger text-white">
                <div class="card-body text-center">
                    <i class="ti ti-receipt" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                    <small>{{ __('Total Expenses') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-calendar-month" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                    <small>{{ __('This Month') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small>{{ __('Pending Bills') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-trending-down" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small>{{ __('vs Last Month') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Expenses') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create expense')
                            <a href="{{ route('expense.create') }}" class="btn btn-success btn-sm">
                                <i class="ti ti-plus me-1"></i>{{ __('Add Expense') }}
                            </a>
                        @endcan
                        <a href="{{ route('expense.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Description') }}</th>
                                    <th>{{ __('Category') }}</th>
                                    <th>{{ __('Amount') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $expenses = \App\Models\Expense::where('created_by', \Auth::user()->creatorId())->latest()->limit(5)->get();
                                @endphp
                                @forelse($expenses as $expense)
                                <tr>
                                    <td>{{ \Auth::user()->dateFormat($expense->date) }}</td>
                                    <td>{{ $expense->description }}</td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ !empty($expense->category) ? $expense->category->name : '-' }}
                                        </span>
                                    </td>
                                    <td>{{ \Auth::user()->priceFormat($expense->amount) }}</td>
                                    <td>{{ !empty($expense->account) ? $expense->account->account_name : '-' }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ __('Paid') }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('expense.show', $expense->id) }}" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            @can('edit expense')
                                                <a href="{{ route('expense.edit', $expense->id) }}" class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                            @endcan
                                            <button class="btn btn-sm btn-outline-info" title="{{ __('Receipt') }}">
                                                <i class="ti ti-receipt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-receipt" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Expenses Found') }}</h5>
                                            <p>{{ __('Start tracking your business expenses') }}</p>
                                            @can('create expense')
                                                <a href="{{ route('expense.create') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Add Expense') }}
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Categories View -->
<div id="categories-view" class="expense-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Expense Categories') }}</h5>
                    @can('create constant category')
                        <a href="{{ route('product-category.create') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-plus me-1"></i>{{ __('Add Category') }}
                        </a>
                    @endcan
                </div>
                <div class="card-body">
                    <div class="row">
                        @php
                            $categories = \App\Models\ProductServiceCategory::where('created_by', \Auth::user()->creatorId())->where('type', 'expense')->get();
                        @endphp
                        @forelse($categories as $category)
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <div class="theme-avtar bg-warning mx-auto mb-3" style="width: 50px; height: 50px;">
                                        <i class="ti ti-category" style="font-size: 1.2rem;"></i>
                                    </div>
                                    <h6 class="mb-2">{{ $category->name }}</h6>
                                    <p class="text-muted small mb-3">{{ __('Category for expense tracking') }}</p>
                                    <div class="d-flex gap-1 justify-content-center">
                                        @can('edit constant category')
                                            <a href="{{ route('product-category.edit', $category->id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-edit"></i>
                                            </a>
                                        @endcan
                                        @can('delete constant category')
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        @endcan
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="ti ti-category text-muted" style="font-size: 4rem;"></i>
                                <h5 class="mt-3">{{ __('No Categories Found') }}</h5>
                                <p class="text-muted">{{ __('Create categories to organize your expenses') }}</p>
                                @can('create constant category')
                                    <a href="{{ route('product-category.create') }}" class="btn btn-primary">
                                        <i class="ti ti-plus me-1"></i>{{ __('Add Category') }}
                                    </a>
                                @endcan
                            </div>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Quick Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('expense.create') }}" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-plus mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Add Expense') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('bill.create') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Create Bill') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('product-category.index') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-category mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Categories') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('vender.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Vendors') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('report.expense.summary') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Reports') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-download mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Export') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Expense view switching
    const expenseViewButtons = document.querySelectorAll('[data-expense-view]');
    const expenseViews = document.querySelectorAll('.expense-view');
    
    expenseViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-expense-view');
            
            // Remove active class from all buttons
            expenseViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            expenseViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });
});
</script>
