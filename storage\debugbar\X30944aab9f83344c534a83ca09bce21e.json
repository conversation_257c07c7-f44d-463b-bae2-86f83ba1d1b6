{"__meta": {"id": "X30944aab9f83344c534a83ca09bce21e", "datetime": "2025-07-30 05:55:49", "utime": **********.120187, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854948.287818, "end": **********.120237, "duration": 0.8324191570281982, "duration_str": "832ms", "measures": [{"label": "Booting", "start": 1753854948.287818, "relative_start": 0, "end": **********.054802, "relative_end": **********.054802, "duration": 0.7669839859008789, "duration_str": "767ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.054816, "relative_start": 0.****************, "end": **********.12024, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "65.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ucfR4zNPVo7wcvwDE2EBjq7qnM46YWfcO5CX3j2x", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1181887676 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1181887676\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1641667690 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1641667690\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-597715855 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-597715855\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-881883839 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881883839\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-609298475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-609298475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2069860108 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:55:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklCbnVXbElHcXRKUjFRM2wyM1NxK3c9PSIsInZhbHVlIjoiN0M0NmYyR0RONXZ1ZUZkZCtQRlBZNzdvUFVRZVNZUlFZcDk5TDZMOVV1NnEyZkFiS01WUEtXcDl6S3ZrL3JKQ1Z6T3ZYT1NMa0d3UnpDY25NZGpCYVcxUGRqVFBFdkhFREE3ZUtrZHpVbzBOaGYwaWtGdnB0aHYwSTVsT2JKRTVWNjFUUFhJNEIvN2p1WWdzbXBFTkpTMm5wazVreS9oci95Q0hCVEtwMW43RjdDbzBtbXcvZDNHSlRXeEdQUEMrQWpwZG8wZ0cwaXZZSmpnZlBoS0FhL1NVN0lRU1hBMWhSbWlXTGtsK29nYzU0YXNrMSt1V1N0VE1MYXFQY1JRWUJLMUd1cUhIcGpqbVp3VUlsQXFpQ1JaVmdmaURBQjZIUnNwNkFtcTRYUWtGajhLamkxTVY3RSs1V0ZpYjkxYnpLNzQyNVBOeG4wY3RROE5YQlM1c084dUtKK2JpcStKY1pTSUF1UWdFWDJuMHpMa1RPaW5TRTR0U2kxZlVibHhrYTM4VUNwT2JMRDh2UEJhQk4rQW5YM0VGMmkzeVltMGlzaFVzSVEzSXJXSEZLdklCSWRLQ2p4Q01ibzZiNU1WL2FySjhOalAweXdyMDJXd25SVDYzb3BoVk1mbExMcjZGSkVpemNLd3NzYndneXJJdURGbGxJQmZxY1NNZ0dYVmkiLCJtYWMiOiJmYTM3Mjg5ZDk1NmE4MmNkODM1NjFmMTU0OGVjYTIxODk0OTdlOWM2ZDVlMTY1YjU2ZDMyNzI1MWVkYzU4NzM1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:55:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNmUU9xR1pxTFVjOUdITEQ2ZzlvZ1E9PSIsInZhbHVlIjoiV1BabkJJckZVclRJTlVMQmRzQnN0SkFrbTA2U2xrZ1NDVGs5TXZoOFAwcFVmV0RqdFM2ZDRBWDJ1bEV2eEc1NGZlbkt5eXdDRG4rSGlRUmgxSDBjMFRVQTJVdGxIeEF3ZHhrZkdYVnFwZ3ljRzMvUDhuNVkvZnVwSi94RnNzdHVQNUhramRaRmFkd2lNbWZGSGtqOW1BRnY5UGZoMTlrTmpXU3ovaElFWEIyZ0VxckJ0bHpUU3Y5N0VzU0JaT0g3bTIxRTNYdk9GVzNYQndMdzYzOHVqMnUwNG5USU5JWDFoZjBDcVd6bUdLcWZiWmgyV3lWRXNaY2FiZU9xQXdQNUNWbU4vaGhFZk5oM20rUkdCYjFwSms4dVNoSXlNRGhERWczMjRSRWhuQ1NjenpFU2NNcGdQRHdsdWFjaGUvOWNacEFIbzZkMktaUmE5UGhJaDBFcEhYNDRZbWF2dUFLbkZ4cjY0VWY1YnFIQ1RSenVaRjNjMGpKVHBqTUlMTHpWQVl4NG41WEllM01ONHROVWllUTBpemFYbDBEZnJDRWR1cks3WURmMkx0OElYQkEyY01mNXg1dlFDWVRXT3ZtUlNFVGhxL2hqWDR0NmZjc3VKaHl6OHZpRDNnTTcxY1pNN0svcFliRko0azZadnFHTGZrVlRYRkFCbmE2enNBNnAiLCJtYWMiOiI4NTViZmU5NGMyNzM5ZTA2YWZiNWJmZjM0YjQwMTNjMGEwNGJhZDMzMWU4ZTI3MWY4YWFjYTE1MjVmNTdmOTE2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:55:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklCbnVXbElHcXRKUjFRM2wyM1NxK3c9PSIsInZhbHVlIjoiN0M0NmYyR0RONXZ1ZUZkZCtQRlBZNzdvUFVRZVNZUlFZcDk5TDZMOVV1NnEyZkFiS01WUEtXcDl6S3ZrL3JKQ1Z6T3ZYT1NMa0d3UnpDY25NZGpCYVcxUGRqVFBFdkhFREE3ZUtrZHpVbzBOaGYwaWtGdnB0aHYwSTVsT2JKRTVWNjFUUFhJNEIvN2p1WWdzbXBFTkpTMm5wazVreS9oci95Q0hCVEtwMW43RjdDbzBtbXcvZDNHSlRXeEdQUEMrQWpwZG8wZ0cwaXZZSmpnZlBoS0FhL1NVN0lRU1hBMWhSbWlXTGtsK29nYzU0YXNrMSt1V1N0VE1MYXFQY1JRWUJLMUd1cUhIcGpqbVp3VUlsQXFpQ1JaVmdmaURBQjZIUnNwNkFtcTRYUWtGajhLamkxTVY3RSs1V0ZpYjkxYnpLNzQyNVBOeG4wY3RROE5YQlM1c084dUtKK2JpcStKY1pTSUF1UWdFWDJuMHpMa1RPaW5TRTR0U2kxZlVibHhrYTM4VUNwT2JMRDh2UEJhQk4rQW5YM0VGMmkzeVltMGlzaFVzSVEzSXJXSEZLdklCSWRLQ2p4Q01ibzZiNU1WL2FySjhOalAweXdyMDJXd25SVDYzb3BoVk1mbExMcjZGSkVpemNLd3NzYndneXJJdURGbGxJQmZxY1NNZ0dYVmkiLCJtYWMiOiJmYTM3Mjg5ZDk1NmE4MmNkODM1NjFmMTU0OGVjYTIxODk0OTdlOWM2ZDVlMTY1YjU2ZDMyNzI1MWVkYzU4NzM1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:55:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNmUU9xR1pxTFVjOUdITEQ2ZzlvZ1E9PSIsInZhbHVlIjoiV1BabkJJckZVclRJTlVMQmRzQnN0SkFrbTA2U2xrZ1NDVGs5TXZoOFAwcFVmV0RqdFM2ZDRBWDJ1bEV2eEc1NGZlbkt5eXdDRG4rSGlRUmgxSDBjMFRVQTJVdGxIeEF3ZHhrZkdYVnFwZ3ljRzMvUDhuNVkvZnVwSi94RnNzdHVQNUhramRaRmFkd2lNbWZGSGtqOW1BRnY5UGZoMTlrTmpXU3ovaElFWEIyZ0VxckJ0bHpUU3Y5N0VzU0JaT0g3bTIxRTNYdk9GVzNYQndMdzYzOHVqMnUwNG5USU5JWDFoZjBDcVd6bUdLcWZiWmgyV3lWRXNaY2FiZU9xQXdQNUNWbU4vaGhFZk5oM20rUkdCYjFwSms4dVNoSXlNRGhERWczMjRSRWhuQ1NjenpFU2NNcGdQRHdsdWFjaGUvOWNacEFIbzZkMktaUmE5UGhJaDBFcEhYNDRZbWF2dUFLbkZ4cjY0VWY1YnFIQ1RSenVaRjNjMGpKVHBqTUlMTHpWQVl4NG41WEllM01ONHROVWllUTBpemFYbDBEZnJDRWR1cks3WURmMkx0OElYQkEyY01mNXg1dlFDWVRXT3ZtUlNFVGhxL2hqWDR0NmZjc3VKaHl6OHZpRDNnTTcxY1pNN0svcFliRko0azZadnFHTGZrVlRYRkFCbmE2enNBNnAiLCJtYWMiOiI4NTViZmU5NGMyNzM5ZTA2YWZiNWJmZjM0YjQwMTNjMGEwNGJhZDMzMWU4ZTI3MWY4YWFjYTE1MjVmNTdmOTE2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:55:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069860108\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2016365464 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ucfR4zNPVo7wcvwDE2EBjq7qnM46YWfcO5CX3j2x</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016365464\", {\"maxDepth\":0})</script>\n"}}