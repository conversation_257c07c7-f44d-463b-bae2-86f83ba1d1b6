{"__meta": {"id": "X18756615be14a17e6966c2c328ce638f", "datetime": "2025-07-30 08:10:58", "utime": **********.179773, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863056.746836, "end": **********.179817, "duration": 1.432981014251709, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753863056.746836, "relative_start": 0, "end": **********.048583, "relative_end": **********.048583, "duration": 1.3017470836639404, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.048629, "relative_start": 1.***************, "end": **********.17982, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LauDBNluCWwZ8n0u4UuZIM9FUPtR0MHvAyjyd15X", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1186094322 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1186094322\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-375818600 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-375818600\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-550026636 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-550026636\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1565629458 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565629458\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1622001196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1622001196\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1628283940 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:10:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9DWUNhRzR1d01QSldPd29NNldWT1E9PSIsInZhbHVlIjoiY1ZGU0Y3ZDNDSWpGS1d1aGJYeGk4Q0xSdUVvaFNreklFdU9LNGxoeHZieC9MZFVwNWZPVU1KdkF1TnhXUEQ4aGxjSGpzemhiV2s5bzRSRWNac0xMTnQya294RzhJbkhadXBKNTFUKzdSR0VJQm5FYkZPV1B4clF3cDVkcW5HKzBtTUN0WGxIMmE5V0o4RlhEM2tFK3RaU1FBb292Q3I5d3g0ZWFZT2trcGh1UHJ4RFdqTzFjbEdDQ3BZWGJKR3p2cnhNYXNPRGJOYTdsbmZwMFZ6ZTQvL2l5NFRxVE5HRjljVUNxcng0UytJQlp3Ulp1bWFRaTVnUHE4ckI0WEEzZmNzdlhxZUhnMHh6ZTVyN3VQYzZrSThrYjdOSTRxdTYvME95M05HMC9jNklMYmRTUjI0cno0ZHE1c1pUay9JZUpmNXg1endZNWZ6Z0JUUnQzUVZZSUdDbWswcWRxL0xIakc0YzhRNHhBeHFEKy9JellMVEw3Q0o3aGsxcmJwa2JaQWRxV1hoTzhWN3F6cSswMnpIWGVkeEhRcTlqN21JQ3BEdFNtYkNaY3hMb1hKRElKOTJ5Qndsb3JmZlE1NEpETG1xSFNwenhPMkt5TmFIQVVEVHk0MVl6N0lKNXhJclVycGpFNURLYkRBVjc3T0hSSU5JOGpKcTNHVUhJcUZkcDAiLCJtYWMiOiJhM2MyOWUwN2QxMDE2ZjM4NDU5NzkyZWJlNDU0OWJkNWMzNmM1NTIwMDgzZjkwZDkyNTM5MjNiMzdmYTM1YjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:10:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZIdFJqQnBrdUluY2tkdE44NGY0ZVE9PSIsInZhbHVlIjoiRU9WbzNsSmZndlAweVNvaVkwTjhtaGVlMUZKZnpZYUxYZ3lMeHhQMVFEcVQ5U05RWFdHQ0tzNUdDdVZQZVJZS00rQ3RjbXlNUGhnSWxlMFM1U2hDZEVtalJ2aG5YZGVva3ZqVGI0ckRqakQ2OFZxSlIwVXRINTRSaXErQjcwNm9uVW1qdUttalBLR1N2OUNHOHYzUU5vRFNHek1scjVCWlFhL3R5eGRCNVU1M1M0Rzc1dzVNZHVWM3ZuK0hHS0RPbEFkN05xOEVsOFlXSEFQMk94N0FMa0pzKzhLQWJUWXlvQUtKYmlNYVMwWG9MUGllL3h5SEF5R0FqTlBOVWp5Z2NpNG9BYytEL2Jqa2IzajdqT3Q2NE5mQS9yM0NVdTJUNkZwcjlnaWJpMUYwRGFqMUlaUk0vQ0JZWElUVW5PMUtFQkFRUytxek4xN3Z3MWlUY1FBQm9odWkzcmRWQ3VaNmlZK3c2dkZxd041NG9SZGFrU3pvQllhbi95RTl6ZGp4aTE1aCtvMFRjWm9pUDlOY2kzVll3bXpyRW81MVN2RWtqYlRzL2swOUNiNDBVUksxalpPVkxSTm5oMlJIeXk4N00ySDM3RG8xQWMxMmd0ckIydlY1bHRqclFVYW4rS2c2WUZkY1QrZ0ZNYks2TlhOMHpsTjErbjY3cDJsbzl4MWgiLCJtYWMiOiJhM2I2N2NjNmZlNmIyMGNmYzM2ZWZiODVlZmYzMGQwNzkwNTgzMDkxZWU1OWQxMTMxZDE4MGViZGJkNWYyZTRkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:10:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9DWUNhRzR1d01QSldPd29NNldWT1E9PSIsInZhbHVlIjoiY1ZGU0Y3ZDNDSWpGS1d1aGJYeGk4Q0xSdUVvaFNreklFdU9LNGxoeHZieC9MZFVwNWZPVU1KdkF1TnhXUEQ4aGxjSGpzemhiV2s5bzRSRWNac0xMTnQya294RzhJbkhadXBKNTFUKzdSR0VJQm5FYkZPV1B4clF3cDVkcW5HKzBtTUN0WGxIMmE5V0o4RlhEM2tFK3RaU1FBb292Q3I5d3g0ZWFZT2trcGh1UHJ4RFdqTzFjbEdDQ3BZWGJKR3p2cnhNYXNPRGJOYTdsbmZwMFZ6ZTQvL2l5NFRxVE5HRjljVUNxcng0UytJQlp3Ulp1bWFRaTVnUHE4ckI0WEEzZmNzdlhxZUhnMHh6ZTVyN3VQYzZrSThrYjdOSTRxdTYvME95M05HMC9jNklMYmRTUjI0cno0ZHE1c1pUay9JZUpmNXg1endZNWZ6Z0JUUnQzUVZZSUdDbWswcWRxL0xIakc0YzhRNHhBeHFEKy9JellMVEw3Q0o3aGsxcmJwa2JaQWRxV1hoTzhWN3F6cSswMnpIWGVkeEhRcTlqN21JQ3BEdFNtYkNaY3hMb1hKRElKOTJ5Qndsb3JmZlE1NEpETG1xSFNwenhPMkt5TmFIQVVEVHk0MVl6N0lKNXhJclVycGpFNURLYkRBVjc3T0hSSU5JOGpKcTNHVUhJcUZkcDAiLCJtYWMiOiJhM2MyOWUwN2QxMDE2ZjM4NDU5NzkyZWJlNDU0OWJkNWMzNmM1NTIwMDgzZjkwZDkyNTM5MjNiMzdmYTM1YjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:10:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZIdFJqQnBrdUluY2tkdE44NGY0ZVE9PSIsInZhbHVlIjoiRU9WbzNsSmZndlAweVNvaVkwTjhtaGVlMUZKZnpZYUxYZ3lMeHhQMVFEcVQ5U05RWFdHQ0tzNUdDdVZQZVJZS00rQ3RjbXlNUGhnSWxlMFM1U2hDZEVtalJ2aG5YZGVva3ZqVGI0ckRqakQ2OFZxSlIwVXRINTRSaXErQjcwNm9uVW1qdUttalBLR1N2OUNHOHYzUU5vRFNHek1scjVCWlFhL3R5eGRCNVU1M1M0Rzc1dzVNZHVWM3ZuK0hHS0RPbEFkN05xOEVsOFlXSEFQMk94N0FMa0pzKzhLQWJUWXlvQUtKYmlNYVMwWG9MUGllL3h5SEF5R0FqTlBOVWp5Z2NpNG9BYytEL2Jqa2IzajdqT3Q2NE5mQS9yM0NVdTJUNkZwcjlnaWJpMUYwRGFqMUlaUk0vQ0JZWElUVW5PMUtFQkFRUytxek4xN3Z3MWlUY1FBQm9odWkzcmRWQ3VaNmlZK3c2dkZxd041NG9SZGFrU3pvQllhbi95RTl6ZGp4aTE1aCtvMFRjWm9pUDlOY2kzVll3bXpyRW81MVN2RWtqYlRzL2swOUNiNDBVUksxalpPVkxSTm5oMlJIeXk4N00ySDM3RG8xQWMxMmd0ckIydlY1bHRqclFVYW4rS2c2WUZkY1QrZ0ZNYks2TlhOMHpsTjErbjY3cDJsbzl4MWgiLCJtYWMiOiJhM2I2N2NjNmZlNmIyMGNmYzM2ZWZiODVlZmYzMGQwNzkwNTgzMDkxZWU1OWQxMTMxZDE4MGViZGJkNWYyZTRkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:10:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628283940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-758099028 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LauDBNluCWwZ8n0u4UuZIM9FUPtR0MHvAyjyd15X</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758099028\", {\"maxDepth\":0})</script>\n"}}