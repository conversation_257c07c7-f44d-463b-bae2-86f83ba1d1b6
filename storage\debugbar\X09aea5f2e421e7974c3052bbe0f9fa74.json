{"__meta": {"id": "X09aea5f2e421e7974c3052bbe0f9fa74", "datetime": "2025-07-30 02:42:18", "utime": **********.43172, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843335.581336, "end": **********.431773, "duration": 2.8504369258880615, "duration_str": "2.85s", "measures": [{"label": "Booting", "start": 1753843335.581336, "relative_start": 0, "end": **********.246263, "relative_end": **********.246263, "duration": 2.6649270057678223, "duration_str": "2.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.246293, "relative_start": 2.***************, "end": **********.431779, "relative_end": 5.9604644775390625e-06, "duration": 0.**************, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EibLGvZVI3JAHFKOJoexjizMedzDpXQrBLmNDGDh", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-516059939 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-516059939\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-390542774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-390542774\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-478159973 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-478159973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-849465210 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849465210\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1395795048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1395795048\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-956048545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:42:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhodHRGdDVMc1hySVZ4YkFXazFMblE9PSIsInZhbHVlIjoiV0huNnpYUm9ZN0gvN0NRZTZZak14M0RYaDRVdDhwZDFBUXVHRnhZaC9qbUY4ZFd0bUFnRkhRTlNDSTYwK3VGWmpVZWQzM1VwaXlIZjNNQ216cDcvbm0vYUh1cXk1TnZVVEJZbmV4OUtLZnZvb2JoYUd1a0tCNUZVVEVTaG9UNm5ZaDlpS1oxa1EzTDJRa0FjdDFJRTdrL3FBbjZNRVZjL2RmcjVmMjFLaDdIdVM3TDdtNmhOZ3ZkL081bTBnaG1HT2l2dC9kdFV1bCtzM0RTUnN6QzhzVzQ0Q0VCRXRPamRaWUo5UXozSUtnRW0zeUwzMDB4SFExVzBXS2Y4NWVpZlNhZlVCMUJLQU8xcm5scWtyUFBFUEYrUERvSGdiWTI4VExZYXlZUUdlQjVWcG5VaHoveWlWMnhZbitGcis1Y0FsNmNRTXBSYnhQa0dXMENWT09CZ0x5NllUN0ZkTkx6ZG5qNC9sUVpXdm5qazlHdmtHRzd6cHhOV1RaTWVTUGtxOWJlV2ltaFNRM242TWxSVjN5bVVJSFdaMCt2eExNZTc1WWJxWUtDZlhkSmtNdHR5a0hnbE56WERSVDkyTHhlQk5IR3VIblBaRnR6aVoxSUJicEVGTHFERDJCQWhNM25Xelg2MGJkdm85ejQ2enVlQVpmTWVaY0tQV1IwQndGNTYiLCJtYWMiOiI4OTI1ZDFhOTkwNzkzZGNhYjU4MDEyMGIyNGE2MDNlODI1YmU3NWM0ODdiOWFlZTNiNTJkNzJjZDU5NGQzODY1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:42:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImFZOHM3ZmQyVjNiUVE1OHdzVFlLVWc9PSIsInZhbHVlIjoidGU0dVZhTUw3bmNiREptZWVwdzliVkZrU1lHOUVUMHdZNHZSNXFETnlZUXVWTldQR2E5dFFsRm5WR3FxZW5BYllUZnNlRnpNSGJWRk1SeXdZUTRsTVJ5VGZPeE5nWkptTmdnc2R0OHV2YW1uNFc1TThTcnIrNjIxTnNOZWYyU0w4TzVreXd6WVpsSUVvTU1id0VYdytEVytqQXFYYWtBbnJJSGpLYnM4NGFVTnAxNC9tdkhwSnNmNDNYQXpaa25wZmo3QkpldElZanNSYURsTzYyRXY0RFcrYXpTakRKV1QyZnVhNVpOWmZHeTVMS3owT0lqdW1pcVI1NUxOZE5tWEZOTzJCMmplS0llTzhORHBMMHN4UUhZZ09WbnBFdkpCTWErb1hpMTNON2l4cUdxYVhiVUhLSWd1Sk4xYkpoT3JFTTBKWGxWRHpuNzArYzlwYmJyOVc4M0tSR3BuNVdzanhvaFNnWWM2cnFha3pucHlMTjA4RW9SN0VJMis0U0hhRGhnQjZHd2I5TmZBTmN2OGY3aWV3b1dIU3Y4ZzJmbUdUWTRyVGk3M3p6YlNzYmRKeGtBL3BZRFJha3JENDRubmgxSTdsSWYvcXdNelloT0lTb1ZYL3RQTkVMZEh4K1pEMEF1U3VQOGt4NnVTMzdlRE9ZNkpFSW1aRWFQWGNucmYiLCJtYWMiOiI3ZDU2NDhlYjg4NmM2MjJlNTVhYzllOWMyZWI4Mzk2MmQ3MjExMzY5MmYzNDRlNGNmYTMyYWFjNzM4YzgyOTUzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:42:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhodHRGdDVMc1hySVZ4YkFXazFMblE9PSIsInZhbHVlIjoiV0huNnpYUm9ZN0gvN0NRZTZZak14M0RYaDRVdDhwZDFBUXVHRnhZaC9qbUY4ZFd0bUFnRkhRTlNDSTYwK3VGWmpVZWQzM1VwaXlIZjNNQ216cDcvbm0vYUh1cXk1TnZVVEJZbmV4OUtLZnZvb2JoYUd1a0tCNUZVVEVTaG9UNm5ZaDlpS1oxa1EzTDJRa0FjdDFJRTdrL3FBbjZNRVZjL2RmcjVmMjFLaDdIdVM3TDdtNmhOZ3ZkL081bTBnaG1HT2l2dC9kdFV1bCtzM0RTUnN6QzhzVzQ0Q0VCRXRPamRaWUo5UXozSUtnRW0zeUwzMDB4SFExVzBXS2Y4NWVpZlNhZlVCMUJLQU8xcm5scWtyUFBFUEYrUERvSGdiWTI4VExZYXlZUUdlQjVWcG5VaHoveWlWMnhZbitGcis1Y0FsNmNRTXBSYnhQa0dXMENWT09CZ0x5NllUN0ZkTkx6ZG5qNC9sUVpXdm5qazlHdmtHRzd6cHhOV1RaTWVTUGtxOWJlV2ltaFNRM242TWxSVjN5bVVJSFdaMCt2eExNZTc1WWJxWUtDZlhkSmtNdHR5a0hnbE56WERSVDkyTHhlQk5IR3VIblBaRnR6aVoxSUJicEVGTHFERDJCQWhNM25Xelg2MGJkdm85ejQ2enVlQVpmTWVaY0tQV1IwQndGNTYiLCJtYWMiOiI4OTI1ZDFhOTkwNzkzZGNhYjU4MDEyMGIyNGE2MDNlODI1YmU3NWM0ODdiOWFlZTNiNTJkNzJjZDU5NGQzODY1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:42:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImFZOHM3ZmQyVjNiUVE1OHdzVFlLVWc9PSIsInZhbHVlIjoidGU0dVZhTUw3bmNiREptZWVwdzliVkZrU1lHOUVUMHdZNHZSNXFETnlZUXVWTldQR2E5dFFsRm5WR3FxZW5BYllUZnNlRnpNSGJWRk1SeXdZUTRsTVJ5VGZPeE5nWkptTmdnc2R0OHV2YW1uNFc1TThTcnIrNjIxTnNOZWYyU0w4TzVreXd6WVpsSUVvTU1id0VYdytEVytqQXFYYWtBbnJJSGpLYnM4NGFVTnAxNC9tdkhwSnNmNDNYQXpaa25wZmo3QkpldElZanNSYURsTzYyRXY0RFcrYXpTakRKV1QyZnVhNVpOWmZHeTVMS3owT0lqdW1pcVI1NUxOZE5tWEZOTzJCMmplS0llTzhORHBMMHN4UUhZZ09WbnBFdkpCTWErb1hpMTNON2l4cUdxYVhiVUhLSWd1Sk4xYkpoT3JFTTBKWGxWRHpuNzArYzlwYmJyOVc4M0tSR3BuNVdzanhvaFNnWWM2cnFha3pucHlMTjA4RW9SN0VJMis0U0hhRGhnQjZHd2I5TmZBTmN2OGY3aWV3b1dIU3Y4ZzJmbUdUWTRyVGk3M3p6YlNzYmRKeGtBL3BZRFJha3JENDRubmgxSTdsSWYvcXdNelloT0lTb1ZYL3RQTkVMZEh4K1pEMEF1U3VQOGt4NnVTMzdlRE9ZNkpFSW1aRWFQWGNucmYiLCJtYWMiOiI3ZDU2NDhlYjg4NmM2MjJlNTVhYzllOWMyZWI4Mzk2MmQ3MjExMzY5MmYzNDRlNGNmYTMyYWFjNzM4YzgyOTUzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:42:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956048545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-857246144 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EibLGvZVI3JAHFKOJoexjizMedzDpXQrBLmNDGDh</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857246144\", {\"maxDepth\":0})</script>\n"}}