{"__meta": {"id": "X0f5a14d05bc8afca85444f03a94ec008", "datetime": "2025-07-30 06:05:28", "utime": **********.254936, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855527.201644, "end": **********.254966, "duration": 1.0533220767974854, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1753855527.201644, "relative_start": 0, "end": **********.152947, "relative_end": **********.152947, "duration": 0.9513030052185059, "duration_str": "951ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.152973, "relative_start": 0.****************, "end": **********.254969, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Tl5ATJPCcJEUTFqW3mr0YLG8SNDV9OULfqLBKh9R", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1472422712 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1472422712\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1816749366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1816749366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1391312654 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391312654\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-190616659 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190616659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-658968858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-658968858\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1750814412 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:05:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ6SkpLa0ZNMTVRMWFqR0U5K0Fxb0E9PSIsInZhbHVlIjoieXIvZ1N4RTBFbSs2ZXlyUGF3VEpqQWRQTFc3SmMxems0SWQ5MkREaFlPWTJzdldJZXNPdk9YTFd3Z2JZMnVMWUdCT3dTNGNacldHWWZVeXFFSWVaRWV2cm5PYXBUYldMeHlySjRLb3RlQ2FrdU5yMFZMUE84T2wwNmFDRld4ejlGdmZ3SjAvS2dFbnFRQ3V5OFUzRUtEdDZhZVRQYlhrZjBEV2labEVrUzU4dGNuNWxPU2FlMElVK2kxU3pWbDI4L3k5RUR4ZmZaUlhxdUJMdGMwRXpWVTRWd090UFRvcnVja3ZMQW9Rbk1vYWx4NDFUc2lTYkNxbHg5VVZJZDBSRDNzMGkyVm1NZ0x1RzI4S0JDZ0llbWtPMitrT0hvampod2tiYzR4L2VPK2NoNjhqdkxYNmtCWWkybzJIQTZ6LzlmVWtlYlMxMTFFS056SjkwV2dTeUM4aXpxTlFkR2c2UFRGTkx0Wk53NDZNQWZEUmEwWmJuOXJhOWFhNmhxbGVKenJHUy95WEoxK3dXT0xhYlROT2pSam83M1lxc09YTVFXSmFLcVhrb3NXdHlsb0lYMnJLVHNFZ0dLWEUyRmlEOFZyUjc4V1pBWENiWUxBYmdLYml6NHQyMHVoQzlkVWpQUnhDSUZmY3RLcE04R0hhR0l4ZWJmeTJ6SzhFTWZtcVAiLCJtYWMiOiJhNTYxNDI3NjI3N2E4ZDc4M2JmYWQ1ZjViYmEwNjM4ZmFhOGQzZTk5MzMyZDU5OWU2NzMzNjI2MTQxM2ZhN2IxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iit2S3J5NFA1ZVZVRHdjWC9PWEcvNWc9PSIsInZhbHVlIjoieWRESmJCWlQ5UlFtRFZFTlQ3Uyt4SFU0NVQ2SEhQdTVXWWR6cjlPdUdJamo4ZDJZRFFuNGpsb2JMNUdKcG5lQ3ZMQ0JpbE9RTTlacFlKUnZGNkFrSmUzU05NT2FvQ0NVT3pwMkQvWEQrV0VMdDdLY3Y2cUZtb1BTcVhpUldubU1seERSQ0hXeXdjSGhTZlBVdUw0VGorSjgrTEpGVENKdzZaL0tYKy9UdG5PdlpIL0dKcExrMmFWY2FHanU3QkszMCszYVNrUTZOcU44RzBIVDc4ZWI4SkNGNUxEczNBR1A5QVFtWUpWSXJCM2o0RDZzYlA1SjBwWVdzVUxielNuNHY2Z3hJNitlSXNEZWpQL2ZBSGtFWW1COFE4SlJ4REhYbEl3MXJHUGZFenp0MnMyUHVnY1M4cHFxUUtBSDY5TGtVRlZVSG5mYlhJMmgwR25Pa0RETW9ZNG9ncGtpcElzZlRxRjE2UStCQlhyYk5uOW5sQUt4ZFlSSjdpQWJoeHdZQmtQcWRPQXk3dnVnSzdqUVNlYUVjYWpvK2hXSDJFL0dRdmtiQzlPLzBIdmxydGVmczRrRVFuMGFwb1FoWDVhMXhIQTFuS2EzNDZZa3VSSCtpZFd0dytwTm9qeGNxbWw3R0JpbWxXanVRS2dOZEFVQnM2QzQzVGdmRFBxQWZSRU0iLCJtYWMiOiI5OWEyYjYxYmI0NTE0NzljYjY2MDY1MjdlZTNhMmNlZmIxNTE0MDMxYmVhNTFkZGU5YTg3NDA4Yzg5ZWE2ODMxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:05:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ6SkpLa0ZNMTVRMWFqR0U5K0Fxb0E9PSIsInZhbHVlIjoieXIvZ1N4RTBFbSs2ZXlyUGF3VEpqQWRQTFc3SmMxems0SWQ5MkREaFlPWTJzdldJZXNPdk9YTFd3Z2JZMnVMWUdCT3dTNGNacldHWWZVeXFFSWVaRWV2cm5PYXBUYldMeHlySjRLb3RlQ2FrdU5yMFZMUE84T2wwNmFDRld4ejlGdmZ3SjAvS2dFbnFRQ3V5OFUzRUtEdDZhZVRQYlhrZjBEV2labEVrUzU4dGNuNWxPU2FlMElVK2kxU3pWbDI4L3k5RUR4ZmZaUlhxdUJMdGMwRXpWVTRWd090UFRvcnVja3ZMQW9Rbk1vYWx4NDFUc2lTYkNxbHg5VVZJZDBSRDNzMGkyVm1NZ0x1RzI4S0JDZ0llbWtPMitrT0hvampod2tiYzR4L2VPK2NoNjhqdkxYNmtCWWkybzJIQTZ6LzlmVWtlYlMxMTFFS056SjkwV2dTeUM4aXpxTlFkR2c2UFRGTkx0Wk53NDZNQWZEUmEwWmJuOXJhOWFhNmhxbGVKenJHUy95WEoxK3dXT0xhYlROT2pSam83M1lxc09YTVFXSmFLcVhrb3NXdHlsb0lYMnJLVHNFZ0dLWEUyRmlEOFZyUjc4V1pBWENiWUxBYmdLYml6NHQyMHVoQzlkVWpQUnhDSUZmY3RLcE04R0hhR0l4ZWJmeTJ6SzhFTWZtcVAiLCJtYWMiOiJhNTYxNDI3NjI3N2E4ZDc4M2JmYWQ1ZjViYmEwNjM4ZmFhOGQzZTk5MzMyZDU5OWU2NzMzNjI2MTQxM2ZhN2IxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iit2S3J5NFA1ZVZVRHdjWC9PWEcvNWc9PSIsInZhbHVlIjoieWRESmJCWlQ5UlFtRFZFTlQ3Uyt4SFU0NVQ2SEhQdTVXWWR6cjlPdUdJamo4ZDJZRFFuNGpsb2JMNUdKcG5lQ3ZMQ0JpbE9RTTlacFlKUnZGNkFrSmUzU05NT2FvQ0NVT3pwMkQvWEQrV0VMdDdLY3Y2cUZtb1BTcVhpUldubU1seERSQ0hXeXdjSGhTZlBVdUw0VGorSjgrTEpGVENKdzZaL0tYKy9UdG5PdlpIL0dKcExrMmFWY2FHanU3QkszMCszYVNrUTZOcU44RzBIVDc4ZWI4SkNGNUxEczNBR1A5QVFtWUpWSXJCM2o0RDZzYlA1SjBwWVdzVUxielNuNHY2Z3hJNitlSXNEZWpQL2ZBSGtFWW1COFE4SlJ4REhYbEl3MXJHUGZFenp0MnMyUHVnY1M4cHFxUUtBSDY5TGtVRlZVSG5mYlhJMmgwR25Pa0RETW9ZNG9ncGtpcElzZlRxRjE2UStCQlhyYk5uOW5sQUt4ZFlSSjdpQWJoeHdZQmtQcWRPQXk3dnVnSzdqUVNlYUVjYWpvK2hXSDJFL0dRdmtiQzlPLzBIdmxydGVmczRrRVFuMGFwb1FoWDVhMXhIQTFuS2EzNDZZa3VSSCtpZFd0dytwTm9qeGNxbWw3R0JpbWxXanVRS2dOZEFVQnM2QzQzVGdmRFBxQWZSRU0iLCJtYWMiOiI5OWEyYjYxYmI0NTE0NzljYjY2MDY1MjdlZTNhMmNlZmIxNTE0MDMxYmVhNTFkZGU5YTg3NDA4Yzg5ZWE2ODMxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:05:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750814412\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-114338143 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Tl5ATJPCcJEUTFqW3mr0YLG8SNDV9OULfqLBKh9R</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114338143\", {\"maxDepth\":0})</script>\n"}}