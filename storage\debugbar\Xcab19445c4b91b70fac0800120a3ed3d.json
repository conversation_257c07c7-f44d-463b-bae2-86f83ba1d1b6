{"__meta": {"id": "Xcab19445c4b91b70fac0800120a3ed3d", "datetime": "2025-07-30 08:02:49", "utime": **********.785552, "method": "GET", "uri": "/finance/sales/contacts/search?search=gun", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862568.089506, "end": **********.785614, "duration": 1.696108102798462, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1753862568.089506, "relative_start": 0, "end": **********.45828, "relative_end": **********.45828, "duration": 1.368774175643921, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.458309, "relative_start": 1.3688030242919922, "end": **********.78562, "relative_end": 5.9604644775390625e-06, "duration": 0.32731103897094727, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665256, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03106, "accumulated_duration_str": "31.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.633278, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 79.137}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7101362, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 79.137, "width_percent": 5.988}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%gun%' or `email` like '%gun%' or `contact` like '%gun%')", "type": "query", "params": [], "bindings": ["79", "1", "%gun%", "%gun%", "%gun%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.731494, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 85.126, "width_percent": 7.824}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%gun%' or `email` like '%gun%' or `phone` like '%gun%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%gun%", "%gun%", "%gun%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7456799, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 92.949, "width_percent": 7.051}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1621618801 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1621618801\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-790791367 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">gun</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790791367\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1357920083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357920083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112902302 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im9Ma0dNOWJTbmxwbGRnWm9wdWl1bVE9PSIsInZhbHVlIjoidkZXUFpWYmtRYzhRNmdYYzVLeDh0aVRoT1grSm1yUk1LNkxseUxTQTBOYVNzNEdEY2hXTzNXMW5hVHZMd25CZzZ5c2lEZmhMUXNSVHRwaU5tV0x6L3ZaWTd3WVZzTzdSVXQ4bkJMUkJYMnBINVBkcmQvcE0zMFN4aGZDcnZMaWp2Y0tjdHd0UTlPNnlFdjRjSHlrVmNvTGJRaC82dFFpV0dtejdnNFFQdlRlK1dVVDhOZllOMWIzaXVhZnZ3VTkxb3BwZjY3V0ZGL3BRVG5IVHpwYythbWwwTGpNbnFFR09LbGlEMnR6Y2hicHdRS1B2eklvYk5TT242aklIWk5Wb3R3cWQrREhrU1o1SytqTkZua29mTURzKzJhSHNwVmV6VWtxc2JUU2QzMFJRMHZNL0RVdXpJSFByemRLYk5NaENDVWh4YUFSRWV4RFpaNjVXanVQVjZFSXJFc0orMVFLQU9rRVNPN1NHSWdFcGd2UXBsSlJLRGVpdU0rWW1uaVJMMHpkZ0FyNzNFUzVINGF5WlRiK0YySVJ6WVh4N1JjSC9lNGRqMEZjYnR4Z3J5M0dJY2VUQmZmV2tBY3MxcjlIcno0Wlp2NHlQcmJVTUF5b05iY0lZR050Zmtsd1V0ZkJrTENLSnhEUHMydlN1NXdBRFlGSlBvcEdIK0lKVzE3ZzAiLCJtYWMiOiJhMmRlYWRlM2FhYmRjNzk1MDJkY2ZiY2RkYzAwZGYzNjdkNzE3NjNjMmM4OTQxY2Y3YmMyZThjNjRlZjQ2MjlkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InBLTk5yMG1MNTRhN2l2VG51WktiOGc9PSIsInZhbHVlIjoiSm9zZXdoQWphR2FiVW0yL3RHMVN1OHFZTjhiNUhVbW5salJNK2svWnc4S3FBQlFENWR2TWM3TU5jUnR6b2FsWGw5dFlYL2o2MzBPQ0QySXRGTjBMaWxOTDFHSzh3dmZleldPSXFCMHk1eVlGaDZjTUNFWDFuMVp3NllSNnhGOGJMSHZFUDEvL3IxcXU0SVFaeTB1OFNyNmhRNG12TjJZckdndldYSHVTRzl4aWlZK3F3NDBYQzB1WU1ZWkwrREdRRkhBMDFKZXprWVpnbDhxSDFoSG00L2ZsWW15WFNsYUc3VmZOZElJMnpDdmVDdFFLSjJJaHJKS3NNaENWbENzV1cydHRRMUVGK0JnOUtkTGlnZndvcEVhVDY2bzExamVaZ25YUSttaHlZbUx5ZEZHdk1CMTVCTTYxelN4M09qTTgrWjBvbWpHVVNxMGU5ZnFBYWtMUzhkNzZDK2xkMk93YVFsa2ZXYi93KytMSUN6MExMZUpGQ3FaN01sT1JuVWt1bVFiOTBXZWMreUY2azZpdE5ZRWUzbVI3cEFsd0x1T3dFYXA4RUFieWpEcHN0eGNBbUJodkNXOWk1K2ZHaWtQemVwTmgvSXNYOWFQT3JJekNtaURaTC9LRDlSSUtRUk9JZ3BBZTY2elZIQ1owWWoyV1NERlNJSTBFY2hMKzJ3MVgiLCJtYWMiOiJjZWQxYWQ5OTJkNDg4MTIwNmU0NTVjOGQ5MjliMmRiYjFkOTlmMTFhYzhkYTVjMDI4NWE3NTliMTViYTE2MmYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112902302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1160745276 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160745276\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-155132567 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd4S0loTW5aOVYzSDBpaU43K3JvU1E9PSIsInZhbHVlIjoic3orVVltallZQ2dTNXQ2dEY4SW4xOTFqR0tUYmlyUVV4VThYU0VCakRvMlR6Nmd0UmJFZHlOTWRLdUdWYUxzbzd1QzY3UU13b3dqSVZ2R0ZvVytQUXpyaVdVQ2Vnb01KbnBjakl2ZkdBeGxzUU40QldyVXZ1Mks5clNrMUcrZHBqVHhWZnI3T3ppRkgyNDV6ZmpHcWJCK0RGWTcrdE5wd2NkWGcwM0NvZ0ZWamh1NWJJeVYyY1c4bVprdEc5R0hCQWpsMXpkT2NOd1FLdFVOZTJrR09DMFhEOE5oT1hPNFEvKzZ0Z0NkQ05hdXVnTXJCZjg0UDViQnNpQ041b3BGODRCSm1pQWw2c1NzSklwQ2pMNmVvL3RLRnExRk9RVFpYYmVoeWpjVTRtV2tnUXZMbm9YSjJXYVhCTXIvQ0JYdnFuUkhGRXVNUDMxbFFMazlBNGc3SVE3QUIzTDNYTkRpZGxMRk5id0hxdGxkWWJNRjVGR0Nmc3dTM3NYUWNlTTRPTUp3bHIrbG1mZ3Vad3cvMlgvekN4ekFNVXFjeGYzbG5rdGFINFdTamVYd1h6TVIzZEsrTjIvbGhUeWhkaVlLOHpVTTI4aUo1NllvbWplbmprOTZPVDRZdHNZM212TXJPajZqU2pVRWwzMytyeHpXMi9UdU9YcGtwbmJ2VzhmWTEiLCJtYWMiOiJiYjM5MTgxNGExZjVhZDBkM2I5MTMwMjBmZDZmNzdmMThlNDgzY2QyZmJjNTA5MzlhY2Q1NmNkZTU0YTkwYjQzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InQ1NEFMZ0NKMEhCckpjNzlHQ2NuUUE9PSIsInZhbHVlIjoiVXk3VGJNR2pNYUFZQWFLNlptck0zdG9RQnV3TSswQ0JzR1E2LzNZeEVBWUxYZWNmNVBVclRnVGZaV1ZHVHoxNVZka3dnT0QxL3hVT1I2aTU5ZXhhYkFwaVFRYjdiVWthbkFFUmlnMzhSMG9BT2V4NDJxRzVBa3JMUnptSUc1NVhvSU1MMndIYkVNNzRvR1lvM0hvZ20zSDUrWDVjQldKVEVQQ1laVlR1Witsa3c0aEV1eDRzK1VQSUpnRVJNVGNDa2FNWkRpVHAyanMxQ0ptT0R3OUMvTDZyT0ltWms5dlMwMmtTcndTbWE1TTZlS0pSL2U2M3RVL2gxcHRCZTBMY045VWFpTTNTRTUrWnYrNkdicmd3Q0gxWE1TR2M2RXNTbHN4NVFkQWN2NHpUZ1l2b1lqVXhaTW5FNktpSFNCTWZ3R2xBN3dzUW02NHRXMHRxb1d6VzhKb2c1U1EyaGFHQmNxUmNqTXlJVS8vWVVLaWFXcy95c3oyc3g3eTl5OGNldUdvK2dPa2JQZktWR3dkN0ZuRnc3MnlzN0FGOGZrVFBVekZOTnNIb3N5dDQ0MHhqdU1QcXBwcjFVM1NFZWVJeGMvZmtvd1B3QmxWbXEwRFE0N0Rib2NXcFp0YUpVaHFYTk10Z3F5OXFtS3hxWWhUOEpoSVR2b09TUWJjUkJKbkIiLCJtYWMiOiIyZDE1OTEyMTcxZmIyMDdmOTgyZWY5OTUzNjM1YjM2MTE3MzE3YmUzNTVhNzNlZmE5ZmRmYjM0NTViYWQwNGZmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd4S0loTW5aOVYzSDBpaU43K3JvU1E9PSIsInZhbHVlIjoic3orVVltallZQ2dTNXQ2dEY4SW4xOTFqR0tUYmlyUVV4VThYU0VCakRvMlR6Nmd0UmJFZHlOTWRLdUdWYUxzbzd1QzY3UU13b3dqSVZ2R0ZvVytQUXpyaVdVQ2Vnb01KbnBjakl2ZkdBeGxzUU40QldyVXZ1Mks5clNrMUcrZHBqVHhWZnI3T3ppRkgyNDV6ZmpHcWJCK0RGWTcrdE5wd2NkWGcwM0NvZ0ZWamh1NWJJeVYyY1c4bVprdEc5R0hCQWpsMXpkT2NOd1FLdFVOZTJrR09DMFhEOE5oT1hPNFEvKzZ0Z0NkQ05hdXVnTXJCZjg0UDViQnNpQ041b3BGODRCSm1pQWw2c1NzSklwQ2pMNmVvL3RLRnExRk9RVFpYYmVoeWpjVTRtV2tnUXZMbm9YSjJXYVhCTXIvQ0JYdnFuUkhGRXVNUDMxbFFMazlBNGc3SVE3QUIzTDNYTkRpZGxMRk5id0hxdGxkWWJNRjVGR0Nmc3dTM3NYUWNlTTRPTUp3bHIrbG1mZ3Vad3cvMlgvekN4ekFNVXFjeGYzbG5rdGFINFdTamVYd1h6TVIzZEsrTjIvbGhUeWhkaVlLOHpVTTI4aUo1NllvbWplbmprOTZPVDRZdHNZM212TXJPajZqU2pVRWwzMytyeHpXMi9UdU9YcGtwbmJ2VzhmWTEiLCJtYWMiOiJiYjM5MTgxNGExZjVhZDBkM2I5MTMwMjBmZDZmNzdmMThlNDgzY2QyZmJjNTA5MzlhY2Q1NmNkZTU0YTkwYjQzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InQ1NEFMZ0NKMEhCckpjNzlHQ2NuUUE9PSIsInZhbHVlIjoiVXk3VGJNR2pNYUFZQWFLNlptck0zdG9RQnV3TSswQ0JzR1E2LzNZeEVBWUxYZWNmNVBVclRnVGZaV1ZHVHoxNVZka3dnT0QxL3hVT1I2aTU5ZXhhYkFwaVFRYjdiVWthbkFFUmlnMzhSMG9BT2V4NDJxRzVBa3JMUnptSUc1NVhvSU1MMndIYkVNNzRvR1lvM0hvZ20zSDUrWDVjQldKVEVQQ1laVlR1Witsa3c0aEV1eDRzK1VQSUpnRVJNVGNDa2FNWkRpVHAyanMxQ0ptT0R3OUMvTDZyT0ltWms5dlMwMmtTcndTbWE1TTZlS0pSL2U2M3RVL2gxcHRCZTBMY045VWFpTTNTRTUrWnYrNkdicmd3Q0gxWE1TR2M2RXNTbHN4NVFkQWN2NHpUZ1l2b1lqVXhaTW5FNktpSFNCTWZ3R2xBN3dzUW02NHRXMHRxb1d6VzhKb2c1U1EyaGFHQmNxUmNqTXlJVS8vWVVLaWFXcy95c3oyc3g3eTl5OGNldUdvK2dPa2JQZktWR3dkN0ZuRnc3MnlzN0FGOGZrVFBVekZOTnNIb3N5dDQ0MHhqdU1QcXBwcjFVM1NFZWVJeGMvZmtvd1B3QmxWbXEwRFE0N0Rib2NXcFp0YUpVaHFYTk10Z3F5OXFtS3hxWWhUOEpoSVR2b09TUWJjUkJKbkIiLCJtYWMiOiIyZDE1OTEyMTcxZmIyMDdmOTgyZWY5OTUzNjM1YjM2MTE3MzE3YmUzNTVhNzNlZmE5ZmRmYjM0NTViYWQwNGZmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155132567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2133100729 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133100729\", {\"maxDepth\":0})</script>\n"}}