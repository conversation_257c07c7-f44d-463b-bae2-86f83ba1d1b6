{"__meta": {"id": "X5e4431808c44f35e8bbff31aab86c016", "datetime": "2025-07-30 08:07:30", "utime": **********.240208, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862847.449618, "end": **********.240247, "duration": 2.7906289100646973, "duration_str": "2.79s", "measures": [{"label": "Booting", "start": 1753862847.449618, "relative_start": 0, "end": **********.086529, "relative_end": **********.086529, "duration": 2.636910915374756, "duration_str": "2.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086563, "relative_start": 2.****************, "end": **********.240252, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tBMirT70z0vNLzoAnSl2kVxLH4U29Knlhx7sdaHM", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-289824990 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-289824990\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1572422535 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1572422535\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256956448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256956448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1855357995 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855357995\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1690878127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1690878127\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1639436634 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:07:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpRUCtqTkJQNDNvaGNubmhTM1RsVmc9PSIsInZhbHVlIjoiZTRyRWdySzNkbXNHQUFOajVxWWcyTmh2WXhWWHQ0amJoOEdJR2h2UElhVmsvVENMZEdXNm94S3lMNGpRT0ZqQXpXY1NYR3NrMmd0WGlnem1QcCt2RG4wZmp5YXpTNHhmaHB2a0RWTjF4dHFUdmxxYzhpNkpVNDNXYnFOZ2Q3TjNjM0xndHNXdklDWWV1VDAwSjFiVE9UVk5LeDc5UG1aTU1MZTI5aFEyUWFRK2FPYnRwdkJ0VlV4dEgwZnM0c3BFT0RNTXgwamVTQjJKZ2VTRmFmODRWZ3RtalZQYzBjWmhQS2NyQTM4MmhNUmdUUzVUbmlxT0k2L09IVWloUTNwM3FzdS9obWtsZlYrZFlKaDE4M0lraTJZb1BDMTVIN3U3UjlsOWkrWlZ3VHRjOFBqR1ZuUkxHYndBbFN4VjVoMGg3dTlqai9oZHNLUWRyN1JUSjc5YS9WUzczMWN4Z0R4bEdDaWpjcUpmRzdKelFxMmhmbnE0SXJSUGN2cXowZ1JoZ3Jscm5iV2NEZ3pDQlNjd0N0NlZRc2NPTk5KQW1WeTFPbWhSMlBidGNQZm1XTit3R28wcmg3dXBtUVB2aUxiTVVvS245bnNpOTFKOFlDb1JRUm50dnpneU5ZTitDT2dtejZ4N0dUZm9XbThjZEhPb0JjWjFQV05DSTZERDlSQ1ciLCJtYWMiOiIzMmI3MTBjNDJhNWU3ZWE1YTk5YTIyZGZlMjVhODVjYTIxZWU1M2MwNmViOGE5YWFkN2QwOWEwOTAzM2YzYjFkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikh0aEhHRHpBbEZTRTgrU2tvVkVwZ2c9PSIsInZhbHVlIjoielNQWm9lZSt5M01YMWljbk5Wdnd4VkRBOGRLcDRkc0JYZlp0OFpoZVB2clpCVTFtTDdqNnNURmp3bW4raGFpNFViMTdnQk5XaEMzYmo1clJMcWc0Zkt4Y0k3VmoxOWNrbFkyWGE5MGRNZEhOR21UbEJUdEJ2KytTMm9BZmpPTDROSldHOHg1aGNjckpvUHZnYTNwZXlpNUtLb0NtNTd2VWJqSitDTnFQR2x4RG55a1VjL0lpT240WHp0Sm5DQlo3aG0rVS9yUXFSQVFMNHNxNG5YSExqcEozRWlSQ09sWit1RWh5WVRkbkF2NDZNQmN1a3pESm5tKzE1Zm92VGhPVHZFWjcxTzR6YStCZW9CMnYrWXU2cHFKMXFPT3JJVzRTMExocXlhQVI4UDZvUS9jQjVCMDFqek1lYk4yOXJmOTZqTmFuZkFGWlRKRGtwUDJvMmQ1K1JIbU1QbzB6N1VlSmJwZ2ZEdXhJUEw0YXQrakFlOXV4NG94blpoa256VkcwN0xYTGMvVzFaMVAybkg5ZzFJdWxEN25TbnV3cE5pRmNJVWYzWEJwcUpaY1pZbkpyQjhYdDVHTVFaWGFBUGpNaU93emJKcEVzN3JWV3ozSy8zL2Jobng5MzlOZlVPWkRWME9Fa1IvM1UwcnFKbUZDK0dWTHd1M0hiZFRCMFEwUDEiLCJtYWMiOiI3NWIzMTZhMGQ1YTg3NjNlZjMxYjdlYzMzNmYwZDg5NGZiMjE2OTllOWE5ZWVhOGQ0NTU4OWE4NDFlZDg1YTQyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpRUCtqTkJQNDNvaGNubmhTM1RsVmc9PSIsInZhbHVlIjoiZTRyRWdySzNkbXNHQUFOajVxWWcyTmh2WXhWWHQ0amJoOEdJR2h2UElhVmsvVENMZEdXNm94S3lMNGpRT0ZqQXpXY1NYR3NrMmd0WGlnem1QcCt2RG4wZmp5YXpTNHhmaHB2a0RWTjF4dHFUdmxxYzhpNkpVNDNXYnFOZ2Q3TjNjM0xndHNXdklDWWV1VDAwSjFiVE9UVk5LeDc5UG1aTU1MZTI5aFEyUWFRK2FPYnRwdkJ0VlV4dEgwZnM0c3BFT0RNTXgwamVTQjJKZ2VTRmFmODRWZ3RtalZQYzBjWmhQS2NyQTM4MmhNUmdUUzVUbmlxT0k2L09IVWloUTNwM3FzdS9obWtsZlYrZFlKaDE4M0lraTJZb1BDMTVIN3U3UjlsOWkrWlZ3VHRjOFBqR1ZuUkxHYndBbFN4VjVoMGg3dTlqai9oZHNLUWRyN1JUSjc5YS9WUzczMWN4Z0R4bEdDaWpjcUpmRzdKelFxMmhmbnE0SXJSUGN2cXowZ1JoZ3Jscm5iV2NEZ3pDQlNjd0N0NlZRc2NPTk5KQW1WeTFPbWhSMlBidGNQZm1XTit3R28wcmg3dXBtUVB2aUxiTVVvS245bnNpOTFKOFlDb1JRUm50dnpneU5ZTitDT2dtejZ4N0dUZm9XbThjZEhPb0JjWjFQV05DSTZERDlSQ1ciLCJtYWMiOiIzMmI3MTBjNDJhNWU3ZWE1YTk5YTIyZGZlMjVhODVjYTIxZWU1M2MwNmViOGE5YWFkN2QwOWEwOTAzM2YzYjFkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikh0aEhHRHpBbEZTRTgrU2tvVkVwZ2c9PSIsInZhbHVlIjoielNQWm9lZSt5M01YMWljbk5Wdnd4VkRBOGRLcDRkc0JYZlp0OFpoZVB2clpCVTFtTDdqNnNURmp3bW4raGFpNFViMTdnQk5XaEMzYmo1clJMcWc0Zkt4Y0k3VmoxOWNrbFkyWGE5MGRNZEhOR21UbEJUdEJ2KytTMm9BZmpPTDROSldHOHg1aGNjckpvUHZnYTNwZXlpNUtLb0NtNTd2VWJqSitDTnFQR2x4RG55a1VjL0lpT240WHp0Sm5DQlo3aG0rVS9yUXFSQVFMNHNxNG5YSExqcEozRWlSQ09sWit1RWh5WVRkbkF2NDZNQmN1a3pESm5tKzE1Zm92VGhPVHZFWjcxTzR6YStCZW9CMnYrWXU2cHFKMXFPT3JJVzRTMExocXlhQVI4UDZvUS9jQjVCMDFqek1lYk4yOXJmOTZqTmFuZkFGWlRKRGtwUDJvMmQ1K1JIbU1QbzB6N1VlSmJwZ2ZEdXhJUEw0YXQrakFlOXV4NG94blpoa256VkcwN0xYTGMvVzFaMVAybkg5ZzFJdWxEN25TbnV3cE5pRmNJVWYzWEJwcUpaY1pZbkpyQjhYdDVHTVFaWGFBUGpNaU93emJKcEVzN3JWV3ozSy8zL2Jobng5MzlOZlVPWkRWME9Fa1IvM1UwcnFKbUZDK0dWTHd1M0hiZFRCMFEwUDEiLCJtYWMiOiI3NWIzMTZhMGQ1YTg3NjNlZjMxYjdlYzMzNmYwZDg5NGZiMjE2OTllOWE5ZWVhOGQ0NTU4OWE4NDFlZDg1YTQyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639436634\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1709213828 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tBMirT70z0vNLzoAnSl2kVxLH4U29Knlhx7sdaHM</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709213828\", {\"maxDepth\":0})</script>\n"}}