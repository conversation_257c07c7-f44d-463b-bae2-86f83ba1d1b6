{"__meta": {"id": "Xb96dc56bc1583f7977b7c75127eb1ef5", "datetime": "2025-07-30 02:37:10", "utime": **********.095617, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843026.781099, "end": **********.095671, "duration": 3.3145718574523926, "duration_str": "3.31s", "measures": [{"label": "Booting", "start": 1753843026.781099, "relative_start": 0, "end": 1753843029.890239, "relative_end": 1753843029.890239, "duration": 3.109139919281006, "duration_str": "3.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753843029.890275, "relative_start": 3.**************, "end": **********.095678, "relative_end": 7.152557373046875e-06, "duration": 0.*****************, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "09MxLIYi5KrOAOH7LQkDzLGBULjx6niAHgbrlyux", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1226490125 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1226490125\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-438305594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-438305594\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-699817749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-699817749\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-888019598 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888019598\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1513952351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1513952351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:37:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldjakdENnFFWll4UlhpNEJRcitUa1E9PSIsInZhbHVlIjoiamZ1OFk3RVhPVjdUY3ZMOHJ5dzJSRk9XdUhhVXJOSnVnZENaeHJkZjUzNDREU2lycGdLZTBCMHlGZFdvYzdUeElRWCs2LzdUWE1mOUN4MTF2b1NVUTlCSHA5bUxHUGJVdFFBMDk5RENtajJJSkhqaEdhSkRuTDJDL0VPMHZHMmN0RGUrNW03dnlhdW40VmMrZnJDUyt5dEFvNmgwaUNPOWJhNGYwRCs2OGJEM2JjSXNzdmQ5V0lZL1ZpTEUxWVFFL1k3K0MyWHFaa2NkSU9vTUlTR0pMUkxPdkpLd1MrNG1OcmJrZ1YxTlRwWXpzZXZ2TUFPTmI0OWtaRFpPUGExTENwTEVjOWxCSmtFVEVWMmJwcWhNak1aeVRTNkMwQ0JFcVVQcTJhVk9lcE1IMCs3SWZpc1NuYUlzeExrL3RObGRPeXBNbnZpT3R1a1VsV3BQNnlRNUswMEp2d2RoRmNyY2FGOC9ZckFncXB0MXdhM1gvNzBDb3htem5jSTdpNllmNXZkTnd2cGQwWnQ4MTJxVFE2VlZ6SzNEVTViakMwL3lkT3lHWGlIK0JiQlByQUNyRVJPY1l3STUweG0yU0NoajJ4enM0Z3NCZHNZR09FeTl3dTNRQ0l6RHo3N1BSam5LN3lyMi84OEZpQlBwcm83eUZZSzl3NjJuM0p0aSs1eXUiLCJtYWMiOiI2MjFlNDY3MDhhNTc1ZWZlNGE5ZmJhZTNmYTIzNjQzZmM3N2RmOWNjYjNlYTg2M2JmMDYxMGZmNjAxZDRkMTBjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkErSUJlL2NOVnJjazQrL2Y4a2pISXc9PSIsInZhbHVlIjoiOE9aNnJvVTRpYUV5R0RBM3JGbDBmbFliellNWmdqOGNUNkFudDcrWFIweC8rWGdZWXBTTEh5T1Y1UGJLYXcwcDM1cGJzUnZ2RS9XRDA3OEJ0M01sMnBXb0NPYmdzQUYya21uNndQSjVGRE95bFZGZ0FZUXB0ZDh3c2RpcTlqRVZSYmNMNmpkZlUwcFJNaVJrdUR3cmlFd2ZZc1k1bkdKT2t5ZmJlWElNemp5WFRHYTlnSkpPZ3ZJM256eGswZTA4VjBjRC9Ca2FNUytqYzJ1ZllUd0hRMlV4eWZWSCtnTlRkN0dzQVFDcUFZQ001ekRGUS8wV0NVdkJaTzMybGtuUEowNjU3d3plZGppK2lHZFRnTzhJZmhiV3N1a3JmZnpnMVczVkh4T1lDb3ArbUpvMmYxMVZDbWZFeG5OUlVHa0N5SlJIcUdPNzJlWmNsMXVZem5rKzMvVGI5VlZKanBPOXZTZDlDUE0vUGo1TVRtRVVJRXN3SUh5MDE1TUpZWFJqWkJ0UnVqUWlBc2tob1MvWWpWU3NuVE54WjNkdmpWRW9Da3dPLyswVytCaFJkUjVlb0dSR1N5ZTZZSnJ2MWZ5Tk9pRTBRbm16VnZOVGZOTmJDLyt5cHRyK2V2VXZucmx2bm5qREMzWklLUjFZbzU1aEc0Z051anprelRlcDF2MXgiLCJtYWMiOiI1ZGUzNWI5OWI3ZWM2NTRmZDRjMjJhZTZmM2RhM2I2OGJkYjg4NDhmYjI3OWY1Zjk2ZGZlOGY3NzFmZGFhMTJmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldjakdENnFFWll4UlhpNEJRcitUa1E9PSIsInZhbHVlIjoiamZ1OFk3RVhPVjdUY3ZMOHJ5dzJSRk9XdUhhVXJOSnVnZENaeHJkZjUzNDREU2lycGdLZTBCMHlGZFdvYzdUeElRWCs2LzdUWE1mOUN4MTF2b1NVUTlCSHA5bUxHUGJVdFFBMDk5RENtajJJSkhqaEdhSkRuTDJDL0VPMHZHMmN0RGUrNW03dnlhdW40VmMrZnJDUyt5dEFvNmgwaUNPOWJhNGYwRCs2OGJEM2JjSXNzdmQ5V0lZL1ZpTEUxWVFFL1k3K0MyWHFaa2NkSU9vTUlTR0pMUkxPdkpLd1MrNG1OcmJrZ1YxTlRwWXpzZXZ2TUFPTmI0OWtaRFpPUGExTENwTEVjOWxCSmtFVEVWMmJwcWhNak1aeVRTNkMwQ0JFcVVQcTJhVk9lcE1IMCs3SWZpc1NuYUlzeExrL3RObGRPeXBNbnZpT3R1a1VsV3BQNnlRNUswMEp2d2RoRmNyY2FGOC9ZckFncXB0MXdhM1gvNzBDb3htem5jSTdpNllmNXZkTnd2cGQwWnQ4MTJxVFE2VlZ6SzNEVTViakMwL3lkT3lHWGlIK0JiQlByQUNyRVJPY1l3STUweG0yU0NoajJ4enM0Z3NCZHNZR09FeTl3dTNRQ0l6RHo3N1BSam5LN3lyMi84OEZpQlBwcm83eUZZSzl3NjJuM0p0aSs1eXUiLCJtYWMiOiI2MjFlNDY3MDhhNTc1ZWZlNGE5ZmJhZTNmYTIzNjQzZmM3N2RmOWNjYjNlYTg2M2JmMDYxMGZmNjAxZDRkMTBjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkErSUJlL2NOVnJjazQrL2Y4a2pISXc9PSIsInZhbHVlIjoiOE9aNnJvVTRpYUV5R0RBM3JGbDBmbFliellNWmdqOGNUNkFudDcrWFIweC8rWGdZWXBTTEh5T1Y1UGJLYXcwcDM1cGJzUnZ2RS9XRDA3OEJ0M01sMnBXb0NPYmdzQUYya21uNndQSjVGRE95bFZGZ0FZUXB0ZDh3c2RpcTlqRVZSYmNMNmpkZlUwcFJNaVJrdUR3cmlFd2ZZc1k1bkdKT2t5ZmJlWElNemp5WFRHYTlnSkpPZ3ZJM256eGswZTA4VjBjRC9Ca2FNUytqYzJ1ZllUd0hRMlV4eWZWSCtnTlRkN0dzQVFDcUFZQ001ekRGUS8wV0NVdkJaTzMybGtuUEowNjU3d3plZGppK2lHZFRnTzhJZmhiV3N1a3JmZnpnMVczVkh4T1lDb3ArbUpvMmYxMVZDbWZFeG5OUlVHa0N5SlJIcUdPNzJlWmNsMXVZem5rKzMvVGI5VlZKanBPOXZTZDlDUE0vUGo1TVRtRVVJRXN3SUh5MDE1TUpZWFJqWkJ0UnVqUWlBc2tob1MvWWpWU3NuVE54WjNkdmpWRW9Da3dPLyswVytCaFJkUjVlb0dSR1N5ZTZZSnJ2MWZ5Tk9pRTBRbm16VnZOVGZOTmJDLyt5cHRyK2V2VXZucmx2bm5qREMzWklLUjFZbzU1aEc0Z051anprelRlcDF2MXgiLCJtYWMiOiI1ZGUzNWI5OWI3ZWM2NTRmZDRjMjJhZTZmM2RhM2I2OGJkYjg4NDhmYjI3OWY1Zjk2ZGZlOGY3NzFmZGFhMTJmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1163466046 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">09MxLIYi5KrOAOH7LQkDzLGBULjx6niAHgbrlyux</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163466046\", {\"maxDepth\":0})</script>\n"}}