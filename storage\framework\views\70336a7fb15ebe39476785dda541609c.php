<style>
    .sales-view {
        display: none;
    }

    .sales-view.active {
        display: block;
    }

    /* Customer search dropdown styling */
    #customerDropdown {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1050;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #fff;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        display: none;
    }

    #customerDropdown.show {
        display: block !important;
    }

    .customer-option {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f8f9fa;
        text-decoration: none;
        color: #212529;
        display: block;
        cursor: pointer;
    }

    .customer-option:hover {
        background-color: #f8f9fa;
        color: #212529;
        text-decoration: none;
    }

    .customer-option:last-child {
        border-bottom: none;
    }

    #customerNameSearch {
        position: relative;
    }

    .position-relative {
        position: relative;
    }

    .dropdown-item-text {
        padding: 0.75rem 1rem;
        color: #6c757d;
    }
</style>

<!-- Sales Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Sales Management')); ?></h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success active" data-sales-view="subscription">
                    <i class="ti ti-repeat me-1"></i><?php echo e(__('Subscription')); ?>

                </button>
                <button type="button" class="btn btn-outline-success" data-sales-view="installment">
                    <i class="ti ti-calendar-time me-1"></i><?php echo e(__('Installment')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Subscription View -->
<div id="subscription-view" class="sales-view active">
    <div class="row mb-4">
        <!-- Subscription Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-users" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Active Subscribers')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat(0)); ?></h4>
                    <small><?php echo e(__('Monthly Recurring Revenue')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Pending Subscriptions')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small><?php echo e(__('Churn Rate')); ?></small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Subscription Plans')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="subscriptionsTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Status / Created At')); ?></th>
                                    <th><?php echo e(__('Next EMI Date')); ?></th>
                                    <th><?php echo e(__('Customer Name / Email / Phone')); ?></th>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('Product Price / Downpayment')); ?></th>
                                    <th><?php echo e(__('Paid Amount / Pending Amount')); ?></th>
                                    <th><?php echo e(__('Discount Amount')); ?></th>
                                    <th><?php echo e(__('Receipt')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $subscriptions = \App\Models\Subscription::where('created_by', \Auth::user()->creatorId())->orderBy('created_at', 'desc')->get();
                                    } catch (\Exception $e) {
                                        $subscriptions = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($subscription->subscription_id); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($subscription->getStatusBadgeClass()); ?> mb-1">
                                            <?php echo e($subscription->getFormattedStatus()); ?>

                                        </span>
                                        <br>
                                        <small class="text-muted"><?php echo e($subscription->created_at->format('M d, Y')); ?></small>
                                    </td>
                                    <td>
                                        <?php if($subscription->next_emi_date): ?>
                                            <span class="text-<?php echo e($subscription->isOverdue() ? 'danger' : 'primary'); ?>">
                                                <?php echo e($subscription->next_emi_date->format('M d, Y')); ?>

                                            </span>
                                            <?php if($subscription->isOverdue()): ?>
                                                <br><small class="text-danger"><?php echo e(__('Overdue')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($subscription->customer_name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($subscription->customer_email); ?></small><br>
                                            <?php if($subscription->customer_phone): ?>
                                                <small class="text-muted"><?php echo e($subscription->customer_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?php echo e($subscription->product_name); ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e(\Auth::user()->priceFormat($subscription->product_price)); ?></strong><br>
                                            <small class="text-muted"><?php echo e(__('Down:')); ?> <?php echo e(\Auth::user()->priceFormat($subscription->down_payment)); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="text-success"><?php echo e(\Auth::user()->priceFormat($subscription->paid_amount)); ?></span><br>
                                            <span class="text-warning"><?php echo e(\Auth::user()->priceFormat($subscription->pending_amount)); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-info"><?php echo e(\Auth::user()->priceFormat($subscription->discount_amount)); ?></span>
                                    </td>
                                    <td>
                                        <?php if($subscription->receipt_url): ?>
                                            <a href="<?php echo e($subscription->receipt_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-file-text"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ti ti-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="cancelSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-x me-2"></i><?php echo e(__('Cancel Subscription')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="showSubscriptionNotes(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-notes me-2"></i><?php echo e(__('Subscription Notes')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="openWhatsAppChat('<?php echo e($subscription->customer_phone); ?>')">
                                                        <i class="ti ti-brand-whatsapp me-2"></i><?php echo e(__('WhatsApp Chat')); ?>

                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-repeat" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3"><?php echo e(__('No Subscription Plans')); ?></h5>
                                            <p><?php echo e(__('Create your first subscription plan to start recurring billing')); ?></p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Installment View -->
<div id="installment-view" class="sales-view" style="display: none;">
    <div class="row mb-4">
        <!-- Installment Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-calendar-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Active Plans')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat(0)); ?></h4>
                    <small><?php echo e(__('Total Value')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock-exclamation" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Overdue Payments')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small><?php echo e(__('Collection Rate')); ?></small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Installment Plans')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Customer')); ?></th>
                                    <th><?php echo e(__('Product/Service')); ?></th>
                                    <th><?php echo e(__('Total Amount')); ?></th>
                                    <th><?php echo e(__('Installments')); ?></th>
                                    <th><?php echo e(__('Paid')); ?></th>
                                    <th><?php echo e(__('Remaining')); ?></th>
                                    <th><?php echo e(__('Next Due')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="9" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-calendar-time" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3"><?php echo e(__('No Installment Plans')); ?></h5>
                                            <p><?php echo e(__('Create installment plans for large purchases')); ?></p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Quick Actions')); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('customer.index')); ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Customers')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('invoice.index')); ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Invoices')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('productservice.index')); ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-package mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Products')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('report.income.summary')); ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Reports')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                            <i class="ti ti-repeat mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('New Subscription')); ?></span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                            <i class="ti ti-calendar-time mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('New Installment')); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Subscription Modal -->
<div class="modal fade" id="createSubscriptionModal" tabindex="-1" aria-labelledby="createSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createSubscriptionModalLabel"><?php echo e(__('Create Subscription Plan')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createSubscriptionForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Customer Details')); ?></h6>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerName" class="form-label"><?php echo e(__('Customer Name')); ?></label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="customerNameSearch" placeholder="<?php echo e(__('Search customer name...')); ?>" autocomplete="off">
                                <input type="hidden" id="customerName" name="customer_id" required>
                                <input type="hidden" id="customerType" name="customer_type">
                                <div class="dropdown-menu w-100" id="customerDropdown" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Dynamic customer options will be loaded here -->
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted"><?php echo e(__('Search from Customers and Leads')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerEmail" class="form-label"><?php echo e(__('Customer Email')); ?></label>
                            <select class="form-select" id="customerEmail" name="customer_email" required>
                                <option value=""><?php echo e(__('Select Customer Email')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted" id="emailHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerContact" class="form-label"><?php echo e(__('Customer Contact')); ?></label>
                            <select class="form-select" id="customerContact" name="customer_phone">
                                <option value=""><?php echo e(__('Select Customer Contact')); ?></option>
                            </select>
                            <small class="text-muted" id="phoneHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Product Details')); ?></h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-success btn-sm">
                                        <i class="ti ti-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm">
                                        <?php echo e(__('View')); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="product" class="form-label"><?php echo e(__('Product')); ?></label>
                            <select class="form-select" id="product" name="product_id" required>
                                <option value=""><?php echo e(__('Select Product')); ?></option>
                                <?php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                ?>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($product->id); ?>" data-price="<?php echo e($product->sale_price); ?>" data-name="<?php echo e($product->name); ?>">
                                        <?php echo e($product->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productQty" class="form-label"><?php echo e(__('Product Qty')); ?></label>
                            <input type="number" class="form-control" id="productQty" name="quantity" value="1" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="activeDate" class="form-label"><?php echo e(__('Active Date')); ?></label>
                            <input type="date" class="form-control" id="activeDate" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productPrice" class="form-label"><?php echo e(__('Product Price')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="productPrice" name="product_price" placeholder="<?php echo e(__('Product Price')); ?>" readonly>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="invoiceDescription" class="form-label"><?php echo e(__('Invoice Description')); ?></label>
                            <textarea class="form-control" id="invoiceDescription" name="description" rows="3" placeholder="<?php echo e(__('Invoice Description')); ?>"></textarea>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="mb-3 text-primary"><?php echo e(__('Payment Method')); ?></h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="offline" value="offline" checked>
                                <label class="form-check-label" for="offline">
                                    <?php echo e(__('Offline')); ?>

                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="online" value="online">
                                <label class="form-check-label" for="online">
                                    <?php echo e(__('Online')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="downPayment" class="form-label"><?php echo e(__('Down Payment')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="downPayment" name="down_payment" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label"><?php echo e(__('Discount Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount_amount" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="totalEmis" class="form-label"><?php echo e(__('Total EMIs')); ?></label>
                            <input type="number" class="form-control" id="totalEmis" name="total_emis" placeholder="12" min="1" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="billingCycle" class="form-label"><?php echo e(__('Billing Cycle')); ?></label>
                            <select class="form-select" id="billingCycle" name="billing_cycle" required>
                                <option value="monthly"><?php echo e(__('Monthly')); ?></option>
                                <option value="quarterly"><?php echo e(__('Quarterly')); ?></option>
                                <option value="yearly"><?php echo e(__('Yearly')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Create Subscription')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Installment Modal -->
<div class="modal fade" id="createInstallmentModal" tabindex="-1" aria-labelledby="createInstallmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createInstallmentModalLabel"><?php echo e(__('Create Installment Plan')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer" class="form-label"><?php echo e(__('Customer')); ?></label>
                                <select class="form-select" id="customer">
                                    <option value=""><?php echo e(__('Select Customer')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="totalAmount" class="form-label"><?php echo e(__('Total Amount')); ?></label>
                                <input type="number" class="form-control" id="totalAmount" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="installments" class="form-label"><?php echo e(__('Number of Installments')); ?></label>
                                <input type="number" class="form-control" id="installments" placeholder="12">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="frequency" class="form-label"><?php echo e(__('Payment Frequency')); ?></label>
                                <select class="form-select" id="frequency">
                                    <option value="monthly"><?php echo e(__('Monthly')); ?></option>
                                    <option value="weekly"><?php echo e(__('Weekly')); ?></option>
                                    <option value="quarterly"><?php echo e(__('Quarterly')); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="startDate" class="form-label"><?php echo e(__('Start Date')); ?></label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <p class="text-muted small"><?php echo e(__('Installment feature will be available soon.')); ?></p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
                <button type="button" class="btn btn-success" disabled><?php echo e(__('Create Plan')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Notes Modal -->
<div class="modal fade" id="subscriptionNotesModal" tabindex="-1" aria-labelledby="subscriptionNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subscriptionNotesModalLabel"><?php echo e(__('Subscription Notes')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="subscriptionNotesForm">
                    <input type="hidden" id="notesSubscriptionId" name="subscription_id">
                    <div class="mb-3">
                        <label for="subscriptionNotes" class="form-label"><?php echo e(__('Notes')); ?></label>
                        <textarea class="form-control" id="subscriptionNotes" name="notes" rows="5" placeholder="<?php echo e(__('Enter subscription notes...')); ?>"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-primary" onclick="saveSubscriptionNotes()"><?php echo e(__('Save Notes')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Subscription Modal -->
<div class="modal fade" id="editSubscriptionModal" tabindex="-1" aria-labelledby="editSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSubscriptionModalLabel"><?php echo e(__('Edit Subscription')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSubscriptionForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="editSubscriptionId" name="subscription_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editStatus" class="form-label"><?php echo e(__('Status')); ?></label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="active"><?php echo e(__('Active')); ?></option>
                                <option value="paused"><?php echo e(__('Paused')); ?></option>
                                <option value="cancelled"><?php echo e(__('Cancelled')); ?></option>
                                <option value="expired"><?php echo e(__('Expired')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editNextEmiDate" class="form-label"><?php echo e(__('Next EMI Date')); ?></label>
                            <input type="date" class="form-control" id="editNextEmiDate" name="next_emi_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPaidAmount" class="form-label"><?php echo e(__('Paid Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editPaidAmount" name="paid_amount">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPendingAmount" class="form-label"><?php echo e(__('Pending Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editPendingAmount" name="pending_amount">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="editReceiptUrl" class="form-label"><?php echo e(__('Receipt URL')); ?></label>
                            <input type="url" class="form-control" id="editReceiptUrl" name="receipt_url" placeholder="https://...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__('Update Subscription')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales view switching
    const salesViewButtons = document.querySelectorAll('[data-sales-view]');
    const salesViews = document.querySelectorAll('.sales-view');

    salesViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-sales-view');

            // Remove active class from all buttons
            salesViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Hide all views
            salesViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // Customer search functionality
    let searchTimeout;
    let selectedCustomer = null;

    $('#customerNameSearch').on('input', function() {
        const searchTerm = $(this).val();

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            $('#customerDropdown').removeClass('show').empty();
            return;
        }

        searchTimeout = setTimeout(function() {
            searchCustomers(searchTerm);
        }, 300);
    });

    // Handle customer search
    function searchCustomers(searchTerm) {
        $.ajax({
            url: '<?php echo e(route("finance.sales.search-contacts")); ?>',
            type: 'GET',
            data: { search: searchTerm },
            success: function(response) {
                if (response.success) {
                    displayCustomerOptions(response.contacts);
                } else {
                    $('#customerDropdown').removeClass('show').empty();
                }
            },
            error: function() {
                $('#customerDropdown').removeClass('show').empty();
            }
        });
    }

    // Display customer search results
    function displayCustomerOptions(contacts) {
        const dropdown = $('#customerDropdown');
        dropdown.empty();

        if (contacts.length === 0) {
            dropdown.append('<div class="dropdown-item-text text-muted"><?php echo e(__("No contacts found")); ?></div>');
        } else {
            contacts.forEach(function(contact) {
                const item = $(`
                    <a class="dropdown-item customer-option" href="#"
                       data-id="${contact.id}"
                       data-type="${contact.type}"
                       data-name="${contact.name}"
                       data-email="${contact.email}"
                       data-phone="${contact.phone}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${contact.display_name}</strong>
                                <br>
                                <small class="text-muted">${contact.display_info}</small>
                            </div>
                            <span class="badge bg-${contact.type === 'customer' ? 'primary' : 'info'} ms-2">
                                ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}
                            </span>
                        </div>
                    </a>
                `);
                dropdown.append(item);
            });
        }

        dropdown.addClass('show');
    }

    // Handle customer selection
    $(document).on('click', '.customer-option', function(e) {
        e.preventDefault();

        const customerId = $(this).data('id');
        const customerType = $(this).data('type');
        const customerName = $(this).data('name');

        // Set selected customer
        selectedCustomer = {
            id: customerId,
            type: customerType,
            name: customerName
        };

        // Update form fields
        $('#customerNameSearch').val(customerName);
        $('#customerName').val(customerId);
        $('#customerType').val(customerType);
        $('#customerDropdown').removeClass('show').empty();

        // Fetch detailed contact information
        fetchContactDetails(customerType, customerId);
    });

    // Fetch detailed contact information
    function fetchContactDetails(type, id) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.get-contact-details", ["type" => ":type", "id" => ":id"])); ?>`
                .replace(':type', type)
                .replace(':id', id),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    populateContactFields(response.contact);
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Failed to fetch contact details")); ?>');
                }
            },
            error: function() {
                show_toastr('error', '<?php echo e(__("Failed to fetch contact details")); ?>');
            }
        });
    }

    // Populate email and phone fields
    function populateContactFields(contact) {
        // Clear and populate email dropdown
        const emailSelect = $('#customerEmail');
        emailSelect.empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');

        if (contact.emails && contact.emails.length > 0) {
            contact.emails.forEach(function(email) {
                if (email) {
                    emailSelect.append(`<option value="${email}">${email}</option>`);
                }
            });
            // Auto-select first email
            emailSelect.val(contact.emails[0]);
        }

        // Clear and populate phone dropdown
        const phoneSelect = $('#customerContact');
        phoneSelect.empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');

        if (contact.phones && contact.phones.length > 0) {
            contact.phones.forEach(function(phone) {
                if (phone) {
                    phoneSelect.append(`<option value="${phone}">${phone}</option>`);
                }
            });
            // Auto-select first phone
            phoneSelect.val(contact.phones[0]);
        }

        // Update help text
        $('#emailHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
        $('#phoneHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
    }

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#customerNameSearch, #customerDropdown').length) {
            $('#customerDropdown').removeClass('show');
        }
    });

    // Clear customer selection when search field is cleared
    $('#customerNameSearch').on('keyup', function() {
        if ($(this).val() === '') {
            selectedCustomer = null;
            $('#customerName').val('');
            $('#customerType').val('');
            $('#customerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
            $('#customerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
            $('#emailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
            $('#phoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        }
    });

    // Product selection handling
    $('#product').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const price = selectedOption.data('price');

        if (price) {
            $('#productPrice').val(price);
            calculateEmiAmount();
        }
    });

    // Calculate EMI amount when values change
    $('#productPrice, #downPayment, #discountAmount, #totalEmis').on('input', calculateEmiAmount);

    function calculateEmiAmount() {
        const productPrice = parseFloat($('#productPrice').val()) || 0;
        const downPayment = parseFloat($('#downPayment').val()) || 0;
        const discountAmount = parseFloat($('#discountAmount').val()) || 0;
        const totalEmis = parseInt($('#totalEmis').val()) || 1;

        const remainingAmount = productPrice - downPayment - discountAmount;
        const emiAmount = remainingAmount / totalEmis;

        // You can display this EMI amount somewhere if needed
        console.log('EMI Amount:', emiAmount);
    }

    // Handle subscription form submission
    $('#createSubscriptionForm').on('submit', function(e) {
        e.preventDefault();

        // Validate customer selection
        if (!selectedCustomer || !$('#customerName').val()) {
            show_toastr('error', '<?php echo e(__("Please select a customer")); ?>');
            $('#customerNameSearch').addClass('is-invalid');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.sales.store-subscription")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    $('#createSubscriptionModal').modal('hide');
                    resetSubscriptionForm();
                    location.reload(); // Refresh to show new subscription
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                // Clear previous validation errors
                $('.form-control, #customerNameSearch').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        let input = $(`[name="${field}"]`);

                        // Handle customer_id field specially
                        if (field === 'customer_id') {
                            input = $('#customerNameSearch');
                        }

                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Reset subscription form
    function resetSubscriptionForm() {
        $('#createSubscriptionForm')[0].reset();
        selectedCustomer = null;
        $('#customerNameSearch').val('');
        $('#customerName').val('');
        $('#customerType').val('');
        $('#customerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
        $('#customerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
        $('#emailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        $('#phoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        $('.form-control, #customerNameSearch').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    }

    // Reset modal when closed
    $('#createSubscriptionModal').on('hidden.bs.modal', function() {
        resetSubscriptionForm();
    });

    // Clear validation errors when user starts typing in customer search
    $('#customerNameSearch').on('input', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });
});

// Subscription action functions
function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.cancel-subscription", ":id")); ?>`.replace(':id', subscriptionId),
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to cancel subscription');
            }
        });
    }
}

function showSubscriptionNotes(subscriptionId) {
    // Load existing notes
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#notesSubscriptionId').val(subscriptionId);
                $('#subscriptionNotes').val(response.subscription.notes || '');
                $('#subscriptionNotesModal').modal('show');
            }
        }
    });
}

function saveSubscriptionNotes() {
    const subscriptionId = $('#notesSubscriptionId').val();
    const notes = $('#subscriptionNotes').val();

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-subscription-notes", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            notes: notes
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#subscriptionNotesModal').modal('hide');
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to save notes');
        }
    });
}

function openWhatsAppChat(phone) {
    if (!phone) {
        show_toastr('error', 'No phone number available');
        return;
    }

    const cleanPhone = phone.replace(/[^\d]/g, '');
    const whatsappUrl = `https://wa.me/${cleanPhone}`;
    window.open(whatsappUrl, '_blank');
}

function editSubscription(subscriptionId) {
    // Load subscription data
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const subscription = response.subscription;
                $('#editSubscriptionId').val(subscriptionId);
                $('#editStatus').val(subscription.status);
                $('#editNextEmiDate').val(subscription.next_emi_date);
                $('#editPaidAmount').val(subscription.paid_amount);
                $('#editPendingAmount').val(subscription.pending_amount);
                $('#editReceiptUrl').val(subscription.receipt_url);
                $('#editSubscriptionModal').modal('show');
            }
        }
    });
}

function deleteSubscription(subscriptionId) {
    if (confirm('Are you sure you want to delete this subscription? This action cannot be undone.')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.delete-subscription", ":id")); ?>`.replace(':id', subscriptionId),
            type: 'DELETE',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to delete subscription');
            }
        });
    }
}

// Handle edit subscription form submission
$(document).on('submit', '#editSubscriptionForm', function(e) {
    e.preventDefault();

    const subscriptionId = $('#editSubscriptionId').val();
    const formData = new FormData(this);

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#editSubscriptionModal').modal('hide');
                location.reload();
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to update subscription');
        }
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/sales.blade.php ENDPATH**/ ?>