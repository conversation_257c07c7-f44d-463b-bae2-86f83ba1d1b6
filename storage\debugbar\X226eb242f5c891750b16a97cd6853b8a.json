{"__meta": {"id": "X226eb242f5c891750b16a97cd6853b8a", "datetime": "2025-07-30 08:37:50", "utime": **********.189005, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753864668.743198, "end": **********.189039, "duration": 1.4458410739898682, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1753864668.743198, "relative_start": 0, "end": **********.012375, "relative_end": **********.012375, "duration": 1.2691771984100342, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.012448, "relative_start": 1.2692501544952393, "end": **********.189043, "relative_end": 4.0531158447265625e-06, "duration": 0.17659497261047363, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00792, "accumulated_duration_str": "7.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.125544, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 64.394}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.155423, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 64.394, "width_percent": 17.803}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1653929, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 82.197, "width_percent": 17.803}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-1323178500 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1323178500\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-452660870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-452660870\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-778301259 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-778301259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1542683730 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im5ET1NXMVZ2S21aUm1KY2dlMUkzR0E9PSIsInZhbHVlIjoic2xicnJLeE9zQXdGV1Y4elNvLzdKdVRWeGN0bDFOaFVsQnVWUW9EcW1QeXIzRmxXOGVzVENrbDNPWFVHaFRzSS9uQURWVXBnLzJWZm9xZjFpeFdGOHg2TjEvRkhYVWJ2b0tIQ0x1UHZPQlkzOXFFR0l2dnJvVTY2VmhIUTFZVThLWXNnMnlBKzRFeVNFQnF3aEt2L2RrZndwcnRWczRzWTlhR3ZoTHFXNldPa2VTdVcrR3N1UmpRVjh1Zk5OUGNoNHFleGxvSmtaTkxBTkRlNEVvMUZFTS9iQmFtWWZmNm85SDBYZVE4STJMRWRPRU01ODhjUUZxU0MwejFCOUhYTlNBczBscWFud1ZTVzMzTGx5VFVQWUlVWXVQUk5VT0hMaEhGSlNzM2JZSjZVSEs4VE1yaGIzVm9ZVWt0NkdrLzdVTFFJbkE5ZjZxR29nekd6Z0xXSzRjZ3hoUVdFVE1HMWRDU2xZUFVSYVFESUhYeHdNeFJQbGw3NVRzMEQyU1lxU01RcEFkWmw3Vk03N0xWallsQm9CNnhzVmFrMFVhN1hGWEwvRDZSc1gra0U3NmRQZzFZNWF4VDNMNmI0cm95U3dZbkp0Wjh0aWtIMFNtNjVsSVFib0xQN1NDaDhkQ2tOSmlpd2Y4K1NkY2M0OXFuU3IrbitiNjA1UHl2K05uNUQiLCJtYWMiOiJjNDI1MzQzYmM4YmYwYWY3NDY4NjI3MmUxZGZhZjllOTk1NWVkM2YwYzFjY2FkN2U2MTYzOTJjYTcwY2EzNDc4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRoanN3bHpuSnErODIvMXArb2YwQWc9PSIsInZhbHVlIjoiV3dTV096ZE9LelN3Z0pYMTFSVE13bThqQ1ZPY2R2ZmEwQ3dPTjVTV2k3eHlZRWZ4aEpjQlFvUDZjZTFWSWIvUmxESU1URURJUm5lYVIyV3MxSjFBSk9UVjZuM3BRVWpiSVBjVnZxbm5wNkE1Y3R4Q29TK2JialFBS24vZ09ReWdMeWNvS0ZhTEl6Q3JFWi9qL0xTZmxSN3FoeWpPRmN3dFY3Q0o1cnJYanNrUlYrSlNtU3RLMU0vTWlHRy9CZVVYWjhJUFhUbEpSV2t0UlVkTGZBejdCQVNueEdTKy92elNvbDZ5QWRKc1VBNW02QUE0K1dWU0x5cUw1Y1ZqZldzNW5MNGFxNlNWUHdUYk92TE5kMlllT0dqL1FVeFVYUGpsMlErZXVaelloYkxEUEFaUHFvWnBBaktudlZVK01lcVRudDdvQis5WkhXUnVZVWU1amd2NFljYU5mVjhJWVpNdC94b3Q2WFZMYW5RREVtWVhicTBVaDA2aEl1UWJvRzZ6Mi9WUlhMeWNTZENNQnNwMWRLOHlNWTcxU0Z6ZFFOd2cxUlRMSFlpQnRXNEpHWWRzQlk1b0R1WHMwcGpCTjhyZSs3Yk5td2h1bmRrMTBIbmo5eDAxQ3dZVXQ1WjhualpWOG5HOTVOdUIyV2xQS2hTWUdqbFNoeFFTNmFodEdXMWMiLCJtYWMiOiIzYTVhZWY2MjZiZDMzNGU2Mzk3MjJjYzExNDE5YTZlMzE5ZjFhNDU0ZThmNzgxNjg3N2FkMGM0NzM0YTQ2ZGZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542683730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2133382717 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133382717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1093154067 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:37:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlWWDExZEEySnE1eGxKRldZV0s3RHc9PSIsInZhbHVlIjoieXNRczY3Lzg0eXh3THhqKzVnMnFYTGZ6Y3VYUmZmaW9mbHo4U3p6UU9Pd1NCeDY0NjFPN1J3Z2dpcVhSZUpoMU9vUTVELzBIMlRCNUpDY1JsUk1pS1BySzBabHVaV01sNG13dnZMdWVqS2E1NnRIV3hmSCtydmU1UEpLRklzQWN4UmdIWDkzdW81NmtBQVF3bGJCcS9Pc1VlK1lZdStBVzRlOUJRSWx5aVk5VUVYek5WdUJ6cTlEcWhNUStxaGYzWmFWV3VOMEN1VmxLdnFsS1FYbnR1cW56a1BtOUErVnRTN1A3d0Nvc0Y2SGF1TW1PVVZPelB4NmxRRHB1OGZnTTdjS2o2SE1PWFAxRXNleEdGQ2k5V2hYT1NSME43YzJnUGxpVnJ2eXo4eGRxbjNIRjZ4UnJOZzVwR0V0b2Y3WXpWWlJIdDFxSTMvL0hnZ0g2RHZCOEZ2ZGVGcDZ6a0lSNTJ6OWdVaGdRWk55eE1ObjdTaS9RVnRlVkgveE9zTjBpM3BUdWtYaHlpMVhUUkVGWGQ4ZGxMbG5sL0tjSVBXWWh4QVNqOVlNTmorS0Y2OWpQY282RjhraVFQM3JuZ2l3WmYrNnI0SEpsajltNm45dDY1V2NTcDJDNWFmcGxVUklDcnUyTmJvZXlSNXIwTWl3RFVacE5Ya2JKZzF1QXJoUk4iLCJtYWMiOiJmYmMwYzBmNGM4ZTFlMTQ1OTIwNzRiMGMyYmNhOTdmNmYyMTlkOTIzMzY1NjhkN2QzYjAyYTJmZTU2MGYzODEzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:37:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImlBY0dmcFUxUEFrTjRZWjVCTDdEckE9PSIsInZhbHVlIjoiT1psOWxFc0pVS0xVd0tidVZEb3VuTzdGd3I2OW5tQ003WHZlVWt6eVU3Q1NQMjBoL0hqTDdyVHhKeGljWG50aFByK2piQnF4RkNFejJ6OFhtQmhEZm5pVVBuZTdacEZaVHVpcVhQd2ZxaUNvL3B1cW9kMnVjMHJYc1hYaG5LSlhLYjlpZTV4TkNFRERSRUU5WGtmRSs5dVRBMTBwVVl0N2R5R0hYZHVzb1ZGSXBtSVN4ZEltazV6VVdCS1VwN3FSR04wTEFvNW1WRHlXQ085dTVvaGNFYnJUU0RCd2dwazdjMStzZUYwbnFTeE1vOGt5eWEyREFFTzdHMk53aW1qUzZSS0VwN0UyNGIzaFVVU0FWZTE4YldPcHlSdkxuUUZNMTRwR2NWKzN6emRLRVh5SEtRNXFPeW03ZzZUeTJnQ1lQVWRueUk1UEpNUVNEZUdQd1pWOEtxS2RuNWhHZjVCRWNYbERMLzNnbGNNNFIwK3dwcG55YnlSSHVzNTNsQmlTY25ReWtRWlM1N2ZsWkZqZmxvOFRKRC9MRG50SjdMU09tdWp6MG44TTE1Yk1LSzlFNWpWSGZKdHBhaEJWc2gvQUxwZDNjSlN1cnF6bU5LV0hVRXJpUEN3K3ZrcjRmbFM2L2R2emhoMVFtUWoxeFhPYXZ5SUJHVWVtT2VZVFdpNEsiLCJtYWMiOiI1YjljNWE5NTgxMGQwY2JjNGFhNTY1YzJmMzRkMWJlYWQ2NjU3YzMyOTI0NWVmYjM0NTYzN2Q5MzQzMWViZDI3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:37:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlWWDExZEEySnE1eGxKRldZV0s3RHc9PSIsInZhbHVlIjoieXNRczY3Lzg0eXh3THhqKzVnMnFYTGZ6Y3VYUmZmaW9mbHo4U3p6UU9Pd1NCeDY0NjFPN1J3Z2dpcVhSZUpoMU9vUTVELzBIMlRCNUpDY1JsUk1pS1BySzBabHVaV01sNG13dnZMdWVqS2E1NnRIV3hmSCtydmU1UEpLRklzQWN4UmdIWDkzdW81NmtBQVF3bGJCcS9Pc1VlK1lZdStBVzRlOUJRSWx5aVk5VUVYek5WdUJ6cTlEcWhNUStxaGYzWmFWV3VOMEN1VmxLdnFsS1FYbnR1cW56a1BtOUErVnRTN1A3d0Nvc0Y2SGF1TW1PVVZPelB4NmxRRHB1OGZnTTdjS2o2SE1PWFAxRXNleEdGQ2k5V2hYT1NSME43YzJnUGxpVnJ2eXo4eGRxbjNIRjZ4UnJOZzVwR0V0b2Y3WXpWWlJIdDFxSTMvL0hnZ0g2RHZCOEZ2ZGVGcDZ6a0lSNTJ6OWdVaGdRWk55eE1ObjdTaS9RVnRlVkgveE9zTjBpM3BUdWtYaHlpMVhUUkVGWGQ4ZGxMbG5sL0tjSVBXWWh4QVNqOVlNTmorS0Y2OWpQY282RjhraVFQM3JuZ2l3WmYrNnI0SEpsajltNm45dDY1V2NTcDJDNWFmcGxVUklDcnUyTmJvZXlSNXIwTWl3RFVacE5Ya2JKZzF1QXJoUk4iLCJtYWMiOiJmYmMwYzBmNGM4ZTFlMTQ1OTIwNzRiMGMyYmNhOTdmNmYyMTlkOTIzMzY1NjhkN2QzYjAyYTJmZTU2MGYzODEzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:37:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImlBY0dmcFUxUEFrTjRZWjVCTDdEckE9PSIsInZhbHVlIjoiT1psOWxFc0pVS0xVd0tidVZEb3VuTzdGd3I2OW5tQ003WHZlVWt6eVU3Q1NQMjBoL0hqTDdyVHhKeGljWG50aFByK2piQnF4RkNFejJ6OFhtQmhEZm5pVVBuZTdacEZaVHVpcVhQd2ZxaUNvL3B1cW9kMnVjMHJYc1hYaG5LSlhLYjlpZTV4TkNFRERSRUU5WGtmRSs5dVRBMTBwVVl0N2R5R0hYZHVzb1ZGSXBtSVN4ZEltazV6VVdCS1VwN3FSR04wTEFvNW1WRHlXQ085dTVvaGNFYnJUU0RCd2dwazdjMStzZUYwbnFTeE1vOGt5eWEyREFFTzdHMk53aW1qUzZSS0VwN0UyNGIzaFVVU0FWZTE4YldPcHlSdkxuUUZNMTRwR2NWKzN6emRLRVh5SEtRNXFPeW03ZzZUeTJnQ1lQVWRueUk1UEpNUVNEZUdQd1pWOEtxS2RuNWhHZjVCRWNYbERMLzNnbGNNNFIwK3dwcG55YnlSSHVzNTNsQmlTY25ReWtRWlM1N2ZsWkZqZmxvOFRKRC9MRG50SjdMU09tdWp6MG44TTE1Yk1LSzlFNWpWSGZKdHBhaEJWc2gvQUxwZDNjSlN1cnF6bU5LV0hVRXJpUEN3K3ZrcjRmbFM2L2R2emhoMVFtUWoxeFhPYXZ5SUJHVWVtT2VZVFdpNEsiLCJtYWMiOiI1YjljNWE5NTgxMGQwY2JjNGFhNTY1YzJmMzRkMWJlYWQ2NjU3YzMyOTI0NWVmYjM0NTYzN2Q5MzQzMWViZDI3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:37:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093154067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2010696415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010696415\", {\"maxDepth\":0})</script>\n"}}