{"__meta": {"id": "Xd9e6d0759491af639f3f980278384564", "datetime": "2025-07-30 08:08:38", "utime": **********.935642, "method": "GET", "uri": "/chart-of-account/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.217461, "end": **********.935679, "duration": 2.****************, "duration_str": "2.72s", "measures": [{"label": "Booting", "start": **********.217461, "relative_start": 0, "end": **********.376067, "relative_end": **********.376067, "duration": 1.****************, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.376086, "relative_start": 1.****************, "end": **********.935682, "relative_end": 3.0994415283203125e-06, "duration": 1.***************, "duration_str": "1.56s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x chartOfAccount.create", "param_count": null, "params": [], "start": **********.532809, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.phpchartOfAccount.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2FchartOfAccount%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "chartOfAccount.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.916915, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET chart-of-account/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "chart-of-account.create", "controller": "App\\Http\\Controllers\\ChartOfAccountController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=54\" onclick=\"\">app/Http/Controllers/ChartOfAccountController.php:54-76</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.023940000000000003, "accumulated_duration_str": "23.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.430011, "duration": 0.01855, "duration_str": "18.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.485}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4806352, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.485, "width_percent": 6.266}, {"sql": "select * from `chart_of_account_types` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChartOfAccountController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ChartOfAccountController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4939952, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "ChartOfAccountController.php:57", "source": "app/Http/Controllers/ChartOfAccountController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=57", "ajax": false, "filename": "ChartOfAccountController.php", "line": "57"}, "connection": "radhe_same", "start_percent": 83.751, "width_percent": 6.976}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4779}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.887337, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4779", "source": "app/Models/Utility.php:4779", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4779", "ajax": false, "filename": "Utility.php", "line": "4779"}, "connection": "radhe_same", "start_percent": 90.727, "width_percent": 5.221}, {"sql": "select * from `plans` where `plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4784}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8976572, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4784", "source": "app/Models/Utility.php:4784", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4784", "ajax": false, "filename": "Utility.php", "line": "4784"}, "connection": "radhe_same", "start_percent": 95.948, "width_percent": 4.052}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/product-unit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chart-of-account/create", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImxFWVhVb21vekJSa0M4UTJTYU9Kd1E9PSIsInZhbHVlIjoiS2FVd2VUL1gxdTNJcVNWRUJGdUZmWS83SmRXSWJneEphRUhoZE9PRkdxM0c3MXlZcTlzeW9pM1FrU2MyZ1M5bTByNmJYZ3JmNzNSek02dTNkUWxoRjVIbUt6d2J4eFZkT09wcnlFTWoreWp0ZjNnNFB4a0p3eHBjSEh3TXlzekt4aWcyRXAydHNqdGRCSWwwVnA0YVFPTFJmeTRvbklRSFE1L1lzVFZQa3ZLMkdFREZtLzN1NXRTZzZyK1FmMmdJb25iS1NsWDNQYXB1OG9FUkVtRHd4WEdqbS90Z0NsWXhDRGRiMlB6dk5RbGEzZEhMQmpGdHpEM1FHTVI4dnB5VGxQb1hyTGFKelpnMGlJK3VCeVB6cEZTcWtYTlo3a05EbXVHczFyMVpIb2tJd1l3MDlPblNvQXpqUXVwdVk3cWVFcU5teE01TkY5c3hMM1d4THNMZlNvU05FZEN2NzVSNHpQa2pxa2Uzd3FDam9YWTYxQm1GS0JMUDI5Wm1MWjFPQzAvS0hSOFE0UU45VjZiOTdMcU9lbERsdjRhZ2ZKck1rZDRWTHBNdzVCamlFNjhBN3JCbGcxa2hYY2dKQmZNNkl4bEVoVWR6RkN3UVkwNkJqR21rNmdrKzkzWXJieEIxTlVSampXTU9Odm1wWjVaSDdMYzNIUzdWUG9JSjBRMWgiLCJtYWMiOiIxMTMzMjk5MDBmODQxZGMwYjYwZWYwNTM2Y2ZiNzNiNDIwZDliMzY0ODZlMmEwNDI1MWI3ZjkyYjI3ZTJhMmM4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkZwNWxZZE1kR2VDT3RpcGxnbWl3NHc9PSIsInZhbHVlIjoiZkxnWEI5WHdsRHBuU05wVXBmWFIyMmF6M1J6cW40bUszMWZTQUhBVGdnOWRxU09mcktlN3R0blRsY1FRWWVjSGRUQnFPSElhaHJET05tNzZjemlCK3NicFA4NjJINTc4dTBvM2svR3NGSCtVRTJ1R2tRd2lNQzFERGJTZUxIVjZsR1N5Q1JHNFlwaSt3cWtnWEo3eDBLSzcyd2lOQmJtVE0welpqbVREc2xMbHdvaUw5c2s3czFxTlh2L2lBODZ6ZEg0c2g5dEVna2ZPU2FmTk9BNjljMUVtTURXanpoTjhlb0FVNDdWY21rc1hhRVlXRWZucW9tczRmZklzc0dnYUZyWXZZdXVPRWk3YUhORGIxUm01d2QxaW1HTnoyMFZCcy9PUjlDTS85M0lYSjFCWWdseklvNHB0allpT1ZmbVFGQkFXL2JudHdPWUllSW9TZHhqdVdwR3dXU0ZxUEtNSmE4eVoyejJmSlRsNWtVQmNIbldqd2daeFg2Rzhma0dtVmpBSEhnUHd5UDFNS0NGVUU0V2phSUsyL1RxQytvemZiMm5qeXU2YzlEaUpYKzgvQVRtOHFBMFpLb2FMbDd2UjZhbTQ0U1Rka3ZWSCs0QVJVZHp3WUVUTmhFZk5qaHRyL2ZSZzc0WnkrS0pFRHdxRlhsNC9PVEtET2ZFSmZUY2oiLCJtYWMiOiIwMDBmYjdiMzYwZDQ0NjYwNmFkNmM3MGVlZmZlOGY0YTFhMGJkMmFmOWU2OTYwZDU3ZGY1ZTk3MTIwM2U1MGQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-82792274 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82792274\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1864915378 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:08:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVjRzBwVEw3VTY5NU92ODdKWktqY1E9PSIsInZhbHVlIjoiNG9MVHYvRTZ0RzBjc3JmZjJFaThuM3h5RDdKWCtMdnJGMzNYb0RIam9nRU5xMlUxODlCSWJvT1JTNE81UDVrVDRwMHcxWk52b1JkVDVBZ3JQNkM5bktOZTdZTGwxczNvMklOUnlObndTUElzb1FXQ3JxU0E5VmQrUE5DdXd0cktFamlaYis2RXRET0Zta3lwZm5JV1IyMlJsaUhYWDFqSmVsNHBmcjBJazBPay9kODEvZHlwY09yZHJiRHFtaUtlQlhQeWFqWFR5czlma0E2SE9iYWhJWE9DNWM0czlZWFVDb1VWZmthaHZVMml5TVJpUFhjQWQwaUV2aWF0bFBtWnR4cXBXL1R4eCt0elBncVBwNURIOFdhR2tCMXk4ZWRITmRxcUhXdWFyL1ljdTVlMHBjY3lwSnRNdlJLQUNVb1ZtUm5XZ0FXQXE3OHpzaDlDL1pEWmkwQzBpMmhnU21LOWwyYjVqRUtwYUh4Sm40T2ZXVHVta0lLRG9mMjM4MFdOKzRMTDVqa2EzcDBEN1o1RkRkTnlyU0IzS095dVVuMVZ3NE1obExvYmdRR2s4Z3ZINVpHSEVFZGU1MEIvNGphcktYeHZOMzRodkMxc1dNQmUxaGNuSDczRGpvMkhDdEVlNS9OcU14emFaMmJDODNmNWtpNmplcmRZZ3lCNENubCsiLCJtYWMiOiJlNTA1NzE3MWY0YWYyNzc3M2FmMjZhNmUxMzllMmFmZTRjMjIxZjI5YmI4Njk1NDRiZjU1ZDdmMDZjN2Q3YmY2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:08:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZUNHVnY0RoVlZPdUtsa0RIKzdmM2c9PSIsInZhbHVlIjoiYUdtTWNVN2g1Njc0aUw2bGxyYnZ2NU5vTW5WeENyZkJhdTZDRmpOTjNmMnZjWXJFekM0RCtXMTNuclJISGRray8wRk40cHk3b1ZweHloK09vL0RtRERxQ1R5ekY0RkpnOGM3ZXRKcHNUd1RpRk8rSlIzQ3JIMTFCcmJrRDRNbCtsNGJvWWVuMVB6N3B0VXZ1ODBYamd1ejdCRkovNGZaNmR5VDUwMko0RG5FOXQyem9hczBQdG13TzNIbjhFRkc4S0RsOVdtb3BoTUV2MUtVdlR5ZWF2UXNZSUUrS2JDMDVGdksvVjlDVzBhMlRsM01lL0tETXBNV0JSWDl6ZzdGWnN6RkVCTzZQayt2NGg2VWtORmV4OXpVeVE0dkR1dmZOOVZlMzd5UDBZeTZkaUpnOWQxRTJwZlc2TmRQTlhTMUJUY2IxUE5TcnliMGZpcHU5dUh3VFhFRnZpSnNWVWk0RVByWEQ0U2NBbDladndjZUY2aVNLeFBBUEJoWGtWcUpHOXVXN1NlYWJaZEhsL0RZUU81Y24vdExHNldBaVBzYnkyMUFEOGJMZEl5M0JNMXhyWjE3RE1rZ3I1bEZHUzRKK2kvVVpvbGs5L3ErV2xmRjNaTHVoYldwYTMzcEFJSnFmTWdVOGd1Y3FuQ0o0eE8rTy94emJaeHFzb1dHNkMrSjMiLCJtYWMiOiIyMzQzNzE5ZTQ0MThjZDdkNDNhNjc2ZDk4ZmIzMzkxMDExNTA5MDRiOWNlYzA3MWU3YmVhMmM3ZjRiOWU1YTFjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:08:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVjRzBwVEw3VTY5NU92ODdKWktqY1E9PSIsInZhbHVlIjoiNG9MVHYvRTZ0RzBjc3JmZjJFaThuM3h5RDdKWCtMdnJGMzNYb0RIam9nRU5xMlUxODlCSWJvT1JTNE81UDVrVDRwMHcxWk52b1JkVDVBZ3JQNkM5bktOZTdZTGwxczNvMklOUnlObndTUElzb1FXQ3JxU0E5VmQrUE5DdXd0cktFamlaYis2RXRET0Zta3lwZm5JV1IyMlJsaUhYWDFqSmVsNHBmcjBJazBPay9kODEvZHlwY09yZHJiRHFtaUtlQlhQeWFqWFR5czlma0E2SE9iYWhJWE9DNWM0czlZWFVDb1VWZmthaHZVMml5TVJpUFhjQWQwaUV2aWF0bFBtWnR4cXBXL1R4eCt0elBncVBwNURIOFdhR2tCMXk4ZWRITmRxcUhXdWFyL1ljdTVlMHBjY3lwSnRNdlJLQUNVb1ZtUm5XZ0FXQXE3OHpzaDlDL1pEWmkwQzBpMmhnU21LOWwyYjVqRUtwYUh4Sm40T2ZXVHVta0lLRG9mMjM4MFdOKzRMTDVqa2EzcDBEN1o1RkRkTnlyU0IzS095dVVuMVZ3NE1obExvYmdRR2s4Z3ZINVpHSEVFZGU1MEIvNGphcktYeHZOMzRodkMxc1dNQmUxaGNuSDczRGpvMkhDdEVlNS9OcU14emFaMmJDODNmNWtpNmplcmRZZ3lCNENubCsiLCJtYWMiOiJlNTA1NzE3MWY0YWYyNzc3M2FmMjZhNmUxMzllMmFmZTRjMjIxZjI5YmI4Njk1NDRiZjU1ZDdmMDZjN2Q3YmY2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:08:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZUNHVnY0RoVlZPdUtsa0RIKzdmM2c9PSIsInZhbHVlIjoiYUdtTWNVN2g1Njc0aUw2bGxyYnZ2NU5vTW5WeENyZkJhdTZDRmpOTjNmMnZjWXJFekM0RCtXMTNuclJISGRray8wRk40cHk3b1ZweHloK09vL0RtRERxQ1R5ekY0RkpnOGM3ZXRKcHNUd1RpRk8rSlIzQ3JIMTFCcmJrRDRNbCtsNGJvWWVuMVB6N3B0VXZ1ODBYamd1ejdCRkovNGZaNmR5VDUwMko0RG5FOXQyem9hczBQdG13TzNIbjhFRkc4S0RsOVdtb3BoTUV2MUtVdlR5ZWF2UXNZSUUrS2JDMDVGdksvVjlDVzBhMlRsM01lL0tETXBNV0JSWDl6ZzdGWnN6RkVCTzZQayt2NGg2VWtORmV4OXpVeVE0dkR1dmZOOVZlMzd5UDBZeTZkaUpnOWQxRTJwZlc2TmRQTlhTMUJUY2IxUE5TcnliMGZpcHU5dUh3VFhFRnZpSnNWVWk0RVByWEQ0U2NBbDladndjZUY2aVNLeFBBUEJoWGtWcUpHOXVXN1NlYWJaZEhsL0RZUU81Y24vdExHNldBaVBzYnkyMUFEOGJMZEl5M0JNMXhyWjE3RE1rZ3I1bEZHUzRKK2kvVVpvbGs5L3ErV2xmRjNaTHVoYldwYTMzcEFJSnFmTWdVOGd1Y3FuQ0o0eE8rTy94emJaeHFzb1dHNkMrSjMiLCJtYWMiOiIyMzQzNzE5ZTQ0MThjZDdkNDNhNjc2ZDk4ZmIzMzkxMDExNTA5MDRiOWNlYzA3MWU3YmVhMmM3ZjRiOWU1YTFjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:08:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864915378\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-393700856 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/product-unit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393700856\", {\"maxDepth\":0})</script>\n"}}