{"__meta": {"id": "X5ded0b5c629a113cd8b7adb735eb0041", "datetime": "2025-07-30 02:41:36", "utime": **********.805386, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843293.356275, "end": **********.805441, "duration": 3.4491658210754395, "duration_str": "3.45s", "measures": [{"label": "Booting", "start": 1753843293.356275, "relative_start": 0, "end": **********.551893, "relative_end": **********.551893, "duration": 3.195617914199829, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551972, "relative_start": 3.****************, "end": **********.805446, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "j2sdiefPT6OW1FVtpB5OAf9N2dHPW0M7YeKZVaRw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1376111182 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1376111182\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-71423301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71423301\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-177564896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-177564896\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-635889678 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635889678\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-764055012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-764055012\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-255293712 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:41:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxnSGhjNlVLSEVCUytUTkthaDRHOWc9PSIsInZhbHVlIjoiZXhNZXRxOVlzZDVjOERnMW9kNDFxTVJmLzdTQkVHUDcyQ0t3SkpMYWY3YTJZbUQ2Y2gwNjRhR3ZwNTUvZU0vUzlFZ3RaREc3VzhXbW9Lbm10ZVJIQXBmWWR1TjFxbVBieXFqYlhEYlRtUWlPcWVxMGxMLzBxKzM3a2YralVqN3ppbFFqWGwwc2c2VnBXd0JxN3hZUUtZQmxOQnI3NTdnb2xkK200Vk9ucjZxV0VwSzlBTktKd3pOWWp0RllzUzRqczJCK1JGdGZLS0dkQnhDdnQxMTFvT0Z6MHE5OHgxNW9MMmZDamYxVGdUb09UTitMaFU1TjM5SEhzS0FzTmRqWjZWck9Td2QzSy9HaGhqcDZBSHNYZ2xQVktlcmtFK3puZnFiRXRaUXdCcnJycEp4RzFhUlQ5bXQ3SG5zb1F0MFZUb05TVElkelA3OE9HaUZxYWM3TVc3TlY0b2J5UlpNdVBja2liMlZiQ242MWVIcFJDNmx6WDZoUnRMRTZDWmNsR1FqL3lpdG5hSmJZT1AybmhkU3Bta1pROGtLWUZXbjBiakxpZFZIdGdTYnU3WFJOMXJLaXdHR3RJT1RQbFJyZTVTejNVV3VnZlRON0F6Y3pPakxTY0ZyaXQ2VVl1d0VVOFN2Q0RhUUlCYXlSQldPU0FVYjdmMjRpZGx4TUZsMmoiLCJtYWMiOiI4MWUwMzQ3YjVlMGU5OTBlNzFlMTY3OWJjZDdmYTMyZTU4Mzc4NTg2NTRhZmJjNWVkOGUyMjFhY2Y2ZTM5N2ViIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:41:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9VREtJMWpBc3hQQXZOeFVPSi9WTkE9PSIsInZhbHVlIjoiM1Y3R0NubjlwUTBxUnVNU2EyUjFsUWJaeS9mMXJ5ZlJJYUhyV2QwbGxadllzTENTdU9oakhNcy8yRkFGR1JBazZOV2xXWEYxNVZCSGZreXZiTkNKSHp6d0hvYTR5d3hSZUVLYTUyVVFWekMwNXkwWWkvMjVmMlFZdFR2OUI2Q3l2S25QWW1qL0k3T3cxZ2tnMzdQeWdVYURFQ1ZzdkdEaHZEMDlYbFpwaTNRV3hnVzZwelVaOXJaL2kxTGRWdGZRQWNvYUNvbWxhMlV5MHdEa0xPcUc5cW9PaG1XZVRnQTRjN1cvbURHVGQ0OVNWQ1lTcytzQ2xtSEJaQ0JHd0Q2aExWR3YzRjRwOFc3RU9YeVBxazlQR3hRamptMnR6Ri9YUnREK2xSak5xd01SVUphTVpncENRMEx5dUdIbitQMlVhLzNlRFRHZmVYSnQ4RURnWXVRZS85RmdjTHNsNG14Q0RqV2F4Yy84QWZ6SXFIeUdsalU2aEsrc0loWmIzV0tpYWhhNTVmclBlRmFGYWR2T2RXbStDZ1B1YXVzMUlkQTUwelpySk9lTTNkU0xwUHhGVWNvR0w5NEJXT3Raa29qc3YzNW1iSVA0ZGZtczV4eDY0djVzM1hwaFhyZTB2VGFrRVpyM2pDbm9DWkNvdk1DTjhrbWFIWHViYnUwSEYxSEgiLCJtYWMiOiIzZTZjYWYzZGRjMTAyYjU1YWI4YWZiMjYyOTBjMGRhOTM4MmEwZWY0ZTM1ZTFjMjVmNzExYTFjYzViZTljNjkwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:41:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxnSGhjNlVLSEVCUytUTkthaDRHOWc9PSIsInZhbHVlIjoiZXhNZXRxOVlzZDVjOERnMW9kNDFxTVJmLzdTQkVHUDcyQ0t3SkpMYWY3YTJZbUQ2Y2gwNjRhR3ZwNTUvZU0vUzlFZ3RaREc3VzhXbW9Lbm10ZVJIQXBmWWR1TjFxbVBieXFqYlhEYlRtUWlPcWVxMGxMLzBxKzM3a2YralVqN3ppbFFqWGwwc2c2VnBXd0JxN3hZUUtZQmxOQnI3NTdnb2xkK200Vk9ucjZxV0VwSzlBTktKd3pOWWp0RllzUzRqczJCK1JGdGZLS0dkQnhDdnQxMTFvT0Z6MHE5OHgxNW9MMmZDamYxVGdUb09UTitMaFU1TjM5SEhzS0FzTmRqWjZWck9Td2QzSy9HaGhqcDZBSHNYZ2xQVktlcmtFK3puZnFiRXRaUXdCcnJycEp4RzFhUlQ5bXQ3SG5zb1F0MFZUb05TVElkelA3OE9HaUZxYWM3TVc3TlY0b2J5UlpNdVBja2liMlZiQ242MWVIcFJDNmx6WDZoUnRMRTZDWmNsR1FqL3lpdG5hSmJZT1AybmhkU3Bta1pROGtLWUZXbjBiakxpZFZIdGdTYnU3WFJOMXJLaXdHR3RJT1RQbFJyZTVTejNVV3VnZlRON0F6Y3pPakxTY0ZyaXQ2VVl1d0VVOFN2Q0RhUUlCYXlSQldPU0FVYjdmMjRpZGx4TUZsMmoiLCJtYWMiOiI4MWUwMzQ3YjVlMGU5OTBlNzFlMTY3OWJjZDdmYTMyZTU4Mzc4NTg2NTRhZmJjNWVkOGUyMjFhY2Y2ZTM5N2ViIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:41:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9VREtJMWpBc3hQQXZOeFVPSi9WTkE9PSIsInZhbHVlIjoiM1Y3R0NubjlwUTBxUnVNU2EyUjFsUWJaeS9mMXJ5ZlJJYUhyV2QwbGxadllzTENTdU9oakhNcy8yRkFGR1JBazZOV2xXWEYxNVZCSGZreXZiTkNKSHp6d0hvYTR5d3hSZUVLYTUyVVFWekMwNXkwWWkvMjVmMlFZdFR2OUI2Q3l2S25QWW1qL0k3T3cxZ2tnMzdQeWdVYURFQ1ZzdkdEaHZEMDlYbFpwaTNRV3hnVzZwelVaOXJaL2kxTGRWdGZRQWNvYUNvbWxhMlV5MHdEa0xPcUc5cW9PaG1XZVRnQTRjN1cvbURHVGQ0OVNWQ1lTcytzQ2xtSEJaQ0JHd0Q2aExWR3YzRjRwOFc3RU9YeVBxazlQR3hRamptMnR6Ri9YUnREK2xSak5xd01SVUphTVpncENRMEx5dUdIbitQMlVhLzNlRFRHZmVYSnQ4RURnWXVRZS85RmdjTHNsNG14Q0RqV2F4Yy84QWZ6SXFIeUdsalU2aEsrc0loWmIzV0tpYWhhNTVmclBlRmFGYWR2T2RXbStDZ1B1YXVzMUlkQTUwelpySk9lTTNkU0xwUHhGVWNvR0w5NEJXT3Raa29qc3YzNW1iSVA0ZGZtczV4eDY0djVzM1hwaFhyZTB2VGFrRVpyM2pDbm9DWkNvdk1DTjhrbWFIWHViYnUwSEYxSEgiLCJtYWMiOiIzZTZjYWYzZGRjMTAyYjU1YWI4YWZiMjYyOTBjMGRhOTM4MmEwZWY0ZTM1ZTFjMjVmNzExYTFjYzViZTljNjkwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:41:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255293712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-586296595 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j2sdiefPT6OW1FVtpB5OAf9N2dHPW0M7YeKZVaRw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586296595\", {\"maxDepth\":0})</script>\n"}}