{"__meta": {"id": "X7f2149996690d4fc9c7e901c533357d2", "datetime": "2025-07-30 02:36:40", "utime": 1753843000.904153, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[02:36:40] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753843000.889084, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753842996.640886, "end": 1753843000.904214, "duration": 4.2633278369903564, "duration_str": "4.26s", "measures": [{"label": "Booting", "start": 1753842996.640886, "relative_start": 0, "end": **********.269274, "relative_end": **********.269274, "duration": 2.628387928009033, "duration_str": "2.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.269309, "relative_start": 2.628422975540161, "end": 1753843000.90422, "relative_end": 6.198883056640625e-06, "duration": 1.634911060333252, "duration_str": "1.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46419632, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.979941, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04799, "accumulated_duration_str": "47.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.839707, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 11.732}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.882832, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 11.732, "width_percent": 2.896}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.89864, "duration": 0.03886, "duration_str": "38.86ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 14.628, "width_percent": 80.975}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.94732, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.603, "width_percent": 4.397}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-127348852 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-127348852\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1287770087 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287770087\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497583607 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497583607\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1787528190 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjlVbTZyWkpoRGlqbjRTYy8zRlBIYkE9PSIsInZhbHVlIjoiRzRTWVJac1A1VGhWRWlGNXlUQmIwN2RlS1pjM09XbUFGMlhCZUsrb3hNMmNtVjg3WXMyRUsxUEl4Tk5FSFVTNkE5TFE5V2Y3Rm85WFFkQnV3TjZGWU9uMmRGcGVSNk1ySW9nUXo1VFVOaVJOTEFaSVlkbmVSSG43U2RlZUdOUGoxa2d4dHBmUzlNeTdYazNYSTZHM1JrUlhudG1OQ2VtNFhHbkNYVmljdXU4bVFFT2JhS1hiaHlxWmxpUVBXem8yYXROMWxZU01Ebml4UEpXU3Q5K0IvOTNqdUIzZ083SGlhb1lWbW1sZmRRRjR6ZXBxNWg2bkk5TmtqZiswbHd4YXQvNm9MenFVUWs5UGVSOGs2OHBuSzVVeExKL2dGRFBVT2JqVS9LMWlvSlFJd0piTkJSREsxSUVmVmJVbXNuei8rUHBDYktmazQ2NFI0cTFoMlNJeG4wQlNmWURQMGxKcVFCa3ZlR3gyYlRqdEVzNGFNRkNjSEZTUjZFbWR6ZnZGS2dTTWROMFk0aFoxbTFlT0paQzBuL1pkeHBwUDRSQjZKVU5ueGRKbm8zZG1BWmh5TXhiTjZFWnAwT3padU40Y29uRCtuelZsY2c3WEZuQS92VHd2cmhWckwxSFlUWksxNWwzU1paYm54RTlBRVFGYklpdFpxUXdNaXZueUxZRXIiLCJtYWMiOiIwN2I4ODA0N2Y4MjcxZTE4OWZmNTZmMzZlZDllYzcyMjMzOTM2MTU1ODM4NjhjMDhhYjI2NWFmM2UyZDQ2NDkwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhvSjJiWmM0NExLbUJZU2hjM3Q0YkE9PSIsInZhbHVlIjoiME55bG9OanlDWjU4M0txbk5hMjZXVmJuNGNmaVBBQ3hmYW93N1RqYlZvaXFZdStxTSswMGNlU1NXdVZWQTZuU1hlbkw2dW5VVWNDM1dPb0FYYUE1Mnp2d1AxTlJtY2dQaDh2MlNRdXkyb3ZCSU93QzRVNEVKNGk0SDdVUDlFcVV0U09IOUIyUnFPbjM3bUwraGN3bzF4WUJ6N2EwQmttTXZ1NDAxNDBxZVc1NDkyVXBXTDdXMDRGZUpmS3E1SHBnV094YTJIWmpLK2ppYU5STGU3aE92WVZEUGVid2h2MzJPRkVjQmVNWnQwWjFWMnhqUGFKNU9oRUpDQWR6Nk1vUnF6ZUxIUkkyeGk1dkkvTC9SSmxtbmFKbUJETWQxd3l0N1lhKzB6cVp1ZGtTcGxFejV3dFVrNk5CcGxqdTIrSUVlYldCNlVnZDh4cUhMYVNNcExyWmhNSTJvVk83UG9ReWVKVU9YTk9HeU4zczVzcEVTRGdVdUdkUDY3RlFReitxTEJ2MHJpNlI5Zi9FOU4xemQrWm5aZnBIMVZyQVdDMkpIbHV4bGpoWjlKLzl1andlRFpOV0ZURUtXdVZINHg1N0drU1o3RWpFQS9TM1pHTjBvNGNKUXhRNWhGN3NSdGJaV1hJMVRiZEZnUEUvbG1aK2REa2RsclU4aUFFV25jYkgiLCJtYWMiOiIxNTRkNGI0MDI5YjEwNmIyYzI4ZDZiMmQ5M2I5ZTc1MGYyMDAzNjc3N2Y3NDU4YjdlOGI0YzVmYTZiN2I2OTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787528190\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1894176296 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XRhqfNhIljuWkeh2CYPLg15rfgYjPgg1Mschh0S6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894176296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1224972134 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtCV0EwYkxmNE53NU9oVmJXZ09qYkE9PSIsInZhbHVlIjoiY2RiRVJkUlYzMnBKWkJiYXdqSms0RmtVSjBERzd3TVVWZnk1VWxuMGZ1SXdqRlFmZjA0djU3Y1NmOG5JU1VoUXBZMGR4bDZ1VUN4T2tKemhhMVY0cm1UZ244ZHVIUmt5aG1lRWFvWW9DeHVNWVpIM0lCN2c4MjQ0QWhFell2bzRjSUlGSVJFVEo5cG1jRU9uVDAveEZ0ME5Eb0lPVTRoYlNLWTVaalIyNEMzcm9nMCsyZTN5c2tIZHVqQ3VIblZBZVhLYzBESUswVjMrUDloejFUUnBWRjJHK0o5MmFNZmpIb3d1WVNVdHYrRzVrai9ndUprbk8yMGErcFdaL05HdWtZczNPcVZWMWl1aUYreE5SMytURmRmRFVmR0V2NzYyZGRyMnZteXV2T1JZNVQ0Zy9WV0RZcjNNdnVObitzbHBlWDcyS1g2QW1KUG40RUNvYmJTaml0OTN6dW43SDRpME5WeG1aYndmVmpkc1JpNklJRFNaaW1kWk91cXNvSWx1SFpTQkQ1KzdRNzJGT09xcUkxcXFGMiszSEV0RFdRQURqQ0FneFhsQzV3a2dqbEdaYW83L29nSmZreHQ5S2liL3M1ZmlyZk5sQXI2aE10RG5FcTNHMWY1Y2dIZUREZVN2RG5kUU1HRldmeTdlQnlIR045NENsZWwyOUVJMnBSUXMiLCJtYWMiOiJhYTEyYzM5YTljMmQ1ZjY2NjI1MDA2NjUzMmJiNWU2OTc3YzFkMjZjNjJiZTQzMjJlNzBhNmY2YjczYmE4M2E3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVNUjdpamxDOUVpbkpuRTQ5eXhoekE9PSIsInZhbHVlIjoiOGtmeG00Q3dUbkVqQWtic3pZNHpGSFhSdjdnQURlaS90dkVFZURvaXdkbnphOElIcDdIZFJ6YzdhTjBzZzZPektmL0t0SkZkT1FIaWYvMlBOckJJU3VUUjJJajByM2hEM1RVOFlsZ0RDc1Zuck5sNUU3WnM3REFWOFoxSHBsS0hxZEtTbVFhMG43KzI3ejYrajVsK3g1eXlCbTZwL2tHcEREZXg0M1F2a3FkSFRXYlRrWEo1VmdFSXkxMkZEMzRoS0h1YXROZEdFOHRMaUxhd0NGdmxnM204Z0VFRWFwZlJrazRjbnR0NldWL1dKdHY5eENYZncwQnIyQTg1aHFaMTJWeFBiRWIvWVhWTTUveVZsbXVIbDFmVlBwOThJMU96aGRzc0llM0IveEtuRi9BMVM0TG1UVnV1MmFNMTdvUTdHSGEyVnlXK00yWjRFSDVyWTZQdU85NFRQVGNsa3JXaDI0dW9iVnA1ZCtBNjZGbzZvZ0piT0Z4eWhYZVk5Q0NiSnhGZUxNU240MW1waU5vcGF3Q0dWZkp3RkhjaDE1Z2Z4dzlwaHQ4aDBsdlBaMElCMWVFRzA2YWF5NDdrNlFlYUhmQ1lRT1oxWVVPQm9OQ1VlZW12bmxXdWtXeVRLQzQzcFJWcTk0cFQvUkxsOUlvTDdBRktvcHJKNEs1VE5WMFEiLCJtYWMiOiJmOWQyN2EwMzVlOTFhYWNkNmRjNWFhZDY4Y2UyMDNkNDgwZGFiNmE5NWViY2VhNzYyMTgxZTg1ZTA0ZmJjZTg2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtCV0EwYkxmNE53NU9oVmJXZ09qYkE9PSIsInZhbHVlIjoiY2RiRVJkUlYzMnBKWkJiYXdqSms0RmtVSjBERzd3TVVWZnk1VWxuMGZ1SXdqRlFmZjA0djU3Y1NmOG5JU1VoUXBZMGR4bDZ1VUN4T2tKemhhMVY0cm1UZ244ZHVIUmt5aG1lRWFvWW9DeHVNWVpIM0lCN2c4MjQ0QWhFell2bzRjSUlGSVJFVEo5cG1jRU9uVDAveEZ0ME5Eb0lPVTRoYlNLWTVaalIyNEMzcm9nMCsyZTN5c2tIZHVqQ3VIblZBZVhLYzBESUswVjMrUDloejFUUnBWRjJHK0o5MmFNZmpIb3d1WVNVdHYrRzVrai9ndUprbk8yMGErcFdaL05HdWtZczNPcVZWMWl1aUYreE5SMytURmRmRFVmR0V2NzYyZGRyMnZteXV2T1JZNVQ0Zy9WV0RZcjNNdnVObitzbHBlWDcyS1g2QW1KUG40RUNvYmJTaml0OTN6dW43SDRpME5WeG1aYndmVmpkc1JpNklJRFNaaW1kWk91cXNvSWx1SFpTQkQ1KzdRNzJGT09xcUkxcXFGMiszSEV0RFdRQURqQ0FneFhsQzV3a2dqbEdaYW83L29nSmZreHQ5S2liL3M1ZmlyZk5sQXI2aE10RG5FcTNHMWY1Y2dIZUREZVN2RG5kUU1HRldmeTdlQnlIR045NENsZWwyOUVJMnBSUXMiLCJtYWMiOiJhYTEyYzM5YTljMmQ1ZjY2NjI1MDA2NjUzMmJiNWU2OTc3YzFkMjZjNjJiZTQzMjJlNzBhNmY2YjczYmE4M2E3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVNUjdpamxDOUVpbkpuRTQ5eXhoekE9PSIsInZhbHVlIjoiOGtmeG00Q3dUbkVqQWtic3pZNHpGSFhSdjdnQURlaS90dkVFZURvaXdkbnphOElIcDdIZFJ6YzdhTjBzZzZPektmL0t0SkZkT1FIaWYvMlBOckJJU3VUUjJJajByM2hEM1RVOFlsZ0RDc1Zuck5sNUU3WnM3REFWOFoxSHBsS0hxZEtTbVFhMG43KzI3ejYrajVsK3g1eXlCbTZwL2tHcEREZXg0M1F2a3FkSFRXYlRrWEo1VmdFSXkxMkZEMzRoS0h1YXROZEdFOHRMaUxhd0NGdmxnM204Z0VFRWFwZlJrazRjbnR0NldWL1dKdHY5eENYZncwQnIyQTg1aHFaMTJWeFBiRWIvWVhWTTUveVZsbXVIbDFmVlBwOThJMU96aGRzc0llM0IveEtuRi9BMVM0TG1UVnV1MmFNMTdvUTdHSGEyVnlXK00yWjRFSDVyWTZQdU85NFRQVGNsa3JXaDI0dW9iVnA1ZCtBNjZGbzZvZ0piT0Z4eWhYZVk5Q0NiSnhGZUxNU240MW1waU5vcGF3Q0dWZkp3RkhjaDE1Z2Z4dzlwaHQ4aDBsdlBaMElCMWVFRzA2YWF5NDdrNlFlYUhmQ1lRT1oxWVVPQm9OQ1VlZW12bmxXdWtXeVRLQzQzcFJWcTk0cFQvUkxsOUlvTDdBRktvcHJKNEs1VE5WMFEiLCJtYWMiOiJmOWQyN2EwMzVlOTFhYWNkNmRjNWFhZDY4Y2UyMDNkNDgwZGFiNmE5NWViY2VhNzYyMTgxZTg1ZTA0ZmJjZTg2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224972134\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1363475419 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363475419\", {\"maxDepth\":0})</script>\n"}}