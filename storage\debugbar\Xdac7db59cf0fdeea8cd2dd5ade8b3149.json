{"__meta": {"id": "Xdac7db59cf0fdeea8cd2dd5ade8b3149", "datetime": "2025-07-30 05:19:52", "utime": **********.915791, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753852791.292903, "end": **********.91584, "duration": 1.6229369640350342, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1753852791.292903, "relative_start": 0, "end": **********.763389, "relative_end": **********.763389, "duration": 1.4704861640930176, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.763413, "relative_start": 1.****************, "end": **********.915846, "relative_end": 6.198883056640625e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y06ZehaFrJRD2WItwxzgQxDZDj8hI3r0cu8c90go", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1266339831 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1266339831\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1442794368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1442794368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-555446829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-555446829\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1444057194 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444057194\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-19543669 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-19543669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1142748384 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:19:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikk0RmFERlZlQzBzWnhtMzFkMXhPWnc9PSIsInZhbHVlIjoiKzZaVlJHb2dXN09BZnhJNHZOclRBKy9iaXlhQWVBNHQ4ZW5OUTFWWkd5cFhRZG9QMDZKWHVIUnc2VTZNWE0vaUVBSzN6SFIyQW00WnhWZGVxZjVJVWp6YU41dUswdnBHMy9WUGsxSzdkTmZwNE9SMUp5a1BINTd2aVp5WGlHUFFRYzFFWDJ0SUVyQnliQTJGbWFMUWlHS3JCZk1KU1YvSFVhcFJIbmQ4aVU3eUg2VUZNYmNXZ2lab2N3ZnNjYmozQzNPcGpIWkFHTDJIMWhVTWRBaXgyd0pjQUdRaGR5SnVQTi95Y3JiZzRjR0ZwMDkzc0FHTk9ucDJuUnVsUzVNQjRVeWVDbDFEbWpIVmg5blNXRDBEZExGNWN6TEY4TnVSdThJMG9FTW9EcnQ3OTZDOWQwbXE1YkZ2SHkwSFQ4cjdzaDh2dnJ6MWRzbVBEUFpsOEF2WlBJNXZDaVVCbEFMSzZ2ODNRbTRoT0taajlqWitUazVHQndCTEFxeDR0RUVKSUE1eWxnSHZVZ0h6VUZVS3I5NHVpc3UzWHVsS1lReUY1eHE3Z0ErWmN2UjlGalJLblNScEcyUHQ3TlNFdk5nWFJJTUhFd25GdjdKVVBWcWhEM0FjeURRQ3R5dGVHb29nTENlN0RyVGh2aHNjSkFRb3owNnErMUxFdTdnTjRVbzYiLCJtYWMiOiJkMTg3ZDA5MTExNDE4ZWI1Yzg4ZWZkMTA1OTRhMDZhNDM5Y2M4NGMxMzdjZjMxYmI5NjFjZTAzNDc5ZmVkNmM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:19:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJjV2xZYmp1UFd1bStVeVVsU2lRVVE9PSIsInZhbHVlIjoiVFVYbGJjS3MxMUlUUWJMQklrcThFakJ5WFJPNVRoNG1tU1lGNFNyVG8rcUJGVVdXU2YwVlpWYzFLd2tPTXF0ZGlPckx2cjV6UEtRMVZEeEFJRUxSWlJHWlBmRVhyeVA2R1dTZGx6ZmRwSDJRQzBlcGx5dEFoZVd0TFA5K3YzNzVhbW9MTTZhMFd6MXgvRjdCUEZDaVhDMU52SUlTZXJYNGQ5a256U25VMlVNYjRVODZwSzBOblN3RTdTRVUwV1QrYVd5eWlBcEFuazhrV2V2VkUxcGd3UUVFUDlYaVQyQmVKMDhWYTl2SEFpL0ZqUkRSZU1DWWJTQUhES2JUUUNVN1dsUWhLbTJ0dGI2S3RhaytlUGEraVJvMU1jcTN6Z0l5UXM4QXRoK29kaGs3MTR1SENpVzFCalovYmh4TkgyTGdTeUJUSHB5enp2Nmo1NjJTaVQ2YTZ1Nm5LMUlqSXhsUHVld1NMVENKcDJvZWhHZyt1VWROa0tuQ3c3d0ZjdDZ1UlRjeVZSeWZDQU9TcUJKMHVyZ1M2RzNnbmpyVGdZdDNtN1NZU2dlWHBZdms3YVlwR214WkdqdkZQU3ZGeGpGM2d2ZGdmUHh3VjM1QWlPMU9sVmZTNDJhTVNGNTQ2QVNCUUw1RExOS1l5SFhNV1AwTWVGdmcvakZWNG1DWTJMQmIiLCJtYWMiOiI1YTNkNzY3NjBlZjA3N2QxYmMyZjc5MzE0MWNkZDY2NTIyYjU2ODJmZDNjYTU0YWMzZDA2M2Y1NDA0NmIwMmRjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:19:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikk0RmFERlZlQzBzWnhtMzFkMXhPWnc9PSIsInZhbHVlIjoiKzZaVlJHb2dXN09BZnhJNHZOclRBKy9iaXlhQWVBNHQ4ZW5OUTFWWkd5cFhRZG9QMDZKWHVIUnc2VTZNWE0vaUVBSzN6SFIyQW00WnhWZGVxZjVJVWp6YU41dUswdnBHMy9WUGsxSzdkTmZwNE9SMUp5a1BINTd2aVp5WGlHUFFRYzFFWDJ0SUVyQnliQTJGbWFMUWlHS3JCZk1KU1YvSFVhcFJIbmQ4aVU3eUg2VUZNYmNXZ2lab2N3ZnNjYmozQzNPcGpIWkFHTDJIMWhVTWRBaXgyd0pjQUdRaGR5SnVQTi95Y3JiZzRjR0ZwMDkzc0FHTk9ucDJuUnVsUzVNQjRVeWVDbDFEbWpIVmg5blNXRDBEZExGNWN6TEY4TnVSdThJMG9FTW9EcnQ3OTZDOWQwbXE1YkZ2SHkwSFQ4cjdzaDh2dnJ6MWRzbVBEUFpsOEF2WlBJNXZDaVVCbEFMSzZ2ODNRbTRoT0taajlqWitUazVHQndCTEFxeDR0RUVKSUE1eWxnSHZVZ0h6VUZVS3I5NHVpc3UzWHVsS1lReUY1eHE3Z0ErWmN2UjlGalJLblNScEcyUHQ3TlNFdk5nWFJJTUhFd25GdjdKVVBWcWhEM0FjeURRQ3R5dGVHb29nTENlN0RyVGh2aHNjSkFRb3owNnErMUxFdTdnTjRVbzYiLCJtYWMiOiJkMTg3ZDA5MTExNDE4ZWI1Yzg4ZWZkMTA1OTRhMDZhNDM5Y2M4NGMxMzdjZjMxYmI5NjFjZTAzNDc5ZmVkNmM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:19:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJjV2xZYmp1UFd1bStVeVVsU2lRVVE9PSIsInZhbHVlIjoiVFVYbGJjS3MxMUlUUWJMQklrcThFakJ5WFJPNVRoNG1tU1lGNFNyVG8rcUJGVVdXU2YwVlpWYzFLd2tPTXF0ZGlPckx2cjV6UEtRMVZEeEFJRUxSWlJHWlBmRVhyeVA2R1dTZGx6ZmRwSDJRQzBlcGx5dEFoZVd0TFA5K3YzNzVhbW9MTTZhMFd6MXgvRjdCUEZDaVhDMU52SUlTZXJYNGQ5a256U25VMlVNYjRVODZwSzBOblN3RTdTRVUwV1QrYVd5eWlBcEFuazhrV2V2VkUxcGd3UUVFUDlYaVQyQmVKMDhWYTl2SEFpL0ZqUkRSZU1DWWJTQUhES2JUUUNVN1dsUWhLbTJ0dGI2S3RhaytlUGEraVJvMU1jcTN6Z0l5UXM4QXRoK29kaGs3MTR1SENpVzFCalovYmh4TkgyTGdTeUJUSHB5enp2Nmo1NjJTaVQ2YTZ1Nm5LMUlqSXhsUHVld1NMVENKcDJvZWhHZyt1VWROa0tuQ3c3d0ZjdDZ1UlRjeVZSeWZDQU9TcUJKMHVyZ1M2RzNnbmpyVGdZdDNtN1NZU2dlWHBZdms3YVlwR214WkdqdkZQU3ZGeGpGM2d2ZGdmUHh3VjM1QWlPMU9sVmZTNDJhTVNGNTQ2QVNCUUw1RExOS1l5SFhNV1AwTWVGdmcvakZWNG1DWTJMQmIiLCJtYWMiOiI1YTNkNzY3NjBlZjA3N2QxYmMyZjc5MzE0MWNkZDY2NTIyYjU2ODJmZDNjYTU0YWMzZDA2M2Y1NDA0NmIwMmRjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:19:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142748384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1194877932 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y06ZehaFrJRD2WItwxzgQxDZDj8hI3r0cu8c90go</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194877932\", {\"maxDepth\":0})</script>\n"}}