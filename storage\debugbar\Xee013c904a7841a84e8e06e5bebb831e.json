{"__meta": {"id": "Xee013c904a7841a84e8e06e5bebb831e", "datetime": "2025-07-30 08:09:04", "utime": **********.251387, "method": "GET", "uri": "/chart-of-account/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.25475, "end": **********.251425, "duration": 1.****************, "duration_str": "2s", "measures": [{"label": "Booting", "start": **********.25475, "relative_start": 0, "end": **********.982765, "relative_end": **********.982765, "duration": 1.****************, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.982791, "relative_start": 1.****************, "end": **********.251429, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "269ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x chartOfAccount.create", "param_count": null, "params": [], "start": **********.199101, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.phpchartOfAccount.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2FchartOfAccount%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "chartOfAccount.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.238602, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET chart-of-account/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "chart-of-account.create", "controller": "App\\Http\\Controllers\\ChartOfAccountController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=54\" onclick=\"\">app/Http/Controllers/ChartOfAccountController.php:54-76</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.03085, "accumulated_duration_str": "30.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.070294, "duration": 0.023870000000000002, "duration_str": "23.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.374}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1347518, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.374, "width_percent": 6.515}, {"sql": "select * from `chart_of_account_types` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChartOfAccountController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ChartOfAccountController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.151464, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ChartOfAccountController.php:57", "source": "app/Http/Controllers/ChartOfAccountController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=57", "ajax": false, "filename": "ChartOfAccountController.php", "line": "57"}, "connection": "radhe_same", "start_percent": 83.89, "width_percent": 4.927}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4779}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.203986, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4779", "source": "app/Models/Utility.php:4779", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4779", "ajax": false, "filename": "Utility.php", "line": "4779"}, "connection": "radhe_same", "start_percent": 88.817, "width_percent": 6.451}, {"sql": "select * from `plans` where `plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4784}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.2161949, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4784", "source": "app/Models/Utility.php:4784", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4784", "ajax": false, "filename": "Utility.php", "line": "4784"}, "connection": "radhe_same", "start_percent": 95.267, "width_percent": 4.733}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/product-unit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chart-of-account/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlVjRzBwVEw3VTY5NU92ODdKWktqY1E9PSIsInZhbHVlIjoiNG9MVHYvRTZ0RzBjc3JmZjJFaThuM3h5RDdKWCtMdnJGMzNYb0RIam9nRU5xMlUxODlCSWJvT1JTNE81UDVrVDRwMHcxWk52b1JkVDVBZ3JQNkM5bktOZTdZTGwxczNvMklOUnlObndTUElzb1FXQ3JxU0E5VmQrUE5DdXd0cktFamlaYis2RXRET0Zta3lwZm5JV1IyMlJsaUhYWDFqSmVsNHBmcjBJazBPay9kODEvZHlwY09yZHJiRHFtaUtlQlhQeWFqWFR5czlma0E2SE9iYWhJWE9DNWM0czlZWFVDb1VWZmthaHZVMml5TVJpUFhjQWQwaUV2aWF0bFBtWnR4cXBXL1R4eCt0elBncVBwNURIOFdhR2tCMXk4ZWRITmRxcUhXdWFyL1ljdTVlMHBjY3lwSnRNdlJLQUNVb1ZtUm5XZ0FXQXE3OHpzaDlDL1pEWmkwQzBpMmhnU21LOWwyYjVqRUtwYUh4Sm40T2ZXVHVta0lLRG9mMjM4MFdOKzRMTDVqa2EzcDBEN1o1RkRkTnlyU0IzS095dVVuMVZ3NE1obExvYmdRR2s4Z3ZINVpHSEVFZGU1MEIvNGphcktYeHZOMzRodkMxc1dNQmUxaGNuSDczRGpvMkhDdEVlNS9OcU14emFaMmJDODNmNWtpNmplcmRZZ3lCNENubCsiLCJtYWMiOiJlNTA1NzE3MWY0YWYyNzc3M2FmMjZhNmUxMzllMmFmZTRjMjIxZjI5YmI4Njk1NDRiZjU1ZDdmMDZjN2Q3YmY2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InZUNHVnY0RoVlZPdUtsa0RIKzdmM2c9PSIsInZhbHVlIjoiYUdtTWNVN2g1Njc0aUw2bGxyYnZ2NU5vTW5WeENyZkJhdTZDRmpOTjNmMnZjWXJFekM0RCtXMTNuclJISGRray8wRk40cHk3b1ZweHloK09vL0RtRERxQ1R5ekY0RkpnOGM3ZXRKcHNUd1RpRk8rSlIzQ3JIMTFCcmJrRDRNbCtsNGJvWWVuMVB6N3B0VXZ1ODBYamd1ejdCRkovNGZaNmR5VDUwMko0RG5FOXQyem9hczBQdG13TzNIbjhFRkc4S0RsOVdtb3BoTUV2MUtVdlR5ZWF2UXNZSUUrS2JDMDVGdksvVjlDVzBhMlRsM01lL0tETXBNV0JSWDl6ZzdGWnN6RkVCTzZQayt2NGg2VWtORmV4OXpVeVE0dkR1dmZOOVZlMzd5UDBZeTZkaUpnOWQxRTJwZlc2TmRQTlhTMUJUY2IxUE5TcnliMGZpcHU5dUh3VFhFRnZpSnNWVWk0RVByWEQ0U2NBbDladndjZUY2aVNLeFBBUEJoWGtWcUpHOXVXN1NlYWJaZEhsL0RZUU81Y24vdExHNldBaVBzYnkyMUFEOGJMZEl5M0JNMXhyWjE3RE1rZ3I1bEZHUzRKK2kvVVpvbGs5L3ErV2xmRjNaTHVoYldwYTMzcEFJSnFmTWdVOGd1Y3FuQ0o0eE8rTy94emJaeHFzb1dHNkMrSjMiLCJtYWMiOiIyMzQzNzE5ZTQ0MThjZDdkNDNhNjc2ZDk4ZmIzMzkxMDExNTA5MDRiOWNlYzA3MWU3YmVhMmM3ZjRiOWU1YTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-547451391 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547451391\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-599270996 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:09:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZyQkhTVlk3K3BSMWRCWDJjNEx1WFE9PSIsInZhbHVlIjoiNk81cmkzTEE2S0pqUUZWLzc1cXRNRzBmMzZOVzlsYzN6R2kyZ1dJV0EvSThlZlB2UlpjOS9pNDRCMFMzdHVWcWREQVhuSFBkZVNGU000MmhWZGdlT0djdTJidmxlTXhxZFArNklvQkhkTVBXS0g3K0ZtdXh0S0lwc3VzY1ZVU3FWMHpnK1ZDTytuUHpaWkJ1Yi8rWlNqeFRkY29HaVZmVG15aHkvcUFNN1NoMTh2ckRJRlQrdUNwaWxGY09DTG9ZZjM1cFZIb1QzcUZrcGVENlY4RU9BMXU5dDRwYVR6REVvYi9BcDQ2UWlIT05RdTYvY3dHV1pXQmdGR1Q0OUVnVDVvOEtDLzdOZG5sK2hzUElSWVlpdlJ0ZzdyRy9WK3VBQk5mSUhsaVRsbW8zRVNjR3hCTHlkK0IrRDh0NTJWdG9RTVZQV1FmcGd6UXE5NWh6MWhTTExSelpXeHZMNWVjVDBMTjViN3BiWmlKQ2lCdGE4cEtEU3ZJekhSUHVMTHJidjVpNitlWUNlT3ppMkpmU0t5T0lVaTVqQ1JlOWtkWmNKa2M4UEd6ZjlpdVJHelBqVjN3Y2liQmxkRndMZTM2ZHJYNE1pVkhPc2p1c3h6WHkzT2ZCSDJxRmNsYSsrWFhRRFJsN0pHZGpCWG9MdE5yT3pxRWh6MmhTZ016UitZQnUiLCJtYWMiOiI3MGY5Y2FkYjAwM2NiZmVkMDFlNzU5NDM0OTk4NzdmZjAxZTdhZGQ0ZTQ4MTI0ZjQ2OWUzZTZjMzAwNTkwZDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjU1NW9lWUhkZ09DVVBQRllqSVBhZ1E9PSIsInZhbHVlIjoiZkw4aVUyb1RWVGk4QTgxR2xySEQ4WEdMZnBTb2VQU05oK3NBV0R2ZzB1MjRuQnJ5dzNzbTFFeEJqaEI1NGFUYXc3WTZhMk52WHR4anpZZjZUN3Nsd0RjcVoyM0pvWnZDVCsyc2NFRE0vTS9HeXFock4xNEpjMFNxdURYMFdjUVlHWmo3RUs2Q25MZXUwT3JHTGFNblMwRFJKYVA2b0IzQm5PRkVrZnJMV00vVmE3MXNIbHE2dDZVYzVoeFl0MU1RbWxQQnVDRjNQZktoVTNEYUpLem0xS1JmZktNc3RWb1B1UXhkUGh1S1dycExMaGVmQVpCTmIyK3JIazRkam1ZWU1QNWpCWmhvU0h5Tm1Zd1ZEcmFoRi9LNG9wcHNETmZBb0ltQkNCaGwySU11V1lxcmtOVkNnWWVmSHZQdGl5SWNtY0FjVFZpK3VMZk1RYmJZZHR2NVlzQ1NmT0VCeVpWcTR0MlB2ZFQxemg2WVFMZDhYYkI1ZXNNY3hKc0Q5THNYSmpaRmNGSDQwdlN2RG1yc2RVODdZQmJ0dTgxZzZkelZ4TW9hRkNNWW1mWGJ2VlpKaTJwRDRHcExSa214OEZ3SU40Nmd0WXM2bnNGaEs2T1BuQ0RVbDBwN204Z2NnUkh5OXBWNzVBM2R5K3VGd2Y5a0hJVFhaeC9pZlVSVmlKVzMiLCJtYWMiOiIwNWY2NmNmMjU1NmY4NmMyM2MyMGFkMTk5OTI0ODM4ZjQ0NWVlZWM3YWM1NWJiMDRmMjQ3ZGM3YjA0ZWFlNWIwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZyQkhTVlk3K3BSMWRCWDJjNEx1WFE9PSIsInZhbHVlIjoiNk81cmkzTEE2S0pqUUZWLzc1cXRNRzBmMzZOVzlsYzN6R2kyZ1dJV0EvSThlZlB2UlpjOS9pNDRCMFMzdHVWcWREQVhuSFBkZVNGU000MmhWZGdlT0djdTJidmxlTXhxZFArNklvQkhkTVBXS0g3K0ZtdXh0S0lwc3VzY1ZVU3FWMHpnK1ZDTytuUHpaWkJ1Yi8rWlNqeFRkY29HaVZmVG15aHkvcUFNN1NoMTh2ckRJRlQrdUNwaWxGY09DTG9ZZjM1cFZIb1QzcUZrcGVENlY4RU9BMXU5dDRwYVR6REVvYi9BcDQ2UWlIT05RdTYvY3dHV1pXQmdGR1Q0OUVnVDVvOEtDLzdOZG5sK2hzUElSWVlpdlJ0ZzdyRy9WK3VBQk5mSUhsaVRsbW8zRVNjR3hCTHlkK0IrRDh0NTJWdG9RTVZQV1FmcGd6UXE5NWh6MWhTTExSelpXeHZMNWVjVDBMTjViN3BiWmlKQ2lCdGE4cEtEU3ZJekhSUHVMTHJidjVpNitlWUNlT3ppMkpmU0t5T0lVaTVqQ1JlOWtkWmNKa2M4UEd6ZjlpdVJHelBqVjN3Y2liQmxkRndMZTM2ZHJYNE1pVkhPc2p1c3h6WHkzT2ZCSDJxRmNsYSsrWFhRRFJsN0pHZGpCWG9MdE5yT3pxRWh6MmhTZ016UitZQnUiLCJtYWMiOiI3MGY5Y2FkYjAwM2NiZmVkMDFlNzU5NDM0OTk4NzdmZjAxZTdhZGQ0ZTQ4MTI0ZjQ2OWUzZTZjMzAwNTkwZDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjU1NW9lWUhkZ09DVVBQRllqSVBhZ1E9PSIsInZhbHVlIjoiZkw4aVUyb1RWVGk4QTgxR2xySEQ4WEdMZnBTb2VQU05oK3NBV0R2ZzB1MjRuQnJ5dzNzbTFFeEJqaEI1NGFUYXc3WTZhMk52WHR4anpZZjZUN3Nsd0RjcVoyM0pvWnZDVCsyc2NFRE0vTS9HeXFock4xNEpjMFNxdURYMFdjUVlHWmo3RUs2Q25MZXUwT3JHTGFNblMwRFJKYVA2b0IzQm5PRkVrZnJMV00vVmE3MXNIbHE2dDZVYzVoeFl0MU1RbWxQQnVDRjNQZktoVTNEYUpLem0xS1JmZktNc3RWb1B1UXhkUGh1S1dycExMaGVmQVpCTmIyK3JIazRkam1ZWU1QNWpCWmhvU0h5Tm1Zd1ZEcmFoRi9LNG9wcHNETmZBb0ltQkNCaGwySU11V1lxcmtOVkNnWWVmSHZQdGl5SWNtY0FjVFZpK3VMZk1RYmJZZHR2NVlzQ1NmT0VCeVpWcTR0MlB2ZFQxemg2WVFMZDhYYkI1ZXNNY3hKc0Q5THNYSmpaRmNGSDQwdlN2RG1yc2RVODdZQmJ0dTgxZzZkelZ4TW9hRkNNWW1mWGJ2VlpKaTJwRDRHcExSa214OEZ3SU40Nmd0WXM2bnNGaEs2T1BuQ0RVbDBwN204Z2NnUkh5OXBWNzVBM2R5K3VGd2Y5a0hJVFhaeC9pZlVSVmlKVzMiLCJtYWMiOiIwNWY2NmNmMjU1NmY4NmMyM2MyMGFkMTk5OTI0ODM4ZjQ0NWVlZWM3YWM1NWJiMDRmMjQ3ZGM3YjA0ZWFlNWIwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599270996\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-52857917 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/product-unit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52857917\", {\"maxDepth\":0})</script>\n"}}