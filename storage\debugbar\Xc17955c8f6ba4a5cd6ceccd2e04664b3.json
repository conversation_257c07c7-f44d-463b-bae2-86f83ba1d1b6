{"__meta": {"id": "Xc17955c8f6ba4a5cd6ceccd2e04664b3", "datetime": "2025-07-30 08:11:30", "utime": **********.052128, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863087.790486, "end": **********.052191, "duration": 2.261704921722412, "duration_str": "2.26s", "measures": [{"label": "Booting", "start": 1753863087.790486, "relative_start": 0, "end": 1753863089.883281, "relative_end": 1753863089.883281, "duration": 2.092794895172119, "duration_str": "2.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753863089.883311, "relative_start": 2.***************, "end": **********.052198, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lS5WnQ2QDXqUV1RZOzccvbuLIoWkzprZW0Tb059t", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-539874995 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-539874995\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-626374098 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-626374098\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1628620262 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1628620262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1635164422 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635164422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1761937026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1761937026\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1836041994 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:11:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InU0UC9uQ0JJS0lmVGhvWXRXSk9ldXc9PSIsInZhbHVlIjoiTmhhRS9Tc3BqbnZFcjJ0RUN6QzlwTExlY3daRit4M3REV2NSOGdXSEZYOUZ5bStXQTFLUE80MGszdldQVjlXWllIa2dVM2NJajRmQmZWcTBlV2RFcGo3cE1jNERKYkIySVZ0bGhKcWN0MUM5ek5hQy9PbDVDU3k4ZExkSTZocGhIY0x2ejRqV0M0OUNxZzZyQ3QyY0FIWXNPK2FmM0MzMzJpbTR5a3ozTk56N0FabWN6a0lVNkE2VHB0OFBRNjR3S2NjanhUdDBPOE5pOXZ6WnpyVmtTNU0rcUd5eVBpbW1LMFl4U1ZJZUtRUThoN1R6NUVLRjdsS0FHTmpmRCtRUWpJWjloYXdtWTJjSVZNdDhxQ3hFK3hZL0VXNExrbUNONWZ5RDlRY2ljeFhESDJzZkRiOGthaCtYdUk1S1huNVRKZVhpR1FFV0RFR1R0ZXFla1M1MHh6VkFNSmtNUCtodFNHa29Vc2VMS25JT1dzSVlmUTFRODBVcUFUUCtqUjZmaVRuS2pNSkEya2hTQ2lBNDRLUkZ4Qk1hQURWbFp4MDYwMldKQVNrT1I5TmM0a09iNU9ZME5FVitKWGg2YUxaNWdXLzNNUFJ0SFN2TEsyMlFLRmNEaEpXZlZpRUllV0t6SkVVb1VSWmQ4VTRSVUx2LzlaMnBBNXRBUTIrVXZhV0giLCJtYWMiOiI3ZTc3MDMyZjZlOTM4ZDIyNWQ3MDVkOTQ1Y2YzNjhjOTFjOWUyZDAwNzVjMDlkYWI1N2IwYjdiMmYxMGM1OWMwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkRiVjZKemUvcTZuOHNFL0JoQWNRaGc9PSIsInZhbHVlIjoiTGVZMWo1TWY3MlN6bmF4MXJpTXNsNEl6THZPVEhtWEI1c3U3Ymx0QUM0R3I3dnVVNGZicERqcjJIdk10RVFieE0rQURuczJoQ211ejFVWFkyY3ZrZEZrMy9lNXF6R2JvN1oxQ2VMYlZFVmhDdzJUT05UVk8yNnVYYjNhUWNiZ2taSlozK3RSZEdHYnc5N1dnc21NejFmS1VnbTlNNXd0ZjgzZkpFTEkvdHQ2Q0xFNWZ1UHJGS0ZTeEFaRHpjVU9iamNTRWllRmpWR3BTOC9WMVFrcVYwOXJ3WnkzZkFVZDlsQTVsYzROVVlGWncxRURQbnQvQmJ6dUZsWGN4VjlCWFQ3R1hxSklkZE1kSGErdmU2TEJPbWVSb3BzbHRkLzZPSURqTDZ0eHoxWks2Vm11N3NqalhQeHc0Mis4U0JUYVVQcDNWU2JFRFU4VDAwRFBOR21aVVhOTURFNXNFRXQ2WC9PUlNqbHZwQjhtZHNPS0pkVXNKQkVOUC9xQ3BsblhLWVpQdlEzdDQ5SzVKd0pMN3EzaERMUHZCNVBOSWVCOGZrNzZHVHYyV0dKckNNbkpKV3AwL2hmSmJacEIwVXZWZmVVN21GZWkyTnkrWm1MVXhNMm5NdlpnK1RUYWZxRVRYTXJiS1RLSEpGeE91dFBPcVFMc3BOeHNLT1hsN1BUbzAiLCJtYWMiOiI1YmMzOGEyY2YwZGEzZDczZGYwZTdkZGU5Mzc2Zjc2YTE3OGRkM2ZmNWRkZDk1MGFlNzUxMGM3NDQwMTI5NTNhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InU0UC9uQ0JJS0lmVGhvWXRXSk9ldXc9PSIsInZhbHVlIjoiTmhhRS9Tc3BqbnZFcjJ0RUN6QzlwTExlY3daRit4M3REV2NSOGdXSEZYOUZ5bStXQTFLUE80MGszdldQVjlXWllIa2dVM2NJajRmQmZWcTBlV2RFcGo3cE1jNERKYkIySVZ0bGhKcWN0MUM5ek5hQy9PbDVDU3k4ZExkSTZocGhIY0x2ejRqV0M0OUNxZzZyQ3QyY0FIWXNPK2FmM0MzMzJpbTR5a3ozTk56N0FabWN6a0lVNkE2VHB0OFBRNjR3S2NjanhUdDBPOE5pOXZ6WnpyVmtTNU0rcUd5eVBpbW1LMFl4U1ZJZUtRUThoN1R6NUVLRjdsS0FHTmpmRCtRUWpJWjloYXdtWTJjSVZNdDhxQ3hFK3hZL0VXNExrbUNONWZ5RDlRY2ljeFhESDJzZkRiOGthaCtYdUk1S1huNVRKZVhpR1FFV0RFR1R0ZXFla1M1MHh6VkFNSmtNUCtodFNHa29Vc2VMS25JT1dzSVlmUTFRODBVcUFUUCtqUjZmaVRuS2pNSkEya2hTQ2lBNDRLUkZ4Qk1hQURWbFp4MDYwMldKQVNrT1I5TmM0a09iNU9ZME5FVitKWGg2YUxaNWdXLzNNUFJ0SFN2TEsyMlFLRmNEaEpXZlZpRUllV0t6SkVVb1VSWmQ4VTRSVUx2LzlaMnBBNXRBUTIrVXZhV0giLCJtYWMiOiI3ZTc3MDMyZjZlOTM4ZDIyNWQ3MDVkOTQ1Y2YzNjhjOTFjOWUyZDAwNzVjMDlkYWI1N2IwYjdiMmYxMGM1OWMwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkRiVjZKemUvcTZuOHNFL0JoQWNRaGc9PSIsInZhbHVlIjoiTGVZMWo1TWY3MlN6bmF4MXJpTXNsNEl6THZPVEhtWEI1c3U3Ymx0QUM0R3I3dnVVNGZicERqcjJIdk10RVFieE0rQURuczJoQ211ejFVWFkyY3ZrZEZrMy9lNXF6R2JvN1oxQ2VMYlZFVmhDdzJUT05UVk8yNnVYYjNhUWNiZ2taSlozK3RSZEdHYnc5N1dnc21NejFmS1VnbTlNNXd0ZjgzZkpFTEkvdHQ2Q0xFNWZ1UHJGS0ZTeEFaRHpjVU9iamNTRWllRmpWR3BTOC9WMVFrcVYwOXJ3WnkzZkFVZDlsQTVsYzROVVlGWncxRURQbnQvQmJ6dUZsWGN4VjlCWFQ3R1hxSklkZE1kSGErdmU2TEJPbWVSb3BzbHRkLzZPSURqTDZ0eHoxWks2Vm11N3NqalhQeHc0Mis4U0JUYVVQcDNWU2JFRFU4VDAwRFBOR21aVVhOTURFNXNFRXQ2WC9PUlNqbHZwQjhtZHNPS0pkVXNKQkVOUC9xQ3BsblhLWVpQdlEzdDQ5SzVKd0pMN3EzaERMUHZCNVBOSWVCOGZrNzZHVHYyV0dKckNNbkpKV3AwL2hmSmJacEIwVXZWZmVVN21GZWkyTnkrWm1MVXhNMm5NdlpnK1RUYWZxRVRYTXJiS1RLSEpGeE91dFBPcVFMc3BOeHNLT1hsN1BUbzAiLCJtYWMiOiI1YmMzOGEyY2YwZGEzZDczZGYwZTdkZGU5Mzc2Zjc2YTE3OGRkM2ZmNWRkZDk1MGFlNzUxMGM3NDQwMTI5NTNhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836041994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1283666782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lS5WnQ2QDXqUV1RZOzccvbuLIoWkzprZW0Tb059t</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283666782\", {\"maxDepth\":0})</script>\n"}}