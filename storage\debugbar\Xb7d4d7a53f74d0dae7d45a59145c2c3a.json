{"__meta": {"id": "Xb7d4d7a53f74d0dae7d45a59145c2c3a", "datetime": "2025-07-30 08:26:51", "utime": **********.146034, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753864008.318448, "end": **********.146094, "duration": 2.827646017074585, "duration_str": "2.83s", "measures": [{"label": "Booting", "start": 1753864008.318448, "relative_start": 0, "end": 1753864010.823282, "relative_end": 1753864010.823282, "duration": 2.504833936691284, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753864010.823323, "relative_start": 2.5048749446868896, "end": **********.146101, "relative_end": 6.9141387939453125e-06, "duration": 0.32277798652648926, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02997, "accumulated_duration_str": "29.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.018011, "duration": 0.02527, "duration_str": "25.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.318}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.085778, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.318, "width_percent": 7.241}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1024199, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 91.558, "width_percent": 8.442}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-1323638662 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1323638662\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1927261485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1927261485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1154668845 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154668845\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-713387359 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImdMRGZoeXFZQmFweFk0OGFPRE1wRGc9PSIsInZhbHVlIjoiNHRxS1RSeTB5bXNnWnJRN2VFQ2VsUlhodDZ0Tit1TzFNQWw5N09pc2V3UlErazZSZnEyV1FvMDVOYVNwYm1lTHA3Z251U3J6b1lzbHRuU3h1S25JODNETzJGaTdvczNCcHByNjBuelNJN2FucUdxL0FYYWpDNkdtR3pYMmhJNnE1T3pHUk1jaE5vcWZKcUtPVytUMGMxYzhrN1hUYWdUbm14UHlzQ1hlUlZ4ZkJ4QmpUU1ErRzFGdUpCSzQzc1JVM1ZKb3pJVGlhZTczS2dxQ1NSeHcvbW1CWHkzRloxTFRlQ25DM1cwbjJJbFdLbVNHZU1Md29GM05STWhrYXpUOVUzT2RtNWN1OVZ4KzdYbDlNYUNabVBBbzllcjU0U05hL0NpYTNpWUhGbUJIOEpia3pSS2x0Ymcva3pLc0RZMkFvVHF3MHVBR2dDMFJtM3YrYzVEYlFkQmd0RmVNSlc1NUkyY1RBbGN0NjcvWXNNUW1aaWhKT0Y5cmJ6ZjdnQTB1TkNQcUN2a3VtRHFmT2ZXODZNcG5HZ3duZ1V6MTV0LzRObHhRK0ZYV3RHVTZ4MkhYRDJQeXNTTlduODNidmZVNW1mM3Z5QUsxSjBJSWFseWNuWms1THFIVjFiVkQ1b2doOWZka1krZ3c2Uzhnam9DOGh6NWFPUWttdHl5UTV1L1MiLCJtYWMiOiIyOWQzZWRiNGQ2YWFkM2NlNDU4ODI5M2Y2ZDFiZmZjYTY4Y2RlMzMxNGJkN2Y2ZmVhNWMyY2YxYzIyMGMzNDJlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ikw3ZE5OSithWGNhTEc2MVdzRTBMUVE9PSIsInZhbHVlIjoiOE0zeWRrVU5nK2loMS9WeFREenJIUG16R0REZC84bVhOT2txY2xldThtNXJLcmRSMzN0UzVqRzBUNGhvajBiWUlwN3hjVjlEWkZOMEYwOXd5UEgzTXQzTkpzaFdJeEFBVnhZRGZhbEZaWTBSa0M4U2syU3dUclIzMFlOQlMrWk9LcENMT2cwbzlGV3R6N0o0TEhVbkhrN2Z0SEk4WnIrR0JHd2d0M3ZDZlNXVVovMVpMakMxMXQ1WVM1RS8rTWlSSHE2ZnJVVkNxQWhNdXBxYVJkV010b1ArNU1uYk04UHE0UGE3TXB3VXZnSmZBUm80dFZPQkd2TG9YZ2FXL1cvZnFMK0dpRlF2dW01N21YWWNQTEJrN1JsTjh5aVV4NEJsc0M3QkYycW1sWnExRGUwMm1pMTJJV1BadHlWRU5ub2s2alYvUkV4YUlzMHVhT0wxY2duNGNEQm9ROU82Y2t2L0VIVkhobXEwanQyK0themdVS0lZOTVSU2VqRlRiL0QrL0Y5TUVra2ZBR2psWW5yV1NZaGFqRXNRVjRZRTdUZldRaHJwcnkrTWt1UXZBWjFwdmZqbzJEL1pqMFpjeVI3Q0NPVXFHQ2lqUm5mWUR3VlM5N1pqZEtKVUtCYnNHYWtsdjJ5TkNmWVYvYkhXL1VkTUgzNjlZdWloY2oyVnBmMVkiLCJtYWMiOiJiYjk4ZjVlMWNiMzNiMTk3NmQ4MTc4YWI4ODQ3NzgwZjdiMTQzMTgzODE0NzA1NWViN2E0NzIzMzhiYTkwOGQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713387359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1252152664 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252152664\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1259134721 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:26:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQzZWIzeFV1ZC9KaXIyS1ROMUozT0E9PSIsInZhbHVlIjoiVndmeHVQQ1FscjdkN0FMTHVGcXhPUVdIWE9aaUowVHFsV3M4bWttTjJjMTFid0ZGeHg2WU5FbW1ZZVhVZUVvNEk3Z2ZsNi9BbVlxZ09Ga2ZSemtEK3ZVRGdRYUlsZnI3M0c0dHpMWkljSWNrUTQrVC80OGVmc3hHZnRLd3AzZkkzMkpKN1NUYWY2QldBOGpRU0J0UTM3Zzl3ZVEzMmRVUmRxMmxubGVyMGp2RGJjbWRZRGFKRHBhNXRBMCtvZlNrUDBRQTY1MkFOc3hXQ0FRb25wK2RDaHlRWWdzUzh0T3FhdkwyMlg5TjRvZW1FTWlhd1dHdFVuY1Z5N2lrQXV5VnY3V0lwNmxIUjMvdzBqczUzZVUxV3NxTmU1UnhWQ2EwOUcxc3FlV3VUMGZiSjAvZENCSVN0NVJ2TFJIRm95NjQ5cTduNkoxbU96YXNCZlE5RUdIQ2hJUXZaV2RjcWJlUFBFK3VJYjkybnJiNFJ3blpET0N1dUdGdnM0emZ2Qk9pblEzOEZRbmFnWldFbjdSK05EdTlTZG9HYitnYlRMeHNPdmE1Z1pGdFBYc1NpSXVjSVZXMEJiaXBLK0l3WkRaSGhDQ0FJdUhnd0hnbzd4eUpSL1R3L1c2YTBkYnN2VUhpRnA4c3JVdU5LU2lYQk14RUdSSVBzTjJpQ24yTnRUb2oiLCJtYWMiOiJhMTJhMWRhZjY4MjI4NjJkMTVjY2E0NGM1MDIxZjQyNjYxODBlM2U4OTIyM2MzZDI1NTYwODJkMGZlZjExMzEwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:26:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhPRXpTVU5mRXlNVUl1U2twWmx3enc9PSIsInZhbHVlIjoidmRjem5qWEF2STR5MENmbmlodU5pUlcwSXFhbTRXM0JGTmxXVlorWkducGpDbE5QZmhkUzdjVDFpZUVGc2JDc0daOWV5VndjY1doeGQ3QkVTRTNrSjZNRGM0VTVtazZGM0ZDc3MwRVpIUHpKNGhiOWF4dnJwNUJpMnd3Qm5wNnZOTXlxTUtkZE9SSWFLZE9YbUQ5YVVqekhUVjZUYlRsbmErM0RvYk5JSy9RVjVHdGhVNThNTjA2d3RhWml1azk5VlBtM3VTWUlGNHB3c1BvalU3OFdsdXQ5YTdjcHdqbVowZ0k3ZktEbFA0RzV4VVVMd094d3c3L2JjMk5WYVlsRHNvWjE1eHBBb0hOVXp5TTRFMDhHbm9aOWVoLytybTI4T0VzU05ET0J6bjdzRjZycGVUOHFuNkQ1RnpySDE4UnY4a09lRnYzOGhYLzI2NmxSTzd2SEJwdm1qajFPSkxrdUYybDd2THA1dUg3T1FUbXlFSFZKK0pCeHNxWDd4WnBxekFuZDJ3UGVYcjc0cTh3RGJidHZ5ZE9WNVRheXdlN0p0d3NSVnpjdUdLUXhhMTBEUlJLU2NoYmlUNDZSbWN1eFA3YXA5M0RMZDh2a05OR3QxZ3JBc3gvMXY5NGFqWVdpalJyQ09iMG0zVnhPRENENjdpQjZqSk9CWU95SkJlNDUiLCJtYWMiOiI2NmYwOWFiMDc0ZWUzYWQ5MzIyNTM2NTBhODlmNzU4MmM1YmI2ZmIyZjUwMjRmY2M0MWNiNTJjZDQ4ZGExZmViIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:26:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQzZWIzeFV1ZC9KaXIyS1ROMUozT0E9PSIsInZhbHVlIjoiVndmeHVQQ1FscjdkN0FMTHVGcXhPUVdIWE9aaUowVHFsV3M4bWttTjJjMTFid0ZGeHg2WU5FbW1ZZVhVZUVvNEk3Z2ZsNi9BbVlxZ09Ga2ZSemtEK3ZVRGdRYUlsZnI3M0c0dHpMWkljSWNrUTQrVC80OGVmc3hHZnRLd3AzZkkzMkpKN1NUYWY2QldBOGpRU0J0UTM3Zzl3ZVEzMmRVUmRxMmxubGVyMGp2RGJjbWRZRGFKRHBhNXRBMCtvZlNrUDBRQTY1MkFOc3hXQ0FRb25wK2RDaHlRWWdzUzh0T3FhdkwyMlg5TjRvZW1FTWlhd1dHdFVuY1Z5N2lrQXV5VnY3V0lwNmxIUjMvdzBqczUzZVUxV3NxTmU1UnhWQ2EwOUcxc3FlV3VUMGZiSjAvZENCSVN0NVJ2TFJIRm95NjQ5cTduNkoxbU96YXNCZlE5RUdIQ2hJUXZaV2RjcWJlUFBFK3VJYjkybnJiNFJ3blpET0N1dUdGdnM0emZ2Qk9pblEzOEZRbmFnWldFbjdSK05EdTlTZG9HYitnYlRMeHNPdmE1Z1pGdFBYc1NpSXVjSVZXMEJiaXBLK0l3WkRaSGhDQ0FJdUhnd0hnbzd4eUpSL1R3L1c2YTBkYnN2VUhpRnA4c3JVdU5LU2lYQk14RUdSSVBzTjJpQ24yTnRUb2oiLCJtYWMiOiJhMTJhMWRhZjY4MjI4NjJkMTVjY2E0NGM1MDIxZjQyNjYxODBlM2U4OTIyM2MzZDI1NTYwODJkMGZlZjExMzEwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:26:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhPRXpTVU5mRXlNVUl1U2twWmx3enc9PSIsInZhbHVlIjoidmRjem5qWEF2STR5MENmbmlodU5pUlcwSXFhbTRXM0JGTmxXVlorWkducGpDbE5QZmhkUzdjVDFpZUVGc2JDc0daOWV5VndjY1doeGQ3QkVTRTNrSjZNRGM0VTVtazZGM0ZDc3MwRVpIUHpKNGhiOWF4dnJwNUJpMnd3Qm5wNnZOTXlxTUtkZE9SSWFLZE9YbUQ5YVVqekhUVjZUYlRsbmErM0RvYk5JSy9RVjVHdGhVNThNTjA2d3RhWml1azk5VlBtM3VTWUlGNHB3c1BvalU3OFdsdXQ5YTdjcHdqbVowZ0k3ZktEbFA0RzV4VVVMd094d3c3L2JjMk5WYVlsRHNvWjE1eHBBb0hOVXp5TTRFMDhHbm9aOWVoLytybTI4T0VzU05ET0J6bjdzRjZycGVUOHFuNkQ1RnpySDE4UnY4a09lRnYzOGhYLzI2NmxSTzd2SEJwdm1qajFPSkxrdUYybDd2THA1dUg3T1FUbXlFSFZKK0pCeHNxWDd4WnBxekFuZDJ3UGVYcjc0cTh3RGJidHZ5ZE9WNVRheXdlN0p0d3NSVnpjdUdLUXhhMTBEUlJLU2NoYmlUNDZSbWN1eFA3YXA5M0RMZDh2a05OR3QxZ3JBc3gvMXY5NGFqWVdpalJyQ09iMG0zVnhPRENENjdpQjZqSk9CWU95SkJlNDUiLCJtYWMiOiI2NmYwOWFiMDc0ZWUzYWQ5MzIyNTM2NTBhODlmNzU4MmM1YmI2ZmIyZjUwMjRmY2M0MWNiNTJjZDQ4ZGExZmViIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:26:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259134721\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2137688653 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137688653\", {\"maxDepth\":0})</script>\n"}}