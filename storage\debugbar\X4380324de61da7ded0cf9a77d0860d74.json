{"__meta": {"id": "X4380324de61da7ded0cf9a77d0860d74", "datetime": "2025-07-30 08:01:40", "utime": **********.197165, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:01:40] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.19042, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753862498.563489, "end": **********.197205, "duration": 1.633716106414795, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1753862498.563489, "relative_start": 0, "end": 1753862499.939539, "relative_end": 1753862499.939539, "duration": 1.3760499954223633, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862499.939562, "relative_start": 1.3760731220245361, "end": **********.197208, "relative_end": 2.86102294921875e-06, "duration": 0.257645845413208, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48359520, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.029829999999999995, "accumulated_duration_str": "29.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.014406, "duration": 0.01769, "duration_str": "17.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 59.303}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.053345, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 59.303, "width_percent": 3.486}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.061252, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 62.789, "width_percent": 2.749}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0678542, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 65.538, "width_percent": 2.682}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.090724, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 68.22, "width_percent": 3.755}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1055412, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 71.975, "width_percent": 3.252}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.114116, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 75.226, "width_percent": 4.66}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.122871, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.886, "width_percent": 20.114}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-104958042 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-104958042\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-117437369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-117437369\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-45004356 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45004356\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-740958767 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkhuTVMwWjM0Wi9yRzBhdVpNc2VKS2c9PSIsInZhbHVlIjoiMGEybGVRaFBnUkhHR2pNbHp3K3BZQW1lOUozRDFWaUh5UjJPaDVKQUxmZE1UT0FTM3JFWlYvaFYwR3VMb21rTUZOektnMG5iM1U2QUd3ZXJZSVNIbXBYemVZYU8wejBBZVdSSjVRZ3VZKzZQcmRqV0NuZElZclA3dzRkL3lWQkcrbURXeFhFTys3WVV5YitBS2JPa2hJVG9FcXJNcGYzVytGenlHLytsWUxySGlZS3lYVWIvMW1WVGZaVno2YXI5dGlBQTRlZzY2UThDeGhGWCtSY2FEc1ZRNEtCek44WFMxdHBJWjlFVXlDUUl5OXhRN2ZBMnlBajV2U0ZtOXlhZTI2bVZ3S1hnM08yUVJLeXVzOHh4ZFdyWDIwbXJQS0kreDlwMStiSkxVVDdwQW9CMTJXWXMxWTBnZ3NsdEpRbUJHYW9GdndiRFZoYi84YzBhVGdUV2VXQ0h0cHUxVHFnS1V6QlM5VFAxdGl4WlAyNE1mK2NNT1NKQVZ5MGxDc0Z0L0hxVTVuZG04aXNzMm9uWS9xekN5OUFFc1IxbU5pR2Npb0M0YnJYODJ1Vy9NTlJ5bXkyNElBbXUxWWd3RWc3MjRqZWdnS0hzaFU5a0hFRFdKY05xR0luQUhVcVB1cytBbzFiRkl0ZE9lU216VDlKUE5Md0s2M2FCSVNkcnpPeWkiLCJtYWMiOiJlODZjOTdlNmJhMzAzZjhiMTUwYzVmNGY1ZDg3MDdkYzFkYjZjNjRkZmM3ZDU3MmZhYzY5MzJjMTZkYjA5MjhiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IksyeDlTNUo2WGMvTjJWNW94KzA2N3c9PSIsInZhbHVlIjoiNkxnWFltK1JlRWx5ZUhFazgyQWprVVBEYWkxVDZlOU1nb0NwZDU3Ni9XZHQyOEVMRDFnSlBoUzZQa1lZSWlJZk5ISGFhRWJCVWRjbVNsZHMxenRtaEJMNXFnNXhPSXFCT2ZEVFpmR1pBNTRtV0hRWWVtdm93aWczTzRGQnp0Y1BEeWJrTTdkM2FmS3UrMmE5UFhnODdRdjZVMk0vSFZWUXZMVXpZdUFOZm5MTzVEU3p3NU82ZkZlV3ZXUkJ0cEQwL2k5K3VLZll6U2tLM0VjM3JNcWdGNzdIVDh1R1Y1bVFPMk5HTUd5am5zNm5LYjJmMmNuK252eGhxaXRpVERjYnZoOHNZdGxQTjFOcFlSbklJK0tLVkxRUEZ1NkpReTQzeElRYktBMitsamYvUk5UUjNtb24zRkYxWFp3R1lWdnBQdkpHek41V09kNUFSa0U5TjNzU0c0THNtL1o2NXFhSS9FOEpzaXpJdGZrYjRjR2s0QVdtSXRPMU42OWV1YXBDT3pzWmxwMDA1K1ZtZ3NsaytqYzRsMVpBMzBVMDFERTVHWDBzWXJ1OVBCZXI2OFpNdExkRDU4cFRqSHlaNnl4L3lQMHRJeEtzaWJ6NHcySFJ1WUloM1JYb2hFQVd4amdHcnBwcjhQbVdWSEtBeGorejRaSWlPWTc4ajdaL3NRcEEiLCJtYWMiOiIzOTMwYmNjMTI1YmEzNDg5YzNkZjA3NzM4MjZlZDAwYzY3MWI0Nzk0YTYwZWU2NmE3Njg5NjViZWVlNzc4OWE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740958767\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1338447826 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338447826\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1408459653 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:01:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtKSTF6ZmIwb2dOQndqTnU5a1FrTkE9PSIsInZhbHVlIjoiY1kwU1hiUGNjczhTRHZlamF0VVpkejViTzRQVEE3UTFQWnA1bG5nTDJyN1Q0YXU4a2VjcnlMWWFYbk9YMFgxbURwSjV6S0t2Q1c3dVBYSGFrRFQ5Wll6SUF3MkN4UVlCNDNOMXVNNzU4RlBFSDZqMU5TWDBDSWhuUlNVczJBM3BFNytuQ0hXYU5hRE9CbFdPcHgrSVhYRGh5WFZOb2lYcFZwcmJkWEJ2bDN2azR1em9SczEzZkVycXBRNjRFZFRXZm9QNWE2eVJ1ZVlRcWtpeHozYWVqeXhjMTd3WjlnMHdxVzZxa1lNU3Byd3o2TUoyZ2NKVGZTQWg0SnNEN3Y2NVg0dlNGb0h2S0VKS2ZwWnpIeHIxVkMzT2JkVkpiUDc5ZHMybmVMd1lLWC83NlprSm5HNDlyVXhGK2FDMlQzdlMvVGtTallmMWpmREsrU2x3Wjd3aXZvT2xYVXpra0JxS1Eyc29Kd0NsYmd2RXRPYXViUVJzOUtTQVI1NWtPMTNQanlOczVQMHVKVU5OUkdVa0x1dHMzcjlVNmxEL1BXemhEYnZWRGZaNSttc1NBclJiZng3MkxOeCtaM2w0VVdOcGtjWkpJTndjZWxQdzVNRnFaeEoyWHdkdkhGVUJkN0sxcUEwS3Z6NjhlQ0tuUnV4dVd4UGJIZFkxRWplVnIrTFgiLCJtYWMiOiI0MTRjZTcxZWI1ZGVhN2U5YWMwZTc2NGQ0ODM3NmUzMjhkMTZlYzQ1MzY2NDFlNTM4YzcwYTNkMWIwYjcxNmQ5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpEMkxBU2RqTnozVTBVakhiL1BaZkE9PSIsInZhbHVlIjoiL3M1NTJrYzNkZllSbmNPN2VLWDk2RVBaY0RTUXhYR000UFpwdkhYR1p1bDlsblpxdFJRajNhNmZ4YXVlM1pCbnR2dmM5b3F5ajlZU3FxWTdsTGwzeHVsRlR4ZkZ5TWp4R3dxNmc5VDgxN3JTbEtyRk9Fd1ZQZzR6R1NYR3NpYW82YXp1OE5UTHpMMVpZb2xHbEROTHRTaDc5Z3ZVTlVqL1MvMithQWZFM2RVUlJrVHBZK0tUMSt5Y21BZk0zL29saEJpWFdRaDNRZ2dPVm00QkM5WkdYZzBEclYxYldDc3ZoZ1VhTzJKQmZBZmpIOG9vT2NGNTdqd0NJNFRsSmwyTE9LZ05ncm5vQW1QSWludy9FYW9VeWJld1Y1eTFNb1hVSUxsM1BQTjlSbjd3VGlublVSWDVVNzQ2K3RMRHh6aTlxcUY2eXZ3cjVvdVlUQjEwd0h6dWFUYUtaYitCYXdTWlNGSVJEVVZ4cjgyTXlqdytHUis0YTlsUVlpM3RvM3pESGlzeHBGdHJQeGkrV3NmRzNiNUFNR29tRjFaVFQ5MWhvdUtaNmJVWUxTMHBsK1g2RGMzQnNQTmR0WWRRNEtSRnhqcDNTSWc3NzJZc0YwOFRhODRMNWhMdG9veG9Ha2NGQTVhZVg5Vlc4ejJvQVREd25aZk41bG1tUlcvdGlTVGwiLCJtYWMiOiIxZmU3ZmFjN2Q5OGVmMjE1OTc2YWEwMWUxNWUwMzcyM2NhYWU2ZDA2NTJjNmMwYjM0MmM4OTkwMWI1ZDIzNTBkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtKSTF6ZmIwb2dOQndqTnU5a1FrTkE9PSIsInZhbHVlIjoiY1kwU1hiUGNjczhTRHZlamF0VVpkejViTzRQVEE3UTFQWnA1bG5nTDJyN1Q0YXU4a2VjcnlMWWFYbk9YMFgxbURwSjV6S0t2Q1c3dVBYSGFrRFQ5Wll6SUF3MkN4UVlCNDNOMXVNNzU4RlBFSDZqMU5TWDBDSWhuUlNVczJBM3BFNytuQ0hXYU5hRE9CbFdPcHgrSVhYRGh5WFZOb2lYcFZwcmJkWEJ2bDN2azR1em9SczEzZkVycXBRNjRFZFRXZm9QNWE2eVJ1ZVlRcWtpeHozYWVqeXhjMTd3WjlnMHdxVzZxa1lNU3Byd3o2TUoyZ2NKVGZTQWg0SnNEN3Y2NVg0dlNGb0h2S0VKS2ZwWnpIeHIxVkMzT2JkVkpiUDc5ZHMybmVMd1lLWC83NlprSm5HNDlyVXhGK2FDMlQzdlMvVGtTallmMWpmREsrU2x3Wjd3aXZvT2xYVXpra0JxS1Eyc29Kd0NsYmd2RXRPYXViUVJzOUtTQVI1NWtPMTNQanlOczVQMHVKVU5OUkdVa0x1dHMzcjlVNmxEL1BXemhEYnZWRGZaNSttc1NBclJiZng3MkxOeCtaM2w0VVdOcGtjWkpJTndjZWxQdzVNRnFaeEoyWHdkdkhGVUJkN0sxcUEwS3Z6NjhlQ0tuUnV4dVd4UGJIZFkxRWplVnIrTFgiLCJtYWMiOiI0MTRjZTcxZWI1ZGVhN2U5YWMwZTc2NGQ0ODM3NmUzMjhkMTZlYzQ1MzY2NDFlNTM4YzcwYTNkMWIwYjcxNmQ5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpEMkxBU2RqTnozVTBVakhiL1BaZkE9PSIsInZhbHVlIjoiL3M1NTJrYzNkZllSbmNPN2VLWDk2RVBaY0RTUXhYR000UFpwdkhYR1p1bDlsblpxdFJRajNhNmZ4YXVlM1pCbnR2dmM5b3F5ajlZU3FxWTdsTGwzeHVsRlR4ZkZ5TWp4R3dxNmc5VDgxN3JTbEtyRk9Fd1ZQZzR6R1NYR3NpYW82YXp1OE5UTHpMMVpZb2xHbEROTHRTaDc5Z3ZVTlVqL1MvMithQWZFM2RVUlJrVHBZK0tUMSt5Y21BZk0zL29saEJpWFdRaDNRZ2dPVm00QkM5WkdYZzBEclYxYldDc3ZoZ1VhTzJKQmZBZmpIOG9vT2NGNTdqd0NJNFRsSmwyTE9LZ05ncm5vQW1QSWludy9FYW9VeWJld1Y1eTFNb1hVSUxsM1BQTjlSbjd3VGlublVSWDVVNzQ2K3RMRHh6aTlxcUY2eXZ3cjVvdVlUQjEwd0h6dWFUYUtaYitCYXdTWlNGSVJEVVZ4cjgyTXlqdytHUis0YTlsUVlpM3RvM3pESGlzeHBGdHJQeGkrV3NmRzNiNUFNR29tRjFaVFQ5MWhvdUtaNmJVWUxTMHBsK1g2RGMzQnNQTmR0WWRRNEtSRnhqcDNTSWc3NzJZc0YwOFRhODRMNWhMdG9veG9Ha2NGQTVhZVg5Vlc4ejJvQVREd25aZk41bG1tUlcvdGlTVGwiLCJtYWMiOiIxZmU3ZmFjN2Q5OGVmMjE1OTc2YWEwMWUxNWUwMzcyM2NhYWU2ZDA2NTJjNmMwYjM0MmM4OTkwMWI1ZDIzNTBkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408459653\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1532608439 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532608439\", {\"maxDepth\":0})</script>\n"}}