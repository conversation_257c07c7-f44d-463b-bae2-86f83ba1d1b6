{"__meta": {"id": "X019837e2288897c53b3abba6556283be", "datetime": "2025-07-30 04:37:00", "utime": **********.154506, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753850219.262515, "end": **********.154532, "duration": 0.8920168876647949, "duration_str": "892ms", "measures": [{"label": "Booting", "start": 1753850219.262515, "relative_start": 0, "end": **********.076089, "relative_end": **********.076089, "duration": 0.8135738372802734, "duration_str": "814ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.07611, "relative_start": 0.****************, "end": **********.154535, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "78.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UjxlODrKbyDL3P3fmDh0H7z3CYQhiCfvxwry8ph3", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-191800948 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-191800948\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-687196479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-687196479\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-954795422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954795422\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1640089394 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640089394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-842431572 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842431572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 04:37:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVvc25mT0o2clh1Yk1mbVB4bmxQU1E9PSIsInZhbHVlIjoiWDUzL3ZvVkVOcTY3MlR6cHk3bko3TE9lSmhSTlVWN09UOFkrQWgyZW9xN0dJWjNib3hGaTRPNEQ3UmZ0VVNMeDdWSTA1ZnRkVkxaRXhhUHBsS2QzbmNuU1RXTXhweDI4N3dpNEs0RytJM2Y4ZnVUbUN2UmZkRnNlMzZ1aForR3RiT0d5N0RwanZCNm9UV2taVFFPdmtOZ3NHazBobXU1TlpCTWpGYVFCM3lRSWMraEp4Z0pzQVIwL0Z0bWZ4U2Nsb2Y2Uk9jYS8rdDRGM0k3Sm5meTlZL29QTHVsblcycEdvRk11UlgyUU1CQ2x3ZmVHU3k4NUNOL21lcVlYRERVZERPOTA4blU1QW1oMWpLRUZzUmJkUFcydytsdXRhVnZqSlBYbVdBZEw1Wm5jYUQ4anl3R0dibGtWeUhvbk9nNUVUaThiRUN4QnZ3WUY1d3o5T0VwOHBBOFZxYlhuZjlyNk1HZythYVFzbkFLelFUQkkrWXlsdzU3SmRYdHAyZ2pxaE5pUGk3d1ZDSjZJam5BR2Uvb29Wc2MzblAreklTMUx5L3E5UUk3QS95cFNUYXAyb3NCZXFOMjNrM1ZoaG50Nkwyd3hSVlUzQVNqemRROEV3REd4TWtoM1JTbnpUWjd2S1RnOXlSSnp1eEVEbGVFZXI3T01CZzI2QU00WEFkR2siLCJtYWMiOiI3MmRiNzE3NmQzNmRkMWRkZGYzYzhmMTJiMDJkMTRlMzgzYmZjZDI5ZTczYWQzMDgyY2I0NjUyMzdiMjc0NjAxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 06:37:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlrUFpEN3FicU5YYitYRER0SHBYSVE9PSIsInZhbHVlIjoiaUN0NWxYTXpsUXdkdGswVkZNTW5FS0VVZVRCRVhaRnJRT1U5S2p1UTM4bzhxTDQvckZmdEpjNEdsSDh1S3JETUo5TWVORCs0SWtOK1JweGIzaEhGVEVqbHVOZ1hFd3JLWFA2T3N0eGZNQjlCbDYvT054NnRVaDBTK253MzZuNEl2U2tQWTUxL1lCa3N1bnBiN3dsR1lEOXlnRVpKMFZzRGFWa1YyandXeVJEeFd5WXFSQS9KZ2lxODRLOEVPMzVHbDJWeG92UWZOSmRZV3ZSVDl2Vnl0ZGkydnlhd1Q5QVVaSnI0ZlJoWHB1a0tsVkRSQU56VlZ0QzdmVFZUK3ZxYWQrb1VMM2hyVjdGaFViSXFPemlHNVROVEZ4RzNhN3ByM2JMYkwxUkVYMmFEUUF0TVl1cGFJbkY1cWNaVVplWk9uUEYrdjF6YnZOUXZDVS9VeHNJYldoUUVhdEllNlBhQ2hOOUxQNE0vNGlhT1NiU25QOFRKNkVUWEFjOU5GWDBHWVVoMFN3MlhwZ3RLNCtYamFIblpZTGx6b1E2UUgrZTJOZnJoVUtsVGFqZDFnMS9DY25iVTg3SXhnMGJLeWhBWW90aW9GZWF0a2k2Qi9HZnJRMUEweVFVemxacnNpZlhBV1dEc0x1VVV6NUNWS0EyUjhoQWcyRU5PWHpNQURwYVAiLCJtYWMiOiJlMDg4NzA3M2Y2YjNhOTk3MjFkZmU1Y2E1NTNiYWViZTI2Nzg4YWQyMDVhYzU5YTlmNzQ5MGFhNzM4Zjc0ODVmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 06:37:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVvc25mT0o2clh1Yk1mbVB4bmxQU1E9PSIsInZhbHVlIjoiWDUzL3ZvVkVOcTY3MlR6cHk3bko3TE9lSmhSTlVWN09UOFkrQWgyZW9xN0dJWjNib3hGaTRPNEQ3UmZ0VVNMeDdWSTA1ZnRkVkxaRXhhUHBsS2QzbmNuU1RXTXhweDI4N3dpNEs0RytJM2Y4ZnVUbUN2UmZkRnNlMzZ1aForR3RiT0d5N0RwanZCNm9UV2taVFFPdmtOZ3NHazBobXU1TlpCTWpGYVFCM3lRSWMraEp4Z0pzQVIwL0Z0bWZ4U2Nsb2Y2Uk9jYS8rdDRGM0k3Sm5meTlZL29QTHVsblcycEdvRk11UlgyUU1CQ2x3ZmVHU3k4NUNOL21lcVlYRERVZERPOTA4blU1QW1oMWpLRUZzUmJkUFcydytsdXRhVnZqSlBYbVdBZEw1Wm5jYUQ4anl3R0dibGtWeUhvbk9nNUVUaThiRUN4QnZ3WUY1d3o5T0VwOHBBOFZxYlhuZjlyNk1HZythYVFzbkFLelFUQkkrWXlsdzU3SmRYdHAyZ2pxaE5pUGk3d1ZDSjZJam5BR2Uvb29Wc2MzblAreklTMUx5L3E5UUk3QS95cFNUYXAyb3NCZXFOMjNrM1ZoaG50Nkwyd3hSVlUzQVNqemRROEV3REd4TWtoM1JTbnpUWjd2S1RnOXlSSnp1eEVEbGVFZXI3T01CZzI2QU00WEFkR2siLCJtYWMiOiI3MmRiNzE3NmQzNmRkMWRkZGYzYzhmMTJiMDJkMTRlMzgzYmZjZDI5ZTczYWQzMDgyY2I0NjUyMzdiMjc0NjAxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 06:37:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlrUFpEN3FicU5YYitYRER0SHBYSVE9PSIsInZhbHVlIjoiaUN0NWxYTXpsUXdkdGswVkZNTW5FS0VVZVRCRVhaRnJRT1U5S2p1UTM4bzhxTDQvckZmdEpjNEdsSDh1S3JETUo5TWVORCs0SWtOK1JweGIzaEhGVEVqbHVOZ1hFd3JLWFA2T3N0eGZNQjlCbDYvT054NnRVaDBTK253MzZuNEl2U2tQWTUxL1lCa3N1bnBiN3dsR1lEOXlnRVpKMFZzRGFWa1YyandXeVJEeFd5WXFSQS9KZ2lxODRLOEVPMzVHbDJWeG92UWZOSmRZV3ZSVDl2Vnl0ZGkydnlhd1Q5QVVaSnI0ZlJoWHB1a0tsVkRSQU56VlZ0QzdmVFZUK3ZxYWQrb1VMM2hyVjdGaFViSXFPemlHNVROVEZ4RzNhN3ByM2JMYkwxUkVYMmFEUUF0TVl1cGFJbkY1cWNaVVplWk9uUEYrdjF6YnZOUXZDVS9VeHNJYldoUUVhdEllNlBhQ2hOOUxQNE0vNGlhT1NiU25QOFRKNkVUWEFjOU5GWDBHWVVoMFN3MlhwZ3RLNCtYamFIblpZTGx6b1E2UUgrZTJOZnJoVUtsVGFqZDFnMS9DY25iVTg3SXhnMGJLeWhBWW90aW9GZWF0a2k2Qi9HZnJRMUEweVFVemxacnNpZlhBV1dEc0x1VVV6NUNWS0EyUjhoQWcyRU5PWHpNQURwYVAiLCJtYWMiOiJlMDg4NzA3M2Y2YjNhOTk3MjFkZmU1Y2E1NTNiYWViZTI2Nzg4YWQyMDVhYzU5YTlmNzQ5MGFhNzM4Zjc0ODVmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 06:37:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UjxlODrKbyDL3P3fmDh0H7z3CYQhiCfvxwry8ph3</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}