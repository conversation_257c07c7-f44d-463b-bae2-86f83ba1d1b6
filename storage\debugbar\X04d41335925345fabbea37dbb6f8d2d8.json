{"__meta": {"id": "X04d41335925345fabbea37dbb6f8d2d8", "datetime": "2025-07-30 08:09:52", "utime": **********.788058, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862990.714319, "end": **********.788129, "duration": 2.07381010055542, "duration_str": "2.07s", "measures": [{"label": "Booting", "start": 1753862990.714319, "relative_start": 0, "end": **********.620344, "relative_end": **********.620344, "duration": 1.9060249328613281, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.620384, "relative_start": 1.****************, "end": **********.788135, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "109rFt0oK4eq5eZU1FuraV9kRtALcT3dq7Sz2gsR", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-242649959 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-242649959\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1902406655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902406655\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1144646792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1144646792\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-368938522 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368938522\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-642759651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-642759651\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1947022708 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:09:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktVRUdrREQ5MXFENEl2alhtS1piR3c9PSIsInZhbHVlIjoiNUc4UmRhTnNPMk5sM1lib0JLZVMxbWJ0YXBSV0JBZC9IWUQ3d3FLSTdiRGNQd1JqdkY0bjZaREc2TDRlTG91Z0VmUnlyamR1KzhOTFZPVGVSUnlRSjVCcUk0RFY5WnlpaWtFY2F2SU10c2VPWCswdU9CdlpGUmxwRkczNmNqMUd1N1I1RmJqdWJ0MkNmNGxMK0dEdnpSbHh3c2F3TE1pVklNSzBTbElRQ3FuREJWekhJOEF0WnhINFpERWU4SUxiMTREaTlRMlBqZXUvL1I2Z21ZNWF5eTFOV25hdzlRV2dCR1I1RnpHZEo5enF5bTgveS84cWg0WDFIZXlHQll4alVpZnZGL0x4cncrV1djVTdCTU16QzlYbGR3MVJGKy82OHB3WlkyNTBQcGYwNC9RcytqQzlFODdLNk9xQ0IwNVVsUFhzak9zcGtSbEhpdjlIS3FQZmlaY1pRcmFRZmc5MjZCNEtrVkRZM1RjdmJhWUE2Ynp1M2VyYlpOQkViZmpGVW1tc0hBUm9yVC9OYTY5UmE4WXdKRW5TZCt6SG9LeUtEYTdGUVlabmxjRlRaaDRpbFF3SURwUzBJVGRkMVViUXU2elQ4K3Q2QWowNUVVak1OeWtleUN0dFAyckZ3NXRoK1p5c3lsQjdIWWZ5a3NVOEttdllKaGdYS0FTbHVia2siLCJtYWMiOiI1YjE1MjIzNzM0MTU5OGI0MmRjMmE0MzUzMDE0NmE3Y2Q2ZjEzN2M0MTNkZTZmYTRjMTA5ZjI1Njg4NjBjMjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitxM09TWTJyOHRIeVFVV3FtSmQ0T2c9PSIsInZhbHVlIjoib3RMQXBRQ0oxOUgyb1NUSDVZQks0L0U4UnJhczdnT0VkNnJHdFFRd2wwb0Q0Y3ZlR1prczY1NVJkTEI3WVl3bURjWnU1ZFk2c2EyWXhUMHBjUGNjTXZjT1hITlNPaEFWSmNqWitERGFLZlc3cVFlZUZHS1U5SFRjaGhmQXJjVGpMNVJpNHdQM254dUdWMllISGRMeXZUeHRjNnljTHJNWE8wemkyUmw1bHh3S2RZYjJPZ0dFQS9keWZubm1GcGRNQ3dLN09MUGNZTTlIczVOY1l2T3g1Z2Q3bm93SzVYdGxXYmV0Zk5xREl6MENSNXhvQU9HQSs0Ly9DY1RvQkk4a1dHL0d5VG1qZlk3ZFk0eTdmNjBiL0hDc1VpZ1lsVElUS04xaEJmc1U2Qk1YNVU1VW43ckxUVEpLbFpxTVhkczd6amNwZWJzREpweTJBWlFyeGNlR0FuOHFYaXAzaVhNOEdGbnlOdmMvYmdwRHVIMEMzNk41aEpPRGhERnkrVm40R1BORkpJRjFuMktJVlR0amxtYXJhNjROUitQNm1QaUI2SG9yNG9EZjdSTjZjOVdrV2hNTUZ0RGxCdUV6VjYzSkpEaDVBM21rSWZmdGZFNDhmZ0cyeWpNTzVubzhPZVR0OGdwVDFlNTVTT2dsaUVzcWpYYUNlUXU4Szk2TnhJTysiLCJtYWMiOiIxNjZkNDVkMjkxMGY0MjI0NDM0YzZmZjgzMTBlNGJjZWM0NmUzODk5MzJiYTE1YjQzOWMzNzEyODRmNGEyMTQzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktVRUdrREQ5MXFENEl2alhtS1piR3c9PSIsInZhbHVlIjoiNUc4UmRhTnNPMk5sM1lib0JLZVMxbWJ0YXBSV0JBZC9IWUQ3d3FLSTdiRGNQd1JqdkY0bjZaREc2TDRlTG91Z0VmUnlyamR1KzhOTFZPVGVSUnlRSjVCcUk0RFY5WnlpaWtFY2F2SU10c2VPWCswdU9CdlpGUmxwRkczNmNqMUd1N1I1RmJqdWJ0MkNmNGxMK0dEdnpSbHh3c2F3TE1pVklNSzBTbElRQ3FuREJWekhJOEF0WnhINFpERWU4SUxiMTREaTlRMlBqZXUvL1I2Z21ZNWF5eTFOV25hdzlRV2dCR1I1RnpHZEo5enF5bTgveS84cWg0WDFIZXlHQll4alVpZnZGL0x4cncrV1djVTdCTU16QzlYbGR3MVJGKy82OHB3WlkyNTBQcGYwNC9RcytqQzlFODdLNk9xQ0IwNVVsUFhzak9zcGtSbEhpdjlIS3FQZmlaY1pRcmFRZmc5MjZCNEtrVkRZM1RjdmJhWUE2Ynp1M2VyYlpOQkViZmpGVW1tc0hBUm9yVC9OYTY5UmE4WXdKRW5TZCt6SG9LeUtEYTdGUVlabmxjRlRaaDRpbFF3SURwUzBJVGRkMVViUXU2elQ4K3Q2QWowNUVVak1OeWtleUN0dFAyckZ3NXRoK1p5c3lsQjdIWWZ5a3NVOEttdllKaGdYS0FTbHVia2siLCJtYWMiOiI1YjE1MjIzNzM0MTU5OGI0MmRjMmE0MzUzMDE0NmE3Y2Q2ZjEzN2M0MTNkZTZmYTRjMTA5ZjI1Njg4NjBjMjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitxM09TWTJyOHRIeVFVV3FtSmQ0T2c9PSIsInZhbHVlIjoib3RMQXBRQ0oxOUgyb1NUSDVZQks0L0U4UnJhczdnT0VkNnJHdFFRd2wwb0Q0Y3ZlR1prczY1NVJkTEI3WVl3bURjWnU1ZFk2c2EyWXhUMHBjUGNjTXZjT1hITlNPaEFWSmNqWitERGFLZlc3cVFlZUZHS1U5SFRjaGhmQXJjVGpMNVJpNHdQM254dUdWMllISGRMeXZUeHRjNnljTHJNWE8wemkyUmw1bHh3S2RZYjJPZ0dFQS9keWZubm1GcGRNQ3dLN09MUGNZTTlIczVOY1l2T3g1Z2Q3bm93SzVYdGxXYmV0Zk5xREl6MENSNXhvQU9HQSs0Ly9DY1RvQkk4a1dHL0d5VG1qZlk3ZFk0eTdmNjBiL0hDc1VpZ1lsVElUS04xaEJmc1U2Qk1YNVU1VW43ckxUVEpLbFpxTVhkczd6amNwZWJzREpweTJBWlFyeGNlR0FuOHFYaXAzaVhNOEdGbnlOdmMvYmdwRHVIMEMzNk41aEpPRGhERnkrVm40R1BORkpJRjFuMktJVlR0amxtYXJhNjROUitQNm1QaUI2SG9yNG9EZjdSTjZjOVdrV2hNTUZ0RGxCdUV6VjYzSkpEaDVBM21rSWZmdGZFNDhmZ0cyeWpNTzVubzhPZVR0OGdwVDFlNTVTT2dsaUVzcWpYYUNlUXU4Szk2TnhJTysiLCJtYWMiOiIxNjZkNDVkMjkxMGY0MjI0NDM0YzZmZjgzMTBlNGJjZWM0NmUzODk5MzJiYTE1YjQzOWMzNzEyODRmNGEyMTQzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947022708\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1959019192 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">109rFt0oK4eq5eZU1FuraV9kRtALcT3dq7Sz2gsR</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959019192\", {\"maxDepth\":0})</script>\n"}}