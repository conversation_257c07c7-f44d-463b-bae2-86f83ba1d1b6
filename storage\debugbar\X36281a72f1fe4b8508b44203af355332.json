{"__meta": {"id": "X36281a72f1fe4b8508b44203af355332", "datetime": "2025-07-30 08:07:18", "utime": **********.603821, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862836.336455, "end": **********.603876, "duration": 2.267421007156372, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": 1753862836.336455, "relative_start": 0, "end": **********.416792, "relative_end": **********.416792, "duration": 2.080336809158325, "duration_str": "2.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.416818, "relative_start": 2.****************, "end": **********.603879, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9sXI8W6NMdM2A10W2i2dj5lQcdKHjHHS0J8lASar", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-350908536 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-350908536\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-11787937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-11787937\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-327692665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-327692665\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-284124031 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284124031\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-716976860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-716976860\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1138112208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:07:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IksxQ2R2MWl5Z0pHQk1jc3lTbHg2UUE9PSIsInZhbHVlIjoiSUdQK0Z3MmtWOXM5Ym9EejJDTUFLdWVOUUYvOVY3OXFJcGQxSnZPSmlDZ0ppVCtCT1V3K0Z2R2t2MmpZdHZNMTZjS0lxam5rT0pWV1V0Q21Wd3pDRFdpWnRqUlErMFpaT1FaM2NkeWFEaXhFbDdPTUtqZDIyVy9PcG80WlRaOFFsMlNkK3NiQ2hXaHoveXROZS9rYWEzZjVxcGhpMk1wZ2tHRnE2a3pOak54L3FySG1TSUJwLzhqZStkTHhBYnkxQzdhcWpuMEE2RWpqNjNFZUlGaWMvODI2aUJYL2xVeW9RMkNaZFJlcktiTFhWMDBXdVY2WHh2T05xcmNHUnZTcU9rUHArTWVLRVkxbWRQT0RCeC96SkJiaHRRcHNIc3RPaFpsWE1mdnpwM1lOZkl5UC96cWlLOXFsTUdvc0wvdWRrY2FNRmZYcVBwd2g4eDA5NGM3YXJQejRVeXh0THRreUZERjFkbXJ0VWdvMExKL0Vsc3p1R2pvYVJXQytBZis0clowOW5MNGhiQldGL3NnRjFrSnBjSGdzeEdnZENWTEZsOUlVOVViNDFKL01FVDZEeTJraE1MNVhTVjhoZWY2S0Jma2V0RjdySm5ubDFOOHhjQmZLT0dMRzBKMjJlZHdaUS9jVlh2cllBdzJwTFZXSXlMSE5hSzdVa3lYVXZzaXYiLCJtYWMiOiI4MDkwNGFiODIwNGIwZjBhMmNkY2ZlMTYyYjNlNTVlM2MyNmM4MzBkMzI0NjNmNDMzOWM4NmUwMDFiYTQ5MzVhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii95V0cvcm1NRkNNNjl5aE03NVl6Umc9PSIsInZhbHVlIjoiZnR1dFBCK2Q0K2hTUXM4eC9PeFFKakpJMnplOGp6ZS9TemJ4YjN6NkpGWXJ6eTVKclh4c0tnMFZ3VE9hMi9yWVk1UXFrREI0cmpJQkRDWkxsczNycTRaS3FjcDdxL3FDQmViMW9FMzYvQTRkRjdoTHFrVTJ0WXhqbHZ3TFBvVk5nMllBV3JpSGordkh1ZUhqVzhVM2ZyeGlvcTdIQmdFNGlQQ0Nod1R5bkhUN2xlRmZTbWhoaXVNaHl4a3JZUjVxUWZ0NkswMjRwV1hzdXUyU0NCWURodTYzSEw0MmNST2ZzZWZBSmNtbjJ2ZWZlNG1DcVljVEZTclJJZ3pxSGRyV3JmRjhQUGJjRUViSzNXN2lCQ09SZ3VxTWxYQW5IbUZhU3NmckN4U0s4K01qUTZwWkNhdmNwSDlLQitSeXpBMmsyZkJTSE1DUTdKT29Sb3dJc0V4OTlienRYcEszQ1BtazNZWHBpL0tCd0JGSTFHKzd5TTZiU2NTdXpWMElDYVZoQlg2TGd4cHhaZkpqb1pHZE5oQjdjenJLajFkWTdQck52aERLTEc1ZURwQ2JRUGoxejRvUXEzYXpzZ3c0b0tQWnJoOVMveWJDdlUzOXk2dnJmdzFpSVpZbFBpeUhCMkNrdVNsQ00wNllEamtsc0oyQmtOcFFRdTlvdU9oVldWbUIiLCJtYWMiOiI5Y2YxMjVjODQ5NWQ5M2FjMGEyMDIwNTY1NTZmY2E1MTk2ZGFiZWRiMjAzNDM2ZmNlNjZjYjk0NTMxY2E5ZWQ2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:07:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IksxQ2R2MWl5Z0pHQk1jc3lTbHg2UUE9PSIsInZhbHVlIjoiSUdQK0Z3MmtWOXM5Ym9EejJDTUFLdWVOUUYvOVY3OXFJcGQxSnZPSmlDZ0ppVCtCT1V3K0Z2R2t2MmpZdHZNMTZjS0lxam5rT0pWV1V0Q21Wd3pDRFdpWnRqUlErMFpaT1FaM2NkeWFEaXhFbDdPTUtqZDIyVy9PcG80WlRaOFFsMlNkK3NiQ2hXaHoveXROZS9rYWEzZjVxcGhpMk1wZ2tHRnE2a3pOak54L3FySG1TSUJwLzhqZStkTHhBYnkxQzdhcWpuMEE2RWpqNjNFZUlGaWMvODI2aUJYL2xVeW9RMkNaZFJlcktiTFhWMDBXdVY2WHh2T05xcmNHUnZTcU9rUHArTWVLRVkxbWRQT0RCeC96SkJiaHRRcHNIc3RPaFpsWE1mdnpwM1lOZkl5UC96cWlLOXFsTUdvc0wvdWRrY2FNRmZYcVBwd2g4eDA5NGM3YXJQejRVeXh0THRreUZERjFkbXJ0VWdvMExKL0Vsc3p1R2pvYVJXQytBZis0clowOW5MNGhiQldGL3NnRjFrSnBjSGdzeEdnZENWTEZsOUlVOVViNDFKL01FVDZEeTJraE1MNVhTVjhoZWY2S0Jma2V0RjdySm5ubDFOOHhjQmZLT0dMRzBKMjJlZHdaUS9jVlh2cllBdzJwTFZXSXlMSE5hSzdVa3lYVXZzaXYiLCJtYWMiOiI4MDkwNGFiODIwNGIwZjBhMmNkY2ZlMTYyYjNlNTVlM2MyNmM4MzBkMzI0NjNmNDMzOWM4NmUwMDFiYTQ5MzVhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii95V0cvcm1NRkNNNjl5aE03NVl6Umc9PSIsInZhbHVlIjoiZnR1dFBCK2Q0K2hTUXM4eC9PeFFKakpJMnplOGp6ZS9TemJ4YjN6NkpGWXJ6eTVKclh4c0tnMFZ3VE9hMi9yWVk1UXFrREI0cmpJQkRDWkxsczNycTRaS3FjcDdxL3FDQmViMW9FMzYvQTRkRjdoTHFrVTJ0WXhqbHZ3TFBvVk5nMllBV3JpSGordkh1ZUhqVzhVM2ZyeGlvcTdIQmdFNGlQQ0Nod1R5bkhUN2xlRmZTbWhoaXVNaHl4a3JZUjVxUWZ0NkswMjRwV1hzdXUyU0NCWURodTYzSEw0MmNST2ZzZWZBSmNtbjJ2ZWZlNG1DcVljVEZTclJJZ3pxSGRyV3JmRjhQUGJjRUViSzNXN2lCQ09SZ3VxTWxYQW5IbUZhU3NmckN4U0s4K01qUTZwWkNhdmNwSDlLQitSeXpBMmsyZkJTSE1DUTdKT29Sb3dJc0V4OTlienRYcEszQ1BtazNZWHBpL0tCd0JGSTFHKzd5TTZiU2NTdXpWMElDYVZoQlg2TGd4cHhaZkpqb1pHZE5oQjdjenJLajFkWTdQck52aERLTEc1ZURwQ2JRUGoxejRvUXEzYXpzZ3c0b0tQWnJoOVMveWJDdlUzOXk2dnJmdzFpSVpZbFBpeUhCMkNrdVNsQ00wNllEamtsc0oyQmtOcFFRdTlvdU9oVldWbUIiLCJtYWMiOiI5Y2YxMjVjODQ5NWQ5M2FjMGEyMDIwNTY1NTZmY2E1MTk2ZGFiZWRiMjAzNDM2ZmNlNjZjYjk0NTMxY2E5ZWQ2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:07:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138112208\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1345123835 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9sXI8W6NMdM2A10W2i2dj5lQcdKHjHHS0J8lASar</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345123835\", {\"maxDepth\":0})</script>\n"}}