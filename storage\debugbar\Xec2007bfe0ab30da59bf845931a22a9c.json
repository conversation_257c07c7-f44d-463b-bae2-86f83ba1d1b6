{"__meta": {"id": "Xec2007bfe0ab30da59bf845931a22a9c", "datetime": "2025-07-30 07:14:56", "utime": **********.523835, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753859695.702392, "end": **********.523876, "duration": 0.821483850479126, "duration_str": "821ms", "measures": [{"label": "Booting", "start": 1753859695.702392, "relative_start": 0, "end": **********.461986, "relative_end": **********.461986, "duration": 0.7595939636230469, "duration_str": "760ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.462003, "relative_start": 0.****************, "end": **********.523879, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "61.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "j6Gfhwu4GYaeJUxGMLR2qsSMJhHpgO6dCGupIddz", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1435849415 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1435849415\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-623382964 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623382964\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-145773857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-145773857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-129119044 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129119044\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1220560201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1220560201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-762648381 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:14:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im55TmhLelMzMEJQWWNpRmRoRlhodUE9PSIsInZhbHVlIjoiMk81R1JhYTljbDhYdGRONjlzZjVYbGJMTDFmOVNlNnRuVlZ0UjFyeVNWN2xjUnp3bHY1dHdpYlVLdXRYUHl0VEYrMGQyaUl5YWZQaEd6SDQvVERLZTJiL1hiMmpRUXRCa0FVQ0JYcy9ueE84VUR6cEdYaVRmN3NFbTNVdW81U1lQN2pidDNnVVV1alVpZjdhR1Q5SE1XNlk5VG9WditmVm96V3BEVE9uNmN6d2dieFV5MXdBdjF4R1JTQndqRlcyK0FIWWUyNlBTOU0zK0lqeGNQMEl1OFpkRWZ1Ykp2dmpFWjdHdXNCeHZPZGdBRDZPR3Z0TVVMWWduVERmOGxFcUVFbytYUlc3V1FmNVM2K051NXZtajE5UWlqWnExaEtEdDIxMDE1bm9jc2NUSGtKOXkyMzQ1cUZJQUFWa2VFVWg0Q01ZY3RocXZod21wSUtNNlNDTnVzNjVXSVAyT0NQN1IvR2sydzRIc2NXYTJjN0R6ZjZueW5XNU9rN3l2S1F6eDdsMk13SXZEYmt6d2YrdWp0L1JGN0k0aFY5VWdnUnROQjBvZmYxZUhUcnhmR3N1WTM5QUNGa3hqMTFEaTVHejRKbTlKdTFzOWZ2bUxyT1JydGEwRkFrMVR4QmF6N0VmSEZnbnJueFR3eFk5L2ZPVlIwL2ZEVXJud1JwWW83YWwiLCJtYWMiOiJiMWNlMjNmMDg3YmQ2NGNhNWI2MDJhZGRlNWYzYzI0MGY1MDc0NWY3YjQ2YWQyOTNjOTBlYzdlMTA5ZGY2NDQwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:14:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inp2alNLT1dNckEvbnVkVVhoL1VsYWc9PSIsInZhbHVlIjoiRnYrb3FXekdpaVB3OERuY2tqSnJFd0IzdEtFOGdlL2M4RCs4dFRFakZJTllRY1pYMkpXcC9uaWEyYTVBbG1IalV1b3hGMENlZ3hIeE83cVltVDJIMEFocXBEODh4aGJ0dWlQVkpTRWtENlZ3YXZ6Vk91cUZncFpyWEkzeXAreG5BajYwTG9sK1Vid2t4QXNkL0hKdHFCYVlzenEvQUhCeC9QbGIvSVJlejVwUVFjZ09LcDdJWlNZSlZZcS9SbXlQNkJGSklKL2RZTWhwanF2VmdUTHlZaUxVRStxVVoyOTUvWXVjNXViTEtRTUwvNlM2bkcwcyszQXJ2b0RzUHJSeVJkRXQxWDFvbmlWL1p1eU53L09IS3Jsb2NJbXFablNKaVBJei81VXhqbnNEV3dncUt4R0pKWDljMVpiYmIwREhYUDhaY2ZhMHVzL3lsUmR3c05Ua1hwd1VheVdxMUFYeThEZjJIQ0RzY0ExUXhyMXEvbHRxbDJGTVMrTkNXaFlpOXRucklNS0YzNWhqUEswRFc5ZDZCM1BmL2hoNk55b1htNUJqOSsreWJ1VlppdHRVV1g2OWNUcGV5NUsvQmdBZkpVbjVFYjRWa21IMVZ3Zy8wcUQwRGtqK2VtNVBDcVowK1ZqV1kxL2wyZmU3VmMyZ3RJZ2tIdVZnbWtXcFAzWHIiLCJtYWMiOiI3ODNhZDc4NzAzYjI2ZDU4MjFjMzI1OTY1MDY2ZGE2OThjMTgyMjdjNGY2MGJjODg2NzlkMzE1MGE3YWQ4Y2JkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:14:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im55TmhLelMzMEJQWWNpRmRoRlhodUE9PSIsInZhbHVlIjoiMk81R1JhYTljbDhYdGRONjlzZjVYbGJMTDFmOVNlNnRuVlZ0UjFyeVNWN2xjUnp3bHY1dHdpYlVLdXRYUHl0VEYrMGQyaUl5YWZQaEd6SDQvVERLZTJiL1hiMmpRUXRCa0FVQ0JYcy9ueE84VUR6cEdYaVRmN3NFbTNVdW81U1lQN2pidDNnVVV1alVpZjdhR1Q5SE1XNlk5VG9WditmVm96V3BEVE9uNmN6d2dieFV5MXdBdjF4R1JTQndqRlcyK0FIWWUyNlBTOU0zK0lqeGNQMEl1OFpkRWZ1Ykp2dmpFWjdHdXNCeHZPZGdBRDZPR3Z0TVVMWWduVERmOGxFcUVFbytYUlc3V1FmNVM2K051NXZtajE5UWlqWnExaEtEdDIxMDE1bm9jc2NUSGtKOXkyMzQ1cUZJQUFWa2VFVWg0Q01ZY3RocXZod21wSUtNNlNDTnVzNjVXSVAyT0NQN1IvR2sydzRIc2NXYTJjN0R6ZjZueW5XNU9rN3l2S1F6eDdsMk13SXZEYmt6d2YrdWp0L1JGN0k0aFY5VWdnUnROQjBvZmYxZUhUcnhmR3N1WTM5QUNGa3hqMTFEaTVHejRKbTlKdTFzOWZ2bUxyT1JydGEwRkFrMVR4QmF6N0VmSEZnbnJueFR3eFk5L2ZPVlIwL2ZEVXJud1JwWW83YWwiLCJtYWMiOiJiMWNlMjNmMDg3YmQ2NGNhNWI2MDJhZGRlNWYzYzI0MGY1MDc0NWY3YjQ2YWQyOTNjOTBlYzdlMTA5ZGY2NDQwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:14:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inp2alNLT1dNckEvbnVkVVhoL1VsYWc9PSIsInZhbHVlIjoiRnYrb3FXekdpaVB3OERuY2tqSnJFd0IzdEtFOGdlL2M4RCs4dFRFakZJTllRY1pYMkpXcC9uaWEyYTVBbG1IalV1b3hGMENlZ3hIeE83cVltVDJIMEFocXBEODh4aGJ0dWlQVkpTRWtENlZ3YXZ6Vk91cUZncFpyWEkzeXAreG5BajYwTG9sK1Vid2t4QXNkL0hKdHFCYVlzenEvQUhCeC9QbGIvSVJlejVwUVFjZ09LcDdJWlNZSlZZcS9SbXlQNkJGSklKL2RZTWhwanF2VmdUTHlZaUxVRStxVVoyOTUvWXVjNXViTEtRTUwvNlM2bkcwcyszQXJ2b0RzUHJSeVJkRXQxWDFvbmlWL1p1eU53L09IS3Jsb2NJbXFablNKaVBJei81VXhqbnNEV3dncUt4R0pKWDljMVpiYmIwREhYUDhaY2ZhMHVzL3lsUmR3c05Ua1hwd1VheVdxMUFYeThEZjJIQ0RzY0ExUXhyMXEvbHRxbDJGTVMrTkNXaFlpOXRucklNS0YzNWhqUEswRFc5ZDZCM1BmL2hoNk55b1htNUJqOSsreWJ1VlppdHRVV1g2OWNUcGV5NUsvQmdBZkpVbjVFYjRWa21IMVZ3Zy8wcUQwRGtqK2VtNVBDcVowK1ZqV1kxL2wyZmU3VmMyZ3RJZ2tIdVZnbWtXcFAzWHIiLCJtYWMiOiI3ODNhZDc4NzAzYjI2ZDU4MjFjMzI1OTY1MDY2ZGE2OThjMTgyMjdjNGY2MGJjODg2NzlkMzE1MGE3YWQ4Y2JkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:14:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762648381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14370166 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j6Gfhwu4GYaeJUxGMLR2qsSMJhHpgO6dCGupIddz</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14370166\", {\"maxDepth\":0})</script>\n"}}