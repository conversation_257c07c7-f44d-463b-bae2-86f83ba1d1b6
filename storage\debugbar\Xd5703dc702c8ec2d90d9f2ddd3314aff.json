{"__meta": {"id": "Xd5703dc702c8ec2d90d9f2ddd3314aff", "datetime": "2025-07-30 06:13:06", "utime": **********.723511, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855985.941158, "end": **********.723549, "duration": 0.782390832901001, "duration_str": "782ms", "measures": [{"label": "Booting", "start": 1753855985.941158, "relative_start": 0, "end": **********.648463, "relative_end": **********.648463, "duration": 0.7073049545288086, "duration_str": "707ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.648482, "relative_start": 0.****************, "end": **********.723559, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "75.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "g5WkHNTBk69fgc1VuwDGq660KwMvbG4owbpVj1VW", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-689022741 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-689022741\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2048560413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2048560413\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1937563079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1937563079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-879823859 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879823859\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1683130436 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683130436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1442855534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:13:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpwUnZjdk94V1hQYzhNSlp3VTAxc0E9PSIsInZhbHVlIjoiUjZ2K3NrZ1ludGFlenBtZmhUa09ZYUJtb09rUDJVQ2lXVU9QZUYvUGFHb3crR3E0Z0c3Q2RaemI1MVpTSUp6aklsS0htMFJlWlNRbDNBYWp5YWUwcytPL1F3ZHNpSTRDU05QVFJ0S1ZXMjJlemI2ZkZJYlUwdlhpeDRkZ3FvUHFMeHdmK0Q2ZlhNSFE2ZlVnVzNLUFFscW84SFJMbHUyaDhLSUQwTDdTVGtJRUoyUzlJTHRtcGFyNXQ3bm9GbnZYaGFNQ3JXYlFaWGs1STY0Q1VTNW1PamkvRk1jN2J1c050TGpwcUlxbDNpOFBIYlZoeVFpZU5VK3BEV2lCSm9sajdDWGYyVG1ZT0JDNk9IVSt4RVFuNndNblYrbHcxTGhKT0lIV3RBRmZOZzJsT3pYbDBua0NOd1ZSeG5uRVVhY1V6NkU5aXNTQk9RNFBJYXEyUEJvVy96RUpCU2cvdjg0cjZhalBZYlRoaFlZTzFRblAvVUM0a3YySWN3SHR6ckg3YXFoa094Z0RvM0pRRmdrZ2NJQ20xZzJyelBBSEZRL3p4dU5KVjE0OVJlUEFGNmd4QzRmM3I0WjNiOHpSM1hUNTNVK1d2NkNBZnQ0L3czeEJKblYxNHc1czVsd1VnTFgvMDZXNVpGRTBrZHNkSDUzbkMwMVcrcnNJbDRSTG5mOE0iLCJtYWMiOiIxNmViNDI3NWM5MDUyMmE2MjcyNzcyODVjYjdkYzk5NmZlMWFlNGM2ZDQ0MjJlMzI2NzUzNmQ3MjkzMTdkYWI1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:13:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktXaVhmM0c1MVBFaGNHalZ4Z1JwZ3c9PSIsInZhbHVlIjoiR0p2WWxTdTl0Y2JRWnlYNVpKdFlDMDZDZXc4d3pvWkFCZktZQVBxTkpwN2NKRW8yTGs5Y1RsVkk1em5NVUJacW5rSHo0MGdkYUJISENxZWhueG5RYjhzYy8wdUVUbXVLTEY1OEx3cXUrNUhVMzVqSmhIUkxVV01HS2Y0WUdha3VTR01Dcm9wNHFISjNHN0R4aEFPNG1uWnAyWUkyZ3F2dGVnMFBoRjJtL0V2eDFPVW9leENWRUhPMXNpV0FWOG9xalR2dXZhZk9HOWY0Z3dvdWlwbXJzZ0thWG44TkZQcUpBSW9mRE5OWnptNWgrZkovR2taZ0NoZHFMMXVlZE9WS2R3WjMxOHh4d3Y3Vm9FWURlNkU0clJHakQ0UTlEam1IWWlPYkd1MzYxNXVSczVmcm9rMlgwUG5RMDhLR1U0ci9Nemp1QlRBTWFHejNuQWVGNVNTdGJhYWJmOTBDSktCWGNlZjRKNmlpeEtJeWloKzZjSitoUUdVaFZaY2NkR1lNamdqanNybTZNbG9pL0laQzMreVF5ekVpRDI2c1pXb1JlQkkvUUJZYjlsWm4zOUVCelB3ZjlFb1RnT01ieC9LTWRnT08vVXVhWFZBZU1KbnlNTk41Ky9mcGsvZzhKblpJekVZWTJJRWVZd285NEY2SmNyajYrU3NsbXQ5d1RoU1UiLCJtYWMiOiI4YzBiMzA1ODE2MTk1Y2RmOTQ0MmYyM2UxNmYwZmJmYWM2NDc1MTAwMDJmNDA3MTVmNDlhZDczZGRlMzMzZjk3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:13:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpwUnZjdk94V1hQYzhNSlp3VTAxc0E9PSIsInZhbHVlIjoiUjZ2K3NrZ1ludGFlenBtZmhUa09ZYUJtb09rUDJVQ2lXVU9QZUYvUGFHb3crR3E0Z0c3Q2RaemI1MVpTSUp6aklsS0htMFJlWlNRbDNBYWp5YWUwcytPL1F3ZHNpSTRDU05QVFJ0S1ZXMjJlemI2ZkZJYlUwdlhpeDRkZ3FvUHFMeHdmK0Q2ZlhNSFE2ZlVnVzNLUFFscW84SFJMbHUyaDhLSUQwTDdTVGtJRUoyUzlJTHRtcGFyNXQ3bm9GbnZYaGFNQ3JXYlFaWGs1STY0Q1VTNW1PamkvRk1jN2J1c050TGpwcUlxbDNpOFBIYlZoeVFpZU5VK3BEV2lCSm9sajdDWGYyVG1ZT0JDNk9IVSt4RVFuNndNblYrbHcxTGhKT0lIV3RBRmZOZzJsT3pYbDBua0NOd1ZSeG5uRVVhY1V6NkU5aXNTQk9RNFBJYXEyUEJvVy96RUpCU2cvdjg0cjZhalBZYlRoaFlZTzFRblAvVUM0a3YySWN3SHR6ckg3YXFoa094Z0RvM0pRRmdrZ2NJQ20xZzJyelBBSEZRL3p4dU5KVjE0OVJlUEFGNmd4QzRmM3I0WjNiOHpSM1hUNTNVK1d2NkNBZnQ0L3czeEJKblYxNHc1czVsd1VnTFgvMDZXNVpGRTBrZHNkSDUzbkMwMVcrcnNJbDRSTG5mOE0iLCJtYWMiOiIxNmViNDI3NWM5MDUyMmE2MjcyNzcyODVjYjdkYzk5NmZlMWFlNGM2ZDQ0MjJlMzI2NzUzNmQ3MjkzMTdkYWI1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:13:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktXaVhmM0c1MVBFaGNHalZ4Z1JwZ3c9PSIsInZhbHVlIjoiR0p2WWxTdTl0Y2JRWnlYNVpKdFlDMDZDZXc4d3pvWkFCZktZQVBxTkpwN2NKRW8yTGs5Y1RsVkk1em5NVUJacW5rSHo0MGdkYUJISENxZWhueG5RYjhzYy8wdUVUbXVLTEY1OEx3cXUrNUhVMzVqSmhIUkxVV01HS2Y0WUdha3VTR01Dcm9wNHFISjNHN0R4aEFPNG1uWnAyWUkyZ3F2dGVnMFBoRjJtL0V2eDFPVW9leENWRUhPMXNpV0FWOG9xalR2dXZhZk9HOWY0Z3dvdWlwbXJzZ0thWG44TkZQcUpBSW9mRE5OWnptNWgrZkovR2taZ0NoZHFMMXVlZE9WS2R3WjMxOHh4d3Y3Vm9FWURlNkU0clJHakQ0UTlEam1IWWlPYkd1MzYxNXVSczVmcm9rMlgwUG5RMDhLR1U0ci9Nemp1QlRBTWFHejNuQWVGNVNTdGJhYWJmOTBDSktCWGNlZjRKNmlpeEtJeWloKzZjSitoUUdVaFZaY2NkR1lNamdqanNybTZNbG9pL0laQzMreVF5ekVpRDI2c1pXb1JlQkkvUUJZYjlsWm4zOUVCelB3ZjlFb1RnT01ieC9LTWRnT08vVXVhWFZBZU1KbnlNTk41Ky9mcGsvZzhKblpJekVZWTJJRWVZd285NEY2SmNyajYrU3NsbXQ5d1RoU1UiLCJtYWMiOiI4YzBiMzA1ODE2MTk1Y2RmOTQ0MmYyM2UxNmYwZmJmYWM2NDc1MTAwMDJmNDA3MTVmNDlhZDczZGRlMzMzZjk3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:13:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442855534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2021547366 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g5WkHNTBk69fgc1VuwDGq660KwMvbG4owbpVj1VW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021547366\", {\"maxDepth\":0})</script>\n"}}