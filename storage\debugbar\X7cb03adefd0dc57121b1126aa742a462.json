{"__meta": {"id": "X7cb03adefd0dc57121b1126aa742a462", "datetime": "2025-07-30 07:39:37", "utime": **********.461818, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753861175.950007, "end": **********.461895, "duration": 1.511888027191162, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1753861175.950007, "relative_start": 0, "end": **********.338995, "relative_end": **********.338995, "duration": 1.3889880180358887, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339022, "relative_start": 1.****************, "end": **********.461903, "relative_end": 8.106231689453125e-06, "duration": 0.*****************, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iy0LSprsaG5T09f6u1KnYQntV30cwjKcvuPhlsYY", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2034785158 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2034785158\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1457148470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1457148470\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-546930676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-546930676\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-674903563 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674903563\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1344591855 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1344591855\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1758699047 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:39:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVGMEdaR2ZlZEtLb3gzWWQ4czM5MEE9PSIsInZhbHVlIjoiZXJMNUFkQmdmVi9RRDRSRTBuZnBaM0dsWUNzQTN4N3V2M3FNb2pYbGZJMGZKdXhZZHFUcmQvR25aT2tIVzdHK0JiTzlvajFPa0QrM2IrZWV6ankvVm1WWEp6Q2FINVNDNXVpb3A4d2tpVVV6VHkyTG9pYzRHSUtYSlFUaWdLaHlxcXJOVmdMSkF4OXEvWlZwY1RYbGs1ZkJjYUZpa2VkcG5zYklKNmVFei9CWDY2VHBGaUIxd0ZQY2Z1NDlnMUZYTm0vVnBvcHBQRzhkeFhSa2Jsdkl3WkhkMHVueG9VbkJaaDBLbTFkcUtCanN0RzdyOVJ3YTdsd3VMWHliWG9RTzRWU3o3SmMwSnB2bzJoSXEwYVNWKzd2T3VjaXRGZkxKSTFINUtaZWY5Yjl5aUUxQkhIWGc0YVhjOWFsY2x6VnFOekRrS3Z4S3Z4dUtLRlpUZ0M1WFU2SFJBbkYxR2RSWnp6QzF1MTczeGM5WTRwMWNIQ2hhcUlPUnVXM2VqWUlja0ZOdytXaDBnaEpJSThlR2c0M3UvTUc3QUJiSG5DVUM2cEV1OWlpbHVxNUxJeVJQRnJFNDJKWEtZQ0R4ZzRCaFdaNGJMTndXdHdLS2ViejZlS0RiUUNHTGNnZkt3K25Gb2VJM2V1WUliRFFkWUJWb0IzVjB5VENFcEVVWmY3eG4iLCJtYWMiOiJlNjQ3OGUzNjllODU0M2RjNDMwNGM1Y2U0NzQyYWUxYzQ2MDUyYjJlYzAzODA1NmE5OWQxZmQwYWRlNWY4NjM0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:39:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikx5d0pIUWFzcGwzUndKdXgvL1YvMlE9PSIsInZhbHVlIjoiZDBDT3YvMHJoaks0WGMvbzFaeXhUTFJjQUdCclU5d2NyYUFBWkpMM25UcGF3cUZjbUdjdWRVSmI2dUlBeVJwOWgwT1hUanRha0syeHlYbGl4OTVmeU1PSWVFcmwraWpZcnhHcFkwVTlYWWVQRTBnK2RYM0FLY0pWUlUveDZXTE9VZnNjcEMzWCtiQklONmJKRWdyN285MVdpdHB5SXk4VHg5VHF5dmtabFYxR1VnTXFxNGxlRWlScXNtVGVKd1BnWXpYcmsvTDMvUE9GdTlRYVZNNUo3SDV1TE4wNEVLL3RrZkw0aGV1M1B5MUxjQmtWanMyNFJkTThuMVdoK3BlZUIwemd3eUFlU2Y2RDdraEFiTUFjV2x0bmpYQTVaUkVpQ0pUNmhSaDNYdWQrQmpFRmZ3OFlWWVVIbk1abzVpdE5BVUttKzR6cFRPaHpqZFo5RWJuTlJQL1F6S0JRdW94V0djL0lSNnh6VWUwR2RjOFg0MHd1Q255UEYvZmZ3NzZ5MkpxOXdUWUN6d1FJZ050bUxxdkJ4UUpiVEs0MW1NNjROMVNrWUlUdTR3MW9jdUYyUWZXK201U2xqU21iM1BlZE9lNXNncjZSWE5rWHlsNTBlRGdwem51UGxCVWZCMWNDalVCd3ZyazZtZ3lHbjNSVnZRalc3VkZFUjBxSmIvTkgiLCJtYWMiOiI4MmJkMmEwNjhlOWM1NzEzMTAzODMzM2EwNmFmZTQwNTdkY2Y5NzJhY2IyMWVjMDFlMDczYmFlN2RlN2ViY2E4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:39:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVGMEdaR2ZlZEtLb3gzWWQ4czM5MEE9PSIsInZhbHVlIjoiZXJMNUFkQmdmVi9RRDRSRTBuZnBaM0dsWUNzQTN4N3V2M3FNb2pYbGZJMGZKdXhZZHFUcmQvR25aT2tIVzdHK0JiTzlvajFPa0QrM2IrZWV6ankvVm1WWEp6Q2FINVNDNXVpb3A4d2tpVVV6VHkyTG9pYzRHSUtYSlFUaWdLaHlxcXJOVmdMSkF4OXEvWlZwY1RYbGs1ZkJjYUZpa2VkcG5zYklKNmVFei9CWDY2VHBGaUIxd0ZQY2Z1NDlnMUZYTm0vVnBvcHBQRzhkeFhSa2Jsdkl3WkhkMHVueG9VbkJaaDBLbTFkcUtCanN0RzdyOVJ3YTdsd3VMWHliWG9RTzRWU3o3SmMwSnB2bzJoSXEwYVNWKzd2T3VjaXRGZkxKSTFINUtaZWY5Yjl5aUUxQkhIWGc0YVhjOWFsY2x6VnFOekRrS3Z4S3Z4dUtLRlpUZ0M1WFU2SFJBbkYxR2RSWnp6QzF1MTczeGM5WTRwMWNIQ2hhcUlPUnVXM2VqWUlja0ZOdytXaDBnaEpJSThlR2c0M3UvTUc3QUJiSG5DVUM2cEV1OWlpbHVxNUxJeVJQRnJFNDJKWEtZQ0R4ZzRCaFdaNGJMTndXdHdLS2ViejZlS0RiUUNHTGNnZkt3K25Gb2VJM2V1WUliRFFkWUJWb0IzVjB5VENFcEVVWmY3eG4iLCJtYWMiOiJlNjQ3OGUzNjllODU0M2RjNDMwNGM1Y2U0NzQyYWUxYzQ2MDUyYjJlYzAzODA1NmE5OWQxZmQwYWRlNWY4NjM0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:39:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikx5d0pIUWFzcGwzUndKdXgvL1YvMlE9PSIsInZhbHVlIjoiZDBDT3YvMHJoaks0WGMvbzFaeXhUTFJjQUdCclU5d2NyYUFBWkpMM25UcGF3cUZjbUdjdWRVSmI2dUlBeVJwOWgwT1hUanRha0syeHlYbGl4OTVmeU1PSWVFcmwraWpZcnhHcFkwVTlYWWVQRTBnK2RYM0FLY0pWUlUveDZXTE9VZnNjcEMzWCtiQklONmJKRWdyN285MVdpdHB5SXk4VHg5VHF5dmtabFYxR1VnTXFxNGxlRWlScXNtVGVKd1BnWXpYcmsvTDMvUE9GdTlRYVZNNUo3SDV1TE4wNEVLL3RrZkw0aGV1M1B5MUxjQmtWanMyNFJkTThuMVdoK3BlZUIwemd3eUFlU2Y2RDdraEFiTUFjV2x0bmpYQTVaUkVpQ0pUNmhSaDNYdWQrQmpFRmZ3OFlWWVVIbk1abzVpdE5BVUttKzR6cFRPaHpqZFo5RWJuTlJQL1F6S0JRdW94V0djL0lSNnh6VWUwR2RjOFg0MHd1Q255UEYvZmZ3NzZ5MkpxOXdUWUN6d1FJZ050bUxxdkJ4UUpiVEs0MW1NNjROMVNrWUlUdTR3MW9jdUYyUWZXK201U2xqU21iM1BlZE9lNXNncjZSWE5rWHlsNTBlRGdwem51UGxCVWZCMWNDalVCd3ZyazZtZ3lHbjNSVnZRalc3VkZFUjBxSmIvTkgiLCJtYWMiOiI4MmJkMmEwNjhlOWM1NzEzMTAzODMzM2EwNmFmZTQwNTdkY2Y5NzJhY2IyMWVjMDFlMDczYmFlN2RlN2ViY2E4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:39:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758699047\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1213023884 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iy0LSprsaG5T09f6u1KnYQntV30cwjKcvuPhlsYY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213023884\", {\"maxDepth\":0})</script>\n"}}