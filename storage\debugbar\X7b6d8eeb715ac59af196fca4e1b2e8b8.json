{"__meta": {"id": "X7b6d8eeb715ac59af196fca4e1b2e8b8", "datetime": "2025-07-30 08:01:08", "utime": **********.89023, "method": "POST", "uri": "/lead_stages/move-lead", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[08:01:06] LOG.info: Starting webhook dispatch for action: crm.lead_stage_changed {\n    \"timestamp\": \"2025-07-30T08:01:06.600646Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"user_id\": 79,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 13,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.606638, "xdebug_link": null, "collector": "log"}, {"message": "[08:01:08] LOG.error: <PERSON>hook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-07-30T08:01:08.761073Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2135,\n    \"user_id\": 79,\n    \"entity_id\": 13,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_stage_changed\",\n        \"timestamp\": \"2025-07-30T08:01:06.625734Z\",\n        \"data\": {\n            \"id\": 13,\n            \"name\": \"Gungun Rani\",\n            \"contact_type\": \"Lead\",\n            \"tags\": \"HElo\",\n            \"postal_code\": \"734429\",\n            \"city\": \"Siliguri\",\n            \"state\": \"Dadra and Nagar Haveli\",\n            \"country\": \"India\",\n            \"business_name\": \"NA\",\n            \"business_gst\": \"NA\",\n            \"business_state\": \"West Bengal\",\n            \"business_postal_code\": \"734429\",\n            \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\",\n            \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":true,\\\"whatsapp\\\":true,\\\"sms\\\":true,\\\"calls\\\":true}\",\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"8654201236\",\n            \"date_of_birth\": \"2025-07-31\",\n            \"type\": null,\n            \"status\": \"active\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": null,\n            \"opportunity_source\": null,\n            \"lead_value\": null,\n            \"subject\": \"New Contact\",\n            \"user_id\": 79,\n            \"pipeline_id\": 23,\n            \"stage_id\": 86,\n            \"contact_group_id\": null,\n            \"sources\": [],\n            \"products\": [],\n            \"notes\": null,\n            \"labels\": [],\n            \"order\": 0,\n            \"created_by\": 79,\n            \"is_deleted\": 0,\n            \"is_active\": 1,\n            \"is_converted\": 0,\n            \"date\": \"2025-07-30\",\n            \"next_follow_up_date\": null,\n            \"created_at\": \"2025-07-30T07:59:28.000000Z\",\n            \"updated_at\": \"2025-07-30T07:59:28.000000Z\",\n            \"stage\": {\n                \"id\": 86,\n                \"name\": \"New\",\n                \"pipeline_id\": 23,\n                \"created_by\": 79,\n                \"order\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:03.000000Z\"\n            },\n            \"users\": [\n                {\n                    \"id\": 79,\n                    \"name\": \"Parichay Singha AI\",\n                    \"email\": \"<EMAIL>\",\n                    \"email_verified_at\": \"2025-07-19T06:00:35.000000Z\",\n                    \"plan\": 10,\n                    \"plan_expire_date\": \"2026-07-19\",\n                    \"requested_plan\": 0,\n                    \"trial_plan\": 0,\n                    \"trial_expire_date\": null,\n                    \"type\": \"company\",\n                    \"system_admin_company_id\": null,\n                    \"company_name\": null,\n                    \"company_description\": null,\n                    \"module_permissions\": {\n                        \"crm\": [\n                            \"create lead\",\n                            \"create deal\",\n                            \"create form builder\",\n                            \"create contract\",\n                            \"create pipeline\",\n                            \"create stage\",\n                            \"create source\",\n                            \"create label\",\n                            \"view crm dashboard\",\n                            \"view lead\",\n                            \"view deal\",\n                            \"view form builder\",\n                            \"view contract\",\n                            \"view pipeline\",\n                            \"view stage\",\n                            \"view source\",\n                            \"view label\",\n                            \"delete lead\",\n                            \"delete deal\",\n                            \"delete form builder\",\n                            \"delete contract\",\n                            \"delete pipeline\",\n                            \"delete stage\",\n                            \"delete source\",\n                            \"delete label\",\n                            \"manage lead\",\n                            \"edit lead\",\n                            \"manage deal\",\n                            \"edit deal\",\n                            \"manage form builder\",\n                            \"edit form builder\",\n                            \"manage contract\",\n                            \"edit contract\",\n                            \"manage pipeline\",\n                            \"edit pipeline\",\n                            \"manage stage\",\n                            \"edit stage\",\n                            \"manage source\",\n                            \"edit source\",\n                            \"manage label\",\n                            \"edit label\"\n                        ],\n                        \"hrm\": [\n                            \"create employee\",\n                            \"create set salary\",\n                            \"create pay slip\",\n                            \"create leave\",\n                            \"create attendance\",\n                            \"create training\",\n                            \"create award\",\n                            \"create branch\",\n                            \"create department\",\n                            \"create designation\",\n                            \"create document type\",\n                            \"view hrm dashboard\",\n                            \"view employee\",\n                            \"view set salary\",\n                            \"view pay slip\",\n                            \"view leave\",\n                            \"view attendance\",\n                            \"view training\",\n                            \"view award\",\n                            \"view branch\",\n                            \"view department\",\n                            \"view designation\",\n                            \"view document type\",\n                            \"delete employee\",\n                            \"delete set salary\",\n                            \"delete pay slip\",\n                            \"delete leave\",\n                            \"delete attendance\",\n                            \"delete training\",\n                            \"delete award\",\n                            \"delete branch\",\n                            \"delete department\",\n                            \"delete designation\",\n                            \"delete document type\",\n                            \"manage employee\",\n                            \"edit employee\",\n                            \"manage set salary\",\n                            \"edit set salary\",\n                            \"manage pay slip\",\n                            \"edit pay slip\",\n                            \"manage leave\",\n                            \"edit leave\",\n                            \"manage attendance\",\n                            \"edit attendance\",\n                            \"manage training\",\n                            \"edit training\",\n                            \"manage award\",\n                            \"edit award\",\n                            \"manage branch\",\n                            \"edit branch\",\n                            \"manage department\",\n                            \"edit department\",\n                            \"manage designation\",\n                            \"edit designation\",\n                            \"manage document type\",\n                            \"edit document type\"\n                        ],\n                        \"account\": [\n                            \"create customer\",\n                            \"create vender\",\n                            \"create invoice\",\n                            \"create bill\",\n                            \"create revenue\",\n                            \"create payment\",\n                            \"create proposal\",\n                            \"create goal\",\n                            \"create credit note\",\n                            \"create debit note\",\n                            \"create bank account\",\n                            \"create bank transfer\",\n                            \"create transaction\",\n                            \"create chart of account\",\n                            \"create journal entry\",\n                            \"create assets\",\n                            \"create constant custom field\",\n                            \"view account dashboard\",\n                            \"view customer\",\n                            \"view vender\",\n                            \"view invoice\",\n                            \"view bill\",\n                            \"view revenue\",\n                            \"view payment\",\n                            \"view proposal\",\n                            \"view goal\",\n                            \"view credit note\",\n                            \"view debit note\",\n                            \"view bank account\",\n                            \"view bank transfer\",\n                            \"view transaction\",\n                            \"view chart of account\",\n                            \"view journal entry\",\n                            \"view assets\",\n                            \"view constant custom field\",\n                            \"view report\",\n                            \"delete customer\",\n                            \"delete vender\",\n                            \"delete invoice\",\n                            \"delete bill\",\n                            \"delete revenue\",\n                            \"delete payment\",\n                            \"delete proposal\",\n                            \"delete goal\",\n                            \"delete credit note\",\n                            \"delete debit note\",\n                            \"delete bank account\",\n                            \"delete bank transfer\",\n                            \"delete transaction\",\n                            \"delete chart of account\",\n                            \"delete journal entry\",\n                            \"delete assets\",\n                            \"delete constant custom field\",\n                            \"manage customer\",\n                            \"edit customer\",\n                            \"manage vender\",\n                            \"edit vender\",\n                            \"manage invoice\",\n                            \"edit invoice\",\n                            \"manage bill\",\n                            \"edit bill\",\n                            \"manage revenue\",\n                            \"edit revenue\",\n                            \"manage payment\",\n                            \"edit payment\",\n                            \"manage proposal\",\n                            \"edit proposal\",\n                            \"manage goal\",\n                            \"edit goal\",\n                            \"manage credit note\",\n                            \"edit credit note\",\n                            \"manage debit note\",\n                            \"edit debit note\",\n                            \"manage bank account\",\n                            \"edit bank account\",\n                            \"manage bank transfer\",\n                            \"edit bank transfer\",\n                            \"manage transaction\",\n                            \"edit transaction\",\n                            \"manage chart of account\",\n                            \"edit chart of account\",\n                            \"manage journal entry\",\n                            \"edit journal entry\",\n                            \"manage assets\",\n                            \"edit assets\",\n                            \"manage constant custom field\",\n                            \"edit constant custom field\",\n                            \"manage report\"\n                        ],\n                        \"project\": [\n                            \"create project\",\n                            \"create project task\",\n                            \"create timesheet\",\n                            \"create bug report\",\n                            \"create milestone\",\n                            \"create project stage\",\n                            \"create project task stage\",\n                            \"create project expense\",\n                            \"create activity\",\n                            \"create bug status\",\n                            \"view project dashboard\",\n                            \"view project\",\n                            \"view project task\",\n                            \"view timesheet\",\n                            \"view bug report\",\n                            \"view milestone\",\n                            \"view project stage\",\n                            \"view project task stage\",\n                            \"view project expense\",\n                            \"view activity\",\n                            \"view bug status\",\n                            \"delete project\",\n                            \"delete project task\",\n                            \"delete timesheet\",\n                            \"delete bug report\",\n                            \"delete milestone\",\n                            \"delete project stage\",\n                            \"delete project task stage\",\n                            \"delete project expense\",\n                            \"delete activity\",\n                            \"delete bug status\",\n                            \"manage project\",\n                            \"edit project\",\n                            \"manage project task\",\n                            \"edit project task\",\n                            \"manage timesheet\",\n                            \"edit timesheet\",\n                            \"manage bug report\",\n                            \"edit bug report\",\n                            \"manage milestone\",\n                            \"edit milestone\",\n                            \"manage project stage\",\n                            \"edit project stage\",\n                            \"manage project task stage\",\n                            \"edit project task stage\",\n                            \"manage project expense\",\n                            \"edit project expense\",\n                            \"manage activity\",\n                            \"edit activity\",\n                            \"manage bug status\",\n                            \"edit bug status\"\n                        ],\n                        \"pos\": [\n                            \"create warehouse\",\n                            \"create purchase\",\n                            \"create quotation\",\n                            \"create pos\",\n                            \"create barcode\",\n                            \"create product\",\n                            \"create product category\",\n                            \"create product unit\",\n                            \"view pos dashboard\",\n                            \"view warehouse\",\n                            \"view purchase\",\n                            \"view quotation\",\n                            \"view pos\",\n                            \"view product\",\n                            \"view product category\",\n                            \"view product unit\",\n                            \"delete warehouse\",\n                            \"delete purchase\",\n                            \"delete quotation\",\n                            \"delete pos\",\n                            \"delete product\",\n                            \"delete product category\",\n                            \"delete product unit\",\n                            \"manage warehouse\",\n                            \"edit warehouse\",\n                            \"manage purchase\",\n                            \"edit purchase\",\n                            \"manage quotation\",\n                            \"edit quotation\",\n                            \"manage pos\",\n                            \"edit pos\",\n                            \"manage product\",\n                            \"edit product\",\n                            \"manage product category\",\n                            \"edit product category\",\n                            \"manage product unit\",\n                            \"edit product unit\"\n                        ],\n                        \"support\": [\n                            \"create support\",\n                            \"view support dashboard\",\n                            \"view support\",\n                            \"delete support\",\n                            \"manage support\",\n                            \"edit support\",\n                            \"reply support\"\n                        ],\n                        \"user_management\": [\n                            \"create user\",\n                            \"create client\",\n                            \"view user\",\n                            \"view client\",\n                            \"delete user\",\n                            \"delete client\",\n                            \"manage user\",\n                            \"edit user\",\n                            \"manage client\",\n                            \"edit client\"\n                        ],\n                        \"booking\": [\n                            \"create booking\",\n                            \"create appointment\",\n                            \"create appointment booking\",\n                            \"create calendar event\",\n                            \"view booking dashboard\",\n                            \"view booking\",\n                            \"show booking\",\n                            \"view appointment\",\n                            \"show appointment\",\n                            \"view appointment booking\",\n                            \"show appointment booking\",\n                            \"view calendar event\",\n                            \"show calendar event\",\n                            \"delete booking\",\n                            \"delete appointment\",\n                            \"delete appointment booking\",\n                            \"delete calendar event\",\n                            \"manage booking\",\n                            \"edit booking\",\n                            \"manage appointment\",\n                            \"edit appointment\",\n                            \"manage appointment booking\",\n                            \"edit appointment booking\",\n                            \"manage calendar event\",\n                            \"edit calendar event\"\n                        ],\n                        \"omx_flow\": [\n                            \"access omx flow\",\n                            \"whatsapp_flows\",\n                            \"whatsapp_orders\",\n                            \"campaigns\",\n                            \"templates\",\n                            \"chatbot\"\n                        ],\n                        \"personal_tasks\": [\n                            \"create personal task\",\n                            \"create personal task comment\",\n                            \"create personal task file\",\n                            \"create personal task checklist\",\n                            \"view personal task\",\n                            \"delete personal task\",\n                            \"delete personal task comment\",\n                            \"delete personal task file\",\n                            \"delete personal task checklist\",\n                            \"manage personal task\",\n                            \"edit personal task\",\n                            \"edit personal task comment\",\n                            \"edit personal task checklist\",\n                            \"manage personal task time tracking\"\n                        ],\n                        \"automatish\": [\n                            \"access automatish\"\n                        ]\n                    },\n                    \"storage_limit\": 0,\n                    \"avatar\": \"logo-dark-removebg-preview_1752904903.png\",\n                    \"messenger_color\": \"#2180f3\",\n                    \"lang\": \"en\",\n                    \"default_pipeline\": 23,\n                    \"active_status\": 0,\n                    \"delete_status\": 1,\n                    \"mode\": \"light\",\n                    \"dark_mode\": 0,\n                    \"is_disable\": 1,\n                    \"is_enable_login\": 1,\n                    \"is_active\": 1,\n                    \"referral_code\": 0,\n                    \"used_referral_code\": 0,\n                    \"commission_amount\": 0,\n                    \"last_login_at\": null,\n                    \"created_by\": 7,\n                    \"created_at\": \"2025-07-19T06:00:35.000000Z\",\n                    \"updated_at\": \"2025-07-30T05:11:50.000000Z\",\n                    \"is_email_verified\": 0,\n                    \"profile\": \"http:\\/\\/localhost:8000\\/storage\\/avatar.png\",\n                    \"pivot\": {\n                        \"lead_id\": 13,\n                        \"user_id\": 79\n                    }\n                }\n            ],\n            \"pipeline\": {\n                \"id\": 23,\n                \"name\": \"OMX Digital Bot\",\n                \"created_by\": 79,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:42.000000Z\"\n            },\n            \"old_stage_id\": 86,\n            \"new_stage_id\": \"88\",\n            \"triggered_by\": {\n                \"user_id\": 79,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"Parichay Singha AI\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 79,\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.764641, "xdebug_link": null, "collector": "log"}, {"message": "[08:01:08] LOG.warning: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-07-30T08:01:08.766205Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"user_id\": 79,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.767614, "xdebug_link": null, "collector": "log"}, {"message": "[08:01:08] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/lead_stages\\/move-lead\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.872136, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753862463.866442, "end": **********.890316, "duration": 5.023874044418335, "duration_str": "5.02s", "measures": [{"label": "Booting", "start": 1753862463.866442, "relative_start": 0, "end": **********.56464, "relative_end": **********.56464, "duration": 1.6981980800628662, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.5647, "relative_start": 1.6982579231262207, "end": **********.890322, "relative_end": 5.9604644775390625e-06, "duration": 3.325622081756592, "duration_str": "3.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57125512, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST lead_stages/move-lead", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadStageController@moveLeadToStage", "namespace": null, "prefix": "", "where": [], "as": "lead_stages.moveLeadToStage", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=303\" onclick=\"\">app/Http/Controllers/LeadStageController.php:303-388</a>"}, "queries": {"nb_statements": 23, "nb_failed_statements": 0, "accumulated_duration": 0.09484999999999999, "accumulated_duration_str": "94.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6598449, "duration": 0.02952, "duration_str": "29.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 31.123}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.726646, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 31.123, "width_percent": 1.434}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.755427, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 32.557, "width_percent": 1.687}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7668378, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 34.244, "width_percent": 1.16}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.779148, "duration": 0.0062900000000000005, "duration_str": "6.29ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 35.403, "width_percent": 6.632}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.855595, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 42.035, "width_percent": 5.356}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.9158611, "duration": 0.01451, "duration_str": "14.51ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 47.391, "width_percent": 15.298}, {"sql": "select count(*) as aggregate from `leads` where `id` = '13'", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.3321989, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 62.688, "width_percent": 1.55}, {"sql": "select count(*) as aggregate from `lead_stages` where `id` = '88'", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.341948, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 64.238, "width_percent": 1.075}, {"sql": "select * from `leads` where `leads`.`id` = '13' limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 313}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.351316, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:313", "source": "app/Http/Controllers/LeadStageController.php:313", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=313", "ajax": false, "filename": "LeadStageController.php", "line": "313"}, "connection": "radhe_same", "start_percent": 65.314, "width_percent": 1.318}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 86 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["86"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 314}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.367221, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:314", "source": "app/Http/Controllers/LeadStageController.php:314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=314", "ajax": false, "filename": "LeadStageController.php", "line": "314"}, "connection": "radhe_same", "start_percent": 66.632, "width_percent": 1.223}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = '88' limit 1", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 315}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3798962, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:315", "source": "app/Http/Controllers/LeadStageController.php:315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=315", "ajax": false, "filename": "LeadStageController.php", "line": "315"}, "connection": "radhe_same", "start_percent": 67.855, "width_percent": 1.813}, {"sql": "insert into `lead_activity_logs` (`user_id`, `lead_id`, `log_type`, `remark`, `updated_at`, `created_at`) values (79, 13, 'Move', '{\\\"title\\\":\\\"Gun<PERSON> Rani\\\",\\\"old_status\\\":\\\"New\\\",\\\"new_status\\\":\\\"Discussion\\\"}', '2025-07-30 08:01:06', '2025-07-30 08:01:06')", "type": "query", "params": [], "bindings": ["79", "13", "Move", "{&quot;title&quot;:&quot;<PERSON><PERSON>&quot;,&quot;old_status&quot;:&quot;New&quot;,&quot;new_status&quot;:&quot;Discussion&quot;}", "2025-07-30 08:01:06", "2025-07-30 08:01:06"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 321}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.398609, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:321", "source": "app/Http/Controllers/LeadStageController.php:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=321", "ajax": false, "filename": "LeadStageController.php", "line": "321"}, "connection": "radhe_same", "start_percent": 69.668, "width_percent": 4.091}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 13", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 333}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.416344, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:333", "source": "app/Http/Controllers/LeadStageController.php:333", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=333", "ajax": false, "filename": "LeadStageController.php", "line": "333"}, "connection": "radhe_same", "start_percent": 73.759, "width_percent": 2.499}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 23 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 337}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.434932, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:337", "source": "app/Http/Controllers/LeadStageController.php:337", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=337", "ajax": false, "filename": "LeadStageController.php", "line": "337"}, "connection": "radhe_same", "start_percent": 76.257, "width_percent": 1.666}, {"sql": "select * from `email_templates` where `name` LIKE 'Move Lead' limit 1", "type": "query", "params": [], "bindings": ["Move Lead"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 344}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.447692, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "radhe_same", "start_percent": 77.923, "width_percent": 1.898}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}], "start": **********.549942, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": "app/Models/Lead.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "radhe_same", "start_percent": 79.821, "width_percent": 1.149}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 390}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}], "start": **********.572404, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": "app/Models/Lead.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "radhe_same", "start_percent": 80.97, "width_percent": 1.476}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.585417, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "radhe_same", "start_percent": 82.446, "width_percent": 2.288}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.610132, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:61", "source": "app/Services/ModuleWebhookService.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=61", "ajax": false, "filename": "ModuleWebhookService.php", "line": "61"}, "connection": "radhe_same", "start_percent": 84.734, "width_percent": 1.729}, {"sql": "update `leads` set `stage_id` = '88', `leads`.`updated_at` = '2025-07-30 08:01:08' where `id` = 13", "type": "query", "params": [], "bindings": ["88", "2025-07-30 08:01:08", "13"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 357}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.772264, "duration": 0.0086, "duration_str": "8.6ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:357", "source": "app/Http/Controllers/LeadStageController.php:357", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=357", "ajax": false, "filename": "LeadStageController.php", "line": "357"}, "connection": "radhe_same", "start_percent": 86.463, "width_percent": 9.067}, {"sql": "select * from `leads` where `leads`.`id` = '13' limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 364}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.797888, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:364", "source": "app/Http/Controllers/LeadStageController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=364", "ajax": false, "filename": "LeadStageController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 95.53, "width_percent": 2.594}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.849195, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.123, "width_percent": 1.877}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2788, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => move lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1449535786 data-indent-pad=\"  \"><span class=sf-dump-note>move lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">move lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449535786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308708, "xdebug_link": null}]}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/lead_stages/move-lead", "status_code": "<pre class=sf-dump id=sf-dump-196810210 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-196810210\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-68858651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-68858651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1707381886 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lead_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>new_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  \"<span class=sf-dump-key>old_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707381886\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1499679134 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">128</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkRIc28wSlpBa0twZGlwNTB2T2VhSUE9PSIsInZhbHVlIjoiaFgrQVNXeVZUbVVkUWlJbEVjbTBLVGswL3NiWm1hdGpZcytkdXcyazZ6VFM2MDhaRG9DcWpadHhBN0NLb3B1d3lSb04vRXpoUDY4eTJjN0JnQVNsZkpUS3ViZG0wMVdSeDZDUzFCOGFqOFNNZStSU0lTUU1XLzJscGQ4RnhOTTVVeUJ3SWptcjVDTmxkZk1GWFJwOXVUclhUK05kYnAyT3JMbnFjWjV6SDFRWXpaQWxwTlorZWdyWXRTa3M2TE5HNENoV3dSK3FpS0pYSlZNZ1FtRUxzTUNTazNYRFhPczFQZ1RyZUkzSVo5WFRrOGF0WFFTQTJwRlE2YXdMaHY0b1VoUnkxN09ueVBiOUhFTzlSTjlRbFJxVW81Si91YXRpcndXQktrTDErdXF4VllucmZSclhDeStXemN0a3hWWVNZL2lzKzB6NDZVQUNzak5sUkMzNEVYQXlvRC9vZWc4UUZSSkNGS1Q5aE1VMzdGenlQSnB0aWthdVM4UWJwT240amV2S3VHNHZnbG1FM2h5OTNKc3B6UzFjbjBUTkRtckJQS0ZOUWtMV3EvZUc3ZXdlZVhzNTFDcjN4VWdQaHlqWUxSVTVFVGpyNlhwY3g4OExZVm1ZcGZJWElTLyt3Y1dqM0t6am0ycXc5TUtiNVZZQkJhUmVXdlB0a091ZUhlTWYiLCJtYWMiOiIxYzFiZjZlZWZlNmQ5ZjJiMTgwZjcwNjZiMTAxYTczNTIxMDMwMDZiNzM1OGU2ODUyNTdhZTYyNzEzZWQ2YjcyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InVlSnZrMTJMQ1dlYnhNQ09LNTg1Znc9PSIsInZhbHVlIjoicTFuaG5EZFpZR0lrK2o4YUgveVFJWHhldUg2MVZJY2Q1dVdGbERFdGpQTWN3YWV5akVSY3UrV09pMGJmZ2kwQlkrOWVuZUw3VGFWTG5RenVZQ0p3aTB5Tmh5SmZxTFVFKzc2MFNRZ3pCTkFwVXlDeUNkRXd3Q1prR2hRcDlsNmlkYmFMMFFodGVCQjVtWmRlZERYNDFaSWo5Vi9QRDI2LytUeURWWHlZN2NLNTZEM1poNHFOSW10Yjhia3VFMTVKU0FEbi9vR1RJWGJoV2h1U0ZmRjVkSTVlUWgyR3pBK2RLeW5SSUEwTVlqNEUwSGpMQWpuMVBjOFJDN3lHbnNkdnR5ODF4NWxacjdUS1NzRGVzaDJMeHB4amZ5TkFKZTV6WFdXTUFMS3FPQ0tHelhrUkd5Q3ZuQ05NR3BPRVNRQXYvbjl4Y2N2N0J4SzFOSzdlSG9BSTU4M1VIck9KbUl4VWo2d28zdVNlZDA5MUxxOElzZzFqU1YrYlNvN3NscGRzdFhjQW1LZytBS1NOeXNlOXI3RFRFVWxoNEswbnluMEFTeHJWQm9hQ2dFbTg0OEc5cVFEdGQ0ZGo0STBIci96Z3pQbU93dGtQRkRWQmFQbkRjeElKbUUyRms1YSsvaDM3Z1B6YzdQWUFTTXdYQ2tpL2VGK2Z5SnpYVmJ1S1NuWEEiLCJtYWMiOiIyOWI0YWFjY2ZjODk4ZTRiODJmZDYzYWJhOGIzMzIyY2UxOTFkYzU2MDU3YmVmYzJkODNhMzRjZTIyYTcxYjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499679134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145590484 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145590484\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2075632272 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZZeTg1OHlwRG5LNHoxUlhmZkVGSFE9PSIsInZhbHVlIjoiTGdtQUZDem9vSHNQbTFZSU5nN3Q3dUZObldaT3VaMTFzbmhGZ0dlUjZabDVOOEJTMmVFMWVyaXJ1ZmU0K01aaG8xclY5OGFDKzVxdFVRc0t4UDUrb2F4cEorVHhSc0VKeVptL0FIRi9tMk9Wb3p5REh0WityWmtlM3Z1RUJDTkQvUjlyWExzZ3lYdUdoWGRVOWN1Y2ZhcHlWNVZvQnpaVGlEOFhBQVB2VmI1bzhKMURuSTNUTFF0eWZRUTQ2YmVTOG9oTEhFTUYrOGFwd1Jkc2RKY28zTWJkRUpYZ1lTQitmVmZvcE00S2pSZzI1V21RV3BST29jTmVHRVM5RDhIelB6K1cvUU5mNXJjc1VwaGpjRm1Pc3pBOXFJQkhiMGIrRXdRUnJDQjFYQm9TWVJtM1JSYmt5dVMxZXpwbXFrMmRjTFBxOVBPMXdBUlJEUUpJZnFMTXlGYXp6MEExVG1nSDN6aWdHREY3dzRWcnNmSzVFSjhwbG5jUUhxWTdZY3MrQnJNTUhmZDBtMUVGNjAzMWpPQjNSN3hpd2Z0b0FoN1owM3h6OE9qT0VyOUhVTlR1QUdjWkozYm8vSXZYb1VEZ1B2WU8xMDA1YnNpNmpRdDRSckVJYmZpNXFrNVB6Tjl5WkkwVmY3aEhJTkNGQVVUMXNHWUM3akdXMHZTYUJOdngiLCJtYWMiOiI3OTI5ZTRkYzdhYjM3Njc2ZWFiYjI5MWJiZDEwMDIwNWMwMzk1YjU4YTg1ZDAwMzBmOWRkYjUyMjBjMzYxMzhlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik1sWjNJMTZ2ODBGb3dYbHRyL2xjWFE9PSIsInZhbHVlIjoieXo2QnNiZTNwMDNJdUJSTy9hTXFzWjNOUlJwa1hlVmlxdVFsZ0Y2Ym1qaWE3RHRHZFNzZlE1YkhJbWhqb0NReHpNR04wRVhDSkNiZ3NUTHFYYUFTTm9SV2h3Wm90QVZSUXBPL3lUbS9mTFlvcXU0VEtEUHBVZ1JJdzJrVlJGcDM1b0NvN3hYOFkreW1LKzBvNWM3amZlWk9EdGhDenpEQWZCdEtGeUZ1c3ZMcDhDNlVqb0l0UUd1ZGZ1S0VKNllyQThSbmwxRXNHWDhXK0pmMjVSbjcyOW96U3VTd0dWUEdraGhqcXBydml6R1huTVRxblhxaUF4RUh0UVJTbmNTV3BBZ0I3YlorMjk4aDdlZVRnU0FyTDdEbDlrcWNLTmRvTUNwbDAySkhTUGI0ZndnQ0FFR1RTMzJYMS9XYXNZSGZ4cXVzNkIvVUYrd0hudUVoNk5jb2JXL1ZQK1l0aFp1bGkzL0dVazk5MXBFeWJ1bXd1d3k2eTJndlU5QkhHYkNCWTZhZW4xRmZXeTlCVHI3d3FWUmIxYXpVb0NZVmdXR2RkODhFb0E4U2ZLT2ZRK2tSVWNML1ZOUEthRDRjZWMzTkZ5enluRHUvNjVHbjM0M0l2MlFCb054OVhHbHA0RkZmeG8vUWtMRWZ1eC85d3FxMFNJZlRhdFpobXo1Nm9rN0oiLCJtYWMiOiI2NmUwNWVhNjI5ZDg4MzM5YTM3MTZlYzcyMjQyODVkMTYxNGI3ODcxNmFjZTM4ZTViN2UyN2U4ZWU4ZDVlZTM3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZZeTg1OHlwRG5LNHoxUlhmZkVGSFE9PSIsInZhbHVlIjoiTGdtQUZDem9vSHNQbTFZSU5nN3Q3dUZObldaT3VaMTFzbmhGZ0dlUjZabDVOOEJTMmVFMWVyaXJ1ZmU0K01aaG8xclY5OGFDKzVxdFVRc0t4UDUrb2F4cEorVHhSc0VKeVptL0FIRi9tMk9Wb3p5REh0WityWmtlM3Z1RUJDTkQvUjlyWExzZ3lYdUdoWGRVOWN1Y2ZhcHlWNVZvQnpaVGlEOFhBQVB2VmI1bzhKMURuSTNUTFF0eWZRUTQ2YmVTOG9oTEhFTUYrOGFwd1Jkc2RKY28zTWJkRUpYZ1lTQitmVmZvcE00S2pSZzI1V21RV3BST29jTmVHRVM5RDhIelB6K1cvUU5mNXJjc1VwaGpjRm1Pc3pBOXFJQkhiMGIrRXdRUnJDQjFYQm9TWVJtM1JSYmt5dVMxZXpwbXFrMmRjTFBxOVBPMXdBUlJEUUpJZnFMTXlGYXp6MEExVG1nSDN6aWdHREY3dzRWcnNmSzVFSjhwbG5jUUhxWTdZY3MrQnJNTUhmZDBtMUVGNjAzMWpPQjNSN3hpd2Z0b0FoN1owM3h6OE9qT0VyOUhVTlR1QUdjWkozYm8vSXZYb1VEZ1B2WU8xMDA1YnNpNmpRdDRSckVJYmZpNXFrNVB6Tjl5WkkwVmY3aEhJTkNGQVVUMXNHWUM3akdXMHZTYUJOdngiLCJtYWMiOiI3OTI5ZTRkYzdhYjM3Njc2ZWFiYjI5MWJiZDEwMDIwNWMwMzk1YjU4YTg1ZDAwMzBmOWRkYjUyMjBjMzYxMzhlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik1sWjNJMTZ2ODBGb3dYbHRyL2xjWFE9PSIsInZhbHVlIjoieXo2QnNiZTNwMDNJdUJSTy9hTXFzWjNOUlJwa1hlVmlxdVFsZ0Y2Ym1qaWE3RHRHZFNzZlE1YkhJbWhqb0NReHpNR04wRVhDSkNiZ3NUTHFYYUFTTm9SV2h3Wm90QVZSUXBPL3lUbS9mTFlvcXU0VEtEUHBVZ1JJdzJrVlJGcDM1b0NvN3hYOFkreW1LKzBvNWM3amZlWk9EdGhDenpEQWZCdEtGeUZ1c3ZMcDhDNlVqb0l0UUd1ZGZ1S0VKNllyQThSbmwxRXNHWDhXK0pmMjVSbjcyOW96U3VTd0dWUEdraGhqcXBydml6R1huTVRxblhxaUF4RUh0UVJTbmNTV3BBZ0I3YlorMjk4aDdlZVRnU0FyTDdEbDlrcWNLTmRvTUNwbDAySkhTUGI0ZndnQ0FFR1RTMzJYMS9XYXNZSGZ4cXVzNkIvVUYrd0hudUVoNk5jb2JXL1ZQK1l0aFp1bGkzL0dVazk5MXBFeWJ1bXd1d3k2eTJndlU5QkhHYkNCWTZhZW4xRmZXeTlCVHI3d3FWUmIxYXpVb0NZVmdXR2RkODhFb0E4U2ZLT2ZRK2tSVWNML1ZOUEthRDRjZWMzTkZ5enluRHUvNjVHbjM0M0l2MlFCb054OVhHbHA0RkZmeG8vUWtMRWZ1eC85d3FxMFNJZlRhdFpobXo1Nm9rN0oiLCJtYWMiOiI2NmUwNWVhNjI5ZDg4MzM5YTM3MTZlYzcyMjQyODVkMTYxNGI3ODcxNmFjZTM4ZTViN2UyN2U4ZWU4ZDVlZTM3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075632272\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-423813118 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423813118\", {\"maxDepth\":0})</script>\n"}}