{"__meta": {"id": "X250b83f88f05601670fe198f9112c05c", "datetime": "2025-07-30 06:12:36", "utime": **********.759836, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855955.636077, "end": **********.759876, "duration": 1.1237990856170654, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753855955.636077, "relative_start": 0, "end": **********.688291, "relative_end": **********.688291, "duration": 1.0522141456604004, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.688313, "relative_start": 1.****************, "end": **********.759879, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "71.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "x9FkLUTl8E1UmrK5bfDMxbeibF4BCDU9QwnEXeKI", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2126392896 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2126392896\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1340922544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1340922544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858577361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-858577361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-484535390 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484535390\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-757678517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-757678517\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1687355623 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:12:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhPejYxZVI5dldyWUNkMGhJcHdpY0E9PSIsInZhbHVlIjoidFQwVkI1cTM5WHY4OFFCUHk1Q0tYb3lpZ2JwK1dGUW4yMFFmZVEvaHVtK1ljcmVyckZjTWlsbkJPU1hwOHI2SVcwSFcwbFJkU1ZIUm9Ba3JLRWRnUkM0Ykg5NnpQN1RPOXpiQ0R5NG9rcE50RE8wbzFpQ3ZaUTJnT2dyVUZ3d0kyYmJtakRXRnBoRFdWdHlJMlU2K05CaUVTNlVyUGFUSUQ4YWFjSlI2dlc4bGREM251ckFGVjlkNUxQRUxodkc1bXZUVWJIT2VYeUhjYXNpSWZBQTlaeW9TSE1mMzRramxYREI0QjBVTE5hT3pQUDJkUzJmRnNVdE5iZXFaNEdqUlgwcWFVNnM4WTliU0tQb0JCSjVIUlh5S3JMVnpuUnBlbHZFQkFySjEvUHRwM1UvWU5tVU9VZUMzVFRGSWltNUdoeDFNVWJ5MXdvTHJQL1gvMDMwMGxHNTRMRlJkYmFZQm9Vdkl5YlpLeVhKMkpOWTNiSXFuZmM3WkV6N1N2R0dBL1llZHNqWEFCdnFBQm1HQjVuVHRSamdjTW9QRk9VdWgyNWkxRDJQemhnQkg4YTlJWDBOR0g1OUt3NGJ1YVYyaHJHTEJNNFJ1TjNVbmxtYlNqb0l1NzNyMDQ1M2IrVGRkKzJTa3lPTW9ZaXFFemk2TmlsU3ljSXowT2FzUFMxOW8iLCJtYWMiOiJjNGM5MWE4ZDdjODY1NmVkNjJjNThkZjY2MDYyMzU2MTQ0MTM2MWZjMzE2NzNmMTZhOWE0NTg5OGMyNzc5ZjE1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:12:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlVOcTlRRUhjYmhPWWhVVzNJbm9QbXc9PSIsInZhbHVlIjoiQkRscU1DbDFKU2grcDd1b0twbDlzeC9QMksrQmE3Q0p1Sm9EeXc4U25tZ2hBTXdnaWF0cXJ1WHQ2K0FLWDdqNzZkcjMvUWVjaFpuak9uMDBVbHIrL0MwNlBVcnlTdDZYbzNzMnN3bFRxV0haaUR6eU1FYmZ4NC9jZnFwc0srMk1qTVpqSUR6NkVGTVlydm5tWnE1WHFaM1lmYnZxMTIrNzVFYytRbVpzNll2MUxHeFh2WDhXWEVBZjhNcFJKZHlTMmFoTlUrdERpc1V5cUlSS3RXZlMvd2RoVGE5TDVqdzVUcEVMUHFkQTkzaGd0NDgvRXhDblBIUUk5ci9talpoQkpldEN2QnN3V0E5VDVmRXVuSzA4dVVjdE5TMEJjbWM3WkFibGkraXhEOW0rRjZQbDQxWCsyT0NaZWthb0dJQjQ3UkJjTnVQRHBxRjVqWm83Yys2NUJoYlEyRFdYWms5VGYvZDl6c2RQQlcyR3d5YVVJeHlUR3VlRHVBTTVWL2U0WHIrdzcvZHFWR01ocU5OWXhLRlNKdTE4dTltdHJBSVl4dHJ6Q3JCWG96NU1FVmVhRWx5bzlyZFJLTzFuQ3B6R3lVQTZ2MVpDVWlCQ2N6RGxMMHdjS0tsdndaZWZzMFFVZ1cyejZvb3k1RHdVN095eVF1ajlJcnlLaWxDZkZwUTYiLCJtYWMiOiIwYjAxMmRmMzA2NWE0ZWZmYTc1Y2NlNGVhYzY2MGZiYjIyYmFlNGRkMGVjOTQzOGUxOWVlODg1MjM0MGM3YzE0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:12:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhPejYxZVI5dldyWUNkMGhJcHdpY0E9PSIsInZhbHVlIjoidFQwVkI1cTM5WHY4OFFCUHk1Q0tYb3lpZ2JwK1dGUW4yMFFmZVEvaHVtK1ljcmVyckZjTWlsbkJPU1hwOHI2SVcwSFcwbFJkU1ZIUm9Ba3JLRWRnUkM0Ykg5NnpQN1RPOXpiQ0R5NG9rcE50RE8wbzFpQ3ZaUTJnT2dyVUZ3d0kyYmJtakRXRnBoRFdWdHlJMlU2K05CaUVTNlVyUGFUSUQ4YWFjSlI2dlc4bGREM251ckFGVjlkNUxQRUxodkc1bXZUVWJIT2VYeUhjYXNpSWZBQTlaeW9TSE1mMzRramxYREI0QjBVTE5hT3pQUDJkUzJmRnNVdE5iZXFaNEdqUlgwcWFVNnM4WTliU0tQb0JCSjVIUlh5S3JMVnpuUnBlbHZFQkFySjEvUHRwM1UvWU5tVU9VZUMzVFRGSWltNUdoeDFNVWJ5MXdvTHJQL1gvMDMwMGxHNTRMRlJkYmFZQm9Vdkl5YlpLeVhKMkpOWTNiSXFuZmM3WkV6N1N2R0dBL1llZHNqWEFCdnFBQm1HQjVuVHRSamdjTW9QRk9VdWgyNWkxRDJQemhnQkg4YTlJWDBOR0g1OUt3NGJ1YVYyaHJHTEJNNFJ1TjNVbmxtYlNqb0l1NzNyMDQ1M2IrVGRkKzJTa3lPTW9ZaXFFemk2TmlsU3ljSXowT2FzUFMxOW8iLCJtYWMiOiJjNGM5MWE4ZDdjODY1NmVkNjJjNThkZjY2MDYyMzU2MTQ0MTM2MWZjMzE2NzNmMTZhOWE0NTg5OGMyNzc5ZjE1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:12:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlVOcTlRRUhjYmhPWWhVVzNJbm9QbXc9PSIsInZhbHVlIjoiQkRscU1DbDFKU2grcDd1b0twbDlzeC9QMksrQmE3Q0p1Sm9EeXc4U25tZ2hBTXdnaWF0cXJ1WHQ2K0FLWDdqNzZkcjMvUWVjaFpuak9uMDBVbHIrL0MwNlBVcnlTdDZYbzNzMnN3bFRxV0haaUR6eU1FYmZ4NC9jZnFwc0srMk1qTVpqSUR6NkVGTVlydm5tWnE1WHFaM1lmYnZxMTIrNzVFYytRbVpzNll2MUxHeFh2WDhXWEVBZjhNcFJKZHlTMmFoTlUrdERpc1V5cUlSS3RXZlMvd2RoVGE5TDVqdzVUcEVMUHFkQTkzaGd0NDgvRXhDblBIUUk5ci9talpoQkpldEN2QnN3V0E5VDVmRXVuSzA4dVVjdE5TMEJjbWM3WkFibGkraXhEOW0rRjZQbDQxWCsyT0NaZWthb0dJQjQ3UkJjTnVQRHBxRjVqWm83Yys2NUJoYlEyRFdYWms5VGYvZDl6c2RQQlcyR3d5YVVJeHlUR3VlRHVBTTVWL2U0WHIrdzcvZHFWR01ocU5OWXhLRlNKdTE4dTltdHJBSVl4dHJ6Q3JCWG96NU1FVmVhRWx5bzlyZFJLTzFuQ3B6R3lVQTZ2MVpDVWlCQ2N6RGxMMHdjS0tsdndaZWZzMFFVZ1cyejZvb3k1RHdVN095eVF1ajlJcnlLaWxDZkZwUTYiLCJtYWMiOiIwYjAxMmRmMzA2NWE0ZWZmYTc1Y2NlNGVhYzY2MGZiYjIyYmFlNGRkMGVjOTQzOGUxOWVlODg1MjM0MGM3YzE0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:12:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687355623\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-153759624 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x9FkLUTl8E1UmrK5bfDMxbeibF4BCDU9QwnEXeKI</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153759624\", {\"maxDepth\":0})</script>\n"}}