{"__meta": {"id": "X1ab496caaecc035118087519c13380d4", "datetime": "2025-07-30 07:57:42", "utime": **********.967933, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:57:42] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.956402, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753862259.99891, "end": **********.968009, "duration": 2.9690990447998047, "duration_str": "2.97s", "measures": [{"label": "Booting", "start": 1753862259.99891, "relative_start": 0, "end": **********.444499, "relative_end": **********.444499, "duration": 2.445589065551758, "duration_str": "2.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.444544, "relative_start": 2.445634126663208, "end": **********.968031, "relative_end": 2.193450927734375e-05, "duration": 0.523486852645874, "duration_str": "523ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51792928, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.738618, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.06334000000000001, "accumulated_duration_str": "63.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.593299, "duration": 0.012150000000000001, "duration_str": "12.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 19.182}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.658194, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 19.182, "width_percent": 2.116}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 79 or `ch_messages`.`to_id` = 79 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.67221, "duration": 0.03309, "duration_str": "33.09ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 21.298, "width_percent": 52.242}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 79", "type": "query", "params": [], "bindings": ["client", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.712661, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 73.54, "width_percent": 2.321}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.766717, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 75.86, "width_percent": 2.179}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.791154, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 78.039, "width_percent": 1.721}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.802421, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.76, "width_percent": 3.031}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.817014, "duration": 0.0109, "duration_str": "10.9ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 82.791, "width_percent": 17.209}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 551, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1279661297 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1279661297\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1669326403 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1669326403\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1349269310 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349269310\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1224817433 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IktvWjFzbFVWSDkvcm1YVGpwUEpqbWc9PSIsInZhbHVlIjoiTUxBbkxkRW54cEJMa2JDQTc1ZTB2WDZ4VU1scEZ3WWpIUjIwSEtTYW0zNVdUOU5YZU9RNk1rdnN1bzdwSnZvSWJ3b0xSbFdxRWlWZTNRcUZFaDBNQWZuOHpqZ1pBNzdJSFN3amtxaG94L2poSVpyd1pHNDdlbEZJaUZoa0N1NmdCaUV5QlJRalY3WkZBY2Y5QjVPTnBGb292SHpVMDlJVG9vQVBVUGNIYW9Wa1pvbTVFcmNwZTVHZ25MYnNtRUJLQ1ExempxSjk2MWpTTjBzUFZLUXFXVWRDdjFlRURtRE0vNUFseGRjbllIVjBwUS9VVE9iaXVjRWhyK00xbkRkU3RZeXh3QVpucjBPSmxaMXJtNzQramh3cm5md1FqTEtybEFHUVFkMXRheXZhRVdLQ1JPajVwdlZjaGlIczFEZUFZT0lFdERkR2dFNUlNa0JnZ1NXNzFuR2ZxZEtYMEVvdnkyNGxLZ3dUWS93VnpidFJacGRGZUpHU2tKbWlVc0JTTFI4bjdrNmF2QlBPZXJiWGNWNjQ1YSswTGppM2lBeDhtM0JSeHJkcVN2OWN2Rk01UjFHTFo2OUV3NGFNT3FLY1lrQVRWUzBucmV3Um9pZ1pIakpQS1Z5Rlc4a2VFMG0rTHMyRXkwUnM5RkVXK3dlcHpJT0QxWW53anBRcCtGZUciLCJtYWMiOiJmMDcwZDYwZGQ3MjFmMzM4Mjg1YjRlYjJmYzBmNzM4NzYxMDVmMzk2YTViM2EwNDliNjc5ZWIzNDk1YTFmMjY1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlhxMElvMnhaSXJMbGFDRDVDbnZ0Umc9PSIsInZhbHVlIjoiY25RZnp6TU9ZaStxa3FiSUs4Ty9MbWFkZkRXOUt3M1JjS253WjVQaExTd1huaS95UlU1NStWSHJrNHBwVGNReXJybG83TFNLNFZMclptb09DU2x6WVNGYTVmZUxScnNIcmZvNm1wczV0a05kQ2pBdjlTODV0Z1M2Z0dqMEtXZ2JuSUxlZmRpVE9lOVhUdTlONHZWUTRQYVNNUmovSU1FNmh0aVEvbTc3Y2VGSk1zWWs2bEp0REpmRjkveHdXb0xLVHJ2ZFhoaU1jQVV1YUVzd2FDMit5b3hHVDRDUlJGdjVCeUtydU52dUhpQ0VxQ3pHbGFDd1dodEpSZE0yeUhzODRvWS9FRWhYSTRiWEdNT2lMUHNCQUhZK2hTSi9xcktSWmQvUTl0R1RSQ0c4SjdXVm1OQy94V2VyZ1hHSEdiZzg1bVRvS2ljYUZPeHZZZ3IzZVBXREN0QmRqSlFyRnBCSkg1Q2Jadm1KOWF1MFdIM2JSYjVoVmtCVnkrcktKdmszcVhMd3JBdzlGRFFFL3VvUTFFdVdKd1pBOVozUXpPTGhZR2RFbXQ1eUN0Yy9tRnNBOU1YTjBIMVIwTzZXT3g4TFFmeFFiVVg5UFBZYVBOUDVVMTJieFRxZkNMbHRZdTBxRVNUTFJINGhPWUc5Tm96cUtucnUyR1ltVUcvK25qZHgiLCJtYWMiOiJlY2RhMzk5MWRiMWYwMmM5ZTg0M2RlNjc5Zjg5YmU1MjY5ZDE5ZTUyYzUxNDVlY2Y2ODAyYTY3YWQxNDUyYzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224817433\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:57:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh0T1paTGR0eVM5L2p5dFJOR29PUmc9PSIsInZhbHVlIjoid25zL0xlcytVZlFlZFNrL2hTUGEvZGdaM3VwTkttQTlrYmxya2huRzRBUUNGMzR2bWVySmVTSHArWDlqMERnb01DaEFZeDNsanpQTTR4ZXNJelZEaEpIbGZZOE9hV0RHV3Y1d29pZmc0T2ttU1Nzamx6VFBMMEpLZmJBWWNVTDZzcFNJMms5eHgrMEVBMWZ5dEdZV3dybDFMbm5KaUZoUCtqTFpCT2cxZlhtaUZmdEkrMS9pSzBQRjgrMmVoSlBIeE5yMHBabDJZbDAvZTVXSmw1aHhCUG1sdlJnRGFORUFoajVIM1VEejhhU3BKT09BS003ZkpCWWlmVTR5amtUbHpFYUw5RWhnWU5qMXd6U0drN2laVzc5U0RsTHlhRUhmNXBVakNsQjN1UHZGdCtFTWdaSFJSeHBFMWc0VGpJUUwxTWlnQ1c2bVlGNVVtelowczg4TWVNYmRPT0VISGdlUDRSVmNoUnF0R2xFV1JvLzM4VFN2ZGc0QkZpTy9PUHRmYWM1RHNyaGJKeEgrWXNhR3BPWEwxdElCKzJ0dzhOQXErVE9qRVNxT2lOQVV0NFMzeElwZEFETG92YnZXTUFqYytZTUZJdHNsU0lHNm5FQW54YU92VVIyWmR1RXV3VWZNQ3Y4K01pVE1EWDl5UXNTZGZQaWdiZDhtM3JkQzB6cksiLCJtYWMiOiIzMjEwOTMxZjBjN2E0MzUwOTE5Y2JjNGRkZjIxOTc3ODBjNWVjMDc2OWE5NmFkOTA2ZTIxYzEyNTQzNWM2MDQyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:57:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IndWL0ZDSUtaaXR5ODFxTjc2OWltS0E9PSIsInZhbHVlIjoiTWJKaHl3ZXo5SllSSEllSGNNL0RzL1RTQXE4djVCbG5yaUhPYkhubCt2WGh5dGRWVU4rRGRCenpIdXRkTXZsVjlNWGJpMW5RVlBQSkNVcC9NcmROenFINUNHL3VYUU03UEtuRDNkSWI2TmhUenMyVis4RGJ4QmI3MFlPNk56ZCtkNjd2NzhQZjllc1piditrQnU5UlVreFlUajNVSUJUaWx0dnVzT1RIbk44dkthYmM5cjkyUDdMaXVzL2Q1VzJMU2tXcGNheWFFczRRV2s4TFczRVJmVU1aRnhjTmg5Zmx3RTFXMVd4VENNQXA5QUgvWkxDa3EzWjRQVnFmZmxYTHRHL1gyMkVWRXZTN2RNcmFkOW1rT2pKK1prcHIyNFEzc1hiaUNvMUVQVWZoU0M1RWtaUzA5a3NldkJvZ01KRGZCbXlGMC9oUXVmSGJHSGlsZnZjd2RqRWV6R08zUXRZZXZSeWtoRmtZZFluY01wa1RSeDNGNDZyMzA3YnBKQzBiNFRwZExZdnpwcUVMQlk5THRrekVyT0pERUVlTkNKRktDVnM3TENIRjYwTWY3aUZQc2gyZndkL0xPSVVMUlZMWHZLZlNaZHU0R0NkRS9SdklWcndFdE1NREZnSWZoSEVldUtvWmdBQ0dnR0hpTEY2djUxVGhQc2RTQlRqKzZLM1QiLCJtYWMiOiI3NjQ4ZTRhNGExYzQxZjc0NmNlNjg5OTFkOTFhYmIyNTAyZDA0MjM3ZjhmODE2NGVlOTY0ZWU2NTU2YzRjYjFhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:57:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh0T1paTGR0eVM5L2p5dFJOR29PUmc9PSIsInZhbHVlIjoid25zL0xlcytVZlFlZFNrL2hTUGEvZGdaM3VwTkttQTlrYmxya2huRzRBUUNGMzR2bWVySmVTSHArWDlqMERnb01DaEFZeDNsanpQTTR4ZXNJelZEaEpIbGZZOE9hV0RHV3Y1d29pZmc0T2ttU1Nzamx6VFBMMEpLZmJBWWNVTDZzcFNJMms5eHgrMEVBMWZ5dEdZV3dybDFMbm5KaUZoUCtqTFpCT2cxZlhtaUZmdEkrMS9pSzBQRjgrMmVoSlBIeE5yMHBabDJZbDAvZTVXSmw1aHhCUG1sdlJnRGFORUFoajVIM1VEejhhU3BKT09BS003ZkpCWWlmVTR5amtUbHpFYUw5RWhnWU5qMXd6U0drN2laVzc5U0RsTHlhRUhmNXBVakNsQjN1UHZGdCtFTWdaSFJSeHBFMWc0VGpJUUwxTWlnQ1c2bVlGNVVtelowczg4TWVNYmRPT0VISGdlUDRSVmNoUnF0R2xFV1JvLzM4VFN2ZGc0QkZpTy9PUHRmYWM1RHNyaGJKeEgrWXNhR3BPWEwxdElCKzJ0dzhOQXErVE9qRVNxT2lOQVV0NFMzeElwZEFETG92YnZXTUFqYytZTUZJdHNsU0lHNm5FQW54YU92VVIyWmR1RXV3VWZNQ3Y4K01pVE1EWDl5UXNTZGZQaWdiZDhtM3JkQzB6cksiLCJtYWMiOiIzMjEwOTMxZjBjN2E0MzUwOTE5Y2JjNGRkZjIxOTc3ODBjNWVjMDc2OWE5NmFkOTA2ZTIxYzEyNTQzNWM2MDQyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:57:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IndWL0ZDSUtaaXR5ODFxTjc2OWltS0E9PSIsInZhbHVlIjoiTWJKaHl3ZXo5SllSSEllSGNNL0RzL1RTQXE4djVCbG5yaUhPYkhubCt2WGh5dGRWVU4rRGRCenpIdXRkTXZsVjlNWGJpMW5RVlBQSkNVcC9NcmROenFINUNHL3VYUU03UEtuRDNkSWI2TmhUenMyVis4RGJ4QmI3MFlPNk56ZCtkNjd2NzhQZjllc1piditrQnU5UlVreFlUajNVSUJUaWx0dnVzT1RIbk44dkthYmM5cjkyUDdMaXVzL2Q1VzJMU2tXcGNheWFFczRRV2s4TFczRVJmVU1aRnhjTmg5Zmx3RTFXMVd4VENNQXA5QUgvWkxDa3EzWjRQVnFmZmxYTHRHL1gyMkVWRXZTN2RNcmFkOW1rT2pKK1prcHIyNFEzc1hiaUNvMUVQVWZoU0M1RWtaUzA5a3NldkJvZ01KRGZCbXlGMC9oUXVmSGJHSGlsZnZjd2RqRWV6R08zUXRZZXZSeWtoRmtZZFluY01wa1RSeDNGNDZyMzA3YnBKQzBiNFRwZExZdnpwcUVMQlk5THRrekVyT0pERUVlTkNKRktDVnM3TENIRjYwTWY3aUZQc2gyZndkL0xPSVVMUlZMWHZLZlNaZHU0R0NkRS9SdklWcndFdE1NREZnSWZoSEVldUtvWmdBQ0dnR0hpTEY2djUxVGhQc2RTQlRqKzZLM1QiLCJtYWMiOiI3NjQ4ZTRhNGExYzQxZjc0NmNlNjg5OTFkOTFhYmIyNTAyZDA0MjM3ZjhmODE2NGVlOTY0ZWU2NTU2YzRjYjFhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:57:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}