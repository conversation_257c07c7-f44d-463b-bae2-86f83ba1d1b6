{"__meta": {"id": "X07d343ed29c15117810916a82294ab29", "datetime": "2025-07-30 06:09:13", "utime": **********.247071, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855752.174325, "end": **********.247099, "duration": 1.0727739334106445, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1753855752.174325, "relative_start": 0, "end": **********.170255, "relative_end": **********.170255, "duration": 0.9959299564361572, "duration_str": "996ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.170269, "relative_start": 0.****************, "end": **********.247101, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "76.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2WuNpGTlprodUs3oEgVpI8Vr7TFqaC0AJ0otkAkJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1173622372 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1173622372\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1295609303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1295609303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048268754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048268754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1863228916 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863228916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1571766601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1571766601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1590606219 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:09:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFGTDF6UXNEbExuQzl0K21hbm14eEE9PSIsInZhbHVlIjoiS3JJeGZQR0MweEVESFh2VFFYa3lkaVNpeFNKaFFJeVFZU1hjU2dETy8rVHJ0cTR5bEp5Y3lITHFIU1lnRXpPM2lEVGtVWHZ3RHdVTi8xZjkxYzlBK0c3cFRvMTFtSWR3bmVkbWJCVFNJckJWMENlbWRRTEpHVHUwTk1WRERRdXRKNjdRQ0NNenZUanorK2VqelJXMkVXNjJDcXdEekozQjVqUXpmdUZFRU45R3VKb0Vrc0dVdVQ4TXlzYjBjcFZUUEFVQ2FwcWZ6TWo3ZFlpaUw2cVVROXBRbk9mdXFYRXJxTXZSWlZpVVRNQkRKZmE5M3FUTFV2blpoaVZxcFQ0MGZkUVNEQkM0OTFCbWZoZTNyV1Q1b3JTcEFDSzZwODJLc1ZmVjFLVWNYejVkK1NHREdHTmhyazZJcEVuOHNGdUNmN1RQUmJtNnh5akh2cEx2TGUxZkZZUUxPN2JEOUd6aG5lc2drbjNlbjhlbzNUN29NUEtoYlh5UjdvbWRNVDZsdmQ0WndxdHVvSUd3MExmSTY3VjRJYzhtN2R6VC84NjNtVG4wMk1JYVU5bXZpVDUxRHp6REpHZGJ5NTh2bVpOb3dvQnJ4dFU3VE0xRzd6YSs5RVRlcjUzQW43cVlGTEJ1Y3JzaitabzB3R2VDM1dvMzdVQzJCMXcydWJkd3dCQzEiLCJtYWMiOiI3NzM3ZDc5Mzg4NzQzZTczZWEyMjBjNDMzZTcxZGYxMTM0YjBhYWJlYzUzYmRkODZjZDNiYTJkZmNmNTRmZTY1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:09:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZsZ29pWSt3WGFsc20rS2FDdmpWRlE9PSIsInZhbHVlIjoiQTkwQUNCSXRlQ2VqaUVhT3FkT2RBYlN3WVMzL2RRcTNlU0F4OVB6aWNSTHoyRzJjYmgwMlBwSXcyUVlQTXNlSnMxM1ZLaVcyclhRbDB6bWZtYzhxVi9vMm1Kc2FCa2ZzM1ZXbW5sNGlBdlZHekhmMlNYSGpGL3hISEs2SHdrTng5Y0p2dGc0cGtiVUFwZk5HQkFoT2tYR3p1dmxlTGpWZjA4TldxQ2JuelE1ZkdhM013UUVtazdGS3ZBalB4bE93WEhhSFN2ZFB0eGZOaTd4MFlXUE9QRmduSVhINEVwa1ZCOWpxa3dhUDliQVlpcjdSV2pueStUNCtFRXRwUWNLamxHZWE2aS9KWTBweXpDUHZhNTJ2cVBnT2FoSDhtQmtSWVArVUxjcGViT2RiT1YxTlJUQUo2Nm11aElyOElWdXFzQ21VbkUzQU15UkE0SkhvYnBtaEM5aDM1K2Q3ODY3NXE4UWltUXdHd3dvRWFaU0JjdkJHdit6c2VEYTNFK1JGdFRVRzFZaElydVI0U0J2QUo5Q0hNLysvMkpDRzZQa1QwOUg1OXZPTjFFZ3BZbi90b0NJQ2VBTXNoTWZDcnZuYVpwK3N0K0tJc05iWGMzQXRKbjFaRkFodFNnYzVSZ213bklkeGhyeDk2RVJ3UVByZys0aDdhaTBFNy9vbFIvMnIiLCJtYWMiOiIzOGYzNzEyMjVlMWMxNTNkYzFhNTkzYzc3NzA1NDcwNmRhMjM2MWRmZGU2MGFhZDRjOWE3NTYyNDJmNWRlZTUxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:09:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFGTDF6UXNEbExuQzl0K21hbm14eEE9PSIsInZhbHVlIjoiS3JJeGZQR0MweEVESFh2VFFYa3lkaVNpeFNKaFFJeVFZU1hjU2dETy8rVHJ0cTR5bEp5Y3lITHFIU1lnRXpPM2lEVGtVWHZ3RHdVTi8xZjkxYzlBK0c3cFRvMTFtSWR3bmVkbWJCVFNJckJWMENlbWRRTEpHVHUwTk1WRERRdXRKNjdRQ0NNenZUanorK2VqelJXMkVXNjJDcXdEekozQjVqUXpmdUZFRU45R3VKb0Vrc0dVdVQ4TXlzYjBjcFZUUEFVQ2FwcWZ6TWo3ZFlpaUw2cVVROXBRbk9mdXFYRXJxTXZSWlZpVVRNQkRKZmE5M3FUTFV2blpoaVZxcFQ0MGZkUVNEQkM0OTFCbWZoZTNyV1Q1b3JTcEFDSzZwODJLc1ZmVjFLVWNYejVkK1NHREdHTmhyazZJcEVuOHNGdUNmN1RQUmJtNnh5akh2cEx2TGUxZkZZUUxPN2JEOUd6aG5lc2drbjNlbjhlbzNUN29NUEtoYlh5UjdvbWRNVDZsdmQ0WndxdHVvSUd3MExmSTY3VjRJYzhtN2R6VC84NjNtVG4wMk1JYVU5bXZpVDUxRHp6REpHZGJ5NTh2bVpOb3dvQnJ4dFU3VE0xRzd6YSs5RVRlcjUzQW43cVlGTEJ1Y3JzaitabzB3R2VDM1dvMzdVQzJCMXcydWJkd3dCQzEiLCJtYWMiOiI3NzM3ZDc5Mzg4NzQzZTczZWEyMjBjNDMzZTcxZGYxMTM0YjBhYWJlYzUzYmRkODZjZDNiYTJkZmNmNTRmZTY1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:09:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZsZ29pWSt3WGFsc20rS2FDdmpWRlE9PSIsInZhbHVlIjoiQTkwQUNCSXRlQ2VqaUVhT3FkT2RBYlN3WVMzL2RRcTNlU0F4OVB6aWNSTHoyRzJjYmgwMlBwSXcyUVlQTXNlSnMxM1ZLaVcyclhRbDB6bWZtYzhxVi9vMm1Kc2FCa2ZzM1ZXbW5sNGlBdlZHekhmMlNYSGpGL3hISEs2SHdrTng5Y0p2dGc0cGtiVUFwZk5HQkFoT2tYR3p1dmxlTGpWZjA4TldxQ2JuelE1ZkdhM013UUVtazdGS3ZBalB4bE93WEhhSFN2ZFB0eGZOaTd4MFlXUE9QRmduSVhINEVwa1ZCOWpxa3dhUDliQVlpcjdSV2pueStUNCtFRXRwUWNLamxHZWE2aS9KWTBweXpDUHZhNTJ2cVBnT2FoSDhtQmtSWVArVUxjcGViT2RiT1YxTlJUQUo2Nm11aElyOElWdXFzQ21VbkUzQU15UkE0SkhvYnBtaEM5aDM1K2Q3ODY3NXE4UWltUXdHd3dvRWFaU0JjdkJHdit6c2VEYTNFK1JGdFRVRzFZaElydVI0U0J2QUo5Q0hNLysvMkpDRzZQa1QwOUg1OXZPTjFFZ3BZbi90b0NJQ2VBTXNoTWZDcnZuYVpwK3N0K0tJc05iWGMzQXRKbjFaRkFodFNnYzVSZ213bklkeGhyeDk2RVJ3UVByZys0aDdhaTBFNy9vbFIvMnIiLCJtYWMiOiIzOGYzNzEyMjVlMWMxNTNkYzFhNTkzYzc3NzA1NDcwNmRhMjM2MWRmZGU2MGFhZDRjOWE3NTYyNDJmNWRlZTUxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:09:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590606219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1149800782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2WuNpGTlprodUs3oEgVpI8Vr7TFqaC0AJ0otkAkJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149800782\", {\"maxDepth\":0})</script>\n"}}