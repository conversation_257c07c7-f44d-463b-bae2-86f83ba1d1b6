{"__meta": {"id": "Xe627e5ab82ef19fa33042ddea1f62839", "datetime": "2025-07-30 06:06:15", "utime": **********.612609, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753855574.528161, "end": **********.612649, "duration": 1.0844879150390625, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1753855574.528161, "relative_start": 0, "end": **********.514176, "relative_end": **********.514176, "duration": 0.9860148429870605, "duration_str": "986ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.514193, "relative_start": 0.****************, "end": **********.612651, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "98.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wo7H1JuemQBE6xMq8xrWVyPJVivcUqi02nsseSn7", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-16272577 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-16272577\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1978914321 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1978914321\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1319122231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1319122231\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1424932240 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424932240\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-823077602 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-823077602\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1767810727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:06:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndGUUJCbWVQNkZJcFNrVHBRUEhmQVE9PSIsInZhbHVlIjoiTVlKNVkxa25rckZHY3VGeE1NdDAzeHZnSVE4OXpoVjB6NFVYTGZwZUNUMStwazF0SERPMUMrSzhOYVdlcVJMM1g0YUpiYUpOQ3ZZVm95SCtlZFhVZTc4QnNvR2ZYTUcxRUxmZ2xPTTNxcmQvREIzMTFJYmVWQXlJUkRrZC9ZclZKcXEwQVNpMlpxOXFZeERzajYrRzJ2VmcyK2d4TlhEUmVscHg3bFRtUi8xN01QYU9sSGxsSGVlQk9kYmk0eGpraGI3K1Y3Yk94anNQeldZUVZGL3hFSDlpRVdzeEVXQklsKzhPZHVuTlBPOGg4Um00dVhEMnJhdzhzTzZEeDgzeDkrZVNMbUxoakZza0ZQbmNIdTNNallzVDhEMGg0a20wRU9qTDNMckZxVUlPRkJiZnY2Zmwxb3NsUE4vMmVva1VrUUdCV0I4TkV0eHh2U29pZ1p1Snk0K3Zqa0ZTME9nUHlPdHhud3FVd1AwVGgvSENuN0MrcGxWQk9ISWkrVVFXQkdZeHlsOHFqRW9OR2l0ZnBkbVBGU1ZKc1ZaanYrL0FLTmtTdFNIZ3JOb1J2TEM1c05rM0IvN3E4Zy9yYmUxa3ZucUxSS21Ma0x2L3krdVQ0bnN6c0hab0g0amRFWDNVYWNaand4VlZ4WmNqdEpxTStHeFRMTCtWV0IxZWlTdzciLCJtYWMiOiI3ZDU3OGM2MjVjM2JiNTVkYThiZTZkZjEwYzYyNWRhYmUwZTMwOTkwM2ViMmU2NWNlOTE1MGJiNWFiMTc0NzRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InFXbEtXTDkwREk1bXo3QTd3MmV0eUE9PSIsInZhbHVlIjoiQkJMTEtXRmxxREJIcEwrMWhwTE9CT21mcXJQckx2TEczRTFSNGJjSlNrV1hLbW9xWUx3RTdLdXY2bWlKU3JNeFpRWE5pUUNXK0VLZzZoY3NKOVdvVUFTQ1M4V3FNVERKbUhRazNsSXRMZ1ovZ0JUMkhvMzExRThxSnpyUUdWa0pTUzNLQjBSM1pWQmt6QWMxL0QwcnMxVUo1TEIvSTlxVzRGZnFScklPbkx6c1B0c2crRHJINnVsOGtJODh0TUVGRmY3VFJGcUJMdXlvSTdmcUZxNGQxajVkVHdHSGUycjg4SlR0cWlGOXJpdDM5UXNqTGdzUnpwd1FJN0RnL1VUYmptdUROVFJtL1VpMERpVktYVVJLLzBlQ0FjTDBHY2EwekIwZkRlOWR0UzFxS2NVNTVVbHlSRkNITm9kVHRLSDBSRUNiakpPQnVxU0c0dEY4bHVQZ2lSTjA5QXBNM0NxclBJM0V1SkpGc0xOWVVuMGVUY2RzdUJJYU5icHBGeHdoR05rdDRPZW5lYWYxZHJqQkJSNWNvVDlNWG1JRTVySDBSc3grRXpNbXMvVUhOcGpmaHNlN21OT29tNmZqb05mVTlQMlV4OEhIQTNnODhvM05IVTMvV05WWFV3bzhXZDNYZ3ZPS1VUZGh4Z2kvN2VXV3FycFpmb2M5OWloWFZMOVQiLCJtYWMiOiIxYmJhODg4YzlhOWIxN2U3MTYyNTNkYjNlY2E0Yjg0MjRmZDFlOGQwM2RiOWJiMGQ0MTIyNWU3NTFjNTgzNjVlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:06:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndGUUJCbWVQNkZJcFNrVHBRUEhmQVE9PSIsInZhbHVlIjoiTVlKNVkxa25rckZHY3VGeE1NdDAzeHZnSVE4OXpoVjB6NFVYTGZwZUNUMStwazF0SERPMUMrSzhOYVdlcVJMM1g0YUpiYUpOQ3ZZVm95SCtlZFhVZTc4QnNvR2ZYTUcxRUxmZ2xPTTNxcmQvREIzMTFJYmVWQXlJUkRrZC9ZclZKcXEwQVNpMlpxOXFZeERzajYrRzJ2VmcyK2d4TlhEUmVscHg3bFRtUi8xN01QYU9sSGxsSGVlQk9kYmk0eGpraGI3K1Y3Yk94anNQeldZUVZGL3hFSDlpRVdzeEVXQklsKzhPZHVuTlBPOGg4Um00dVhEMnJhdzhzTzZEeDgzeDkrZVNMbUxoakZza0ZQbmNIdTNNallzVDhEMGg0a20wRU9qTDNMckZxVUlPRkJiZnY2Zmwxb3NsUE4vMmVva1VrUUdCV0I4TkV0eHh2U29pZ1p1Snk0K3Zqa0ZTME9nUHlPdHhud3FVd1AwVGgvSENuN0MrcGxWQk9ISWkrVVFXQkdZeHlsOHFqRW9OR2l0ZnBkbVBGU1ZKc1ZaanYrL0FLTmtTdFNIZ3JOb1J2TEM1c05rM0IvN3E4Zy9yYmUxa3ZucUxSS21Ma0x2L3krdVQ0bnN6c0hab0g0amRFWDNVYWNaand4VlZ4WmNqdEpxTStHeFRMTCtWV0IxZWlTdzciLCJtYWMiOiI3ZDU3OGM2MjVjM2JiNTVkYThiZTZkZjEwYzYyNWRhYmUwZTMwOTkwM2ViMmU2NWNlOTE1MGJiNWFiMTc0NzRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InFXbEtXTDkwREk1bXo3QTd3MmV0eUE9PSIsInZhbHVlIjoiQkJMTEtXRmxxREJIcEwrMWhwTE9CT21mcXJQckx2TEczRTFSNGJjSlNrV1hLbW9xWUx3RTdLdXY2bWlKU3JNeFpRWE5pUUNXK0VLZzZoY3NKOVdvVUFTQ1M4V3FNVERKbUhRazNsSXRMZ1ovZ0JUMkhvMzExRThxSnpyUUdWa0pTUzNLQjBSM1pWQmt6QWMxL0QwcnMxVUo1TEIvSTlxVzRGZnFScklPbkx6c1B0c2crRHJINnVsOGtJODh0TUVGRmY3VFJGcUJMdXlvSTdmcUZxNGQxajVkVHdHSGUycjg4SlR0cWlGOXJpdDM5UXNqTGdzUnpwd1FJN0RnL1VUYmptdUROVFJtL1VpMERpVktYVVJLLzBlQ0FjTDBHY2EwekIwZkRlOWR0UzFxS2NVNTVVbHlSRkNITm9kVHRLSDBSRUNiakpPQnVxU0c0dEY4bHVQZ2lSTjA5QXBNM0NxclBJM0V1SkpGc0xOWVVuMGVUY2RzdUJJYU5icHBGeHdoR05rdDRPZW5lYWYxZHJqQkJSNWNvVDlNWG1JRTVySDBSc3grRXpNbXMvVUhOcGpmaHNlN21OT29tNmZqb05mVTlQMlV4OEhIQTNnODhvM05IVTMvV05WWFV3bzhXZDNYZ3ZPS1VUZGh4Z2kvN2VXV3FycFpmb2M5OWloWFZMOVQiLCJtYWMiOiIxYmJhODg4YzlhOWIxN2U3MTYyNTNkYjNlY2E0Yjg0MjRmZDFlOGQwM2RiOWJiMGQ0MTIyNWU3NTFjNTgzNjVlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:06:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767810727\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1577741272 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wo7H1JuemQBE6xMq8xrWVyPJVivcUqi02nsseSn7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577741272\", {\"maxDepth\":0})</script>\n"}}