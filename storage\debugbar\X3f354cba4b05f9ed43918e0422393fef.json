{"__meta": {"id": "X3f354cba4b05f9ed43918e0422393fef", "datetime": "2025-07-30 08:25:37", "utime": **********.462438, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863935.454169, "end": **********.462514, "duration": 2.008344888687134, "duration_str": "2.01s", "measures": [{"label": "Booting", "start": 1753863935.454169, "relative_start": 0, "end": **********.267608, "relative_end": **********.267608, "duration": 1.813438892364502, "duration_str": "1.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.267634, "relative_start": 1.***************, "end": **********.462519, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "917h0ilmzlzHkxYAePZSJkf8pbtf0AHLtbDtxlAE", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1376300936 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1376300936\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1532134228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532134228\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1881353055 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1881353055\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1292221825 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292221825\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-680821541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-680821541\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1287486729 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:25:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVyckdGdyt4QzZWcVdLU0lCSy9lL0E9PSIsInZhbHVlIjoiYWp4U29UREMyTW5GdGZRTElhcks2YXBhSU9aK1A4V3F1bXZMbjBlQjZPVUF4dXFKMEdTMzJLOE9mbnJrcHJVUXVnZm5UcUNxaU0vajF5YXJkd1JKbUVTbkhaYXRacDJ3cmsxeERPSDl3RFcvMU9iLzJHUm9xaHVhM09xeVlSalVma3BORHZUQklTQ2RDaDBrUzU4UmJQSlVMY3FEMDIzb2Q2bVU4WVdpZkVNcFB3aC96cGZOQzdnQTJUWUtxWERCQUMvbGhna0ZRazA5eU9vOUpWcUROY0c1aHBGYnUvd2pxd2YvWS9MVmxQNHdOSkk4UU1YY1BCN1B2cXFIMGtRU2ZzQ2N5NFYyNEpuaWtHUUtMa0pEWVkxRFE0YndKZ0R4SkxkMTJWZDVqSzkyWmllWGlOa1RtNVhzR3NNcURIalhiaDE5NGpRSEJJcEdYZUZaa0IvUXhaYkdNeEZNcDZXcmJzZG9FQWpBd0VoQ0dWNlY4YkdqNjdXTXZaeU85L3pJaWZaVktZZFZibDNQMGpmcGVialcxbFJIMXhVY1hqSWFkZ0I0NC9oc0RnSVVpRjZZZ0xPTTZ0Uy9JNTRvSUVmNGN2TkVRT1o1QTNGN21SZXVwQm1qbnh4dnRQMkhDa0twWUtUOHAzWUJBamorT1RDZEowdDdraDJaRTBYT25NWTEiLCJtYWMiOiJiMWY2YzFmYmU1M2FjOGQ4MDA1NDgwMzk5YjFiY2RjMGQ3ZTIxODBhOGQwZmY3MWUwNWU3ZjM1YjM3YjYyZDIxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:25:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5UdnIzcmk5akpQV3BaSUFUYjdRZVE9PSIsInZhbHVlIjoiaE4wS3B5aHlSOThNQnlMcTBGdlhVMGZYdmErODRBRnl6UGhRK2E1eXBLT0NWK201Mm5RL3FCc2RCeGNCTUQzWi9lTUlZZU15K3M2OXc5RHhNdlhBOHNUd1FBUXREeDAwMTBJUmN1TFRqMTFESGNWWnNuSEdRbmlMRjV1NUdobVk4ZEdnVEtmNkZTTlVuQWFCeW1RaGgyYnpnOFhnU0NKU2pYZGNSVWRURHNBM1VLK243R3pCUXV2S01PMnVURy9wc0xRL2JxUk5XY29LUHlZSEZPWWxUaERQRmxobW0ydGs1SGdxMnR5N2F6RVJibkQ3akkvbmVoVVRGaFFKTjJyY3FhcDdlTXdoRUI0RVNBQUhETGcvR0s0WER2UUt3WHB1VitnMjhjcTRUOHNYNW42N0c0MEZZRUF2b2xyYkpUdnhMSitHNk5pZW5zb1U3VkpTSmVUM0ZaWGFwaUQvbTB2WGZSb2ZtbXdPc3dyUkpoWk1Ma0ZFSVoyNGE4YUdWOU9mT0h5MU1KTTRwRzFyQy9kUWsvNW1vd2tVZlp2U0lCLzJUeTZ5MU5jUGFtbTRaa0dMNDNWLzJudkxVR1dKRTRFc2F4NEw0UjBWSFpRNFZVSmZBSUF1dHlVbE5UR0hHbVBsZ1NidzQxRnlmN0hQYXNwUElmWEQrbXFZRDZiR3Q4d3EiLCJtYWMiOiJkZWZlNWNmYmRmNWE0NjI4ZWZjZDQ3MTlhY2U3ZjljODk4MzM0NDNmZWRhM2VhYjA4Y2Q3ZjY2NGJlMDAzZWM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:25:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVyckdGdyt4QzZWcVdLU0lCSy9lL0E9PSIsInZhbHVlIjoiYWp4U29UREMyTW5GdGZRTElhcks2YXBhSU9aK1A4V3F1bXZMbjBlQjZPVUF4dXFKMEdTMzJLOE9mbnJrcHJVUXVnZm5UcUNxaU0vajF5YXJkd1JKbUVTbkhaYXRacDJ3cmsxeERPSDl3RFcvMU9iLzJHUm9xaHVhM09xeVlSalVma3BORHZUQklTQ2RDaDBrUzU4UmJQSlVMY3FEMDIzb2Q2bVU4WVdpZkVNcFB3aC96cGZOQzdnQTJUWUtxWERCQUMvbGhna0ZRazA5eU9vOUpWcUROY0c1aHBGYnUvd2pxd2YvWS9MVmxQNHdOSkk4UU1YY1BCN1B2cXFIMGtRU2ZzQ2N5NFYyNEpuaWtHUUtMa0pEWVkxRFE0YndKZ0R4SkxkMTJWZDVqSzkyWmllWGlOa1RtNVhzR3NNcURIalhiaDE5NGpRSEJJcEdYZUZaa0IvUXhaYkdNeEZNcDZXcmJzZG9FQWpBd0VoQ0dWNlY4YkdqNjdXTXZaeU85L3pJaWZaVktZZFZibDNQMGpmcGVialcxbFJIMXhVY1hqSWFkZ0I0NC9oc0RnSVVpRjZZZ0xPTTZ0Uy9JNTRvSUVmNGN2TkVRT1o1QTNGN21SZXVwQm1qbnh4dnRQMkhDa0twWUtUOHAzWUJBamorT1RDZEowdDdraDJaRTBYT25NWTEiLCJtYWMiOiJiMWY2YzFmYmU1M2FjOGQ4MDA1NDgwMzk5YjFiY2RjMGQ3ZTIxODBhOGQwZmY3MWUwNWU3ZjM1YjM3YjYyZDIxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:25:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5UdnIzcmk5akpQV3BaSUFUYjdRZVE9PSIsInZhbHVlIjoiaE4wS3B5aHlSOThNQnlMcTBGdlhVMGZYdmErODRBRnl6UGhRK2E1eXBLT0NWK201Mm5RL3FCc2RCeGNCTUQzWi9lTUlZZU15K3M2OXc5RHhNdlhBOHNUd1FBUXREeDAwMTBJUmN1TFRqMTFESGNWWnNuSEdRbmlMRjV1NUdobVk4ZEdnVEtmNkZTTlVuQWFCeW1RaGgyYnpnOFhnU0NKU2pYZGNSVWRURHNBM1VLK243R3pCUXV2S01PMnVURy9wc0xRL2JxUk5XY29LUHlZSEZPWWxUaERQRmxobW0ydGs1SGdxMnR5N2F6RVJibkQ3akkvbmVoVVRGaFFKTjJyY3FhcDdlTXdoRUI0RVNBQUhETGcvR0s0WER2UUt3WHB1VitnMjhjcTRUOHNYNW42N0c0MEZZRUF2b2xyYkpUdnhMSitHNk5pZW5zb1U3VkpTSmVUM0ZaWGFwaUQvbTB2WGZSb2ZtbXdPc3dyUkpoWk1Ma0ZFSVoyNGE4YUdWOU9mT0h5MU1KTTRwRzFyQy9kUWsvNW1vd2tVZlp2U0lCLzJUeTZ5MU5jUGFtbTRaa0dMNDNWLzJudkxVR1dKRTRFc2F4NEw0UjBWSFpRNFZVSmZBSUF1dHlVbE5UR0hHbVBsZ1NidzQxRnlmN0hQYXNwUElmWEQrbXFZRDZiR3Q4d3EiLCJtYWMiOiJkZWZlNWNmYmRmNWE0NjI4ZWZjZDQ3MTlhY2U3ZjljODk4MzM0NDNmZWRhM2VhYjA4Y2Q3ZjY2NGJlMDAzZWM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:25:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287486729\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1711358350 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">917h0ilmzlzHkxYAePZSJkf8pbtf0AHLtbDtxlAE</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711358350\", {\"maxDepth\":0})</script>\n"}}