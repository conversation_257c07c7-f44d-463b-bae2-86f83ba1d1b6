{"__meta": {"id": "Xb7a4acbf49da92e1ceba8ee4b7cf0e08", "datetime": "2025-07-30 08:02:06", "utime": **********.10913, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862524.436964, "end": **********.109169, "duration": 1.6722049713134766, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1753862524.436964, "relative_start": 0, "end": 1753862525.934669, "relative_end": 1753862525.934669, "duration": 1.4977049827575684, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862525.934693, "relative_start": 1.4977290630340576, "end": **********.109174, "relative_end": 5.0067901611328125e-06, "duration": 0.17448091506958008, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0052699999999999995, "accumulated_duration_str": "5.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.059351, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 61.29}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.084078, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 61.29, "width_percent": 22.96}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.093096, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 84.25, "width_percent": 15.75}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-2018914243 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2018914243\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-997300418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-997300418\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-944421405 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-944421405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-177152377 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InVRazNuaGlrc1dZeVBNd3A2T1J2R3c9PSIsInZhbHVlIjoidklqVXFTbExhdi9WY3M1NVYwNkN4L1FkSkZxcmNwMHl4Ly93d3JDZXlzTnpJZjhCbHd6dEV3azlwMzhwQk1lWlNpWjR0dGdIWnBjYzVxZXhnVnk5RkZFTnBmbXlxNkZDaGY4bFBlRjczTVN3OXc3ZnFHek5RTjFhbEoyWTU3S1ZtckFGOU9STTUxRTJiYmdxM1V0WmIxNGo1UWFjN3hJdGVtVk1IanR2dWQ5emxIOFh6MUVNeEV1dmkwK084cUozUHBBQWI3dlF5WlB1aUhtTmVsdUs4WGtKV2JwTEVvaE1JUXZVdjVxREVQZCsyVm4ralVlaEIvQ3N0RG9xQWloQnU3SVlTdVUzZ1JRWmY4ZUI2eXRleEEySERSOC9NWDBBdi81ZXZvWUY1ZGhxS3EzWTYwZzNxb2M4LzNFYW9LMytVWHhuUDNReDVJSnVOM0lwb1JRUm5rWUxiM0FiVElvOG50emliNjk2Snk4eUFUTk5ibWJNY0doK2lpS2xoTklBbkVvZkV3RjAzUGVnY0dKcUVtUFZ0L3k2RlZSSGkyMm4rTktsQk0yeEJEdCsxcmM0RERIWTRDL2I2blpMNkZkL1d3WW5VR3IySTkzWC9tcVkvazJPQWxUYk5QS3FPUnRINTdaVFBuTHFwaW1vbjU5Z3FSY0U4SHBwMVo3ZXVVcDEiLCJtYWMiOiJmMWM0YjA5YjBkNjVjMzNjYTJhYjRhNzk4NDE5ZjUxZmJlMWNmMzA1ZTQyMzE5Yjc4NmMwZmEwMDdiZDBlN2ZjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImoxOXVFcnk2SXFNbWMzaHp5ZzdJT3c9PSIsInZhbHVlIjoiZGk0b0Y1VjhqTjNpWWFCUVJyOWI1VTVtSnFxTjZ2UjFWdVRYNzdQMjNoSE5TMU1QcVBFb1F4Q1FXck0rTk00VEQxRUtQSCtEcDViR1hWNHFMRzhYaFd1ay9VREFGV2xUM05NdG1tSTB4RE9YZzR4Wk0rbnJ2ZjRQc0RGaWd3cStDQjVTYXpTTWRzVVJkN0F2TjFSbzJuSTlWVjBhSzRWUWhMckF2bG9MN05YeGZoVUwweGJsRy9XRE9aUHN1WDJCUEF6M0FhMWIwNFhrNjlXYVNVVWp6K3BsajVVc1huY09GdDM4SE83YzYvZHVOUC94c21XZERxWEZ5Z0hIZFRIZzlqdUN6NktKdGNCdDJ0aFFrdUlVbWtiT3lMQVR5U05lWmZHdjAwcFNiS0ZKZThGRVdzSzNLNVdCT1piZEtsNkVhUElOOE9ZMFZoRy85SGNpc1FuMGI5WmhYci9td0lZbDVLVTVpS3dUSSt1bGt1QnphZW1ObFB6TGd3bElqRkhhYmxXTG1XcFBCMEJMMVM2dVRZWDFuTnh0VTAyQW9UanpKRndCdHdhVmNSRGlJSW9zRCtTY1QwS2g5Vmd5MFl5TmtDTW5qN2ZSYlczdE5iUk1Ra2pqaDhORDlMdDBGNE5uVTk2NDFHY2NIMGtJc3VydktQUkVBM1h0bW8rby83dGYiLCJtYWMiOiJhNTM4NjhlMGM2ZGM0ZDQxMWZhOWY3MDRiYWRmZDc0NTdlM2ZjYjY0ZDNlZjI2ZGQxMTMwMWY3MWJhZmNiMGRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177152377\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1734563259 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734563259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1640249841 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMxK0I5ajR0SjJZTE9GMitFUE04SXc9PSIsInZhbHVlIjoiempTYkVCWk8wSVJMVzdEVFoxSXBsM3REdk9wM29HVTFiVnlqVTY0L1lvNGZhOHVTVzdIQnlIczM3VjZBQ2R5bXdTeEthYzBHSGRsZENrYVMxYXh3N3VFL1ZjcERTUzV0RzkvMjd0TUtGKzVONmVYMUVrTTN6N1RJOXpId2REU2x1V3FvaHlQenlUbHVYbGxrUlZIcHgwVFVuU3VRNGpVT3Nhd3RoaGo5eG1ObkdxTHA3RmZ4dVplUmxJRURTZ1ZyWGFYaHNYdXBXZXVxT3R1Nm5WS2ZFTlQzRFVxK292OG9oQU83TEpHSTlGRHdCYkVvWVdZSUQ1YjgvRXNnbFNzdU55V3liQUdCZHZFQ3pjTzJWeEY5Qjh5QTh3U1VQU3IvQUhneXZLcTJpZ01vM0FnaElNendZL21CMmszL1ZyQVBvaFZnRlVRdU93dXdjWjBzQStoaldSRWRBaWg5VzFrUVZSL3JQYlJpbWx2QXBPN0VhcUdjS0xVQlovTm1DTUZYbFNDOXU1Tmd4aDVlWXFrSTBuTUs1RUtUMURqSE4rTXlmNzR2Y0xLV2t1d0JLNXFCSkJhNWExQ2NNNkQveTNJQ2dQTGtCMGttVjZSbEhmS3N6UnRiRWFmeUdKZmZIQVQxYk9qSE93cHdNSGhVc2FYbVlVMjBscG40aUIwdStnNDkiLCJtYWMiOiI2MmJiZmM5YTZjOTYyZWQ3ZmI4ODE4MmMxYmFjMDZhY2VlZjQyZmMyNTBjNTBkMTliMGZlN2U1MTFkNjBlMDQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9tcmltOHlGYVpLL1lHUWg5cURxWVE9PSIsInZhbHVlIjoiOVM5Mi90RVFpcVE4T0EvWE82aFNJMWpqRGdKNzJHYVNVS0xqSUwyQ2FxR3ZuS2tFQ3F2dDFOcTNDbnBVYjdGWENyYUJZNUkrdHhMQzVVUVRJVmhBSmFNYnR0NXpzay84MXAreWFiOVhYdVBXK3ZMZnBtSDA2NVFkaktBL1RJVlB2NHRFSllaQ05STXJwM2haSjNINjBsYmxQa1JRVjBWTEhWL3ZacEZSejMraDArcnZtSzczdkg0V0NFNHU4L0tBemtQZTRhT2FwZnhha29BMDB4K09nZHVaRHdnNlBEVHRmQVNzUUJQQ1pDNHJZNENHWEZaWHF1dkh6cDQxcytGbndkb2lSalNrTzFxUTNqdU1YY2lkVDMwNUU2TmRmYUVGbFptWkFId01BY3NjUnZScHdNOTBsWXV5RGJJNHg3dUF1aE56emtlL1hkNmUyU0hSbGlXSmFxeUgyc3J0eXZMSGFHSjU4SktFM1AyNk5rYWUzWWNMUnhDVkxUcWMzQTFTbWYrbFFPdDh2SWtVYVhSc2lUYnh2cmdSSmhpNnRqalJZcEovWkF5VW5iU2FKa1V1WXVoM0xrakVpUkJVN0ZqbmQ4RSt1ei9keWVSaFRJSkgwU2dOMlV5Z2pERG5YZlpmOFowRW5tcTlOWmp3UkNuTmV5NGl6UUtMV1Z0RDZxdlIiLCJtYWMiOiI5NDMzZGQ1NTk1NzA1YjMyYTgyOGI4MTRjMzgyMTc0Njg0YTZjMGZjMDEwOTdkMmQxYmFlOTUxZGFjMDY4NDQ2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMxK0I5ajR0SjJZTE9GMitFUE04SXc9PSIsInZhbHVlIjoiempTYkVCWk8wSVJMVzdEVFoxSXBsM3REdk9wM29HVTFiVnlqVTY0L1lvNGZhOHVTVzdIQnlIczM3VjZBQ2R5bXdTeEthYzBHSGRsZENrYVMxYXh3N3VFL1ZjcERTUzV0RzkvMjd0TUtGKzVONmVYMUVrTTN6N1RJOXpId2REU2x1V3FvaHlQenlUbHVYbGxrUlZIcHgwVFVuU3VRNGpVT3Nhd3RoaGo5eG1ObkdxTHA3RmZ4dVplUmxJRURTZ1ZyWGFYaHNYdXBXZXVxT3R1Nm5WS2ZFTlQzRFVxK292OG9oQU83TEpHSTlGRHdCYkVvWVdZSUQ1YjgvRXNnbFNzdU55V3liQUdCZHZFQ3pjTzJWeEY5Qjh5QTh3U1VQU3IvQUhneXZLcTJpZ01vM0FnaElNendZL21CMmszL1ZyQVBvaFZnRlVRdU93dXdjWjBzQStoaldSRWRBaWg5VzFrUVZSL3JQYlJpbWx2QXBPN0VhcUdjS0xVQlovTm1DTUZYbFNDOXU1Tmd4aDVlWXFrSTBuTUs1RUtUMURqSE4rTXlmNzR2Y0xLV2t1d0JLNXFCSkJhNWExQ2NNNkQveTNJQ2dQTGtCMGttVjZSbEhmS3N6UnRiRWFmeUdKZmZIQVQxYk9qSE93cHdNSGhVc2FYbVlVMjBscG40aUIwdStnNDkiLCJtYWMiOiI2MmJiZmM5YTZjOTYyZWQ3ZmI4ODE4MmMxYmFjMDZhY2VlZjQyZmMyNTBjNTBkMTliMGZlN2U1MTFkNjBlMDQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9tcmltOHlGYVpLL1lHUWg5cURxWVE9PSIsInZhbHVlIjoiOVM5Mi90RVFpcVE4T0EvWE82aFNJMWpqRGdKNzJHYVNVS0xqSUwyQ2FxR3ZuS2tFQ3F2dDFOcTNDbnBVYjdGWENyYUJZNUkrdHhMQzVVUVRJVmhBSmFNYnR0NXpzay84MXAreWFiOVhYdVBXK3ZMZnBtSDA2NVFkaktBL1RJVlB2NHRFSllaQ05STXJwM2haSjNINjBsYmxQa1JRVjBWTEhWL3ZacEZSejMraDArcnZtSzczdkg0V0NFNHU4L0tBemtQZTRhT2FwZnhha29BMDB4K09nZHVaRHdnNlBEVHRmQVNzUUJQQ1pDNHJZNENHWEZaWHF1dkh6cDQxcytGbndkb2lSalNrTzFxUTNqdU1YY2lkVDMwNUU2TmRmYUVGbFptWkFId01BY3NjUnZScHdNOTBsWXV5RGJJNHg3dUF1aE56emtlL1hkNmUyU0hSbGlXSmFxeUgyc3J0eXZMSGFHSjU4SktFM1AyNk5rYWUzWWNMUnhDVkxUcWMzQTFTbWYrbFFPdDh2SWtVYVhSc2lUYnh2cmdSSmhpNnRqalJZcEovWkF5VW5iU2FKa1V1WXVoM0xrakVpUkJVN0ZqbmQ4RSt1ei9keWVSaFRJSkgwU2dOMlV5Z2pERG5YZlpmOFowRW5tcTlOWmp3UkNuTmV5NGl6UUtMV1Z0RDZxdlIiLCJtYWMiOiI5NDMzZGQ1NTk1NzA1YjMyYTgyOGI4MTRjMzgyMTc0Njg0YTZjMGZjMDEwOTdkMmQxYmFlOTUxZGFjMDY4NDQ2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640249841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-909916360 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909916360\", {\"maxDepth\":0})</script>\n"}}