{"__meta": {"id": "X475478108c84d30a7e0270f9d80a1b86", "datetime": "2025-07-30 05:56:17", "utime": **********.653139, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753854976.341402, "end": **********.653271, "duration": 1.3118689060211182, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1753854976.341402, "relative_start": 0, "end": **********.489148, "relative_end": **********.489148, "duration": 1.1477458477020264, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.489174, "relative_start": 1.****************, "end": **********.653275, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TIZP6tkVumWTKcXZgYEmSGVlimHgurqXOAoqYdau", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1918563693 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1918563693\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1393640888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1393640888\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1193373597 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1193373597\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607236736 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607236736\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-197124605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-197124605\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2056222429 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:56:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBxNU9JNXB4UHRVTjJkRU5RSWxjQnc9PSIsInZhbHVlIjoiN3pLMStNdThLQStuTFhSWFhZcEtPMStCM1R1a0M1c3hPVXpTVEZQR0NQN1pLUmVUMmtLVnNiZFY1bkJvY05DK2puWURBdkFWaFVYL3EzcVl5T0g3cUxXUFp5SjVXcE5tMll1RlQvcjlYbDI1LzRvM0F6Z3UvWXZOLzRWVmR2WVg2VXREVzJ3N0IrRE9RMFhFdzB6TkFIbFJSK2s5cUloVHFLT09Rb21lR2JLa0crWVNoL1o1ZDZDV0ZEZElybGpzZWwzQml2cEFHcUFCU1NHWFVUNzRmZ3YxcGw4Q2l5c3VQU3NtL09oNmxRdDl0NFFXQnVGSzVpbEpnVjFsWDRBdXRtbCswbHZack1KVGdyOUYzUUZUMklKQVhSNnM1R2VPd0hYOGN1UXZSa0JEbFEzOHI2NlhVd3R0VnhydndxZUVqajMybzdpamtQTVliRVNEUi80RjVGblVpbnFiT0RvaU1SNTFnSG9mdC8zM1puYkNxbTRPSE9mRk5CSzIzaU10SUV0L1Z6ZHJ3aDZUeVhUOUZDQy9CeFRTVHVmTGVld3YzZ25sZzRzRWVRYWlMVUpPci9td2gxOXBIT08vdkRrUm1PUWdxOTRNZE9aUkVMSDBGYmRaMWZhS3lWRk9pc0JBTHpKWllqdU5zK0o4QlFLQzlQS2FrRmdvei9pUHlnWTEiLCJtYWMiOiJjZmJkZjkzNTZkNDYxOWM0YTRlODVmNmYyYTJkNTdjNTdjMDQ5NTdjMDA5MjJhZjk1NmQ5ZWQwNjZhYTljZDUxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:56:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJTaUN5STFMeGdDWnBTRVFQVGIvNGc9PSIsInZhbHVlIjoiWVYzTXFwTGIzVW9xSFVLRys0TExWdXNtNUdCV29QblNaeU84TDA5WnQ5aTlNV1QzOGRZKzRBbVRwbHkvSDEzbWdMMWlReFRjU3hCaThqdWtiOEJDVU5qekQ1cEpsTnZWbktXcXZmbENXd0d6UGd4bXo1TTJPRForckRZRGU5UjB5Q1hxM1VucTk0akJDMFFRTmZLUlNBV3dVaWNuMlZzR2ZqNmJyOGFLUkhweHpTdDZuNHhXakh1TGhWalYwY2hUUWw1cVpNV0E0TVZBVkdVWk9HTFlUeTNabDB2MW9CbmlNd3VyVjJIVzBRTDBoa3ZpOFkrcHB2YUpsZVhjWmJVVnVOajBGZGFwdXUvdlplcTArdGovSXdZcExNOXpzeE1nN3dLMXRzeng0ZVpmZ2Jkdk1qL2xxVGprZTFPMDRBaGVLTWxhR29pMmdZaEQwTW82SC9OZkhWaTd5VktKQi9mR05aekplaG01aHRiMkdPRVliMjk0ZEhDNWx6cjQ2WTRvajBIS0M2aGpMeExBc29SN3daS1VYdXZKOWV1VzAyNFZoUE53VGd0eHRqVG9RK0ZWVGxaeGR2TzNqMW9lMGt3TGFLVjlLcGdxY2JVK2NKZGo1aFd3TEJBMVRiUFlCNFFxYk8rVzdKd1FFR0Z4OUFVTDA3SkkrY2d2cGxrRG9pdWoiLCJtYWMiOiI4Mzc5NTI2MDIzMzRiMmRjMTQ1NzU3NGYwNWNhNmY1NzkwMDM3NjM2YjM2YjdiOGE0MWQ1Njk2ODNjNGZjNWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:56:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBxNU9JNXB4UHRVTjJkRU5RSWxjQnc9PSIsInZhbHVlIjoiN3pLMStNdThLQStuTFhSWFhZcEtPMStCM1R1a0M1c3hPVXpTVEZQR0NQN1pLUmVUMmtLVnNiZFY1bkJvY05DK2puWURBdkFWaFVYL3EzcVl5T0g3cUxXUFp5SjVXcE5tMll1RlQvcjlYbDI1LzRvM0F6Z3UvWXZOLzRWVmR2WVg2VXREVzJ3N0IrRE9RMFhFdzB6TkFIbFJSK2s5cUloVHFLT09Rb21lR2JLa0crWVNoL1o1ZDZDV0ZEZElybGpzZWwzQml2cEFHcUFCU1NHWFVUNzRmZ3YxcGw4Q2l5c3VQU3NtL09oNmxRdDl0NFFXQnVGSzVpbEpnVjFsWDRBdXRtbCswbHZack1KVGdyOUYzUUZUMklKQVhSNnM1R2VPd0hYOGN1UXZSa0JEbFEzOHI2NlhVd3R0VnhydndxZUVqajMybzdpamtQTVliRVNEUi80RjVGblVpbnFiT0RvaU1SNTFnSG9mdC8zM1puYkNxbTRPSE9mRk5CSzIzaU10SUV0L1Z6ZHJ3aDZUeVhUOUZDQy9CeFRTVHVmTGVld3YzZ25sZzRzRWVRYWlMVUpPci9td2gxOXBIT08vdkRrUm1PUWdxOTRNZE9aUkVMSDBGYmRaMWZhS3lWRk9pc0JBTHpKWllqdU5zK0o4QlFLQzlQS2FrRmdvei9pUHlnWTEiLCJtYWMiOiJjZmJkZjkzNTZkNDYxOWM0YTRlODVmNmYyYTJkNTdjNTdjMDQ5NTdjMDA5MjJhZjk1NmQ5ZWQwNjZhYTljZDUxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:56:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJTaUN5STFMeGdDWnBTRVFQVGIvNGc9PSIsInZhbHVlIjoiWVYzTXFwTGIzVW9xSFVLRys0TExWdXNtNUdCV29QblNaeU84TDA5WnQ5aTlNV1QzOGRZKzRBbVRwbHkvSDEzbWdMMWlReFRjU3hCaThqdWtiOEJDVU5qekQ1cEpsTnZWbktXcXZmbENXd0d6UGd4bXo1TTJPRForckRZRGU5UjB5Q1hxM1VucTk0akJDMFFRTmZLUlNBV3dVaWNuMlZzR2ZqNmJyOGFLUkhweHpTdDZuNHhXakh1TGhWalYwY2hUUWw1cVpNV0E0TVZBVkdVWk9HTFlUeTNabDB2MW9CbmlNd3VyVjJIVzBRTDBoa3ZpOFkrcHB2YUpsZVhjWmJVVnVOajBGZGFwdXUvdlplcTArdGovSXdZcExNOXpzeE1nN3dLMXRzeng0ZVpmZ2Jkdk1qL2xxVGprZTFPMDRBaGVLTWxhR29pMmdZaEQwTW82SC9OZkhWaTd5VktKQi9mR05aekplaG01aHRiMkdPRVliMjk0ZEhDNWx6cjQ2WTRvajBIS0M2aGpMeExBc29SN3daS1VYdXZKOWV1VzAyNFZoUE53VGd0eHRqVG9RK0ZWVGxaeGR2TzNqMW9lMGt3TGFLVjlLcGdxY2JVK2NKZGo1aFd3TEJBMVRiUFlCNFFxYk8rVzdKd1FFR0Z4OUFVTDA3SkkrY2d2cGxrRG9pdWoiLCJtYWMiOiI4Mzc5NTI2MDIzMzRiMmRjMTQ1NzU3NGYwNWNhNmY1NzkwMDM3NjM2YjM2YjdiOGE0MWQ1Njk2ODNjNGZjNWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:56:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056222429\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-106288370 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TIZP6tkVumWTKcXZgYEmSGVlimHgurqXOAoqYdau</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106288370\", {\"maxDepth\":0})</script>\n"}}