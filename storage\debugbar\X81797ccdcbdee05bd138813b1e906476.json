{"__meta": {"id": "X81797ccdcbdee05bd138813b1e906476", "datetime": "2025-07-30 02:37:55", "utime": **********.479304, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843072.734209, "end": **********.479353, "duration": 2.7451438903808594, "duration_str": "2.75s", "measures": [{"label": "Booting", "start": 1753843072.734209, "relative_start": 0, "end": **********.28332, "relative_end": **********.28332, "duration": 2.5491108894348145, "duration_str": "2.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.283365, "relative_start": 2.****************, "end": **********.479362, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qclTBpeyIdeBlfegdUFsuR9crQBfAmwQ11rRmRV7", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1684154547 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1684154547\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1045405225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1045405225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111556641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111556641\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1563671495 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563671495\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-972627407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-972627407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-973946878 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:37:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9sTWk2MnUrNUpUOUZ4TUVGUTl0Zmc9PSIsInZhbHVlIjoiUURXdjZLUGh3aEtEcUFscEFLUG9yaHJVbGkySUk1V0srUTF4Nm5wVSs3VXU1SnlwTFp6V1lwb2pXR0RUNElzMXJ2VWkwM2gzUkFyVXpwV2RKclNIVlFmV21ObEdtQUdIcS9MYWxGejV0NFAzWVkrbkx3SzF6a0lPeU52M01pdm82dlRKZ0Y0TnlDMEQ2YkE1dld2OVA4NUxldU5tWHRPOVA3TnQ0UDNCT1pyUU1qN0NYZFM4SUtvbm84cEp0VWV0QlB4SFhuSVJuU2xiZUprQVZUYWtVMnBHRWJQU3lPdFdWOVhSdmtnZjRyeUFjMmZjUTBDOU1ZZ0ZvMkVWTVIyd2IxYVhkNDNJYmVhZEx4dWhFYVB2cWRlaG96T1BUeEVEVlorS3loUXM0YVk5ZUZLSjFTQjFsWjEwTy9JUGRaOENlV0FzLzAwL2Z2MDFjNFVGSktFNmRaR2ZnSVd4cnpuaVZjRXBnQlNUdUdsVHIrVnFKODhvcm9yRVBhTERiN2tqYXdDcTQ4UDYxK2YwU3loNGZ0aVg1TFB0LzY0Z29LTFo0QnVYTC9oQnRybmd3eDdGdzdFcEgwNFlFSm5NeXYrdzBnUlQ0ZGoybVFraWZ5clJyYTM4aXBybTloQVhPT1h2TElIZFlreVRzYVRUTXhYclFSclBUeDFKR3dmRTVKM2YiLCJtYWMiOiIyMjgxYWQ5YjJhNmQ1NTg5YjZiYWQ3M2E1ZWVmNDBlNzM0NjUxZDVmZTFjYmQ4YjRkYWQ3ZDU4NDU1ZTU2ZGIwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InQvaE0xRHVncUlkVjJzL0I3VEhCMnc9PSIsInZhbHVlIjoidjRDQ05KUEFzcFVCRUJvS3hVRndkcGlXckJNcTUra0NROE9rOVkrOWs1TjQ1ekVrTHRGUml3aUMzTXcxM3lvSFlENE5ISzcwdXozQ1dtTWNadXUzaXF2UjhKUU40d2crekdhZlBNYXhCdVlIYk1XSXJTdXZzekYrV2dOeVY5SnZHaVVZdTBpK0FvNXRlSHk5MFNJakhXaVUxb2t5c21yMmRRSWdtcHd2dGZFZ3ViWGZqQURpN2cyamhVVWd3N0hUSS9YRmhhbjh2eG4yV2ZjYkxzN0VhR1Q5Wm1jQzk4WGlBUjdESWM4L1RpOHEwemhuZFNuZy9scDgvazBMUU5VV0lqZkcvS3ExUUZkQ2oyaFVuVG1hdEVCSks3aGpQUzhTL0tyRE4wbXRuNlNRajErOExZaFlLUHc5MkRUQU5hTkJxNS9QYzRuNG53YThVNHNLQ0cxV29heGZ3dnhWeldvQkdZNDhBcnRmNUlTeWJZOW43MUJZTXRtSEtkRHZjTjYwMWY5MUlJVjZ1aCtaQm9lZDJiN21weHF5eUtwUkZoTFpjZVN6Nk5BNENBT2dybGZRbXlBMm1Sb0hVbW5wQUdsdG1ySWNGNk1GV0hOQUkwNkdvenNUcG1HL2htQUpLeFFhQWM0QW9xWis5SWdyak1BSi83Q29sVTJLNGpKMHg3VEUiLCJtYWMiOiJlZDE4OTUwMDVmMjhiZWUzZWNlY2RiNjY1NjU5NWY5Y2RiOWFkNTJkMmIwZThiM2M2MTQ4MTc0NmRhY2ExYWFhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9sTWk2MnUrNUpUOUZ4TUVGUTl0Zmc9PSIsInZhbHVlIjoiUURXdjZLUGh3aEtEcUFscEFLUG9yaHJVbGkySUk1V0srUTF4Nm5wVSs3VXU1SnlwTFp6V1lwb2pXR0RUNElzMXJ2VWkwM2gzUkFyVXpwV2RKclNIVlFmV21ObEdtQUdIcS9MYWxGejV0NFAzWVkrbkx3SzF6a0lPeU52M01pdm82dlRKZ0Y0TnlDMEQ2YkE1dld2OVA4NUxldU5tWHRPOVA3TnQ0UDNCT1pyUU1qN0NYZFM4SUtvbm84cEp0VWV0QlB4SFhuSVJuU2xiZUprQVZUYWtVMnBHRWJQU3lPdFdWOVhSdmtnZjRyeUFjMmZjUTBDOU1ZZ0ZvMkVWTVIyd2IxYVhkNDNJYmVhZEx4dWhFYVB2cWRlaG96T1BUeEVEVlorS3loUXM0YVk5ZUZLSjFTQjFsWjEwTy9JUGRaOENlV0FzLzAwL2Z2MDFjNFVGSktFNmRaR2ZnSVd4cnpuaVZjRXBnQlNUdUdsVHIrVnFKODhvcm9yRVBhTERiN2tqYXdDcTQ4UDYxK2YwU3loNGZ0aVg1TFB0LzY0Z29LTFo0QnVYTC9oQnRybmd3eDdGdzdFcEgwNFlFSm5NeXYrdzBnUlQ0ZGoybVFraWZ5clJyYTM4aXBybTloQVhPT1h2TElIZFlreVRzYVRUTXhYclFSclBUeDFKR3dmRTVKM2YiLCJtYWMiOiIyMjgxYWQ5YjJhNmQ1NTg5YjZiYWQ3M2E1ZWVmNDBlNzM0NjUxZDVmZTFjYmQ4YjRkYWQ3ZDU4NDU1ZTU2ZGIwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InQvaE0xRHVncUlkVjJzL0I3VEhCMnc9PSIsInZhbHVlIjoidjRDQ05KUEFzcFVCRUJvS3hVRndkcGlXckJNcTUra0NROE9rOVkrOWs1TjQ1ekVrTHRGUml3aUMzTXcxM3lvSFlENE5ISzcwdXozQ1dtTWNadXUzaXF2UjhKUU40d2crekdhZlBNYXhCdVlIYk1XSXJTdXZzekYrV2dOeVY5SnZHaVVZdTBpK0FvNXRlSHk5MFNJakhXaVUxb2t5c21yMmRRSWdtcHd2dGZFZ3ViWGZqQURpN2cyamhVVWd3N0hUSS9YRmhhbjh2eG4yV2ZjYkxzN0VhR1Q5Wm1jQzk4WGlBUjdESWM4L1RpOHEwemhuZFNuZy9scDgvazBMUU5VV0lqZkcvS3ExUUZkQ2oyaFVuVG1hdEVCSks3aGpQUzhTL0tyRE4wbXRuNlNRajErOExZaFlLUHc5MkRUQU5hTkJxNS9QYzRuNG53YThVNHNLQ0cxV29heGZ3dnhWeldvQkdZNDhBcnRmNUlTeWJZOW43MUJZTXRtSEtkRHZjTjYwMWY5MUlJVjZ1aCtaQm9lZDJiN21weHF5eUtwUkZoTFpjZVN6Nk5BNENBT2dybGZRbXlBMm1Sb0hVbW5wQUdsdG1ySWNGNk1GV0hOQUkwNkdvenNUcG1HL2htQUpLeFFhQWM0QW9xWis5SWdyak1BSi83Q29sVTJLNGpKMHg3VEUiLCJtYWMiOiJlZDE4OTUwMDVmMjhiZWUzZWNlY2RiNjY1NjU5NWY5Y2RiOWFkNTJkMmIwZThiM2M2MTQ4MTc0NmRhY2ExYWFhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973946878\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1243593076 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qclTBpeyIdeBlfegdUFsuR9crQBfAmwQ11rRmRV7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243593076\", {\"maxDepth\":0})</script>\n"}}