{"__meta": {"id": "X3f1da70b36ad17f4085705b2ee6f164d", "datetime": "2025-07-30 05:29:00", "utime": **********.690559, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753853339.845497, "end": **********.690634, "duration": 0.8451371192932129, "duration_str": "845ms", "measures": [{"label": "Booting", "start": 1753853339.845497, "relative_start": 0, "end": **********.614654, "relative_end": **********.614654, "duration": 0.7691571712493896, "duration_str": "769ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.614695, "relative_start": 0.****************, "end": **********.690639, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "75.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rcLGaUyBFvmVOyLx8Z9aNLKsvFi3foqVP5vvz9b9", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1302790282 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1302790282\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-43635065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-43635065\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1330526331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1330526331\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-159767477 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159767477\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-599547182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-599547182\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:29:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVrYzVuQmZLTHJpRmliMDVpL055UlE9PSIsInZhbHVlIjoiRzhLQ2x3c2IxbFlMdjZGakpST24zMnJaa01LcGgzdmg4aGhLbHpTdmtncVU2M2ZCYmFyRVFJM2xaYmZ0OGRmdUxOYmNzeHhxZzE3VnNuOVNEN2xlTFlrcVpmQ0ZkbW9RK2F5bWtCTkQxampzc1dHTnZPL2tBZmV6TGJVQWFOVmNucno5TkJpazc1c2pZcmpvdmlFejUwSU53YW1WNGg1OGY2akVsSXJ4UkRJSEVQT3NlV2RvR25FV0E5b2RXVHZWSDg4UFI3YlI1dnVic1dMRWoyNEFaeVhwaS9qMHk2aG1jTWlkSm5TU0l1citXdXBGbHR5U0wxSU0yNFZXZDJKR2V2eHRveThOck9aaXRvYkxuV1YrV21ZeVBIcGx2NGpHa21neVFxNTNXVmdJK2hoSlZ1R2F0aDVzczFLTktwMjdjYVdiaHNQM24xUGthYVpLMzRJcnpmMjhiWVFMZnlQaTV4V0VJZGJrdG81NzJnYTBseStmT0JsaldPYjZNRFBFVWtudld2OFNERmFyRURkVm56clI0blFnek04VFYxcXJZcXF2cjhSdDFpUm9lSUdmZjJtWGdrbHFVRkNwRnJRUXRWZjVUOGNTS1NwNURZYkEyRi85MExoRzZEcHQvNEIwR3dsNEpFQThiVWpOb1ZnTHBOS3Z1U3lWb1B2cElvRkgiLCJtYWMiOiIzZTRmMTczMTM5Mjg5MmNhYjA0MzQ3MzYxMmQwMDlhMmJkZGZhMGE4ZmZkN2M4ZjRmZmEwMDU3OWZlMzVmMDUyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:29:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikg0Zlh2d1dvMW5IUUtKZmQybkJWZlE9PSIsInZhbHVlIjoiT2hwcTRzUkRnZU1DVERrL1NyQnhsZnV6NjRRY0JiMDdlK0ZFTmZKSXZVMXdEL2dhOHk1WThRWnc4NTRTK1NHM01Sd2lFS1FKdThST25DMFJhd0t2Q3p3TFZITE1Oc2k2VWo2YllyNkY0M2tnRnYxbG56YnlobFBJV3pHRW81L0dKS2d3NnQ1ZnlIcGQwcjNXUWVpYldrQXNmbEdrR0pZU1R3amJzV1hyVW9BVUxUdmVMQURESUhhYmM2WWtub3RlL3JnRXduS2c1TlhWSVBLbThicXJjaDdWRUhpRHpEd0lvZXJVOVhZVFdaYlFFMks3SS9DOGtTa0kwcnF2S1k0clVmckh1bnJQQW9KVyt5WXUyVURBZ2krTE9LMHVGeFFud0ZnZm1mUUkxM3FPZ1FkMEhIVFVEVUNFZys4UzNDZFY5cmtTQzJzRVJpVXFyRGphR0wvc0ZId1NTdFBKb0xRaCt1Q0NBTFpVRWxIM2lmbEN4VnBvMFlBY2ptUkZiSkxMTURCUXBwRFVSbDBROVhsWkZqWjJ0SjJNYk5nNVNOSWl2R0N4TndEb215clMyUEpiVTVoR2ZEUFJPTCtESlpBN0Y3SW8zZXppWDZUVUgvc2dUS05JNzlnRSsrdjBncy9uRnliMnZ2NFJYV1J0L0h3SzRQb2VSTzQvcGFHb2x3MEoiLCJtYWMiOiJhOTU1OWU1MmEyYmVhMThjNWEyNTkyYzYyNDdlMGQxM2FjMmNjNWEwZDE3NGExNjUzYjA3OTZiOWUxNTI3ZTRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:29:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVrYzVuQmZLTHJpRmliMDVpL055UlE9PSIsInZhbHVlIjoiRzhLQ2x3c2IxbFlMdjZGakpST24zMnJaa01LcGgzdmg4aGhLbHpTdmtncVU2M2ZCYmFyRVFJM2xaYmZ0OGRmdUxOYmNzeHhxZzE3VnNuOVNEN2xlTFlrcVpmQ0ZkbW9RK2F5bWtCTkQxampzc1dHTnZPL2tBZmV6TGJVQWFOVmNucno5TkJpazc1c2pZcmpvdmlFejUwSU53YW1WNGg1OGY2akVsSXJ4UkRJSEVQT3NlV2RvR25FV0E5b2RXVHZWSDg4UFI3YlI1dnVic1dMRWoyNEFaeVhwaS9qMHk2aG1jTWlkSm5TU0l1citXdXBGbHR5U0wxSU0yNFZXZDJKR2V2eHRveThOck9aaXRvYkxuV1YrV21ZeVBIcGx2NGpHa21neVFxNTNXVmdJK2hoSlZ1R2F0aDVzczFLTktwMjdjYVdiaHNQM24xUGthYVpLMzRJcnpmMjhiWVFMZnlQaTV4V0VJZGJrdG81NzJnYTBseStmT0JsaldPYjZNRFBFVWtudld2OFNERmFyRURkVm56clI0blFnek04VFYxcXJZcXF2cjhSdDFpUm9lSUdmZjJtWGdrbHFVRkNwRnJRUXRWZjVUOGNTS1NwNURZYkEyRi85MExoRzZEcHQvNEIwR3dsNEpFQThiVWpOb1ZnTHBOS3Z1U3lWb1B2cElvRkgiLCJtYWMiOiIzZTRmMTczMTM5Mjg5MmNhYjA0MzQ3MzYxMmQwMDlhMmJkZGZhMGE4ZmZkN2M4ZjRmZmEwMDU3OWZlMzVmMDUyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:29:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikg0Zlh2d1dvMW5IUUtKZmQybkJWZlE9PSIsInZhbHVlIjoiT2hwcTRzUkRnZU1DVERrL1NyQnhsZnV6NjRRY0JiMDdlK0ZFTmZKSXZVMXdEL2dhOHk1WThRWnc4NTRTK1NHM01Sd2lFS1FKdThST25DMFJhd0t2Q3p3TFZITE1Oc2k2VWo2YllyNkY0M2tnRnYxbG56YnlobFBJV3pHRW81L0dKS2d3NnQ1ZnlIcGQwcjNXUWVpYldrQXNmbEdrR0pZU1R3amJzV1hyVW9BVUxUdmVMQURESUhhYmM2WWtub3RlL3JnRXduS2c1TlhWSVBLbThicXJjaDdWRUhpRHpEd0lvZXJVOVhZVFdaYlFFMks3SS9DOGtTa0kwcnF2S1k0clVmckh1bnJQQW9KVyt5WXUyVURBZ2krTE9LMHVGeFFud0ZnZm1mUUkxM3FPZ1FkMEhIVFVEVUNFZys4UzNDZFY5cmtTQzJzRVJpVXFyRGphR0wvc0ZId1NTdFBKb0xRaCt1Q0NBTFpVRWxIM2lmbEN4VnBvMFlBY2ptUkZiSkxMTURCUXBwRFVSbDBROVhsWkZqWjJ0SjJNYk5nNVNOSWl2R0N4TndEb215clMyUEpiVTVoR2ZEUFJPTCtESlpBN0Y3SW8zZXppWDZUVUgvc2dUS05JNzlnRSsrdjBncy9uRnliMnZ2NFJYV1J0L0h3SzRQb2VSTzQvcGFHb2x3MEoiLCJtYWMiOiJhOTU1OWU1MmEyYmVhMThjNWEyNTkyYzYyNDdlMGQxM2FjMmNjNWEwZDE3NGExNjUzYjA3OTZiOWUxNTI3ZTRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:29:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2117168010 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rcLGaUyBFvmVOyLx8Z9aNLKsvFi3foqVP5vvz9b9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117168010\", {\"maxDepth\":0})</script>\n"}}