{"__meta": {"id": "Xc21d4fb72055afa48883bab398bfbdf7", "datetime": "2025-07-30 02:36:43", "utime": **********.919093, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[02:36:43] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.910884, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753843000.950637, "end": **********.91915, "duration": 2.968513011932373, "duration_str": "2.97s", "measures": [{"label": "Booting", "start": 1753843000.950637, "relative_start": 0, "end": **********.595584, "relative_end": **********.595584, "duration": 2.644946813583374, "duration_str": "2.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.595617, "relative_start": 2.644979953765869, "end": **********.919155, "relative_end": 4.76837158203125e-06, "duration": 0.32353782653808594, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45891824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02651, "accumulated_duration_str": "26.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.736234, "duration": 0.022600000000000002, "duration_str": "22.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.251}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.7974179, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.251, "width_percent": 4.866}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.858582, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 90.117, "width_percent": 5.243}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.870186, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 95.36, "width_percent": 4.64}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2028824805 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2028824805\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2036099456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2036099456\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1389746009 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389746009\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1749919047 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjlVbTZyWkpoRGlqbjRTYy8zRlBIYkE9PSIsInZhbHVlIjoiRzRTWVJac1A1VGhWRWlGNXlUQmIwN2RlS1pjM09XbUFGMlhCZUsrb3hNMmNtVjg3WXMyRUsxUEl4Tk5FSFVTNkE5TFE5V2Y3Rm85WFFkQnV3TjZGWU9uMmRGcGVSNk1ySW9nUXo1VFVOaVJOTEFaSVlkbmVSSG43U2RlZUdOUGoxa2d4dHBmUzlNeTdYazNYSTZHM1JrUlhudG1OQ2VtNFhHbkNYVmljdXU4bVFFT2JhS1hiaHlxWmxpUVBXem8yYXROMWxZU01Ebml4UEpXU3Q5K0IvOTNqdUIzZ083SGlhb1lWbW1sZmRRRjR6ZXBxNWg2bkk5TmtqZiswbHd4YXQvNm9MenFVUWs5UGVSOGs2OHBuSzVVeExKL2dGRFBVT2JqVS9LMWlvSlFJd0piTkJSREsxSUVmVmJVbXNuei8rUHBDYktmazQ2NFI0cTFoMlNJeG4wQlNmWURQMGxKcVFCa3ZlR3gyYlRqdEVzNGFNRkNjSEZTUjZFbWR6ZnZGS2dTTWROMFk0aFoxbTFlT0paQzBuL1pkeHBwUDRSQjZKVU5ueGRKbm8zZG1BWmh5TXhiTjZFWnAwT3padU40Y29uRCtuelZsY2c3WEZuQS92VHd2cmhWckwxSFlUWksxNWwzU1paYm54RTlBRVFGYklpdFpxUXdNaXZueUxZRXIiLCJtYWMiOiIwN2I4ODA0N2Y4MjcxZTE4OWZmNTZmMzZlZDllYzcyMjMzOTM2MTU1ODM4NjhjMDhhYjI2NWFmM2UyZDQ2NDkwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhvSjJiWmM0NExLbUJZU2hjM3Q0YkE9PSIsInZhbHVlIjoiME55bG9OanlDWjU4M0txbk5hMjZXVmJuNGNmaVBBQ3hmYW93N1RqYlZvaXFZdStxTSswMGNlU1NXdVZWQTZuU1hlbkw2dW5VVWNDM1dPb0FYYUE1Mnp2d1AxTlJtY2dQaDh2MlNRdXkyb3ZCSU93QzRVNEVKNGk0SDdVUDlFcVV0U09IOUIyUnFPbjM3bUwraGN3bzF4WUJ6N2EwQmttTXZ1NDAxNDBxZVc1NDkyVXBXTDdXMDRGZUpmS3E1SHBnV094YTJIWmpLK2ppYU5STGU3aE92WVZEUGVid2h2MzJPRkVjQmVNWnQwWjFWMnhqUGFKNU9oRUpDQWR6Nk1vUnF6ZUxIUkkyeGk1dkkvTC9SSmxtbmFKbUJETWQxd3l0N1lhKzB6cVp1ZGtTcGxFejV3dFVrNk5CcGxqdTIrSUVlYldCNlVnZDh4cUhMYVNNcExyWmhNSTJvVk83UG9ReWVKVU9YTk9HeU4zczVzcEVTRGdVdUdkUDY3RlFReitxTEJ2MHJpNlI5Zi9FOU4xemQrWm5aZnBIMVZyQVdDMkpIbHV4bGpoWjlKLzl1andlRFpOV0ZURUtXdVZINHg1N0drU1o3RWpFQS9TM1pHTjBvNGNKUXhRNWhGN3NSdGJaV1hJMVRiZEZnUEUvbG1aK2REa2RsclU4aUFFV25jYkgiLCJtYWMiOiIxNTRkNGI0MDI5YjEwNmIyYzI4ZDZiMmQ5M2I5ZTc1MGYyMDAzNjc3N2Y3NDU4YjdlOGI0YzVmYTZiN2I2OTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749919047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-436707628 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XRhqfNhIljuWkeh2CYPLg15rfgYjPgg1Mschh0S6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436707628\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044632479 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:36:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRkcnBHclVKN05YZS91UUhhMW82SGc9PSIsInZhbHVlIjoibGs1WTIwT1lCaXJlODljaWN3TnllWDY5b3RucmlqcFBEVTRVdjQ1aldpd3dqSjVoVFF2Mm5qSlh6eEpOTGRxUmNuWWxPSHk2eWxpWE42M24zL3VHSHBiOUpHcnB4cmczeTVGeHpkazVJelhNYXovbW9IRURWbzRCd2dRZ1Z2blRGK2FLRktvaVlzSlkzU2RTd01nMjhTZFE3eXRUMVVBWmxWRXd3WkZpL1o3MXlBVW5ZZlpnOFlLUDVqbGZSdC8zcnNmWm0vMjJvL0E4cXFmS3VzWUVPbG9hREdkWFNwWDNnaVZhbjF0c05vRlo3dlFjSDZxRGJsc3hLREZncjVqVmtMMis2K3c5K0ZKc1dhWUR3Qnd2RnpYOWk2TVN2aHRwbjdKa0pNcTJoNHJqN3Vzc0FVUEJSNWFhanhJenRnWDhBczFiR1I3T3l6dWNBQXdBN2krSHZGaFZrT3Fxc093R3luWkkvUThnOUxsUXZqcjh1VXBuaUdVcGxseVFwNDZad0VTYU1ZeE9QR2JYRGV2dmRKSHpJRXo1a2d5MWF4a3czMU5IR3RYd0t3aW1hNVNzcEUzazNjNVJoNWxQV012N1JzTGZUZmFtQnRMK0pWeUJ0RmZzZTRCZCtuTnJiWS90TjlXMTQ0VDVWSTdnR0I3YUxKMUZUcmwrT1U3OUtpdlQiLCJtYWMiOiI0NmNiMjUyZGIwNDdjOWY4N2VjOWYyZDBmMTgzZjNkM2ZiYjBlMGYxN2RiN2I5MjM1NWY5MjdmOWIzMWY3NGU3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IldER2FSbVkxRmlhTmNubCtqY2dKZkE9PSIsInZhbHVlIjoiWGx1UUtUMS95c0RCdllNdVpBY3J3dGI4NU5IalNzNXFtcEViNFo2ZGU1bXVQOGhPd3ZFaE1JNXV6MmpjWDl5VkNCbjIwcnZPZE01NmdYeHpOeks5a2tBQnZ3bXBUaGNKcE9ESG5SV205aGcrYjNzd0tBVzVXSUpKaEg1OWNGcHZRL05ZYmFNUmY2ZHFDaysyZWRNeFFNMW9ocUlZZ254NWlVbHd0UzhGL2RVa1BqTXR0ZlptbHVjQVNmNjNTZjBMSzNKYzBqM0dqZFdGNTJwK3BseEVUb2tEQXB6ZTFYVE1Cb2lVWG54V2J4Y1B5WEpGdktKWS9BbjFPOUF0am41M3pvUytubzZ4ajhleC9LSHZkQlBLY28wMU5qMGNVUytuZ2ZkNG1lY2ZlL3dwTDgwQkRNUmc1VXJJd1BwVG9OY2o1ZUJoalJST3NsVTlGY3k4bThCb0xFRzJ4R1h3RHVUQ2RjTkl3K0xHcGdUZGZWRXo3dy9JTUhxaW1EelJNdHF2S0U4c2JuYmhiN1k0d0lUZkhmTE42SFFaUDdRQXFPZjNYemxxTEprZEtsUE5xR1FCeFQ0RGtmNUxtZzQ5NHRwQWJ4UkxBd3JZNVlSYnQzSDBKY2dTWlJzQmU3RzVxc2I2SUVtbW43SWVGOVN3VEtIQjBxSW5yaDdobUc5NEFTNSsiLCJtYWMiOiIwMGMzMzQzZDIxZTYxYzMzM2Q2Mjc0NDdmMWRjZTg2Y2JmMjc4ZmIyY2Q1ZmM1NTYwMWE4MjI5NmU3OWQxNjkyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRkcnBHclVKN05YZS91UUhhMW82SGc9PSIsInZhbHVlIjoibGs1WTIwT1lCaXJlODljaWN3TnllWDY5b3RucmlqcFBEVTRVdjQ1aldpd3dqSjVoVFF2Mm5qSlh6eEpOTGRxUmNuWWxPSHk2eWxpWE42M24zL3VHSHBiOUpHcnB4cmczeTVGeHpkazVJelhNYXovbW9IRURWbzRCd2dRZ1Z2blRGK2FLRktvaVlzSlkzU2RTd01nMjhTZFE3eXRUMVVBWmxWRXd3WkZpL1o3MXlBVW5ZZlpnOFlLUDVqbGZSdC8zcnNmWm0vMjJvL0E4cXFmS3VzWUVPbG9hREdkWFNwWDNnaVZhbjF0c05vRlo3dlFjSDZxRGJsc3hLREZncjVqVmtMMis2K3c5K0ZKc1dhWUR3Qnd2RnpYOWk2TVN2aHRwbjdKa0pNcTJoNHJqN3Vzc0FVUEJSNWFhanhJenRnWDhBczFiR1I3T3l6dWNBQXdBN2krSHZGaFZrT3Fxc093R3luWkkvUThnOUxsUXZqcjh1VXBuaUdVcGxseVFwNDZad0VTYU1ZeE9QR2JYRGV2dmRKSHpJRXo1a2d5MWF4a3czMU5IR3RYd0t3aW1hNVNzcEUzazNjNVJoNWxQV012N1JzTGZUZmFtQnRMK0pWeUJ0RmZzZTRCZCtuTnJiWS90TjlXMTQ0VDVWSTdnR0I3YUxKMUZUcmwrT1U3OUtpdlQiLCJtYWMiOiI0NmNiMjUyZGIwNDdjOWY4N2VjOWYyZDBmMTgzZjNkM2ZiYjBlMGYxN2RiN2I5MjM1NWY5MjdmOWIzMWY3NGU3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IldER2FSbVkxRmlhTmNubCtqY2dKZkE9PSIsInZhbHVlIjoiWGx1UUtUMS95c0RCdllNdVpBY3J3dGI4NU5IalNzNXFtcEViNFo2ZGU1bXVQOGhPd3ZFaE1JNXV6MmpjWDl5VkNCbjIwcnZPZE01NmdYeHpOeks5a2tBQnZ3bXBUaGNKcE9ESG5SV205aGcrYjNzd0tBVzVXSUpKaEg1OWNGcHZRL05ZYmFNUmY2ZHFDaysyZWRNeFFNMW9ocUlZZ254NWlVbHd0UzhGL2RVa1BqTXR0ZlptbHVjQVNmNjNTZjBMSzNKYzBqM0dqZFdGNTJwK3BseEVUb2tEQXB6ZTFYVE1Cb2lVWG54V2J4Y1B5WEpGdktKWS9BbjFPOUF0am41M3pvUytubzZ4ajhleC9LSHZkQlBLY28wMU5qMGNVUytuZ2ZkNG1lY2ZlL3dwTDgwQkRNUmc1VXJJd1BwVG9OY2o1ZUJoalJST3NsVTlGY3k4bThCb0xFRzJ4R1h3RHVUQ2RjTkl3K0xHcGdUZGZWRXo3dy9JTUhxaW1EelJNdHF2S0U4c2JuYmhiN1k0d0lUZkhmTE42SFFaUDdRQXFPZjNYemxxTEprZEtsUE5xR1FCeFQ0RGtmNUxtZzQ5NHRwQWJ4UkxBd3JZNVlSYnQzSDBKY2dTWlJzQmU3RzVxc2I2SUVtbW43SWVGOVN3VEtIQjBxSW5yaDdobUc5NEFTNSsiLCJtYWMiOiIwMGMzMzQzZDIxZTYxYzMzM2Q2Mjc0NDdmMWRjZTg2Y2JmMjc4ZmIyY2Q1ZmM1NTYwMWE4MjI5NmU3OWQxNjkyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044632479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-638952923 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638952923\", {\"maxDepth\":0})</script>\n"}}