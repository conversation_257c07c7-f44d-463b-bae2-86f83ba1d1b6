{"__meta": {"id": "X427d23a773dd12a3fa53500dfff7c1a1", "datetime": "2025-07-30 05:38:59", "utime": **********.179349, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753853938.333177, "end": **********.17938, "duration": 0.8462028503417969, "duration_str": "846ms", "measures": [{"label": "Booting", "start": 1753853938.333177, "relative_start": 0, "end": **********.100123, "relative_end": **********.100123, "duration": 0.7669458389282227, "duration_str": "767ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.100157, "relative_start": 0.****************, "end": **********.179384, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "79.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "vOvhtjerckqBHM16YKj6wJ0QSnoKkexPSStTRf9A", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-111184411 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-111184411\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-751977210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-751977210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263798865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1263798865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1383551390 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383551390\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459039488 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-459039488\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1079837897 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:38:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpveUNoYWtjRXY3bU5kc1MycjlEWlE9PSIsInZhbHVlIjoiWXd5d2ZTaEpHOEJiaVB5aTI5ODZoNjhMa2tQNkRiZUc1eXd4MjlFYjk0d1NIcUgzSzFQZ210VEhjN2xkMitaWmMzKzR6c1NJbEdYc2RQNkNnbDdQUE9PdWZ3eWNqaE9OMmwxdlEvRmNCSzg1dGFjVXMzQThxaVZOSjZBSUc4L3NxSHd5ZG01eWZiWnB3aHJwOEJCN3RLVG5qeEtiOHFsN0JEbldxd0xCMUtPUTJqaXZ6MlZob3ZweEE3M29LdHJpc2pjTm4xWUsyd29RVDdSckh1T1ExZytjNWRNTFlvcGN6bWN6aTNTNjkvK0d5YTh2Tks1RE16TmVJQm9xKzRJMzVkY2M1TTAzODd4cG90b2ZwYTNJblo3bGZaaXg2enpZdDVHWEEyRGVOaHZMbEE1eGZaYWFBakpmUG93OEVsY2VGK2NwemhJWldmZDQ2bWxreGZtRTVNbmJjY2o2eEVFUTFMKzI4T3dkNmVMSkQrWGwrVXlnaithV2lWT3ZFSVFnWGw5bVQ5ZlJOYXJpcE54OGt4MXlpbnBRN0lSWXBvSGR3WTV0QzNEMEhyakhINmwrd0dkb2dvRmV3eHVGL3A2cU5kMFNYSW5aNTBjdGE3SHlQck4xdlBSK0Z5MGg2MEpBOENiNnNSbVdiZFhtcjJtTngrQXhXS0RZWU1SdFFHU2YiLCJtYWMiOiI0ZmFmMGJhMjkxNmI1ZDJjMzVjNDUzNjNkYTc4NzE2ZmZlYWQwMjRjYmUyZGYxYzIzM2QxMjdhYmY4ZTJjZDFlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:38:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9ZTlpsWGNlRUljUk5PTFdzdU5PeGc9PSIsInZhbHVlIjoiUnpWSzBXcGliS2FwbDZsZCttajQrcWpsYk1Dd3hUVHFoS1VVSHdtTnQ0T05rN1hmRlVxRXBmZnovOFNGdnVDMHEyQ0RaTFNYcVdPM2F1MGx4NUFpSVNZUFExK0VmeUZ4N2Nkb3MzOHNuVzAwamVCS3Q3UC9kdVJiRjdpOGRsbjRGQ2h0UmVtRThPS3dkNjFxWEZIeUYzQXBOeFJzbHpzYTdqb0l1Z0ttcDdVb2ZoZnhGZGZSZXg3S2N5RXcycjNwR2tkWnBLdmc5WVJ5d2F3QU5TYkZKeldzWWt2MmVvTkRIRnkvdzg3ZDRlNklEN3ZHcXdFcEM5MUZFMzZ0UHFPZXZDNzZseHl5eHlCZnc0WXUzWHhJZFgrdEErS0Y0dTk0NVFrSG4rU25HUVZQVnovaEFhcm50cklJUHpzMDdzaTRlREQwT1pKMkpJaS9kV2s4VjdjYVRnWWxrdUprVTlEYmNodFU4anFyZlRQS0xRcURuSFZnaUM4eERQSWdPZ3gybzBNdUNMTE1HbUpaUWFRTHJYalRlTHFXMjhySDFyOERqTjVZcU85dWZudEpKbzFaZHZBd2VVS0ZZTlJwS1MvanRvSUExTXpOdHhlclNaVnFQbk02NVpta3N6ekE1UnFjYk1VYUVGalczRlQ5a3dxNTFPMkhtNzJtYk5ZbVAydTQiLCJtYWMiOiI5YzZiYmU5N2VjYTNjMTBkNGQ5YWQ5Y2U5ZDNlNGJjNTM3N2Y4N2ZlOGJlZmNkNWUyZjM1NWM3OGY1ZDM2MDQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:38:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpveUNoYWtjRXY3bU5kc1MycjlEWlE9PSIsInZhbHVlIjoiWXd5d2ZTaEpHOEJiaVB5aTI5ODZoNjhMa2tQNkRiZUc1eXd4MjlFYjk0d1NIcUgzSzFQZ210VEhjN2xkMitaWmMzKzR6c1NJbEdYc2RQNkNnbDdQUE9PdWZ3eWNqaE9OMmwxdlEvRmNCSzg1dGFjVXMzQThxaVZOSjZBSUc4L3NxSHd5ZG01eWZiWnB3aHJwOEJCN3RLVG5qeEtiOHFsN0JEbldxd0xCMUtPUTJqaXZ6MlZob3ZweEE3M29LdHJpc2pjTm4xWUsyd29RVDdSckh1T1ExZytjNWRNTFlvcGN6bWN6aTNTNjkvK0d5YTh2Tks1RE16TmVJQm9xKzRJMzVkY2M1TTAzODd4cG90b2ZwYTNJblo3bGZaaXg2enpZdDVHWEEyRGVOaHZMbEE1eGZaYWFBakpmUG93OEVsY2VGK2NwemhJWldmZDQ2bWxreGZtRTVNbmJjY2o2eEVFUTFMKzI4T3dkNmVMSkQrWGwrVXlnaithV2lWT3ZFSVFnWGw5bVQ5ZlJOYXJpcE54OGt4MXlpbnBRN0lSWXBvSGR3WTV0QzNEMEhyakhINmwrd0dkb2dvRmV3eHVGL3A2cU5kMFNYSW5aNTBjdGE3SHlQck4xdlBSK0Z5MGg2MEpBOENiNnNSbVdiZFhtcjJtTngrQXhXS0RZWU1SdFFHU2YiLCJtYWMiOiI0ZmFmMGJhMjkxNmI1ZDJjMzVjNDUzNjNkYTc4NzE2ZmZlYWQwMjRjYmUyZGYxYzIzM2QxMjdhYmY4ZTJjZDFlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:38:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9ZTlpsWGNlRUljUk5PTFdzdU5PeGc9PSIsInZhbHVlIjoiUnpWSzBXcGliS2FwbDZsZCttajQrcWpsYk1Dd3hUVHFoS1VVSHdtTnQ0T05rN1hmRlVxRXBmZnovOFNGdnVDMHEyQ0RaTFNYcVdPM2F1MGx4NUFpSVNZUFExK0VmeUZ4N2Nkb3MzOHNuVzAwamVCS3Q3UC9kdVJiRjdpOGRsbjRGQ2h0UmVtRThPS3dkNjFxWEZIeUYzQXBOeFJzbHpzYTdqb0l1Z0ttcDdVb2ZoZnhGZGZSZXg3S2N5RXcycjNwR2tkWnBLdmc5WVJ5d2F3QU5TYkZKeldzWWt2MmVvTkRIRnkvdzg3ZDRlNklEN3ZHcXdFcEM5MUZFMzZ0UHFPZXZDNzZseHl5eHlCZnc0WXUzWHhJZFgrdEErS0Y0dTk0NVFrSG4rU25HUVZQVnovaEFhcm50cklJUHpzMDdzaTRlREQwT1pKMkpJaS9kV2s4VjdjYVRnWWxrdUprVTlEYmNodFU4anFyZlRQS0xRcURuSFZnaUM4eERQSWdPZ3gybzBNdUNMTE1HbUpaUWFRTHJYalRlTHFXMjhySDFyOERqTjVZcU85dWZudEpKbzFaZHZBd2VVS0ZZTlJwS1MvanRvSUExTXpOdHhlclNaVnFQbk02NVpta3N6ekE1UnFjYk1VYUVGalczRlQ5a3dxNTFPMkhtNzJtYk5ZbVAydTQiLCJtYWMiOiI5YzZiYmU5N2VjYTNjMTBkNGQ5YWQ5Y2U5ZDNlNGJjNTM3N2Y4N2ZlOGJlZmNkNWUyZjM1NWM3OGY1ZDM2MDQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:38:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079837897\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1261758369 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vOvhtjerckqBHM16YKj6wJ0QSnoKkexPSStTRf9A</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261758369\", {\"maxDepth\":0})</script>\n"}}