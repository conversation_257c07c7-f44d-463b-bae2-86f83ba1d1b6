{"__meta": {"id": "X2aa56b1d5f0f03ee04a8d84397f07436", "datetime": "2025-07-30 02:38:58", "utime": **********.753379, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843135.854303, "end": **********.75344, "duration": 2.899137020111084, "duration_str": "2.9s", "measures": [{"label": "Booting", "start": 1753843135.854303, "relative_start": 0, "end": **********.533286, "relative_end": **********.533286, "duration": 2.678983211517334, "duration_str": "2.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.53333, "relative_start": 2.****************, "end": **********.753446, "relative_end": 6.198883056640625e-06, "duration": 0.*****************, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "X4nzcZT0Yl6QyRdxTWgtNwWAL21f5PXLLwaDJaTL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1792226672 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1792226672\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1115662370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115662370\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2114819590 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2114819590\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1632640615 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632640615\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-946098523 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-946098523\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1048221285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:38:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZhQXoyckJFb2ttY0tlMFBzUlkxbVE9PSIsInZhbHVlIjoiMVpxL1c5N1p1OUEwcEgvRWIzOFJOQmY2WlN4dEJSSndkL1dFenphOW1XdVJRNk43QldoRFJCY1MrUUlzOWFOQ0tNWG5kdXM3UEpjbTVOY3QwWko3eThvcHlqakRqaXVmd0pvVkk4Q0tRVndpdDMvUWs5ak9qemZoNjIyUk5CdTU5d3BqZmJoOGU2dVczdHFTN0kyQ2h5Yk96VndqakVkOFAxa3NjZGZGUk1tS1c4SkgvRlhzdXFWL1ZjaGdaZjVNM1o4VTFqZUw2eDN6ZUlCTktTUTVGUCtZV1dOZnNrVlFzRFFjN3pXblNjRythK3RUQ2NvQkErYlpENTByMmpQekNqdTB5ZWhsMkltem1KRmJ2Ym9lNDQ5TUtiQmwzaFBMVmw5N2lqdVJVK3gyc3ZyQzc2UEoxSXg5NHVnT3U0b2ZNOTlHdW1jL1RDOGIwZDF1Wk5uTU1ONDBRVHR4QnB4VWY5eGw3RTdTNThlbEFzcHEyOWQxMUFIYzN1QlRVYW1Jb2ZDaWVuQ1dLRElwNmxnWVNxSnpyc2ZicjJFdEkyZ1pyZWZXZjV3TldYaWxJS2JoVUVmajhyQWhKMVB5K2NVbGcxWTVlZjU0R3BhSlB3YzlWRlRDNGhxRnNBT2FpZzZRMWd2ZXNMZk42ZWxOZnc2clVtbTVEdXJhMFVQSzhDTk8iLCJtYWMiOiI5ZDZmYWVjNTAwYTQzMzgwOGE1NjcwOGJlZmVmYTQxNmE5MmRlNmY4MWVjY2E5OTcxNDllOTA4MWZjNWM3ZGZkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:38:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkRwNVBuazJDcGlWeW5LcDk0L1pwVFE9PSIsInZhbHVlIjoiRlVDODVGM0dEUzlMRUNmWk44OEJ0MEQ4Q0NyL0lWNlZOTFBJcnVsc2k5andzUlR2ZUpLdGlNWFlNdmRkWmdBWXBZb2NMT0ViYW1WQjRNT3NUZytIcnhaWFl6UU5kQmRDNGpreCs3N29TNVlJdjFoalhKT01PMUh4ZTdGSTBnL1V6V1lSM1NWb0MzQTFzeXBoTmVMc3lybllRVjNoMXV1SlM1TVRpNkZ6R1ViQi9zdEdsQ3g3N0JPYndOSEloMkpnUXFpa0VwSkhSUGFsTEgwb01FNFJBbjl5NFh5KytxTWxkdzFVY1hPUlBBM3FtWGtKQmd5Q3N6TXMyT0llNXdCSHo2TU45MTVvTmNrKzV2MytPeXd4dG9kdk5BMDZqanJtenBTV1BtaVpkczRmV1RXcEpuUXYyOUpxbmxtOXlvdk9LS292eUVXdVVjN0hkS2hCT054WE5QS0RqK3lsL3JYTGRldkhFbE93eUhRSmxWaFkzSmc2ZC91ZWxtQktaMUw0UEhmTG91SFJRT3Ayb21iNTJ6K1ZDTkQ2b0pyUTE5L2FyZWtwRHprbUVpUlEwQksyandIdUJwalZyam5YdVZ4SUJVejNFcjJlZFRuYVZ6UUZ4OEZJQlBtOWxGODJTWWdpdmtvUktJZVc0SmFXeWgwbTFrckFxWEtaYkxLM1NieDMiLCJtYWMiOiJjMjAyNzMwOTQ1MTNhMTMwY2M1YWI5N2ZkZmU0ZmIyMzhlMDM2N2IzZjI5YWE0ZTkyNzIwMmFhMzg4YjBlMTBhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:38:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZhQXoyckJFb2ttY0tlMFBzUlkxbVE9PSIsInZhbHVlIjoiMVpxL1c5N1p1OUEwcEgvRWIzOFJOQmY2WlN4dEJSSndkL1dFenphOW1XdVJRNk43QldoRFJCY1MrUUlzOWFOQ0tNWG5kdXM3UEpjbTVOY3QwWko3eThvcHlqakRqaXVmd0pvVkk4Q0tRVndpdDMvUWs5ak9qemZoNjIyUk5CdTU5d3BqZmJoOGU2dVczdHFTN0kyQ2h5Yk96VndqakVkOFAxa3NjZGZGUk1tS1c4SkgvRlhzdXFWL1ZjaGdaZjVNM1o4VTFqZUw2eDN6ZUlCTktTUTVGUCtZV1dOZnNrVlFzRFFjN3pXblNjRythK3RUQ2NvQkErYlpENTByMmpQekNqdTB5ZWhsMkltem1KRmJ2Ym9lNDQ5TUtiQmwzaFBMVmw5N2lqdVJVK3gyc3ZyQzc2UEoxSXg5NHVnT3U0b2ZNOTlHdW1jL1RDOGIwZDF1Wk5uTU1ONDBRVHR4QnB4VWY5eGw3RTdTNThlbEFzcHEyOWQxMUFIYzN1QlRVYW1Jb2ZDaWVuQ1dLRElwNmxnWVNxSnpyc2ZicjJFdEkyZ1pyZWZXZjV3TldYaWxJS2JoVUVmajhyQWhKMVB5K2NVbGcxWTVlZjU0R3BhSlB3YzlWRlRDNGhxRnNBT2FpZzZRMWd2ZXNMZk42ZWxOZnc2clVtbTVEdXJhMFVQSzhDTk8iLCJtYWMiOiI5ZDZmYWVjNTAwYTQzMzgwOGE1NjcwOGJlZmVmYTQxNmE5MmRlNmY4MWVjY2E5OTcxNDllOTA4MWZjNWM3ZGZkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:38:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkRwNVBuazJDcGlWeW5LcDk0L1pwVFE9PSIsInZhbHVlIjoiRlVDODVGM0dEUzlMRUNmWk44OEJ0MEQ4Q0NyL0lWNlZOTFBJcnVsc2k5andzUlR2ZUpLdGlNWFlNdmRkWmdBWXBZb2NMT0ViYW1WQjRNT3NUZytIcnhaWFl6UU5kQmRDNGpreCs3N29TNVlJdjFoalhKT01PMUh4ZTdGSTBnL1V6V1lSM1NWb0MzQTFzeXBoTmVMc3lybllRVjNoMXV1SlM1TVRpNkZ6R1ViQi9zdEdsQ3g3N0JPYndOSEloMkpnUXFpa0VwSkhSUGFsTEgwb01FNFJBbjl5NFh5KytxTWxkdzFVY1hPUlBBM3FtWGtKQmd5Q3N6TXMyT0llNXdCSHo2TU45MTVvTmNrKzV2MytPeXd4dG9kdk5BMDZqanJtenBTV1BtaVpkczRmV1RXcEpuUXYyOUpxbmxtOXlvdk9LS292eUVXdVVjN0hkS2hCT054WE5QS0RqK3lsL3JYTGRldkhFbE93eUhRSmxWaFkzSmc2ZC91ZWxtQktaMUw0UEhmTG91SFJRT3Ayb21iNTJ6K1ZDTkQ2b0pyUTE5L2FyZWtwRHprbUVpUlEwQksyandIdUJwalZyam5YdVZ4SUJVejNFcjJlZFRuYVZ6UUZ4OEZJQlBtOWxGODJTWWdpdmtvUktJZVc0SmFXeWgwbTFrckFxWEtaYkxLM1NieDMiLCJtYWMiOiJjMjAyNzMwOTQ1MTNhMTMwY2M1YWI5N2ZkZmU0ZmIyMzhlMDM2N2IzZjI5YWE0ZTkyNzIwMmFhMzg4YjBlMTBhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:38:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048221285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1415859907 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">X4nzcZT0Yl6QyRdxTWgtNwWAL21f5PXLLwaDJaTL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415859907\", {\"maxDepth\":0})</script>\n"}}