{"__meta": {"id": "X65661161f2ed12f9d1e39dc4847a8f32", "datetime": "2025-07-30 08:24:55", "utime": **********.300988, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863891.303463, "end": **********.301044, "duration": 3.9975810050964355, "duration_str": "4s", "measures": [{"label": "Booting", "start": 1753863891.303463, "relative_start": 0, "end": **********.548863, "relative_end": **********.548863, "duration": 3.2453999519348145, "duration_str": "3.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.549278, "relative_start": 3.2458150386810303, "end": **********.30105, "relative_end": 5.9604644775390625e-06, "duration": 0.7517719268798828, "duration_str": "752ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.12769, "accumulated_duration_str": "128ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.962264, "duration": 0.11361, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 88.973}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.127684, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 88.973, "width_percent": 3.375}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1433258, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 92.349, "width_percent": 3.759}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1602628, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 96.108, "width_percent": 3.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1375498430 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1375498430\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1816240904 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816240904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1281513171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1281513171\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-409521882 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImtwYW0xNzR6RWpQY2pSVDlpc0Vkb3c9PSIsInZhbHVlIjoiUXNvdlZjYUNMTU1LRHowMHVBSHZ2VDI5M0JwbGJ0RnJ1dzFicURJSGZCTTJPM0VQVUhWSHpza3N0L3FUdHRQSHhCNHJ3NC8ySmxSMlh4R2FQeFhzcndwR0tmQmRsVXpiNng1Z2t5eWRPMEpORXdRUXRTY3lRdVN6RURMdkVDQUZxWUZSNEl5LzlXc3VQT3ZTMHQ5Q0trU1RyZHZGK2huZmJzb1JzOENZdUJpZVhiemZLNXQ5L2oyemtjOFB3K0lpdkVYbDVjbWZ5a2pOdjBwaVYvSUh1ZDE5MkxnVjZENDFkSnZWQUQvS3R0R1VYNS9UdWFHQ2E3bExNeXE5dGlTV0Y4U2s1KzZGd0R4a2FvODV2Q1lQUWlCb1hBeVdHQXRBd1dWQmVQRWdWSkZGdU9XMWNvN1REdHF4a201dlQ0bFBRU2lDRDMzSWJ5VlpDdGdPbTFWWXF3R3RwY043YlFxTnFDbXIrTmFxZ3VIME1ub2xld0FSQlhLN0UrTmhEdzUxY1Z3dnRpaUJMNmlCTEsxcWduQk9DSXlyZFk2V2FtM1g4eE5QbUx1WExZcndLYmxqb3AzU0U5Wk9ad2REdGkxeDdaUmNOR3ZFYnZZUm04dEtuQmJuMTFnaGxvZEFmWHQxS3E3V0dNYUVDdllFVVdVTDZOZG9SQTkxb3QxdWE5TGciLCJtYWMiOiJhOTJjY2QwZmVjNDczMDE5NGU3Y2ZlNDNiZmViMjQ5MDlmODllY2VlNzMzYzhlYTA1ODJlMmUzY2Y4YmM5NWZiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkV4WmRzQ3FWR25XWjlFeTRDUFdzRUE9PSIsInZhbHVlIjoiSjgwMHliaWwxamY1WnhONW1OM0ZTZDQyVWNCZE8zaTFRaEkyVGttSUtIK3FlQUlNZlhqbU5JOTZ2ZmdHUnJscmM1ckZEZEdycEd3N3N3eDhIK2ExTG95UUZXOU9xMkVCWTZyamcwSnZ3M3o4T1d4YVB0YndtUXJVanVvWlBiYmRLUGZHWUgzQ2s4b2pCWm0xdmlBVHNodXpmc0Y3RUxFMFhtQmxraE45SnNJYVpIc0ErRjgyUnV3eFFNRnh3bXAwVllvZkVHK0luM294OW5FYWo0eU03N1J3UXZyNzJYVk1pRlVxaTZVSUQvaUhWTnIrOTRKRlZ2dFVnVDJLS2htUE9RNDFVeDJ2WWYySm0rL0pwM2VucVdXMkh4U3M0OXpqSHYvU2Yrc09YL2ZoQkY5L2kwempSeVZkYmhDUUZXSFdDMHdmeVpGbWJLUkZRU1NYTDg4dVNucXdIV2VpbHRGRkJPbXluYVRoOStUcFA1b3lHVUFjV0JkWnY3TWtMSzAwTEFDSU52TGpQZzNNa0kwWXVmUytBS2pHYVdvZHBuTWhnaDVkcStzV3lsZkpwL04zN0lCdjVDTzNYWGdpcjk4c0Q5NktDZXZzVVgzR1R2MWNPTDduWUU1YktNZ29TRTJjOHNreUlIL21jSmlQMG1jMkhoYkVlTC9NUU5nV2I1akciLCJtYWMiOiI5MWFjNGJkZjllZWNiZmJlNmM5NTQ1ZjM1MzUwODVjNTk0YTUxODgwZWYyOTY0OTYzYjk5MTUxNTVkMTg0MGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409521882\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1779561841 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779561841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1030513549 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:24:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNHZkdQeDhyYXlQN2trRERuaFlWZlE9PSIsInZhbHVlIjoiTEFWRTQ2dmZ5TWljUnJ6ekE0U0FpdEtNN3k3UFo4VFBvSzRrU202VmVYRHF2T1NFM041R1Q2ZTk4cjRFMkRCV1UxclZwMFd6VmJGakJvcytqbllIMUFPb0JjQWl5V3pYeGxNT0JySnlieDl4Q0dPVnMwNE5tV2hmNmRQRiszbjhqN0ZlRzRuazFRQmc5TW1uZmlRQmRqZFN3Z0NiNE1JbitBYW1Xd3VRWGFRZmxic1pTd2hpdWVEV1N0SVl0dUF4dWphQTV5bEdCWHVjQWlFb04vTXJzVlBWZ0hTQThUMndiU2dVMU5hS2hKL2lHT01aOW51TWQzcXR6OFo4enRFcHFUTTIwcGRoVHlCeHlpT2F4M1NMRVVlU1RjaStqeW5IblVzVnNHN21OTFJadGUyTFZRS0c1RisyMkxycFR4SjY1dkhjZEhDVmwvZlRYa2xnRVcwZlFYYVREVURSN3V4L3BKamRLM2xCWWx1NkZtK0ZhVTZSeDMxQjI3SkR2aGFGSDJsUFRBeXJLdmJvNzAzd3ZzYzBxVDIvMUpnZGYyQ3BlRWZaTjNlbjNLMHJ4TmFmdEttWFJhNVdWMldLZDBTWXZIbnZuSUhTYVF2bHFkOXVQMTM4UzFzZzRLQndWR25vOTZZaE4yUVNBQVdETk9pVk40bWVsTjJKYW5NZ1BaT2siLCJtYWMiOiJkZjY5OTA3Y2YzN2JlODYyM2U2YzUzYmI0Nzc2MWJmZmZmZDQwZGI2MDJjMjUzMGYwNjc1ODI3ZDVmMTBmZDRjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:24:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZ6bjF6aXZ5VzkrY3g2YWRZVTQ1RWc9PSIsInZhbHVlIjoiODNINjRqcFhkbkluNGVKaVcxbWxLcWI3T0pzMHdhYXhKa1p3dkNUZlg3YXNmQmllaGZUanlOeUI0aXdGeG9ZK3FoK3RaWERJbDNEMlBvQkRicnlsNnFWQy82RVNmRkF5SmhBVkswelNLT1l4ZkM2aDZ0ZnluQ3F6VUlZYXFSQmIrWmRBZCtUdUwvNm1QaDd6cmh1RHhKNGNHUnFTOHZCWm9ZYW5KbWVicEVSSlRCV2hsNERrZkRaVDdyYTBxYk44dXQyY3c0a3dQamNML1JRV3VtbVdCVlNkdFVmOXVxWXZFWTJLdlVNT0pvbWxIbHo4TWZtTzNxcFFXV29ZMEtqeXZxZGFCUGxKcmlHL2x5a3lOcGt5RjZIWWNDRVB2dnRKM2xWdnp0WGNHZDVKZWNnZHNHQ2VldFlHTFV4cXJFZGdlR0Y0ZmZ5RzRiMEloTnlBMXRTaEJZV0V5QzdWdDlvWEZ5RHN2STJRNHd4WDR5bjBrRU1sR3hGYlBDaGRjS1VUeXVFL2x3Q2o5dDh5d3pSSUNROEdRR3d2ZUNPeXhNNzRqZWd5bHVqM1A5ZVZTM012bk5VMWlTeGQwV1hWSitPMDV2RFkzSDU3cjdvMmIxYlF6M213RjVmRkxFMUFSMTEyT2d0VTRTbiswcUpHenZiSEpCb2NkczROVXB6bXpUcEYiLCJtYWMiOiIyZDE4NmUwMTg3MDQ4NzMxYzM4MGRlYjg4MzFjZjk1MDRmZjg3YTYwODkxYzM3NTAzYmEyODA4ZTRkNjNjYzc1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:24:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNHZkdQeDhyYXlQN2trRERuaFlWZlE9PSIsInZhbHVlIjoiTEFWRTQ2dmZ5TWljUnJ6ekE0U0FpdEtNN3k3UFo4VFBvSzRrU202VmVYRHF2T1NFM041R1Q2ZTk4cjRFMkRCV1UxclZwMFd6VmJGakJvcytqbllIMUFPb0JjQWl5V3pYeGxNT0JySnlieDl4Q0dPVnMwNE5tV2hmNmRQRiszbjhqN0ZlRzRuazFRQmc5TW1uZmlRQmRqZFN3Z0NiNE1JbitBYW1Xd3VRWGFRZmxic1pTd2hpdWVEV1N0SVl0dUF4dWphQTV5bEdCWHVjQWlFb04vTXJzVlBWZ0hTQThUMndiU2dVMU5hS2hKL2lHT01aOW51TWQzcXR6OFo4enRFcHFUTTIwcGRoVHlCeHlpT2F4M1NMRVVlU1RjaStqeW5IblVzVnNHN21OTFJadGUyTFZRS0c1RisyMkxycFR4SjY1dkhjZEhDVmwvZlRYa2xnRVcwZlFYYVREVURSN3V4L3BKamRLM2xCWWx1NkZtK0ZhVTZSeDMxQjI3SkR2aGFGSDJsUFRBeXJLdmJvNzAzd3ZzYzBxVDIvMUpnZGYyQ3BlRWZaTjNlbjNLMHJ4TmFmdEttWFJhNVdWMldLZDBTWXZIbnZuSUhTYVF2bHFkOXVQMTM4UzFzZzRLQndWR25vOTZZaE4yUVNBQVdETk9pVk40bWVsTjJKYW5NZ1BaT2siLCJtYWMiOiJkZjY5OTA3Y2YzN2JlODYyM2U2YzUzYmI0Nzc2MWJmZmZmZDQwZGI2MDJjMjUzMGYwNjc1ODI3ZDVmMTBmZDRjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:24:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZ6bjF6aXZ5VzkrY3g2YWRZVTQ1RWc9PSIsInZhbHVlIjoiODNINjRqcFhkbkluNGVKaVcxbWxLcWI3T0pzMHdhYXhKa1p3dkNUZlg3YXNmQmllaGZUanlOeUI0aXdGeG9ZK3FoK3RaWERJbDNEMlBvQkRicnlsNnFWQy82RVNmRkF5SmhBVkswelNLT1l4ZkM2aDZ0ZnluQ3F6VUlZYXFSQmIrWmRBZCtUdUwvNm1QaDd6cmh1RHhKNGNHUnFTOHZCWm9ZYW5KbWVicEVSSlRCV2hsNERrZkRaVDdyYTBxYk44dXQyY3c0a3dQamNML1JRV3VtbVdCVlNkdFVmOXVxWXZFWTJLdlVNT0pvbWxIbHo4TWZtTzNxcFFXV29ZMEtqeXZxZGFCUGxKcmlHL2x5a3lOcGt5RjZIWWNDRVB2dnRKM2xWdnp0WGNHZDVKZWNnZHNHQ2VldFlHTFV4cXJFZGdlR0Y0ZmZ5RzRiMEloTnlBMXRTaEJZV0V5QzdWdDlvWEZ5RHN2STJRNHd4WDR5bjBrRU1sR3hGYlBDaGRjS1VUeXVFL2x3Q2o5dDh5d3pSSUNROEdRR3d2ZUNPeXhNNzRqZWd5bHVqM1A5ZVZTM012bk5VMWlTeGQwV1hWSitPMDV2RFkzSDU3cjdvMmIxYlF6M213RjVmRkxFMUFSMTEyT2d0VTRTbiswcUpHenZiSEpCb2NkczROVXB6bXpUcEYiLCJtYWMiOiIyZDE4NmUwMTg3MDQ4NzMxYzM4MGRlYjg4MzFjZjk1MDRmZjg3YTYwODkxYzM3NTAzYmEyODA4ZTRkNjNjYzc1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:24:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030513549\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-49334821 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49334821\", {\"maxDepth\":0})</script>\n"}}