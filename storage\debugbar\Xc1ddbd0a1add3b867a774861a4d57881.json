{"__meta": {"id": "Xc1ddbd0a1add3b867a774861a4d57881", "datetime": "2025-07-30 08:02:57", "utime": **********.290087, "method": "GET", "uri": "/finance/sales/contacts/lead/13", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862575.435872, "end": **********.290128, "duration": 1.8542559146881104, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": 1753862575.435872, "relative_start": 0, "end": **********.088952, "relative_end": **********.088952, "duration": 1.6530799865722656, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.089018, "relative_start": 1.6531460285186768, "end": **********.290132, "relative_end": 4.0531158447265625e-06, "duration": 0.20111393928527832, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02478, "accumulated_duration_str": "24.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.198189, "duration": 0.02188, "duration_str": "21.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 88.297}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.251865, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 88.297, "width_percent": 6.497}, {"sql": "select * from `leads` where `id` = '13' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["13", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.262981, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 94.794, "width_percent": 5.206}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/13", "status_code": "<pre class=sf-dump id=sf-dump-1743123160 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1743123160\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-343295612 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-343295612\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1985528513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1985528513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1663394112 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Imd4S0loTW5aOVYzSDBpaU43K3JvU1E9PSIsInZhbHVlIjoic3orVVltallZQ2dTNXQ2dEY4SW4xOTFqR0tUYmlyUVV4VThYU0VCakRvMlR6Nmd0UmJFZHlOTWRLdUdWYUxzbzd1QzY3UU13b3dqSVZ2R0ZvVytQUXpyaVdVQ2Vnb01KbnBjakl2ZkdBeGxzUU40QldyVXZ1Mks5clNrMUcrZHBqVHhWZnI3T3ppRkgyNDV6ZmpHcWJCK0RGWTcrdE5wd2NkWGcwM0NvZ0ZWamh1NWJJeVYyY1c4bVprdEc5R0hCQWpsMXpkT2NOd1FLdFVOZTJrR09DMFhEOE5oT1hPNFEvKzZ0Z0NkQ05hdXVnTXJCZjg0UDViQnNpQ041b3BGODRCSm1pQWw2c1NzSklwQ2pMNmVvL3RLRnExRk9RVFpYYmVoeWpjVTRtV2tnUXZMbm9YSjJXYVhCTXIvQ0JYdnFuUkhGRXVNUDMxbFFMazlBNGc3SVE3QUIzTDNYTkRpZGxMRk5id0hxdGxkWWJNRjVGR0Nmc3dTM3NYUWNlTTRPTUp3bHIrbG1mZ3Vad3cvMlgvekN4ekFNVXFjeGYzbG5rdGFINFdTamVYd1h6TVIzZEsrTjIvbGhUeWhkaVlLOHpVTTI4aUo1NllvbWplbmprOTZPVDRZdHNZM212TXJPajZqU2pVRWwzMytyeHpXMi9UdU9YcGtwbmJ2VzhmWTEiLCJtYWMiOiJiYjM5MTgxNGExZjVhZDBkM2I5MTMwMjBmZDZmNzdmMThlNDgzY2QyZmJjNTA5MzlhY2Q1NmNkZTU0YTkwYjQzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InQ1NEFMZ0NKMEhCckpjNzlHQ2NuUUE9PSIsInZhbHVlIjoiVXk3VGJNR2pNYUFZQWFLNlptck0zdG9RQnV3TSswQ0JzR1E2LzNZeEVBWUxYZWNmNVBVclRnVGZaV1ZHVHoxNVZka3dnT0QxL3hVT1I2aTU5ZXhhYkFwaVFRYjdiVWthbkFFUmlnMzhSMG9BT2V4NDJxRzVBa3JMUnptSUc1NVhvSU1MMndIYkVNNzRvR1lvM0hvZ20zSDUrWDVjQldKVEVQQ1laVlR1Witsa3c0aEV1eDRzK1VQSUpnRVJNVGNDa2FNWkRpVHAyanMxQ0ptT0R3OUMvTDZyT0ltWms5dlMwMmtTcndTbWE1TTZlS0pSL2U2M3RVL2gxcHRCZTBMY045VWFpTTNTRTUrWnYrNkdicmd3Q0gxWE1TR2M2RXNTbHN4NVFkQWN2NHpUZ1l2b1lqVXhaTW5FNktpSFNCTWZ3R2xBN3dzUW02NHRXMHRxb1d6VzhKb2c1U1EyaGFHQmNxUmNqTXlJVS8vWVVLaWFXcy95c3oyc3g3eTl5OGNldUdvK2dPa2JQZktWR3dkN0ZuRnc3MnlzN0FGOGZrVFBVekZOTnNIb3N5dDQ0MHhqdU1QcXBwcjFVM1NFZWVJeGMvZmtvd1B3QmxWbXEwRFE0N0Rib2NXcFp0YUpVaHFYTk10Z3F5OXFtS3hxWWhUOEpoSVR2b09TUWJjUkJKbkIiLCJtYWMiOiIyZDE1OTEyMTcxZmIyMDdmOTgyZWY5OTUzNjM1YjM2MTE3MzE3YmUzNTVhNzNlZmE5ZmRmYjM0NTViYWQwNGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663394112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-734455647 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734455647\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-178441380 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFDSUJ5dmQrLzJIT0p1UlNrb3BTeUE9PSIsInZhbHVlIjoiWU1xazBZTy90QW05Q3RISDlGeHdxa0tFV005Y2NETDJLdFFiZHRPU0sxbjZ3TE9kaFdQbVlDakR6VmE4UkNMWFEreW8yTDNBeVpqTlgvengyMDZtLzlrVFF0NkFmaGFpWlhZRVVTRnJPTi9qUWlqMExSMUxSWi9WSzRoRUtFek55L2JPQ1lZQVBOUjE1WjVzOVRBQUYwajNnV2Vmc0RrOU9rRURnd2dPTVpWZlpzMmRNQXlJenkzNXd5UW1wRDVOei9QUHQ1S3htOGhLalg1TTkrYU5NSW15V2ZxUjVvTmpKU0hpdnFnSVpjNmtWVGVXNXFrbDhjZllsbW54RmlYTWhjSjBNaDB4clM2dVhSQU8vazdUdDU1bk9DR2Rkekl6TjRPS0FwVnpGeEg3d1VRTFVyYjlERGllNmJ2YkV1V1ZDMFhTN3k4cThScmczVC80WU0xTWRvUzdQZ1JKWER4LzZscWYrSU03UTlRL1MrQlZkNVZXVUxxM2JXWXpuZWFMV3RCMVJrajh5RWdLZU9NOUR5OFQ4ZDA5ampmTUpBdzNlVS9JM3JTWkd0WEg5Mm5wZnhhVVpSVS8vVUc3NUFrTTRBVlNUK2pKK0dNRzlPd1pCVE0xd0wwVkhweVRWYUVEQVZMdVdrT05kK0lWT2FHalRJT3RrMVpsR1dXSGlYWnIiLCJtYWMiOiIwNTViNTI2OWM5NTY0ZGRhNjlhZGI5YzNkOTQ1ZDZlN2U4OGVlNzE5Zjk0YjY2MDRlZjQ2ZGMwMjU3NDI1YWRkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklxbmI4Z2tTeU5FajFkL2U2Tmx0bGc9PSIsInZhbHVlIjoiRVhHZlBFL2luTkpjckVtQ3ViV1dMY1JQeVVNMUM5R0dVaUw0VHRGcUFkZzVIeDZUQlQzYllndFVraGpSd1V5ZCtCOHd4NHhsV2ZKUzh4ZWY5V28vWU1qZDRWZnQrNkxmM1FHdEsxbE4wNDJQZlF4UnlNVlRtYzNBWE5DbXVwVUUzN1N6ZUtNVVowWGtKa1A1S2hETTIyRkRaN2h0NWFKVGZUMzNrdG91OU5TSXQ2MnhVN3I0cVJCSVltdnNPV0FUT0hEUEZScFJmbzRqOWNnQjZvYWh6YXAyV01pSmU0ei9JSlNDS2pQZEMwVEJBOTlQRE9HNnMzQU5SZEVHUjJJcndBVXNRaFo0QktDOXJYSVFtaCs3VnM3V2o1aFcxMENkSmpMY1pPRXhNdmJnanFHOUQ5TFpSVDRwM0JYeVpZZUNFUGdOQzh4K0UxalJBNGpIY1B4T1BMVEp2T1hrQTZFTzdWeFAvN2I5WFhaNHV1NW1XdTFmVlg5eU80cDcydVQ1U3NZTS83TFRIblprYzRvL0o4a1FKc2N0VXdrcUl6bWlkNGZyKy9rZlJSVzAwTXBKVXdrVlozMXBGczgwZzBCV3VnMnIrMGs3ZWFWajlnQ0pZQzZOMG5ONzFkNWt3disydmVXZGVsRnBZRHI0eUxCb2FYeHZtQTlHZkJOUjBnQ0oiLCJtYWMiOiIyOGQ4MWZlODRlOTg0YzMzNTQzOTFhYzlhN2U4MmMyYWE3OWYzNTNkNzlmZjNlMjhlOTg5NDI3MDEzOTA4ZDZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFDSUJ5dmQrLzJIT0p1UlNrb3BTeUE9PSIsInZhbHVlIjoiWU1xazBZTy90QW05Q3RISDlGeHdxa0tFV005Y2NETDJLdFFiZHRPU0sxbjZ3TE9kaFdQbVlDakR6VmE4UkNMWFEreW8yTDNBeVpqTlgvengyMDZtLzlrVFF0NkFmaGFpWlhZRVVTRnJPTi9qUWlqMExSMUxSWi9WSzRoRUtFek55L2JPQ1lZQVBOUjE1WjVzOVRBQUYwajNnV2Vmc0RrOU9rRURnd2dPTVpWZlpzMmRNQXlJenkzNXd5UW1wRDVOei9QUHQ1S3htOGhLalg1TTkrYU5NSW15V2ZxUjVvTmpKU0hpdnFnSVpjNmtWVGVXNXFrbDhjZllsbW54RmlYTWhjSjBNaDB4clM2dVhSQU8vazdUdDU1bk9DR2Rkekl6TjRPS0FwVnpGeEg3d1VRTFVyYjlERGllNmJ2YkV1V1ZDMFhTN3k4cThScmczVC80WU0xTWRvUzdQZ1JKWER4LzZscWYrSU03UTlRL1MrQlZkNVZXVUxxM2JXWXpuZWFMV3RCMVJrajh5RWdLZU9NOUR5OFQ4ZDA5ampmTUpBdzNlVS9JM3JTWkd0WEg5Mm5wZnhhVVpSVS8vVUc3NUFrTTRBVlNUK2pKK0dNRzlPd1pCVE0xd0wwVkhweVRWYUVEQVZMdVdrT05kK0lWT2FHalRJT3RrMVpsR1dXSGlYWnIiLCJtYWMiOiIwNTViNTI2OWM5NTY0ZGRhNjlhZGI5YzNkOTQ1ZDZlN2U4OGVlNzE5Zjk0YjY2MDRlZjQ2ZGMwMjU3NDI1YWRkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklxbmI4Z2tTeU5FajFkL2U2Tmx0bGc9PSIsInZhbHVlIjoiRVhHZlBFL2luTkpjckVtQ3ViV1dMY1JQeVVNMUM5R0dVaUw0VHRGcUFkZzVIeDZUQlQzYllndFVraGpSd1V5ZCtCOHd4NHhsV2ZKUzh4ZWY5V28vWU1qZDRWZnQrNkxmM1FHdEsxbE4wNDJQZlF4UnlNVlRtYzNBWE5DbXVwVUUzN1N6ZUtNVVowWGtKa1A1S2hETTIyRkRaN2h0NWFKVGZUMzNrdG91OU5TSXQ2MnhVN3I0cVJCSVltdnNPV0FUT0hEUEZScFJmbzRqOWNnQjZvYWh6YXAyV01pSmU0ei9JSlNDS2pQZEMwVEJBOTlQRE9HNnMzQU5SZEVHUjJJcndBVXNRaFo0QktDOXJYSVFtaCs3VnM3V2o1aFcxMENkSmpMY1pPRXhNdmJnanFHOUQ5TFpSVDRwM0JYeVpZZUNFUGdOQzh4K0UxalJBNGpIY1B4T1BMVEp2T1hrQTZFTzdWeFAvN2I5WFhaNHV1NW1XdTFmVlg5eU80cDcydVQ1U3NZTS83TFRIblprYzRvL0o4a1FKc2N0VXdrcUl6bWlkNGZyKy9rZlJSVzAwTXBKVXdrVlozMXBGczgwZzBCV3VnMnIrMGs3ZWFWajlnQ0pZQzZOMG5ONzFkNWt3disydmVXZGVsRnBZRHI0eUxCb2FYeHZtQTlHZkJOUjBnQ0oiLCJtYWMiOiIyOGQ4MWZlODRlOTg0YzMzNTQzOTFhYzlhN2U4MmMyYWE3OWYzNTNkNzlmZjNlMjhlOTg5NDI3MDEzOTA4ZDZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178441380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1283468044 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283468044\", {\"maxDepth\":0})</script>\n"}}