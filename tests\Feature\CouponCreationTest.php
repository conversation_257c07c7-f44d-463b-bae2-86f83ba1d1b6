<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Coupon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CouponCreationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with appropriate permissions
        $this->user = User::factory()->create([
            'type' => 'company',
            'created_by' => 1
        ]);
    }

    /** @test */
    public function it_can_create_a_coupon_via_ajax()
    {
        $this->actingAs($this->user);

        $couponData = [
            'name' => 'Test Coupon',
            'code' => 'TEST2025',
            'discount' => 10,
            'discount_type' => 'percentage',
            'limit' => 100,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'description' => 'Test coupon description',
            'is_active' => true
        ];

        $response = $this->postJson(route('finance.plan.store-coupon'), $couponData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Coupon created successfully.'
                ]);

        $this->assertDatabaseHas('coupons', [
            'name' => 'Test Coupon',
            'code' => 'TEST2025',
            'discount' => 10,
            'discount_type' => 'percentage',
            'limit' => 100
        ]);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $this->actingAs($this->user);

        $response = $this->postJson(route('finance.plan.store-coupon'), []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'code', 'discount', 'discount_type', 'limit']);
    }

    /** @test */
    public function it_prevents_duplicate_coupon_codes()
    {
        $this->actingAs($this->user);

        // Create first coupon
        Coupon::create([
            'name' => 'First Coupon',
            'code' => 'DUPLICATE',
            'discount' => 10,
            'discount_type' => 'percentage',
            'limit' => 100,
            'created_by' => $this->user->creatorId()
        ]);

        // Try to create second coupon with same code
        $response = $this->postJson(route('finance.plan.store-coupon'), [
            'name' => 'Second Coupon',
            'code' => 'DUPLICATE',
            'discount' => 15,
            'discount_type' => 'percentage',
            'limit' => 50
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['code']);
    }
}
