{"__meta": {"id": "X4fb126356b31d379cd3c9700626760f8", "datetime": "2025-07-30 07:39:02", "utime": **********.866343, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753861140.723324, "end": **********.866395, "duration": 2.143070936203003, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1753861140.723324, "relative_start": 0, "end": **********.741707, "relative_end": **********.741707, "duration": 2.018383026123047, "duration_str": "2.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.741741, "relative_start": 2.****************, "end": **********.866401, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3030\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1852 to 1858\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1852\" onclick=\"\">routes/web.php:1852-1858</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ICU4SUfPhUuGchG076VHAvYUaP4q1xRpbG5AXJRz", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1918987826 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1918987826\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-293253741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-293253741\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-667553334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-667553334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1192452689 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192452689\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-922115172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-922115172\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-853567116 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:39:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkUvMks3Q2xVUFh2WVlnT1FyWHNJL1E9PSIsInZhbHVlIjoiVkZRNFM1SktCTUd3TnZYV1JHNFZGdTVjdjFkZjU3VktpT0FOeTMyRmFjVERjeEdPME1UNS9GdjhuN0drNzFTK1BRQmRjdXZvdGtwTFIxRGVqY0UxZkx0ZFZBNElpdXFralAwcHJJU0s1OCtYM1g5VWtnUXMxdDc4N3Zkck1IbmRjMkRsZ0FWNktibERUVFNuV2IxVVN0M21Ka28vYTZkMEVOd21xZFp5NXpRMTNkcVV6bjV4eG5vd3hZeUF6dFZwQkpRTVVuZkxRaTd6d0orMTVJMjY5WmVqK0pOd281cTVnVDFoU0ROTTBLYVBGbHYvMjVqZHIwYjVvQTZWZTU3aitPRzFjMytkNTdUbmFzRDlmY2RLZXd5L1I4bExEajFYRHdRUGQ5d0JxSmF5STZqM243RjlzbUxub05vNnlvdHZLYUhDUDU1dnZocjhlWUh3cnYwMU02MS9jRWRKOVZrOEVFNzJqSVdGZXpNKzVNQ3pOMy9OTWp0Tkx0eTROWkNreEJTKzJCdWMzSmlTUUc5SzNYZFhocXJyUnpvQmtBb3YyMGdicHgrK2c0bzgyY0ZGYU9tdU85RzFJRkdBWlNzdUgvWEkrRHBHOVFTbHp0aTVKU3d2UVh5Nnd1TCs1aTJNWk1sL0ppem5QdlZDNTBtc0VaY0gvQTVFWkcrN29kQzUiLCJtYWMiOiJmNGMxYWI3MTZkNzY1ZDgxMGYzMWIzZDFiNDYzYTc5MjFiOTZiMGUwZDkxNjVhNzk1MDE3YWFiNTI3YjFhNGJiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:39:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9xMUduSTdtRCt1UVFQSHZ4TjMxT3c9PSIsInZhbHVlIjoiZXFUZnA0ejF3dU5kZCt4RnJzUFgzYWdaUHExdlFyZVFoNm1OWUpHY0svUFRDQlpNS2dWYnYwT0YrWHczN05KbFlaODhVellKaGR4WVlXRWxxamhWRWJSQmhtUzIvU2FhemRSSjlaRFBmQU8zbGkwL2ltUUVlZkM5YWJvNzZ4RlRUT0xQeHFhV3dza3FEWXJyczhlZEJPVWcyaEZoeWxNNCtkZEV2dk02WENsZC9NUnRvdzhveDRyaFEzaWpKakRyV1pqZkRhK2w3d3ZhcktmczY0aStQM2pZd3l6OENmOVRrOUMvSk1rY1pTRnFud3RkYmxpZzEwV3g4UHhrWTIrSDRxWkMrWUp0OHArOFlWaXBRYjJ6YVgrZkFRbis1L2ZBeTZwdmZROVVQbDlsSHJVRVhZaWlXQTRHSVpwQU9zbU5tTlBLNFF1TUZSczVLUTdwTWw5MVNEcFE5OUt3dFc3N3BwbFZrQmcrK2I3REVLQkJ0KzRDR3UyK0JXTzlvUTZuNmxLWkM1TWo5eDFZdklNY3o1NE5xVHJ4d0YyVGE1cHRBOS91ZlF6TFdNbUZHb1pGUEhjRG9EUFllZEJSdzdyMHhWQlNFQVFpRndudENyYjlvTTFiVklGMS9LZmpCL29xVkRhSUtZTXE4Y2w5eVFHbTMxekRVWWYvTWFPaWpzYksiLCJtYWMiOiJmYzc2ZDA3NzhhZTk4MWQ1MDY3OGEzMDQxMGU4MmFiMTVjMjNmYzFmODY2MDI2MmM1NTk3OTRmZGJmMjNhODk1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:39:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkUvMks3Q2xVUFh2WVlnT1FyWHNJL1E9PSIsInZhbHVlIjoiVkZRNFM1SktCTUd3TnZYV1JHNFZGdTVjdjFkZjU3VktpT0FOeTMyRmFjVERjeEdPME1UNS9GdjhuN0drNzFTK1BRQmRjdXZvdGtwTFIxRGVqY0UxZkx0ZFZBNElpdXFralAwcHJJU0s1OCtYM1g5VWtnUXMxdDc4N3Zkck1IbmRjMkRsZ0FWNktibERUVFNuV2IxVVN0M21Ka28vYTZkMEVOd21xZFp5NXpRMTNkcVV6bjV4eG5vd3hZeUF6dFZwQkpRTVVuZkxRaTd6d0orMTVJMjY5WmVqK0pOd281cTVnVDFoU0ROTTBLYVBGbHYvMjVqZHIwYjVvQTZWZTU3aitPRzFjMytkNTdUbmFzRDlmY2RLZXd5L1I4bExEajFYRHdRUGQ5d0JxSmF5STZqM243RjlzbUxub05vNnlvdHZLYUhDUDU1dnZocjhlWUh3cnYwMU02MS9jRWRKOVZrOEVFNzJqSVdGZXpNKzVNQ3pOMy9OTWp0Tkx0eTROWkNreEJTKzJCdWMzSmlTUUc5SzNYZFhocXJyUnpvQmtBb3YyMGdicHgrK2c0bzgyY0ZGYU9tdU85RzFJRkdBWlNzdUgvWEkrRHBHOVFTbHp0aTVKU3d2UVh5Nnd1TCs1aTJNWk1sL0ppem5QdlZDNTBtc0VaY0gvQTVFWkcrN29kQzUiLCJtYWMiOiJmNGMxYWI3MTZkNzY1ZDgxMGYzMWIzZDFiNDYzYTc5MjFiOTZiMGUwZDkxNjVhNzk1MDE3YWFiNTI3YjFhNGJiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:39:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9xMUduSTdtRCt1UVFQSHZ4TjMxT3c9PSIsInZhbHVlIjoiZXFUZnA0ejF3dU5kZCt4RnJzUFgzYWdaUHExdlFyZVFoNm1OWUpHY0svUFRDQlpNS2dWYnYwT0YrWHczN05KbFlaODhVellKaGR4WVlXRWxxamhWRWJSQmhtUzIvU2FhemRSSjlaRFBmQU8zbGkwL2ltUUVlZkM5YWJvNzZ4RlRUT0xQeHFhV3dza3FEWXJyczhlZEJPVWcyaEZoeWxNNCtkZEV2dk02WENsZC9NUnRvdzhveDRyaFEzaWpKakRyV1pqZkRhK2w3d3ZhcktmczY0aStQM2pZd3l6OENmOVRrOUMvSk1rY1pTRnFud3RkYmxpZzEwV3g4UHhrWTIrSDRxWkMrWUp0OHArOFlWaXBRYjJ6YVgrZkFRbis1L2ZBeTZwdmZROVVQbDlsSHJVRVhZaWlXQTRHSVpwQU9zbU5tTlBLNFF1TUZSczVLUTdwTWw5MVNEcFE5OUt3dFc3N3BwbFZrQmcrK2I3REVLQkJ0KzRDR3UyK0JXTzlvUTZuNmxLWkM1TWo5eDFZdklNY3o1NE5xVHJ4d0YyVGE1cHRBOS91ZlF6TFdNbUZHb1pGUEhjRG9EUFllZEJSdzdyMHhWQlNFQVFpRndudENyYjlvTTFiVklGMS9LZmpCL29xVkRhSUtZTXE4Y2w5eVFHbTMxekRVWWYvTWFPaWpzYksiLCJtYWMiOiJmYzc2ZDA3NzhhZTk4MWQ1MDY3OGEzMDQxMGU4MmFiMTVjMjNmYzFmODY2MDI2MmM1NTk3OTRmZGJmMjNhODk1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:39:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853567116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1377286304 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ICU4SUfPhUuGchG076VHAvYUaP4q1xRpbG5AXJRz</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377286304\", {\"maxDepth\":0})</script>\n"}}