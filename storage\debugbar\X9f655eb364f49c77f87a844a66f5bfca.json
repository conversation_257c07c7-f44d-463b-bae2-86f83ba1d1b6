{"__meta": {"id": "X9f655eb364f49c77f87a844a66f5bfca", "datetime": "2025-07-30 02:35:11", "utime": 1753842911.294886, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753842894.320905, "end": 1753842911.294934, "duration": 16.974029064178467, "duration_str": "16.97s", "measures": [{"label": "Booting", "start": 1753842894.320905, "relative_start": 0, "end": 1753842900.950287, "relative_end": 1753842900.950287, "duration": 6.629382133483887, "duration_str": "6.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753842900.95103, "relative_start": 6.630125045776367, "end": 1753842911.294939, "relative_end": 5.0067901611328125e-06, "duration": 10.34390902519226, "duration_str": "10.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44117064, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=58\" onclick=\"\">app/Http/Controllers/DashboardController.php:58-75</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.72438, "accumulated_duration_str": "724ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.443921, "duration": 0.72438, "duration_str": "724ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q8tE5TMNHfUc81P9kdTljPJJToSnHySSOJV6pQbd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1848326311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848326311\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-853621079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-853621079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-13580856 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13580856\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1060971800 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060971800\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1951717152 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:35:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9nTmV0SVNOSDM4VUpZZDNiOC9oU3c9PSIsInZhbHVlIjoiMi8vYW9Xc0hSZ3ZzUGVHbFRmNmY1UDRPQzVmaHFZUERNUGdJUmZLY1p4R3ZKK2xOOWpRZWhZQTFucjZ3UUpJV3hmR0ZIbVhWaDIzRks2SVJFWWlDVDJLcVR6YVFMNEsxdzRiNUdZeGs2eVhiZGpVemtZek5kMzlwL3orTGFULzAva1V3QVhDUlFKN3NPV3psRUJ3VmFJTjJYeWpaNXZXcmJZbDI3U3BHQWtQMWtlUFVnaU4yRXJxdlF3WStsNnRwNTJoejVHSGNnbzNSQUpqeUE5UUNBOWtkNCtwTzMxaGZJTmdvNTJmYkhyYTduOU0ra01BbXUzRnh5MkJTQnh2blowV3JYV1ZxQ3N0emxGZEE5aHk4bmN5U0Q1dmEyR0tOWXowTGppR2hRWGM4djdteW1nOUJzejBWcU5VcUwxYzRxQ2trOEtOaXFISmJlY0xaZGF4ditIU1R1Sk1ZeTA5Z0VJTlRYM3p1WmhHdVlGbVhqLzNQc1gvS2ZlblFBdzkzUkJyb3JPSVZCajlxc2FFVFlJajB6OWcwYVU3eHhkOWNuUWVmYW1FcEFpK3J3MkZvU1lRWjFueDRFblBQVURSVDN2b0ZmUTJaYmlZNkE5S1Y1dFljckx1MVh6N3FYenBxVjRoRllRc0Yvem5XMjhNNVNXM1FCRHowZmlmVnlSL2QiLCJtYWMiOiJmNjhjYTZkOWYxOGQ3NWUwZjU3NTc2MThmOWQxMGM1ZDQ3ZmRhZjVmZjE1YmQxNDI1YmYyNDU1MzAwYzRjNjg4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:35:10 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhuSUZpeFJJZksrOUhSUWM1eHgxc0E9PSIsInZhbHVlIjoiTUhKUGZqY29INVdlR1hKYStaaFdUenA5RjhuR1haaWNiRnlmdXptMk9kMFgwVk4vbzgreU1RTzBPWkprb2VGTkdHVCsrWlNSNU1zUGVVWm8rYnowbGRsTUhQZTErYVNvVC83clhZVkM5Rk9zUnA4bTMyWkIvUG1aU1cvc21wWU5WVHpxYkpQbFhnMk9Jc1JQa2E2RmFIV1V5ckFuN0F2QStjWlJ4YitZUGgrL21xZm5zRUd4S3grUExZUi96ZEVhYmw2VE9tTm5yM3BRTXVJb0JnTmg0OG43bENwZ2ZzVnpPa1k4R1NlYVpjcHV0T2dvcVZ1Qy9DazNSbzhyb1oyT1RBdHZpYzhWY1UrblY0czBsYlB3SzdUMHB4S1lNQUUzdVZ1RFZ1UVF1dlRBZUxkbDJPN1FYVWt3WWJaK1djdnZENVRWU0VmdUk3eitIUXl5dUk5NGVUWkRNbGdBNUV4NW5yWENlVFoxMmpEMDlkc0VPVVNsRkEzSWtuSWQ5Y2t4SUNzZW5ZRUY5WUJUb2VocTJHcHhOY3JoSDdnME9XQmMxUnh1NG5pSUk2MFF2bEk3aTl6WTdMTFZqWGZtSkdWVEN1NTRLa2wxQURaZlNzQXlEQ3g3eDdqUytNbC9CMlkxd2V0NFptRWlCTmIycDhhQmw2SUppTmlIUHZJWmM4OEciLCJtYWMiOiIwMTEzMjNhZDA4OWVhNWI1ZjI2NjZjMTY0MGE5M2U5MmFmZjdjY2FkZjZlMzRhOGYxNzE2ZGUxMDY1NDk4ZGQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:35:10 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9nTmV0SVNOSDM4VUpZZDNiOC9oU3c9PSIsInZhbHVlIjoiMi8vYW9Xc0hSZ3ZzUGVHbFRmNmY1UDRPQzVmaHFZUERNUGdJUmZLY1p4R3ZKK2xOOWpRZWhZQTFucjZ3UUpJV3hmR0ZIbVhWaDIzRks2SVJFWWlDVDJLcVR6YVFMNEsxdzRiNUdZeGs2eVhiZGpVemtZek5kMzlwL3orTGFULzAva1V3QVhDUlFKN3NPV3psRUJ3VmFJTjJYeWpaNXZXcmJZbDI3U3BHQWtQMWtlUFVnaU4yRXJxdlF3WStsNnRwNTJoejVHSGNnbzNSQUpqeUE5UUNBOWtkNCtwTzMxaGZJTmdvNTJmYkhyYTduOU0ra01BbXUzRnh5MkJTQnh2blowV3JYV1ZxQ3N0emxGZEE5aHk4bmN5U0Q1dmEyR0tOWXowTGppR2hRWGM4djdteW1nOUJzejBWcU5VcUwxYzRxQ2trOEtOaXFISmJlY0xaZGF4ditIU1R1Sk1ZeTA5Z0VJTlRYM3p1WmhHdVlGbVhqLzNQc1gvS2ZlblFBdzkzUkJyb3JPSVZCajlxc2FFVFlJajB6OWcwYVU3eHhkOWNuUWVmYW1FcEFpK3J3MkZvU1lRWjFueDRFblBQVURSVDN2b0ZmUTJaYmlZNkE5S1Y1dFljckx1MVh6N3FYenBxVjRoRllRc0Yvem5XMjhNNVNXM1FCRHowZmlmVnlSL2QiLCJtYWMiOiJmNjhjYTZkOWYxOGQ3NWUwZjU3NTc2MThmOWQxMGM1ZDQ3ZmRhZjVmZjE1YmQxNDI1YmYyNDU1MzAwYzRjNjg4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:35:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhuSUZpeFJJZksrOUhSUWM1eHgxc0E9PSIsInZhbHVlIjoiTUhKUGZqY29INVdlR1hKYStaaFdUenA5RjhuR1haaWNiRnlmdXptMk9kMFgwVk4vbzgreU1RTzBPWkprb2VGTkdHVCsrWlNSNU1zUGVVWm8rYnowbGRsTUhQZTErYVNvVC83clhZVkM5Rk9zUnA4bTMyWkIvUG1aU1cvc21wWU5WVHpxYkpQbFhnMk9Jc1JQa2E2RmFIV1V5ckFuN0F2QStjWlJ4YitZUGgrL21xZm5zRUd4S3grUExZUi96ZEVhYmw2VE9tTm5yM3BRTXVJb0JnTmg0OG43bENwZ2ZzVnpPa1k4R1NlYVpjcHV0T2dvcVZ1Qy9DazNSbzhyb1oyT1RBdHZpYzhWY1UrblY0czBsYlB3SzdUMHB4S1lNQUUzdVZ1RFZ1UVF1dlRBZUxkbDJPN1FYVWt3WWJaK1djdnZENVRWU0VmdUk3eitIUXl5dUk5NGVUWkRNbGdBNUV4NW5yWENlVFoxMmpEMDlkc0VPVVNsRkEzSWtuSWQ5Y2t4SUNzZW5ZRUY5WUJUb2VocTJHcHhOY3JoSDdnME9XQmMxUnh1NG5pSUk2MFF2bEk3aTl6WTdMTFZqWGZtSkdWVEN1NTRLa2wxQURaZlNzQXlEQ3g3eDdqUytNbC9CMlkxd2V0NFptRWlCTmIycDhhQmw2SUppTmlIUHZJWmM4OEciLCJtYWMiOiIwMTEzMjNhZDA4OWVhNWI1ZjI2NjZjMTY0MGE5M2U5MmFmZjdjY2FkZjZlMzRhOGYxNzE2ZGUxMDY1NDk4ZGQ3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:35:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951717152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-558274062 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q8tE5TMNHfUc81P9kdTljPJJToSnHySSOJV6pQbd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558274062\", {\"maxDepth\":0})</script>\n"}}