{"__meta": {"id": "Xb60b5959070d8144b650d7b32f9bb08e", "datetime": "2025-07-30 02:37:06", "utime": **********.677626, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843022.31006, "end": **********.677674, "duration": 4.367614030838013, "duration_str": "4.37s", "measures": [{"label": "Booting", "start": 1753843022.31006, "relative_start": 0, "end": 1753843025.3977, "relative_end": 1753843025.3977, "duration": 3.0876400470733643, "duration_str": "3.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753843025.397758, "relative_start": 3.***************, "end": **********.67768, "relative_end": 5.9604644775390625e-06, "duration": 1.****************, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "srZal6ThV6QL8HvBSDGz9CTixBpt9KiHor0GJylJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1141896647 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1141896647\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-578607878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-578607878\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1385833834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1385833834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-464346373 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464346373\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-149615501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-149615501\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-995995771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:37:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9wNW41QnhMandNRExYZk5MWllEaXc9PSIsInZhbHVlIjoiM04weWx2Vy8yRFh6YURreXFDUkRCWlpIYjFkY2FlOG1naVY3VVRzdlM1R2I1M2J0Y2oxZDM0YUhaMS9HSmxnbVlvUzRYU29temNvN2xBeHFjd1RpWjNPdlg5dXJ0amt2aUdZRDR1NVdKWG5vOTJ4T0E1TDMyVGprWjVUa3drVTNhTlg4SHdQbFVsQ1E1NTc0bjgxdVJvQVV6aTFqUGR3RkNlMWFzcnpMNkZtUlR3enZOQk5IcHAyL1h0MVBpU3dEVVRiNVVNRjBBYy9JVkNwaHJnVGhuTVJvY3Y5eDVtcDU0bkFTbFMzWUovQ01SRTRmbWdoRjBlVEFsYytIbGlPUjZrcHlHWGFRN0FZRzM3bzJLZGltQ3ExMDUzMlhWeTZ6YmhoQjVvWks1NWtVZFdUc1hOcHdqZ0p5bm5LZWp6aFJTMXpaRWJzVi9ib2ExcnpMUEsxVU9EQWFIMHJSell3RTAyU3MyYnB0akJ2VU9vUE01UjZwUkJMY1drQytTeHloMTRraVRxcFVQUzlHdVFwZzlISzM1WHVCR0diRms5ZGZoK0dVQ1JjQVdlWXh2SFpXTkE1dHBCRDlxYzJpNkUxUTdsMEt0TWZiZFpmM2p4Tm5GcFNCbzJxbkFwaDdtWXZucldDR2tFc1lhdG9vbDhQbmlObnJGVnhpbkQrWlcyT1EiLCJtYWMiOiIxYWEwNTIwMDVjZGI3MDE3MDBjMzJiNWZjYzE5ODI0MjMxZDFhMDk2NWRjMmYxZmNlZWM1ZTBkM2I3OTI5OTM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJrWWU2QzBhOGZaYWlMVUwyMnE5SUE9PSIsInZhbHVlIjoiQXpuWk5BNjlvTGovVDA0RnkxNTFiTG9aL0NWRTd0K1BKOVdGV3o3OUF4YWhQTGF2TWk0YWhsMDlLVFAvMzNlSWVYZFBZQzdKMi9ZQVlQSWJsVEdSa0dBVFI4ZmtpMjE5WGF2cmZoVFJvRkpMK1Jtbm9qYmhNRnlJWkg0MitzTVlGbVk0NjlYenpkU1ZVS1N0akpFSUJqMXV2bmg1WXN5Tng1ZTg1cktvVlpYd0p2Wk5tL2h0Yk5FMlhqaXY2cWFZMUtRUkNWV3pzRFdmcENxVnpvMXJqZjZnUzRXc1VCUUs3QWNvNUFtNCtsSjFyMHQ5d3d5Mm1vY2RsZ1RJRFhRWTl5VWs2Z1kyZHJhSVBVMThuNlJEay9QaFZVa1pNaFRhNXh3a2N4VjlsN0ptNjVCSXNDWEphMVd4bEx0bFpad1l3ME9SUUlNS2U0TmNKQnhtdlljRklrcUM5dVNoMDI0NHRkTWVwUitMTnFkR2VETzNLTXpMYzM3MmR1RHo1aTk4ODUxdHZoVmZaSGl5emZVdklSWUM0QkNneVlKMHVlak9udUEzZk1kTmZsSzAxZmp2R3ozUGMyTG9oUUE1djFvWGZUbjJEYnc1SFBZWWpET1JQTnYzQklYaFBuOVdsRzJ3aEVHWUR4OGlYTmVhYURBVW12bGpSdzZSWnFTd05EVXAiLCJtYWMiOiI2NGIxNmQxM2MzZThjM2E4MTRhYjk2MmY5ZjMyMDk1ODMxMmY1ZTE4MzY0NzhiZjQ1Yzc3ZTUxYTllZmUxZmRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:37:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9wNW41QnhMandNRExYZk5MWllEaXc9PSIsInZhbHVlIjoiM04weWx2Vy8yRFh6YURreXFDUkRCWlpIYjFkY2FlOG1naVY3VVRzdlM1R2I1M2J0Y2oxZDM0YUhaMS9HSmxnbVlvUzRYU29temNvN2xBeHFjd1RpWjNPdlg5dXJ0amt2aUdZRDR1NVdKWG5vOTJ4T0E1TDMyVGprWjVUa3drVTNhTlg4SHdQbFVsQ1E1NTc0bjgxdVJvQVV6aTFqUGR3RkNlMWFzcnpMNkZtUlR3enZOQk5IcHAyL1h0MVBpU3dEVVRiNVVNRjBBYy9JVkNwaHJnVGhuTVJvY3Y5eDVtcDU0bkFTbFMzWUovQ01SRTRmbWdoRjBlVEFsYytIbGlPUjZrcHlHWGFRN0FZRzM3bzJLZGltQ3ExMDUzMlhWeTZ6YmhoQjVvWks1NWtVZFdUc1hOcHdqZ0p5bm5LZWp6aFJTMXpaRWJzVi9ib2ExcnpMUEsxVU9EQWFIMHJSell3RTAyU3MyYnB0akJ2VU9vUE01UjZwUkJMY1drQytTeHloMTRraVRxcFVQUzlHdVFwZzlISzM1WHVCR0diRms5ZGZoK0dVQ1JjQVdlWXh2SFpXTkE1dHBCRDlxYzJpNkUxUTdsMEt0TWZiZFpmM2p4Tm5GcFNCbzJxbkFwaDdtWXZucldDR2tFc1lhdG9vbDhQbmlObnJGVnhpbkQrWlcyT1EiLCJtYWMiOiIxYWEwNTIwMDVjZGI3MDE3MDBjMzJiNWZjYzE5ODI0MjMxZDFhMDk2NWRjMmYxZmNlZWM1ZTBkM2I3OTI5OTM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJrWWU2QzBhOGZaYWlMVUwyMnE5SUE9PSIsInZhbHVlIjoiQXpuWk5BNjlvTGovVDA0RnkxNTFiTG9aL0NWRTd0K1BKOVdGV3o3OUF4YWhQTGF2TWk0YWhsMDlLVFAvMzNlSWVYZFBZQzdKMi9ZQVlQSWJsVEdSa0dBVFI4ZmtpMjE5WGF2cmZoVFJvRkpMK1Jtbm9qYmhNRnlJWkg0MitzTVlGbVk0NjlYenpkU1ZVS1N0akpFSUJqMXV2bmg1WXN5Tng1ZTg1cktvVlpYd0p2Wk5tL2h0Yk5FMlhqaXY2cWFZMUtRUkNWV3pzRFdmcENxVnpvMXJqZjZnUzRXc1VCUUs3QWNvNUFtNCtsSjFyMHQ5d3d5Mm1vY2RsZ1RJRFhRWTl5VWs2Z1kyZHJhSVBVMThuNlJEay9QaFZVa1pNaFRhNXh3a2N4VjlsN0ptNjVCSXNDWEphMVd4bEx0bFpad1l3ME9SUUlNS2U0TmNKQnhtdlljRklrcUM5dVNoMDI0NHRkTWVwUitMTnFkR2VETzNLTXpMYzM3MmR1RHo1aTk4ODUxdHZoVmZaSGl5emZVdklSWUM0QkNneVlKMHVlak9udUEzZk1kTmZsSzAxZmp2R3ozUGMyTG9oUUE1djFvWGZUbjJEYnc1SFBZWWpET1JQTnYzQklYaFBuOVdsRzJ3aEVHWUR4OGlYTmVhYURBVW12bGpSdzZSWnFTd05EVXAiLCJtYWMiOiI2NGIxNmQxM2MzZThjM2E4MTRhYjk2MmY5ZjMyMDk1ODMxMmY1ZTE4MzY0NzhiZjQ1Yzc3ZTUxYTllZmUxZmRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:37:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995995771\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1218153525 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">srZal6ThV6QL8HvBSDGz9CTixBpt9KiHor0GJylJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218153525\", {\"maxDepth\":0})</script>\n"}}