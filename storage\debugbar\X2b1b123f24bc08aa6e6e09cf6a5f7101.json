{"__meta": {"id": "X2b1b123f24bc08aa6e6e09cf6a5f7101", "datetime": "2025-07-30 02:35:31", "utime": 1753842931.580806, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753842912.105753, "end": 1753842931.580875, "duration": 19.47512197494507, "duration_str": "19.48s", "measures": [{"label": "Booting", "start": 1753842912.105753, "relative_start": 0, "end": **********.051367, "relative_end": **********.051367, "duration": 2.9456140995025635, "duration_str": "2.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.051401, "relative_start": 2.945647954940796, "end": 1753842931.580881, "relative_end": 6.198883056640625e-06, "duration": 16.52948021888733, "duration_str": "16.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46626488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1753842927.215861, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1753842928.906472, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1753842931.366997, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 6.90268, "accumulated_duration_str": "6.9s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.395261, "duration": 0.00684, "duration_str": "6.84ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 0.099}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.417462, "duration": 6.87404, "duration_str": "6.87s", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 0.099, "width_percent": 99.585}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3705559, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 99.684, "width_percent": 0.062}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753842928.143353, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.746, "width_percent": 0.038}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753842929.285438, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.785, "width_percent": 0.035}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753842931.1186981, "duration": 0.00877, "duration_str": "8.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 99.82, "width_percent": 0.127}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753842931.335431, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 99.947, "width_percent": 0.02}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753842931.351845, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.967, "width_percent": 0.033}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q8tE5TMNHfUc81P9kdTljPJJToSnHySSOJV6pQbd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-69323516 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-69323516\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1290662321 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290662321\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-127383582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-127383582\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-148442894 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ii9nTmV0SVNOSDM4VUpZZDNiOC9oU3c9PSIsInZhbHVlIjoiMi8vYW9Xc0hSZ3ZzUGVHbFRmNmY1UDRPQzVmaHFZUERNUGdJUmZLY1p4R3ZKK2xOOWpRZWhZQTFucjZ3UUpJV3hmR0ZIbVhWaDIzRks2SVJFWWlDVDJLcVR6YVFMNEsxdzRiNUdZeGs2eVhiZGpVemtZek5kMzlwL3orTGFULzAva1V3QVhDUlFKN3NPV3psRUJ3VmFJTjJYeWpaNXZXcmJZbDI3U3BHQWtQMWtlUFVnaU4yRXJxdlF3WStsNnRwNTJoejVHSGNnbzNSQUpqeUE5UUNBOWtkNCtwTzMxaGZJTmdvNTJmYkhyYTduOU0ra01BbXUzRnh5MkJTQnh2blowV3JYV1ZxQ3N0emxGZEE5aHk4bmN5U0Q1dmEyR0tOWXowTGppR2hRWGM4djdteW1nOUJzejBWcU5VcUwxYzRxQ2trOEtOaXFISmJlY0xaZGF4ditIU1R1Sk1ZeTA5Z0VJTlRYM3p1WmhHdVlGbVhqLzNQc1gvS2ZlblFBdzkzUkJyb3JPSVZCajlxc2FFVFlJajB6OWcwYVU3eHhkOWNuUWVmYW1FcEFpK3J3MkZvU1lRWjFueDRFblBQVURSVDN2b0ZmUTJaYmlZNkE5S1Y1dFljckx1MVh6N3FYenBxVjRoRllRc0Yvem5XMjhNNVNXM1FCRHowZmlmVnlSL2QiLCJtYWMiOiJmNjhjYTZkOWYxOGQ3NWUwZjU3NTc2MThmOWQxMGM1ZDQ3ZmRhZjVmZjE1YmQxNDI1YmYyNDU1MzAwYzRjNjg4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhuSUZpeFJJZksrOUhSUWM1eHgxc0E9PSIsInZhbHVlIjoiTUhKUGZqY29INVdlR1hKYStaaFdUenA5RjhuR1haaWNiRnlmdXptMk9kMFgwVk4vbzgreU1RTzBPWkprb2VGTkdHVCsrWlNSNU1zUGVVWm8rYnowbGRsTUhQZTErYVNvVC83clhZVkM5Rk9zUnA4bTMyWkIvUG1aU1cvc21wWU5WVHpxYkpQbFhnMk9Jc1JQa2E2RmFIV1V5ckFuN0F2QStjWlJ4YitZUGgrL21xZm5zRUd4S3grUExZUi96ZEVhYmw2VE9tTm5yM3BRTXVJb0JnTmg0OG43bENwZ2ZzVnpPa1k4R1NlYVpjcHV0T2dvcVZ1Qy9DazNSbzhyb1oyT1RBdHZpYzhWY1UrblY0czBsYlB3SzdUMHB4S1lNQUUzdVZ1RFZ1UVF1dlRBZUxkbDJPN1FYVWt3WWJaK1djdnZENVRWU0VmdUk3eitIUXl5dUk5NGVUWkRNbGdBNUV4NW5yWENlVFoxMmpEMDlkc0VPVVNsRkEzSWtuSWQ5Y2t4SUNzZW5ZRUY5WUJUb2VocTJHcHhOY3JoSDdnME9XQmMxUnh1NG5pSUk2MFF2bEk3aTl6WTdMTFZqWGZtSkdWVEN1NTRLa2wxQURaZlNzQXlEQ3g3eDdqUytNbC9CMlkxd2V0NFptRWlCTmIycDhhQmw2SUppTmlIUHZJWmM4OEciLCJtYWMiOiIwMTEzMjNhZDA4OWVhNWI1ZjI2NjZjMTY0MGE5M2U5MmFmZjdjY2FkZjZlMzRhOGYxNzE2ZGUxMDY1NDk4ZGQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148442894\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-777730036 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q8tE5TMNHfUc81P9kdTljPJJToSnHySSOJV6pQbd</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yBiAg1Fqx1agdq7ADLfkeAc2qdg78DsKVcOjqswG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777730036\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-294054060 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:35:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImE3a3lZNjZRamM3TFJkcGFEVVQxdGc9PSIsInZhbHVlIjoiY1A1Uzh2Zk94S0s0Vzk3RWV5SmR5OHpvVDBXa25zazVudER6QnROdk5Mamd3R1MyK2tFVTBsRFlmZzFCSUl4M3pyOTh0bTRXQlhDVVBuR0FhaVRsNnNVbU93TUdtMXlzb3RZbUQ3MHkzN1ZUTjBFcC9TcTlaZUhiaVdaUmtTalhPKzc2eHRIeHJCUW9oSThmQi9LNHNjd1dHbjBVZHpMUnB4cDdGbjdYSUZNYUthdHkwVmNUMGd0SXZMYUZJb2NYR09qYkJEcVRNZ0MrcDBSS1RrMTltczBsRzVQczNJSGljb1p0bG95bGhDVDFFQWpJS3FXVmJtVEpkdXhmTDV2cExFZFlDZFVvNUVWcWwyU0d6dHNEWER3TVNwck5wSnRLaVpsbVQ3b00zdFVDeDgrOEhIMC9HbDdwVkMwc3hsNFRMTE9iL2d3TUxYRnczdEF2KzNyb3FYY3RJQTdUazZEallSb1VaSnp6MlhIZTV0L2RBRTd2Y3hUWG1vaEZKbW1RWWQ3T3JOTUpjZUVxem54d2UzOVVCNDh0RFFlOXp1NkxkeVpDRDRvZEJHdDNDaFVHZzVIVGJSMUVCZmlPQUxaOVhSbjN1c1U1bjJLZm5vN003UDdlekI5NmM1bW1TdlpEN0xheld0enp4WkpzTE53YzBFVUQ4ak1wWThjYmQvZDIiLCJtYWMiOiIzZjYwNGQ5YjE0ZjhiNzkyZGQwZjk1MzU4ZTljMGRkNDU5MjU4NzBlY2UyOWQ5OTk1YzFjY2JlZWFjMDhjNjg1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:35:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9MQVp2YllQN085Ri9IejMyNnp5ZFE9PSIsInZhbHVlIjoiYS9yWW9vTmRGQWxLTGlOUFN6NHl5V0ZucDlKOXYrb2EzZUxlaDVaVDZ5T0V5QUcydWFzT1NId2hXS1JLUmRqN09TdlF4aTE0Vm9scFo3UFRTMCtTekpURlFEbUdVZ21VNFZ4Yk9Tc2d0ZlRPekJTL1VWSm9LVjFSbmRpZDUrSFExc2ZwVDcyMjRLZktEMUlEeStmWWpOMGl1L21ibzdpVkoyM1RsdSsyZC9YTktJdUN2ODZYZXAzd25Ld2lKNEt2aHNZS3h4bEFySWZ3T3psK0ppN0dIeWlxSjJ1NnlDMXllT2dITjZwQ0pKU0FueWhuZ0lqbWc1YlJPMTlidXh4ZExlRVA2SUFlL1B1UzdrbnRWY3NHaW9hT2IwL3g1TDFFdi9YNytuNy9ncm9acWZnVFdiQUtaODlkaUoyVVdTS0tNZzRjeFlEbkV1MERkdTM3WW9pbU1SSS9VdUl5cnYwb0VsNEVURGhpQWZKMk9Nck02Q002MHRNVk43LzJRWGpkbEozM3VtV0R4eVhBSVkyakNhU3NMTzZiNEg5TnZDU2ptZExqVUJka1hROU5CdVhUSWEzZzVKeWhzSFBjZUNtWXlSREVMdmRNall4eG41bFdNSEJOM0lpZkY1V0FMSEU4eE5YUzVwTmRqMVVqQXdwcWJTd0Qxalc0T0xGK1E1bXoiLCJtYWMiOiJiOTQ4ZGUwMjJiZGIwZThiYzU4MmQ1MTIyMGEzOWVmZjVlNDMwM2FkNmMyMTE1M2FmZWM0ZjdlM2E0MjZmYTQyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:35:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImE3a3lZNjZRamM3TFJkcGFEVVQxdGc9PSIsInZhbHVlIjoiY1A1Uzh2Zk94S0s0Vzk3RWV5SmR5OHpvVDBXa25zazVudER6QnROdk5Mamd3R1MyK2tFVTBsRFlmZzFCSUl4M3pyOTh0bTRXQlhDVVBuR0FhaVRsNnNVbU93TUdtMXlzb3RZbUQ3MHkzN1ZUTjBFcC9TcTlaZUhiaVdaUmtTalhPKzc2eHRIeHJCUW9oSThmQi9LNHNjd1dHbjBVZHpMUnB4cDdGbjdYSUZNYUthdHkwVmNUMGd0SXZMYUZJb2NYR09qYkJEcVRNZ0MrcDBSS1RrMTltczBsRzVQczNJSGljb1p0bG95bGhDVDFFQWpJS3FXVmJtVEpkdXhmTDV2cExFZFlDZFVvNUVWcWwyU0d6dHNEWER3TVNwck5wSnRLaVpsbVQ3b00zdFVDeDgrOEhIMC9HbDdwVkMwc3hsNFRMTE9iL2d3TUxYRnczdEF2KzNyb3FYY3RJQTdUazZEallSb1VaSnp6MlhIZTV0L2RBRTd2Y3hUWG1vaEZKbW1RWWQ3T3JOTUpjZUVxem54d2UzOVVCNDh0RFFlOXp1NkxkeVpDRDRvZEJHdDNDaFVHZzVIVGJSMUVCZmlPQUxaOVhSbjN1c1U1bjJLZm5vN003UDdlekI5NmM1bW1TdlpEN0xheld0enp4WkpzTE53YzBFVUQ4ak1wWThjYmQvZDIiLCJtYWMiOiIzZjYwNGQ5YjE0ZjhiNzkyZGQwZjk1MzU4ZTljMGRkNDU5MjU4NzBlY2UyOWQ5OTk1YzFjY2JlZWFjMDhjNjg1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:35:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9MQVp2YllQN085Ri9IejMyNnp5ZFE9PSIsInZhbHVlIjoiYS9yWW9vTmRGQWxLTGlOUFN6NHl5V0ZucDlKOXYrb2EzZUxlaDVaVDZ5T0V5QUcydWFzT1NId2hXS1JLUmRqN09TdlF4aTE0Vm9scFo3UFRTMCtTekpURlFEbUdVZ21VNFZ4Yk9Tc2d0ZlRPekJTL1VWSm9LVjFSbmRpZDUrSFExc2ZwVDcyMjRLZktEMUlEeStmWWpOMGl1L21ibzdpVkoyM1RsdSsyZC9YTktJdUN2ODZYZXAzd25Ld2lKNEt2aHNZS3h4bEFySWZ3T3psK0ppN0dIeWlxSjJ1NnlDMXllT2dITjZwQ0pKU0FueWhuZ0lqbWc1YlJPMTlidXh4ZExlRVA2SUFlL1B1UzdrbnRWY3NHaW9hT2IwL3g1TDFFdi9YNytuNy9ncm9acWZnVFdiQUtaODlkaUoyVVdTS0tNZzRjeFlEbkV1MERkdTM3WW9pbU1SSS9VdUl5cnYwb0VsNEVURGhpQWZKMk9Nck02Q002MHRNVk43LzJRWGpkbEozM3VtV0R4eVhBSVkyakNhU3NMTzZiNEg5TnZDU2ptZExqVUJka1hROU5CdVhUSWEzZzVKeWhzSFBjZUNtWXlSREVMdmRNall4eG41bFdNSEJOM0lpZkY1V0FMSEU4eE5YUzVwTmRqMVVqQXdwcWJTd0Qxalc0T0xGK1E1bXoiLCJtYWMiOiJiOTQ4ZGUwMjJiZGIwZThiYzU4MmQ1MTIyMGEzOWVmZjVlNDMwM2FkNmMyMTE1M2FmZWM0ZjdlM2E0MjZmYTQyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:35:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294054060\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1829728422 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q8tE5TMNHfUc81P9kdTljPJJToSnHySSOJV6pQbd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829728422\", {\"maxDepth\":0})</script>\n"}}