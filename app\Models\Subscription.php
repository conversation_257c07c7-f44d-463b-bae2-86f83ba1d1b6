<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'product_id',
        'product_name',
        'product_price',
        'down_payment',
        'paid_amount',
        'pending_amount',
        'discount_amount',
        'status',
        'next_emi_date',
        'start_date',
        'end_date',
        'emi_count',
        'total_emis',
        'emi_amount',
        'billing_cycle',
        'notes',
        'receipt_url',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'next_emi_date' => 'date',
        'product_price' => 'decimal:2',
        'down_payment' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'pending_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'emi_amount' => 'decimal:2',
    ];

    /**
     * Generate unique subscription ID
     */
    public static function generateSubscriptionId()
    {
        do {
            $id = 'SUB-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('subscription_id', $id)->exists());

        return $id;
    }

    /**
     * Get the customer that owns the subscription
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the product associated with the subscription
     */
    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }

    /**
     * Get the user who created the subscription
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'active' => 'bg-success',
            'cancelled' => 'bg-danger',
            'paused' => 'bg-warning',
            'expired' => 'bg-secondary',
            default => 'bg-primary'
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus()
    {
        return ucfirst($this->status);
    }

    /**
     * Calculate next EMI date
     */
    public function calculateNextEmiDate()
    {
        if (!$this->next_emi_date) {
            return null;
        }

        $nextDate = $this->next_emi_date;
        
        switch ($this->billing_cycle) {
            case 'monthly':
                return $nextDate->addMonth();
            case 'quarterly':
                return $nextDate->addMonths(3);
            case 'yearly':
                return $nextDate->addYear();
            default:
                return $nextDate->addMonth();
        }
    }

    /**
     * Check if subscription is overdue
     */
    public function isOverdue()
    {
        return $this->next_emi_date && $this->next_emi_date->isPast() && $this->status === 'active';
    }

    /**
     * Get remaining EMIs
     */
    public function getRemainingEmis()
    {
        return max(0, $this->total_emis - $this->emi_count);
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage()
    {
        if ($this->total_emis == 0) {
            return 0;
        }
        
        return round(($this->emi_count / $this->total_emis) * 100, 2);
    }
}
