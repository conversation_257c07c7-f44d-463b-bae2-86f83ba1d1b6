# Permission Fix Guide for Coupon Creation

## Issue
The "Create Coupon" button is not visible because the current user doesn't have the 'create coupon' permission.

## Root Cause
The coupon permissions were not included in the company role permissions in the original seeder.

## Solutions

### Solution 1: Run Migrations (Recommended)
This will automatically add the coupon permissions to existing company roles and users.

```bash
# Run the migrations to add missing fields and permissions
php artisan migrate
```

### Solution 2: Manual Permission Assignment via Admin Panel
1. Login as a super admin or system admin
2. Go to Settings → Roles & Permissions
3. Edit the "company" role
4. Add the following permissions:
   - manage coupon
   - create coupon
   - edit coupon
   - delete coupon
5. Save the role

### Solution 3: Database Direct Update (Advanced)
If you have database access, you can run these SQL commands:

```sql
-- Get the company role ID
SELECT id FROM roles WHERE name = 'company';

-- Get the coupon permission IDs
SELECT id, name FROM permissions WHERE name LIKE '%coupon%';

-- Add permissions to role (replace role_id and permission_ids with actual values)
INSERT INTO role_has_permissions (permission_id, role_id) VALUES 
(permission_id_for_manage_coupon, company_role_id),
(permission_id_for_create_coupon, company_role_id),
(permission_id_for_edit_coupon, company_role_id),
(permission_id_for_delete_coupon, company_role_id);
```

### Solution 4: Artisan Command (Custom)
Create and run a custom artisan command:

```bash
php artisan make:command AssignCouponPermissions
```

Then add this code to the command:

```php
public function handle()
{
    $couponPermissions = ['manage coupon', 'create coupon', 'edit coupon', 'delete coupon'];
    
    $companyRoles = \Spatie\Permission\Models\Role::where('name', 'company')->get();
    
    foreach ($companyRoles as $role) {
        foreach ($couponPermissions as $permissionName) {
            $permission = \Spatie\Permission\Models\Permission::where('name', $permissionName)->first();
            if ($permission && !$role->hasPermissionTo($permission)) {
                $role->givePermissionTo($permission);
                $this->info("Added {$permissionName} to {$role->name} role");
            }
        }
    }
    
    $this->info('Coupon permissions assigned successfully!');
}
```

Run with: `php artisan assign:coupon-permissions`

## Temporary Testing Solution
For immediate testing, the permission checks have been temporarily commented out in:
- `resources/views/finance/tabs/plan.blade.php` (lines 173-177 and 249-253)
- `app/Http/Controllers/FinanceController.php` (lines 520-522)

**Important**: Remember to uncomment these permission checks after fixing the permissions!

## Verification
After applying any solution, verify the fix by:

1. Login as a company user
2. Navigate to Finance → Plan → Coupons tab
3. The "Create Coupon" button should now be visible
4. Click the button to test the modal functionality

## Re-enabling Permission Checks
After fixing the permissions, uncomment the permission checks:

### In plan.blade.php:
```php
// Change this:
{{-- @can('create coupon') --}}
    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
        <i class="ti ti-plus me-1"></i>{{ __('Create Coupon') }}
    </button>
{{-- @endcan --}}

// Back to this:
@can('create coupon')
    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
        <i class="ti ti-plus me-1"></i>{{ __('Create Coupon') }}
    </button>
@endcan
```

### In FinanceController.php:
```php
// Change this:
// if (!\Auth::user()->can('create coupon')) {
//     return response()->json(['error' => __('Permission denied.')], 403);
// }

// Back to this:
if (!\Auth::user()->can('create coupon')) {
    return response()->json(['error' => __('Permission denied.')], 403);
}
```

## Files Modified for Permission Fix
1. `database/seeders/UsersTableSeeder.php` - Added coupon permissions to company role
2. `database/migrations/2025_07_30_000001_add_coupon_permissions_to_company_role.php` - Migration to fix existing installations
3. `resources/views/finance/tabs/plan.blade.php` - Temporarily disabled permission checks
4. `app/Http/Controllers/FinanceController.php` - Temporarily disabled permission checks
