{"__meta": {"id": "X596c0f6eb1cf095e934d4f734eb715ce", "datetime": "2025-07-30 06:07:51", "utime": **********.168865, "method": "GET", "uri": "/pipelines/23/stages", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[06:07:50] LOG.info: getStages called for pipeline: 23", "message_html": null, "is_string": false, "label": "info", "time": **********.944517, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: User permissions - manage pipeline: true", "message_html": null, "is_string": false, "label": "info", "time": **********.145213, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: User permissions - create lead: true", "message_html": null, "is_string": false, "label": "info", "time": **********.146338, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: User permissions - edit lead: true", "message_html": null, "is_string": false, "label": "info", "time": **********.147166, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: Pipeline created_by: 79, User creatorId: 79", "message_html": null, "is_string": false, "label": "info", "time": **********.147383, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: Total stages for pipeline 23: 5", "message_html": null, "is_string": false, "label": "info", "time": **********.152274, "xdebug_link": null, "collector": "log"}, {"message": "[06:07:51] LOG.info: Found 5 stages for pipeline 23 with created_by 79", "message_html": null, "is_string": false, "label": "info", "time": **********.156073, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.253493, "end": **********.168896, "duration": 0.915402889251709, "duration_str": "915ms", "measures": [{"label": "Booting", "start": **********.253493, "relative_start": 0, "end": **********.849883, "relative_end": **********.849883, "duration": 0.5963900089263916, "duration_str": "596ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.849898, "relative_start": 0.596405029296875, "end": **********.168898, "relative_end": 2.1457672119140625e-06, "duration": 0.3190000057220459, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54690992, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pipelines/{pipeline}/stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PipelineController@getStages", "namespace": null, "prefix": "", "where": [], "as": "pipelines.stages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPipelineController.php&line=547\" onclick=\"\">app/Http/Controllers/PipelineController.php:547-588</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.0318, "accumulated_duration_str": "31.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9046218, "duration": 0.01744, "duration_str": "17.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 54.843}, {"sql": "select * from `pipelines` where `id` = '23' limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.928395, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "radhe_same", "start_percent": 54.843, "width_percent": 2.013}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.940119, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 56.855, "width_percent": 2.704}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PipelineController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PipelineController.php", "line": 551}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.953949, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.56, "width_percent": 2.358}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PipelineController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PipelineController.php", "line": 551}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9599562, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 61.918, "width_percent": 2.044}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PipelineController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PipelineController.php", "line": 551}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9645941, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 63.962, "width_percent": 8.616}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.994545, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 72.579, "width_percent": 5.975}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.018665, "duration": 0.00556, "duration_str": "5.56ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 78.553, "width_percent": 17.484}, {"sql": "select * from `lead_stages` where `pipeline_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PipelineController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PipelineController.php", "line": 562}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.148886, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PipelineController.php:562", "source": "app/Http/Controllers/PipelineController.php:562", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPipelineController.php&line=562", "ajax": false, "filename": "PipelineController.php", "line": "562"}, "connection": "radhe_same", "start_percent": 96.038, "width_percent": 2.138}, {"sql": "select * from `lead_stages` where `pipeline_id` = 23 and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PipelineController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PipelineController.php", "line": 568}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.152549, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PipelineController.php:568", "source": "app/Http/Controllers/PipelineController.php:568", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPipelineController.php&line=568", "ajax": false, "filename": "PipelineController.php", "line": "568"}, "connection": "radhe_same", "start_percent": 98.176, "width_percent": 1.824}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1594, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1176, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 2782, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[ability => manage pipeline, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-45106668 data-indent-pad=\"  \"><span class=sf-dump-note>manage pipeline</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45106668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144321, "xdebug_link": null}, {"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-720898965 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720898965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146228, "xdebug_link": null}, {"message": "[ability => edit lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1414417753 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414417753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147064, "xdebug_link": null}, {"message": "[ability => manage pipeline, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-392548121 data-indent-pad=\"  \"><span class=sf-dump-note>manage pipeline</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392548121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148097, "xdebug_link": null}]}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pipeline\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/pipelines/23/stages", "status_code": "<pre class=sf-dump id=sf-dump-1613621914 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1613621914\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-859270408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-859270408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-342356405 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-342356405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112802874 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/pipeline</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjNEWEpQeERlUU9lUkhNamp4YitBWEE9PSIsInZhbHVlIjoic0JhdldNSFVNcVZ4bjExTmNWck5VZzFsMksxcytTSWplZGxiaWxBZ3hCZjFBMUgwZWhYUXlKVXZRQUwvZHFoL1Exb0NrTHBpU1ZnWXFkV2k4OVRQUlVBSGg5UDdHZU00TmdNRjh6RFNyODJoODlpUDZ0UGpIYjFYcmRUWlJqNkdmZWprY3h3d0R4L3dBeUpTSlNPSkF3emRuT3hlSElnSEQ0Ulk0R0Y3dmxhaU8xOGdNeHl2YndKNnhtMlZlWnVTZFk3WkF0TnZwaUhqRlA5NFpzWWdHTTd6WnFsdE1Zb0lxaXBWVWJBcjV4MkJaaG5jbnlrWkdIdVpCYkRmVDhGcEVZWmloNUVuZUd4eS83aWpSeW5ib0FrL3c2VjdlM3dGTGQyMFZud2xHT1pJQkRDTzBlV3l1anJUL255VFQvdkVQcStZTytrV2I0amdxbEJRd01Tb0t1Nlg2NDl1YmdJNWFLTnRBbWxhakphNlFDS0VvOE1zVytNWlNGeExNM25NU0hVY25vUEtGdEkyMGVWSmplcDUxMm1mNTFQYVduZzY2SGFBTVEyRS9SbERJQVhHbTBQR1I0WmF2ajEvanJsQWdtelJjYnZWd0czTEFTTVRqMkNtOVpLalZkVllNZ1hMY0N2VW53WEU3c1ZydkJsSm0wT0pqVkJ3bGFEYTNmL1kiLCJtYWMiOiJiZWI0YjgwYmNkYmRjZjMyOWYyZTNmNzQwNzE3OTVmMDQ5YTk3YWMyNTFlMTUxZTIwYjQxZmM3ZDE4OGY1NDAwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjdQRHU3eGt3Z0RLVnMzTkVtcFNMUnc9PSIsInZhbHVlIjoic0RDUy9LWHZkMWhsNkM0Qk04djNveWpEYzhDaW5EcFBmMy9HY2VERDZ0SFdyTGZGY28vc1lveVM3OHNUWnZLRDRxQWlGNm5wa1RBdXowOUNJV3VTbkFhcWRpdE1iUFhuejdiOEhiOUR0UEV4dWVPM2VNanFyUUxsazIrZWNIcmZoNFovRm1zOE9LczJUVm9oYXNDTUZlRnNlZ0lpa3NYN1BMZWg1UmV0alBEalpSM0JZRGQxMU1UY0tsOVdvYm5ZN2VsUGpheEd5NG9VTGQxNnd0RmNEYkFxQkk5QW54eE9NTUJ2NlY5eGo3bHFiQU5pSHk1Rjc3WTBwSks5WHRVOCtGcDIzNnQvZjBBSmtYSmZPbk5PaWlLaGtxdTZDbitmWCtwN1BpVkpYeVNuc0NWNlVCTldBdXM4dUJPdlhQMWNsOEkrYWVnTE8xNk9nb1NqaVJRK292SDEvWU9JT042SlBhZjNZOUl1NGtnV0JXeWpmcDVEckFTRWpFY3BSMDJuRFR3N0xLWkdQcGtGVGEyeVhXUGJmYjgwMEkxRmZ6WE5uaEZyWWZxKzgrbHJxOWczdWt3R29JdlpKbWpRWEhPWXQwUHJpaXBxUW1mQyt2TFBtVjhJRm0wUlhIZm5LOStibk5NOXZTQkVrZm1CMllic2tKbWIzYVZxVEVsR0d3TFQiLCJtYWMiOiJiMDBmYjgzY2RmZDllYTQ0MDJjYjJjZDQ0MjNiODY0OGUzMGRhNDFjM2RiOWMyODE2YTg3Y2ZhNmRkMDdlMTIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112802874\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-623082569 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623082569\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1273857627 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 06:07:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1MQ2JUQkRVUlZ6NmFWTEpoVHZzTlE9PSIsInZhbHVlIjoicXFuWDNQeXFTL3U2cm9sTWpBeFp5a3dIL3k0ZDczeWI5dWMrZFlVcWZWM0l4cDBkZWEycUliVmZYQ1dzdlkzL2U2TjAzVDhPYXRQMWg4NjRJQnBTWTA4cytXNjhQY0hlbW1SRm8xVHo0V1FOd3dKeituWnRQZG12cEFvYmhmejNHRWQxQ2YzWldxQXFUdTBlV2NiV0lRNm1URnVxYmI5cVFqQ09Nc1VkdTM5a1k3ZTRJbEJIdDJaSG13RnFEcHh3SlMxUURjUitZU2h4ZTNEWk96dmdlUnZtYnFQeCtjQVVLSG0yMDI0QTNiU0FDckFjeWdZN29GZkNxY05QRWxPUGZiZ3pzdTY0MmxwRFdEbXE3Vm4yd3AzSHJvaHN2SFZUNkxQMWJzeTBIQmEyUm9hLzRHWFlSdCs5L0FicUo2bkwvRUVvdlN0T3MwWXpqUjRmTGFRUGRWN3hrUnF3d0VScUw0Q2s3cG93QWtVd3JzaFFldHJiOEp1WGFNdGtFSUh5b2ZUNVh3NE40dXo3MnU4czM2SHdJNEd5OUk4ZWRHeStmK0MzcWNkeEhTSkVMR0hxNjR1TkY1SWdZbEN5SXJSSEREQ1UwVXlNWVRIcDRNdll0UU11MUlMTVNzNlhhWWhhcG5GMmp1cWFhVHAvd2h1SGRQbHZWaGNVVC9lRll0KzkiLCJtYWMiOiIwNzEzMWQ1ODllYTgwYjYwY2NkYTA2ODkwYzAwYzA5OGMyMWE5YWM3MDBjODcwZWNmZGFhNzNhZjkwZmI5ZWZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:07:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InhEYjVSUEttbisrS1l0dzUrekZEbmc9PSIsInZhbHVlIjoiMGU5dTB0M1M4WVdhbnlzTXRVOWJGYWVZSk1MS2NkZ0o2U2RMS3oyaml5cGtzZmJuMW1zVy9uQURRS1pORENHMDh5NkNQSHlHYTZDWXdRTURsTTlwMFYxY0lvME9PZHVXaGNuWHlVMWwwVWkyQlNlQ1EvSnFhY0Qvd1BuMk1HYzdMVStmNG1taURsZVJuNjJnQWQ4amVjOERaaUQzVTNaeStwUHhIWTg0WWlGYlNmbEU4T2xsaHpRM0hiaVVudWwyS1c1UVlLelVTejkrajhUOXBoM2tqZDNrdWkwWkVJSmpqa3pKUWJCSTNNS0M5T1dFL1J2amQvM3QrSTFJbmpJSHNBWFNNNm0yREpmTytmaXkzWFlJbjRHMXhuSi83cEs1WUJtSjVUQ0ZKcnZCQzRFVE0rN3lOZnM1SGtkaFNYWnUvR2NGQjFScVdmRHVtTWFwY2FTeUFkUFJiVWxSRzlzNjJTTCs2S01DY0szaUtOMHduZndyZVBCQ3daZ0g5RlRXenVOOWNSYUJ5RVd6V0d1TGFKbzJsSnJDNjI1NXdHL25oRXl5M1ZJNXRJZnhlZFVwd0NPaVEwMVBRZ2pNaXlXQllDUGhqRU95bVlZQ0ZzM3NFSDFTdHpua1RCa3Z4NVFaU3NSSVk2TW5uT0h4OGczYm1mTEVLcENLUzJaV1U3VFoiLCJtYWMiOiIyMDE5ZDI1ZjE1ZDRjYjUyNTE1YmU5YTMwMGEzMzE4ZDU5NmEwMjczNjAwYjEwZTU0Y2RjY2ViNDM1YjQzYzc4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 08:07:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1MQ2JUQkRVUlZ6NmFWTEpoVHZzTlE9PSIsInZhbHVlIjoicXFuWDNQeXFTL3U2cm9sTWpBeFp5a3dIL3k0ZDczeWI5dWMrZFlVcWZWM0l4cDBkZWEycUliVmZYQ1dzdlkzL2U2TjAzVDhPYXRQMWg4NjRJQnBTWTA4cytXNjhQY0hlbW1SRm8xVHo0V1FOd3dKeituWnRQZG12cEFvYmhmejNHRWQxQ2YzWldxQXFUdTBlV2NiV0lRNm1URnVxYmI5cVFqQ09Nc1VkdTM5a1k3ZTRJbEJIdDJaSG13RnFEcHh3SlMxUURjUitZU2h4ZTNEWk96dmdlUnZtYnFQeCtjQVVLSG0yMDI0QTNiU0FDckFjeWdZN29GZkNxY05QRWxPUGZiZ3pzdTY0MmxwRFdEbXE3Vm4yd3AzSHJvaHN2SFZUNkxQMWJzeTBIQmEyUm9hLzRHWFlSdCs5L0FicUo2bkwvRUVvdlN0T3MwWXpqUjRmTGFRUGRWN3hrUnF3d0VScUw0Q2s3cG93QWtVd3JzaFFldHJiOEp1WGFNdGtFSUh5b2ZUNVh3NE40dXo3MnU4czM2SHdJNEd5OUk4ZWRHeStmK0MzcWNkeEhTSkVMR0hxNjR1TkY1SWdZbEN5SXJSSEREQ1UwVXlNWVRIcDRNdll0UU11MUlMTVNzNlhhWWhhcG5GMmp1cWFhVHAvd2h1SGRQbHZWaGNVVC9lRll0KzkiLCJtYWMiOiIwNzEzMWQ1ODllYTgwYjYwY2NkYTA2ODkwYzAwYzA5OGMyMWE5YWM3MDBjODcwZWNmZGFhNzNhZjkwZmI5ZWZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:07:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InhEYjVSUEttbisrS1l0dzUrekZEbmc9PSIsInZhbHVlIjoiMGU5dTB0M1M4WVdhbnlzTXRVOWJGYWVZSk1MS2NkZ0o2U2RMS3oyaml5cGtzZmJuMW1zVy9uQURRS1pORENHMDh5NkNQSHlHYTZDWXdRTURsTTlwMFYxY0lvME9PZHVXaGNuWHlVMWwwVWkyQlNlQ1EvSnFhY0Qvd1BuMk1HYzdMVStmNG1taURsZVJuNjJnQWQ4amVjOERaaUQzVTNaeStwUHhIWTg0WWlGYlNmbEU4T2xsaHpRM0hiaVVudWwyS1c1UVlLelVTejkrajhUOXBoM2tqZDNrdWkwWkVJSmpqa3pKUWJCSTNNS0M5T1dFL1J2amQvM3QrSTFJbmpJSHNBWFNNNm0yREpmTytmaXkzWFlJbjRHMXhuSi83cEs1WUJtSjVUQ0ZKcnZCQzRFVE0rN3lOZnM1SGtkaFNYWnUvR2NGQjFScVdmRHVtTWFwY2FTeUFkUFJiVWxSRzlzNjJTTCs2S01DY0szaUtOMHduZndyZVBCQ3daZ0g5RlRXenVOOWNSYUJ5RVd6V0d1TGFKbzJsSnJDNjI1NXdHL25oRXl5M1ZJNXRJZnhlZFVwd0NPaVEwMVBRZ2pNaXlXQllDUGhqRU95bVlZQ0ZzM3NFSDFTdHpua1RCa3Z4NVFaU3NSSVk2TW5uT0h4OGczYm1mTEVLcENLUzJaV1U3VFoiLCJtYWMiOiIyMDE5ZDI1ZjE1ZDRjYjUyNTE1YmU5YTMwMGEzMzE4ZDU5NmEwMjczNjAwYjEwZTU0Y2RjY2ViNDM1YjQzYzc4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 08:07:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273857627\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-816613144 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/pipeline</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816613144\", {\"maxDepth\":0})</script>\n"}}