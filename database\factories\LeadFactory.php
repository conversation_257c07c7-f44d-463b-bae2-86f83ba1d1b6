<?php

namespace Database\Factories;

use App\Models\Lead;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeadFactory extends Factory
{
    protected $model = Lead::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->phoneNumber,
            'subject' => $this->faker->sentence,
            'user_id' => User::factory(),
            'pipeline_id' => 1, // Default pipeline
            'stage_id' => 1, // Default stage
            'sources' => null,
            'products' => null,
            'notes' => $this->faker->optional()->paragraph,
            'labels' => null,
            'order' => 0,
            'created_by' => User::factory(),
            'is_active' => 1,
            'is_converted' => 0,
            'date' => $this->faker->date(),
            'next_follow_up_date' => $this->faker->optional()->dateTimeBetween('now', '+1 month'),
            'is_deleted' => 0,
            'date_of_birth' => $this->faker->optional()->date(),
            'type' => $this->faker->randomElement(['new', 'existing', 'prospect', 'qualified', 'unqualified']),
            'status' => $this->faker->randomElement(['open', 'contacted', 'qualified', 'lost', 'won']),
            'opportunity_info' => $this->faker->optional()->sentence,
            'opportunity_description' => $this->faker->optional()->paragraph,
            'opportunity_source' => $this->faker->optional()->randomElement(['website', 'referral', 'social_media', 'email', 'phone', 'advertisement', 'other']),
            'lead_value' => $this->faker->optional()->randomFloat(2, 100, 10000),
            'contact_type' => $this->faker->optional()->randomElement(['individual', 'business']),
            'tags' => null,
            'postal_code' => $this->faker->optional()->postcode,
            'city' => $this->faker->optional()->city,
            'state' => $this->faker->optional()->state,
            'country' => $this->faker->optional()->country,
            'business_name' => $this->faker->optional()->company,
            'business_gst' => $this->faker->optional()->regexify('[A-Z0-9]{15}'),
            'business_state' => $this->faker->optional()->state,
            'business_postal_code' => $this->faker->optional()->postcode,
            'business_address' => $this->faker->optional()->address,
            'dnd_settings' => null,
            'contact_group_id' => null,
        ];
    }

    /**
     * Indicate that the lead is active.
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => 1,
            ];
        });
    }

    /**
     * Indicate that the lead is converted.
     */
    public function converted()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_converted' => 1,
            ];
        });
    }

    /**
     * Indicate that the lead is qualified.
     */
    public function qualified()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'qualified',
                'status' => 'qualified',
            ];
        });
    }

    /**
     * Indicate that the lead is a prospect.
     */
    public function prospect()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'prospect',
                'status' => 'contacted',
            ];
        });
    }
}
