{"__meta": {"id": "Xd014b81c0c85d4f394a1d7ee221551a1", "datetime": "2025-07-30 05:14:28", "utime": **********.199443, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753852465.973643, "end": **********.199478, "duration": 2.225834846496582, "duration_str": "2.23s", "measures": [{"label": "Booting", "start": 1753852465.973643, "relative_start": 0, "end": 1753852467.919758, "relative_end": 1753852467.919758, "duration": 1.9461150169372559, "duration_str": "1.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753852467.919777, "relative_start": 1.****************, "end": **********.199481, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "280ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4ht2iM0IthiTg6Rvet27IMxsUay4E0OgixQ86zVw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-271882597 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-271882597\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2079304016 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2079304016\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-172635984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-172635984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1073009082 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073009082\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1330144647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1330144647\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-622844478 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:14:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZHR1JQbk9vcDNrenZVRng5Wm51SlE9PSIsInZhbHVlIjoiUi9xNlJnRkpnb3E0a0IvNnlCUklYdnhiaksveFNvNUNHQlpKaHNYdHpWMWhaOXIvSnkrVTNBZlJDakJ3alNjK1Q3c2c4eWE2ZTRqdFZPZEZ3eENLRUFoTFpIOUpJY1JzbDZJanE4ZkVzbmVFVWhEcU5lbWJaZlRweFNjN2lhMzFSdmhzZVo4bHVLTkFUS1JJNTNlNzl2RzFjZ2lKZWZqRnRGZzRKUVc4bDNmLzk4Q0xLbkdNL1d0WGwvR0NvRmkrRWdFVzNRWWJRN2JFakJwTzV0clNXWkJkTXRFdzhsNXlnUTNWc3RwMXdMYVFBcUpMQVVhekkwczF5UG0ybGRPSnB3S01LOFdlYUJPMU93WVE0ZUlXbGxDdU1WZURGZ0hRMjhGWk5JS2EzZWhNTi9NRkppV1NxWXh2b0VhNDlEckNzNmZLeDRjY3d0dk1aNHUza0JYL2E1czNLUk0ycUhtUzhVUlpZcHZDWllKRk5la3ZBV09YTWdYcjNEcjFNWlEzL3N3VGZ3L1prZUxId0E1YVlHamdFMmFTNWVqTHRjQ1lGeGdTYUJzRFZJVmdrRmxFRTdZSDFSYTJodEhmTms5Mzh6ZC9hcUd5SmkxL1R2SFkweEZCblNZaEV6c1RlMGtaNHRLZVpoNUVUMGcvbGJ4YmtwRzhMYVRLZXRHbm1wT3IiLCJtYWMiOiI5MWFiMDM4MTkwNzVlNGQxM2ZlMDFjOWRkODY4OGQ2MThkY2FjMmE3MzBjMzRhZTE5NDU4ZTZhNWRiMzQ3MjRiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:14:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZxYjl3RXYreTVhOURBcnM0RlRpNUE9PSIsInZhbHVlIjoiWmVwUFRURnY3OWFHNmYzQ2FlT2FsK3pEUDk3VXBZeTFKZ2FsNjZlcmdBQzVJYlRtTmZjWm5pL3pPQ1RVZXgwby9QaWFhV0x2R1RLRjBidlp3ckcyNTJwbi9PZUEyOHVwTUNVQnVJVmhyNFR2N2F0amY5dnRiY1lXdloxTjNib09vUlBTQVVNSVVRajBsajFFZjQ1aHpaWHV4SnlaTkY4cHA1L09qZS9Lc0YzRHJkaVJERTZzeWFLNkFGSzRJcW1idEZ6TncxcU9aYTlkaiszaGZ2SjU2dSsxQ0h0UmEvalNpY3AxVXpUbjJuQmtyOWM4cUFHTjRWVEdkbHNITGVzWUM4Rld4OTV6SHdCVU93ZlFjTmVqc0lmTUo4Qk81WXZNL05rUDZlbUc3RlRHK3lFRUFWVEpHT1V1T1dEZUJubWU5OUNPQnVtQWl4RVhSdkpvWUo5Q2tqV0oxOWpoaEZxZVBJQXBRV1JWcGJhbFNTVnJyOXg1OGhkVHN2RVQ5VUU5RVMyYThaUm14dDBxTUZGUFMrRzdyMHFHVUZFYU5RWm5jeEdGL3NNMSsvOU1kL0dQcWRmWTZlMTI0NnV2dW1JaVh2ejJnWmVqODh0elNGckJNSTRzU1NYRDNUb2RyS2ovMHkrc3lSSWVlVVZwK0JHcnBvSjVibDljejRCZDFWQVEiLCJtYWMiOiIxYTlhZGM4ZDQ5ZDc5MjA1MDVhNDZlYmUxMjg1NTJjYmJlMDlkZjk5NjVkZmQ2NjQ5MGI2ZjNmZDQ2MTNiMGEzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:14:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZHR1JQbk9vcDNrenZVRng5Wm51SlE9PSIsInZhbHVlIjoiUi9xNlJnRkpnb3E0a0IvNnlCUklYdnhiaksveFNvNUNHQlpKaHNYdHpWMWhaOXIvSnkrVTNBZlJDakJ3alNjK1Q3c2c4eWE2ZTRqdFZPZEZ3eENLRUFoTFpIOUpJY1JzbDZJanE4ZkVzbmVFVWhEcU5lbWJaZlRweFNjN2lhMzFSdmhzZVo4bHVLTkFUS1JJNTNlNzl2RzFjZ2lKZWZqRnRGZzRKUVc4bDNmLzk4Q0xLbkdNL1d0WGwvR0NvRmkrRWdFVzNRWWJRN2JFakJwTzV0clNXWkJkTXRFdzhsNXlnUTNWc3RwMXdMYVFBcUpMQVVhekkwczF5UG0ybGRPSnB3S01LOFdlYUJPMU93WVE0ZUlXbGxDdU1WZURGZ0hRMjhGWk5JS2EzZWhNTi9NRkppV1NxWXh2b0VhNDlEckNzNmZLeDRjY3d0dk1aNHUza0JYL2E1czNLUk0ycUhtUzhVUlpZcHZDWllKRk5la3ZBV09YTWdYcjNEcjFNWlEzL3N3VGZ3L1prZUxId0E1YVlHamdFMmFTNWVqTHRjQ1lGeGdTYUJzRFZJVmdrRmxFRTdZSDFSYTJodEhmTms5Mzh6ZC9hcUd5SmkxL1R2SFkweEZCblNZaEV6c1RlMGtaNHRLZVpoNUVUMGcvbGJ4YmtwRzhMYVRLZXRHbm1wT3IiLCJtYWMiOiI5MWFiMDM4MTkwNzVlNGQxM2ZlMDFjOWRkODY4OGQ2MThkY2FjMmE3MzBjMzRhZTE5NDU4ZTZhNWRiMzQ3MjRiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:14:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZxYjl3RXYreTVhOURBcnM0RlRpNUE9PSIsInZhbHVlIjoiWmVwUFRURnY3OWFHNmYzQ2FlT2FsK3pEUDk3VXBZeTFKZ2FsNjZlcmdBQzVJYlRtTmZjWm5pL3pPQ1RVZXgwby9QaWFhV0x2R1RLRjBidlp3ckcyNTJwbi9PZUEyOHVwTUNVQnVJVmhyNFR2N2F0amY5dnRiY1lXdloxTjNib09vUlBTQVVNSVVRajBsajFFZjQ1aHpaWHV4SnlaTkY4cHA1L09qZS9Lc0YzRHJkaVJERTZzeWFLNkFGSzRJcW1idEZ6TncxcU9aYTlkaiszaGZ2SjU2dSsxQ0h0UmEvalNpY3AxVXpUbjJuQmtyOWM4cUFHTjRWVEdkbHNITGVzWUM4Rld4OTV6SHdCVU93ZlFjTmVqc0lmTUo4Qk81WXZNL05rUDZlbUc3RlRHK3lFRUFWVEpHT1V1T1dEZUJubWU5OUNPQnVtQWl4RVhSdkpvWUo5Q2tqV0oxOWpoaEZxZVBJQXBRV1JWcGJhbFNTVnJyOXg1OGhkVHN2RVQ5VUU5RVMyYThaUm14dDBxTUZGUFMrRzdyMHFHVUZFYU5RWm5jeEdGL3NNMSsvOU1kL0dQcWRmWTZlMTI0NnV2dW1JaVh2ejJnWmVqODh0elNGckJNSTRzU1NYRDNUb2RyS2ovMHkrc3lSSWVlVVZwK0JHcnBvSjVibDljejRCZDFWQVEiLCJtYWMiOiIxYTlhZGM4ZDQ5ZDc5MjA1MDVhNDZlYmUxMjg1NTJjYmJlMDlkZjk5NjVkZmQ2NjQ5MGI2ZjNmZDQ2MTNiMGEzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:14:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622844478\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1655066791 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4ht2iM0IthiTg6Rvet27IMxsUay4E0OgixQ86zVw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655066791\", {\"maxDepth\":0})</script>\n"}}