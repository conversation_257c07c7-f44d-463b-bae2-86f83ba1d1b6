{"__meta": {"id": "Xc7da0e0cf89a8bb8ecb8decb6a06e3fd", "datetime": "2025-07-30 02:36:25", "utime": **********.137377, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753842981.394235, "end": **********.137453, "duration": 3.743218183517456, "duration_str": "3.74s", "measures": [{"label": "Booting", "start": 1753842981.394235, "relative_start": 0, "end": 1753842984.665587, "relative_end": 1753842984.665587, "duration": 3.2713520526885986, "duration_str": "3.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753842984.665619, "relative_start": 3.****************, "end": **********.137461, "relative_end": 7.867813110351562e-06, "duration": 0.*****************, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ioULzZGxtdR81oXxM2WPxYaHmVyubQ33NW6v6uDd", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-518327809 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-518327809\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1759808031 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1759808031\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1254844494 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1254844494\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-324772288 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324772288\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-906360958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-906360958\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-219450801 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:36:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijg0ZWwzdmdpeWNkdlgxYk1rK3ZNcmc9PSIsInZhbHVlIjoiVDRSajczOSttallUd0VBR1JHb3JMRVdaV1UvdEhQMUI4Zm82TmY0MGJyZlVPZ01GdCtESjRnVzNDL2ZsNUNwYXlMWlRyNXI3V2ZRN1puMFFIbzBGN0x0Zm5sbnZna2YwbW5vc24vZTlmTUVQVzJ2MVFRSG9oY2RmZGpIbWxHbTIxYUtsNVpUUWJuMjNGUmM4S2dyNTNZMjgyZGlka0pLdlQwT2VHRHd1NUtJeUYzNUJOY2NyN1o4QlUwTUs2OEwwb3JmOFZzWWRJMFB5S085d3RvSHBJSFVaYkJOM3ZoZHVZbms0b2JjSFNKZFNVRGd2emEzdC91SjJmY2JJYnVicHA3VTFaVWdVUW5XMUdIU1daaFNkck9aSE9xbmdXOUIzSmUwVk13bkJKcmQ0R1cyczJRQ1plVndheFZPQkVlTEdscTZoejVMSXB2SXd4QTF1MWtjNFVtQXBFa3dxaDl3U2k0eHZrN0F4Y2lYdnBCakhRcXdBZDIvVytqdG01aW8xaE9ONTlWOUFEOGpBUU5tL2hhWHFRMzVCczNmZ09IaTVEdE5ZRy9OWDd3ZFNiTUdKYmJnYTdLdnZPTkNxZksvVjB6YWpuMXloTnJlSElYYmZMdlMzeVZVMUkwWndCWmZ5VHhheFJWWVJnU25wRmhhUkV4WWJqSGNlSTJKNTl2Zi8iLCJtYWMiOiI4YmZjMjkzZGUwOGU0ODJiZGZjNDcwYmIyMGI3Yjc5YmIyZWM0YmI0MWU1ZmM4ZWI3Mzk0NzY2MTRjZmZlNGJjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktLYmo1T1UzbnVyeTEwRWpxMk9YTmc9PSIsInZhbHVlIjoiNFhKV05jWkNHMThRcTd6UDhmZWpoMTJqTWJnZyt4ZjA5RDR1dXpZbW11N2dMb3oybmJMUlRyQUlxdmlLQ1p5QjNXM09QTGhGcmx3aURpeXFXN1JIcjFyaUVGUk1qUjIreWh4SitzTVl4bmczM0FycmNoQXlTcDJ0V0ZIaTlOenl3QUtZbDRSRDk4VDVLbU0veUtDRlY4WlN3TW5Mb0dEY2N3cmM2a1dyL1k5N0w1M2RNclpDdkhjRFlZNGZld0M4NmgwWDZWRVRmbE8vaXVpTFdiMzFlZzRqelplTFFQdkd1cFlocXo1R3l0TEpmU2ZNRW9ONTZsamxmbGNEdlZlTWYzektRTUtNcjhWSkJWYUNpSUt6V1d3U2ZQK3V1QWQ4dG9XbjlQVGhyUHpZRngyd3o5OWxheUtuNUpYcVRBRUpQUUlVMDdDWDE5dzFNalh3TFFlZGhjUTlGM3ZOTVd2ZHhpL0tZdmJiRTUvUmxzanZ3aTB0b2dnc3pIdXgrNTdzbTBoSVFMTVJZUkxOMkJpMDFaLzhISmc3bUh4MldHbjFxYkR2ZmI5OU5qWWpaUDVLWC96Q0RjNkd4ZmpFU3VacGJnejQvWWFQSVRhSmVpRklsdE9mTG12RWVsSEprbG9aV0FDWmJ1SmFiY2J3enhLcUxYbklvSVdUMVFCdTBiVDIiLCJtYWMiOiI0MGNiNmQxMTlmNzU4MDRlZGRjYjExY2UwODhkZmM1MGI5YjYyMDNiNWMyZGQ5NmVkZDc0Mjk3ZmM5NzllMjg5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:36:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijg0ZWwzdmdpeWNkdlgxYk1rK3ZNcmc9PSIsInZhbHVlIjoiVDRSajczOSttallUd0VBR1JHb3JMRVdaV1UvdEhQMUI4Zm82TmY0MGJyZlVPZ01GdCtESjRnVzNDL2ZsNUNwYXlMWlRyNXI3V2ZRN1puMFFIbzBGN0x0Zm5sbnZna2YwbW5vc24vZTlmTUVQVzJ2MVFRSG9oY2RmZGpIbWxHbTIxYUtsNVpUUWJuMjNGUmM4S2dyNTNZMjgyZGlka0pLdlQwT2VHRHd1NUtJeUYzNUJOY2NyN1o4QlUwTUs2OEwwb3JmOFZzWWRJMFB5S085d3RvSHBJSFVaYkJOM3ZoZHVZbms0b2JjSFNKZFNVRGd2emEzdC91SjJmY2JJYnVicHA3VTFaVWdVUW5XMUdIU1daaFNkck9aSE9xbmdXOUIzSmUwVk13bkJKcmQ0R1cyczJRQ1plVndheFZPQkVlTEdscTZoejVMSXB2SXd4QTF1MWtjNFVtQXBFa3dxaDl3U2k0eHZrN0F4Y2lYdnBCakhRcXdBZDIvVytqdG01aW8xaE9ONTlWOUFEOGpBUU5tL2hhWHFRMzVCczNmZ09IaTVEdE5ZRy9OWDd3ZFNiTUdKYmJnYTdLdnZPTkNxZksvVjB6YWpuMXloTnJlSElYYmZMdlMzeVZVMUkwWndCWmZ5VHhheFJWWVJnU25wRmhhUkV4WWJqSGNlSTJKNTl2Zi8iLCJtYWMiOiI4YmZjMjkzZGUwOGU0ODJiZGZjNDcwYmIyMGI3Yjc5YmIyZWM0YmI0MWU1ZmM4ZWI3Mzk0NzY2MTRjZmZlNGJjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktLYmo1T1UzbnVyeTEwRWpxMk9YTmc9PSIsInZhbHVlIjoiNFhKV05jWkNHMThRcTd6UDhmZWpoMTJqTWJnZyt4ZjA5RDR1dXpZbW11N2dMb3oybmJMUlRyQUlxdmlLQ1p5QjNXM09QTGhGcmx3aURpeXFXN1JIcjFyaUVGUk1qUjIreWh4SitzTVl4bmczM0FycmNoQXlTcDJ0V0ZIaTlOenl3QUtZbDRSRDk4VDVLbU0veUtDRlY4WlN3TW5Mb0dEY2N3cmM2a1dyL1k5N0w1M2RNclpDdkhjRFlZNGZld0M4NmgwWDZWRVRmbE8vaXVpTFdiMzFlZzRqelplTFFQdkd1cFlocXo1R3l0TEpmU2ZNRW9ONTZsamxmbGNEdlZlTWYzektRTUtNcjhWSkJWYUNpSUt6V1d3U2ZQK3V1QWQ4dG9XbjlQVGhyUHpZRngyd3o5OWxheUtuNUpYcVRBRUpQUUlVMDdDWDE5dzFNalh3TFFlZGhjUTlGM3ZOTVd2ZHhpL0tZdmJiRTUvUmxzanZ3aTB0b2dnc3pIdXgrNTdzbTBoSVFMTVJZUkxOMkJpMDFaLzhISmc3bUh4MldHbjFxYkR2ZmI5OU5qWWpaUDVLWC96Q0RjNkd4ZmpFU3VacGJnejQvWWFQSVRhSmVpRklsdE9mTG12RWVsSEprbG9aV0FDWmJ1SmFiY2J3enhLcUxYbklvSVdUMVFCdTBiVDIiLCJtYWMiOiI0MGNiNmQxMTlmNzU4MDRlZGRjYjExY2UwODhkZmM1MGI5YjYyMDNiNWMyZGQ5NmVkZDc0Mjk3ZmM5NzllMjg5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:36:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219450801\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1219179264 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ioULzZGxtdR81oXxM2WPxYaHmVyubQ33NW6v6uDd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219179264\", {\"maxDepth\":0})</script>\n"}}