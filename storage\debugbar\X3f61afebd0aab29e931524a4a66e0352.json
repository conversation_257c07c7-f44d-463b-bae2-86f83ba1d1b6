{"__meta": {"id": "X3f61afebd0aab29e931524a4a66e0352", "datetime": "2025-07-30 05:36:23", "utime": **********.439421, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753853782.541799, "end": **********.439456, "duration": 0.8976569175720215, "duration_str": "898ms", "measures": [{"label": "Booting", "start": 1753853782.541799, "relative_start": 0, "end": **********.331437, "relative_end": **********.331437, "duration": 0.7896380424499512, "duration_str": "790ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.331461, "relative_start": 0.****************, "end": **********.43946, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hzoVrJVC3CBTfnGuEUTMa9qO30HBiiAST5o31kuQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1809169069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1809169069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-547868894 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547868894\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1960213872 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1960213872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:36:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRWT0NxK0hRME1KVHBUSGhqVklmaEE9PSIsInZhbHVlIjoiMVRyS1NDR092NE9aNGIwYWRmcUFBbWNLeStsQmkwTmQrUVAzTXJ3bUZ4OVUrenlrRHk4ZE51ZlhwQXZ6enpPYThVWUpnN21ZZGd4cE1uQ1l0WnZGQ3BUQTd5dGdOcUxCbHYzSjVrYlY0V1N1Sy9QaU5zZmJoR0I0ZkVDTHZreGlhREh6T3NYRXZabm5GK2NWMGRiSTBDOHlaVUNuVWRBY3dVazhvZmV4SlN3TW9EbjJZWVk4V2VKT1JGWFNTdmIyc0JGaGhFKytkY2ZvV3AzSWJpa0MvVDhhdG96ekdiTG5zNEVBd05EOXh5M1BIQ0VVK1dwYXhZMGtxT3hFU09UdC93NGh1azdoa3Mvc0ZHQnhMdTgwRktmQ2djaGV4c2w5Q2tnTHFiM1Q2UVczZkVCVTExdlU0cE4zYnZnMVV5dlZsVVM1RUliblhTamZIUE1VTGVQOU9OVDlLWDJuTEtPTTJvOU4xWTBzWnpEb2M4cWwxR0xtTWg3dk1DdlBmUmdmUlI2T2JHQkZ4dFcyd1paajI0emF0dnJvV3M4MFYyeW1nVitTVkoyMFdjQmo2Yk9jcHQ3aFBwLzByK3hYalBqVW0za0xqblZsVE5zenUxSEV1Mi9iU25xZHdveTY3eGh4eitPelRPNlpiUjZDNFI1Yktxd3YrbWF0TmJ6WTk4a3YiLCJtYWMiOiIzNDU4Y2U1N2NjMjI3NDQ4YzU1OTJkMzg2YjhiZTJhYWNmMjcwZGMxZmUwMjY2MmY3OGY4NTczZmU2ZDlhMjg5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:36:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ilc4RjkwZXFrU0ZWempsemVjZzVrK1E9PSIsInZhbHVlIjoiOXBwakhBajFBeFBrd2lZZW5YR1c5WlI0NkpYZGJxN2UvYWc1TmpwbDBTaEpFRVZkbGhyWGIxaFc1U1hxanphZDd2OFd2NkRKWTFQelE2ZjhMMmYrU3E2Nmd5clducU0yUG9EWHFPRGovYUhGQS90QVQ3eUZIVUtFNDRmM3NJZWViZzVSWTR5Q2hqa3owMVpBT2szdEVjcWNFVVV6bEtwbk5zSHhSYW0vZFNUNUViQllMME9UcFVwQlpyWHpXRUtEVzVXRzBmRWpqQlBHcWJLTmE0elI4bkFyRG5xc1FMYnBsbkVmS2NIb21FQW96Vkc4QkV5K0JDMnZGNmt5STBWdzEwTlVId2x4Z2krVTlobFNtR2lmYnBJK0s1MWY3ME40THFlTU5ZVEt6cFQreStncmpya3ZOQlAzdkt0KytZc2RUcVVjWlRaQXlWdG12bVcyZEFPRDhuMmU2K2d4SVo4T0NFanBqZnRrM3Q3YlUxQ1JoY1JqUmNRck1VeThXb2RPdlZDK3dUQkNjQUNSZ2tNZDVPWVduWTJYSElUQXhIenlBVTBORkJrOGdZckNwL3B6TVJGOWQrbHpXRTByQnpIQU5Qa3ZCYnAvVUtJUEtTTHJiTzd5V0k0amJUUWp4NkllL25wZXk2VHRkT216VWVPdnFTdm10RTdRWGcwWkdENjEiLCJtYWMiOiJiNzllMGU2NTdhOGRiZDg5ZmFkMzdlYTU3NjY1MjhiNDg1M2UyMGMxZWMzZWM2YWZkYmVmNDdlYjI3NDM4MWRiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:36:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRWT0NxK0hRME1KVHBUSGhqVklmaEE9PSIsInZhbHVlIjoiMVRyS1NDR092NE9aNGIwYWRmcUFBbWNLeStsQmkwTmQrUVAzTXJ3bUZ4OVUrenlrRHk4ZE51ZlhwQXZ6enpPYThVWUpnN21ZZGd4cE1uQ1l0WnZGQ3BUQTd5dGdOcUxCbHYzSjVrYlY0V1N1Sy9QaU5zZmJoR0I0ZkVDTHZreGlhREh6T3NYRXZabm5GK2NWMGRiSTBDOHlaVUNuVWRBY3dVazhvZmV4SlN3TW9EbjJZWVk4V2VKT1JGWFNTdmIyc0JGaGhFKytkY2ZvV3AzSWJpa0MvVDhhdG96ekdiTG5zNEVBd05EOXh5M1BIQ0VVK1dwYXhZMGtxT3hFU09UdC93NGh1azdoa3Mvc0ZHQnhMdTgwRktmQ2djaGV4c2w5Q2tnTHFiM1Q2UVczZkVCVTExdlU0cE4zYnZnMVV5dlZsVVM1RUliblhTamZIUE1VTGVQOU9OVDlLWDJuTEtPTTJvOU4xWTBzWnpEb2M4cWwxR0xtTWg3dk1DdlBmUmdmUlI2T2JHQkZ4dFcyd1paajI0emF0dnJvV3M4MFYyeW1nVitTVkoyMFdjQmo2Yk9jcHQ3aFBwLzByK3hYalBqVW0za0xqblZsVE5zenUxSEV1Mi9iU25xZHdveTY3eGh4eitPelRPNlpiUjZDNFI1Yktxd3YrbWF0TmJ6WTk4a3YiLCJtYWMiOiIzNDU4Y2U1N2NjMjI3NDQ4YzU1OTJkMzg2YjhiZTJhYWNmMjcwZGMxZmUwMjY2MmY3OGY4NTczZmU2ZDlhMjg5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:36:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ilc4RjkwZXFrU0ZWempsemVjZzVrK1E9PSIsInZhbHVlIjoiOXBwakhBajFBeFBrd2lZZW5YR1c5WlI0NkpYZGJxN2UvYWc1TmpwbDBTaEpFRVZkbGhyWGIxaFc1U1hxanphZDd2OFd2NkRKWTFQelE2ZjhMMmYrU3E2Nmd5clducU0yUG9EWHFPRGovYUhGQS90QVQ3eUZIVUtFNDRmM3NJZWViZzVSWTR5Q2hqa3owMVpBT2szdEVjcWNFVVV6bEtwbk5zSHhSYW0vZFNUNUViQllMME9UcFVwQlpyWHpXRUtEVzVXRzBmRWpqQlBHcWJLTmE0elI4bkFyRG5xc1FMYnBsbkVmS2NIb21FQW96Vkc4QkV5K0JDMnZGNmt5STBWdzEwTlVId2x4Z2krVTlobFNtR2lmYnBJK0s1MWY3ME40THFlTU5ZVEt6cFQreStncmpya3ZOQlAzdkt0KytZc2RUcVVjWlRaQXlWdG12bVcyZEFPRDhuMmU2K2d4SVo4T0NFanBqZnRrM3Q3YlUxQ1JoY1JqUmNRck1VeThXb2RPdlZDK3dUQkNjQUNSZ2tNZDVPWVduWTJYSElUQXhIenlBVTBORkJrOGdZckNwL3B6TVJGOWQrbHpXRTByQnpIQU5Qa3ZCYnAvVUtJUEtTTHJiTzd5V0k0amJUUWp4NkllL25wZXk2VHRkT216VWVPdnFTdm10RTdRWGcwWkdENjEiLCJtYWMiOiJiNzllMGU2NTdhOGRiZDg5ZmFkMzdlYTU3NjY1MjhiNDg1M2UyMGMxZWMzZWM2YWZkYmVmNDdlYjI3NDM4MWRiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:36:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hzoVrJVC3CBTfnGuEUTMa9qO30HBiiAST5o31kuQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}