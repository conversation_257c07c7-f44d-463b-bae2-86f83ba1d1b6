{"__meta": {"id": "Xff51b505e92e8c585c5b0fdb9b2e73ed", "datetime": "2025-07-30 08:09:42", "utime": **********.070706, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862980.181449, "end": **********.070834, "duration": 1.8893849849700928, "duration_str": "1.89s", "measures": [{"label": "Booting", "start": 1753862980.181449, "relative_start": 0, "end": 1753862981.845176, "relative_end": 1753862981.845176, "duration": 1.663727045059204, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862981.845211, "relative_start": 1.***************, "end": **********.070841, "relative_end": 7.152557373046875e-06, "duration": 0.****************, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mGA2el7fJv5DoZo0hvYgdNAgRnfNxKkAzruqa0R0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-976555384 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-976555384\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1469665620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1469665620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1416008634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1416008634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-582281018 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582281018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1802602810 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1802602810\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-904664972 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:09:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1KN1pOZnlpcm1TUmtGWUNRVTBTOFE9PSIsInZhbHVlIjoiMEdLNXVaRHl0cm93WndKMlZNcjZYUlhXclppcEJ1K0s3NTZVVmx2RmpKNksxU2lPMUVIN0RkU0tWdHp3cWNtcVBNdXdRSStXbC9SbVNBWVZoM2Z6NlFCSE9sV3c4dzVLZlpJU3BNMW5pckZUNmVWYTYxR0Y0NGtMVkdBQzNEdzVYVGJyVEkyZk1GUDJ6bTNTamx1dmJOd3NzcURNN05paVJvVFNkaC9tWThpQ0hnUG11WG1RSzdIVld2OE4yaS9kVndMZ25nbER4WC90QWYyR1lwTEgxSGpkMDR2cCsrb2gxZjhQc292SDdoL0pUTXFEZWQ1SnZaMlB5NDRZM0pIc3cycm9XVk8xbkcybXprU0F2ZDUzeEY5YzFYeUg4Y3dhTnZ4YUlLNlR6RmxrY0NVK1UwSFA1VUcrR3hBbGl2eHBWMGpLM1hPZnhwYzFNM25iZWhQbVlpc3pXZ0NoNUIvcWF1UjUxLzg5WlIxT2tKN3M3cTZ4djdhZU8wQ25BWTRWcGhLZHVUZnNnQVlsWEJROEFDNUxWV3hFdVRTL3NhWGUrbjV4bFJzUXc1Q2MwYUJXd3p4dXE1ZG1HUWxjSElqbVRmQStabGQ0bm8vcnJBN3F4SEtQbHVpZWdGRGl1WG5EdWVybE9TQ0Q0U2xVWU5DQmU0b1RXdGFmVkxNeXdaSFkiLCJtYWMiOiIxOTk1NGJjNzMzMGUyOGZiMWFhYzU4ZGZkOTcxMjViZDljMDQ5ODBhNWIyOGM1MzA0YTgzOTdjM2I2MjRmODk2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImxMWE0xL0Nxcm5mWTlldjNrSWZabVE9PSIsInZhbHVlIjoiNFBYK2p3bXhKcFpiZmxrd3c0KytPVWtRb1c2czNIdTI4NXZnakhGOTFFcVBjUWtPVGUzOWtqdWpNeGRJQnhEaUpxZFdKRjQyWnZ5WkM1QStFVnJjKzliaU1sUS9vSE96NGQyU01qbmQ5S0t1U2w2RE1ZTkE1ZFZZODErZjVaUlg0TmFGU0xzUklkOGovNGI0T2RmMEpZQlJickEyblJDZjRXOCtkUDZzM2dXd3RPNUpqMUdhOTl4cUVmcU1lSUVLTENoRkx2NE1EU3FuOGZUK0VtZDlYWHYyUm5DbnI5NlZOL0pUcldpTE5jSmxwRWZlcnZmVHJ1eFlWNHF2bjBGUklmckJhK3BKOEpZNVgra2hCVVg2c041N0hTVWlnWHByb1llRWNlNVc5M042Mml6aTA1WmlvR25CV09yTFFXSnJoMmQrTTZzMFBoRVdlQ1ZHZzc0ZXUxRHZWUzNQSXRZNm14OExLYVNrb1ZYTFM0VGdCcFJ3cGxrYUNtTG9yb3JBalBFaWRZSmpxMVQrRTA1ZUZabWRURUlCZjJ0VUx2b284UWx0QXUzV29VZFZmbnZ2Z29ET1NGckd2WTNFTUJ0a0ZmMVVsOFI4dmgxNmJtdURGVkp2UkVLd0RnVVBuRkVNYUszandFcWR5Z0I0MmZYczZseWdrVjNBSmhyRXIzaE4iLCJtYWMiOiJmOWNkYWM2NTlkYzBmNzFkNjgzZGVhZGEwZTU3NTI1YjNhNmQ2NzcyODRmODMyZTczYmNiMzIyZjExYTBkMzg0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1KN1pOZnlpcm1TUmtGWUNRVTBTOFE9PSIsInZhbHVlIjoiMEdLNXVaRHl0cm93WndKMlZNcjZYUlhXclppcEJ1K0s3NTZVVmx2RmpKNksxU2lPMUVIN0RkU0tWdHp3cWNtcVBNdXdRSStXbC9SbVNBWVZoM2Z6NlFCSE9sV3c4dzVLZlpJU3BNMW5pckZUNmVWYTYxR0Y0NGtMVkdBQzNEdzVYVGJyVEkyZk1GUDJ6bTNTamx1dmJOd3NzcURNN05paVJvVFNkaC9tWThpQ0hnUG11WG1RSzdIVld2OE4yaS9kVndMZ25nbER4WC90QWYyR1lwTEgxSGpkMDR2cCsrb2gxZjhQc292SDdoL0pUTXFEZWQ1SnZaMlB5NDRZM0pIc3cycm9XVk8xbkcybXprU0F2ZDUzeEY5YzFYeUg4Y3dhTnZ4YUlLNlR6RmxrY0NVK1UwSFA1VUcrR3hBbGl2eHBWMGpLM1hPZnhwYzFNM25iZWhQbVlpc3pXZ0NoNUIvcWF1UjUxLzg5WlIxT2tKN3M3cTZ4djdhZU8wQ25BWTRWcGhLZHVUZnNnQVlsWEJROEFDNUxWV3hFdVRTL3NhWGUrbjV4bFJzUXc1Q2MwYUJXd3p4dXE1ZG1HUWxjSElqbVRmQStabGQ0bm8vcnJBN3F4SEtQbHVpZWdGRGl1WG5EdWVybE9TQ0Q0U2xVWU5DQmU0b1RXdGFmVkxNeXdaSFkiLCJtYWMiOiIxOTk1NGJjNzMzMGUyOGZiMWFhYzU4ZGZkOTcxMjViZDljMDQ5ODBhNWIyOGM1MzA0YTgzOTdjM2I2MjRmODk2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImxMWE0xL0Nxcm5mWTlldjNrSWZabVE9PSIsInZhbHVlIjoiNFBYK2p3bXhKcFpiZmxrd3c0KytPVWtRb1c2czNIdTI4NXZnakhGOTFFcVBjUWtPVGUzOWtqdWpNeGRJQnhEaUpxZFdKRjQyWnZ5WkM1QStFVnJjKzliaU1sUS9vSE96NGQyU01qbmQ5S0t1U2w2RE1ZTkE1ZFZZODErZjVaUlg0TmFGU0xzUklkOGovNGI0T2RmMEpZQlJickEyblJDZjRXOCtkUDZzM2dXd3RPNUpqMUdhOTl4cUVmcU1lSUVLTENoRkx2NE1EU3FuOGZUK0VtZDlYWHYyUm5DbnI5NlZOL0pUcldpTE5jSmxwRWZlcnZmVHJ1eFlWNHF2bjBGUklmckJhK3BKOEpZNVgra2hCVVg2c041N0hTVWlnWHByb1llRWNlNVc5M042Mml6aTA1WmlvR25CV09yTFFXSnJoMmQrTTZzMFBoRVdlQ1ZHZzc0ZXUxRHZWUzNQSXRZNm14OExLYVNrb1ZYTFM0VGdCcFJ3cGxrYUNtTG9yb3JBalBFaWRZSmpxMVQrRTA1ZUZabWRURUlCZjJ0VUx2b284UWx0QXUzV29VZFZmbnZ2Z29ET1NGckd2WTNFTUJ0a0ZmMVVsOFI4dmgxNmJtdURGVkp2UkVLd0RnVVBuRkVNYUszandFcWR5Z0I0MmZYczZseWdrVjNBSmhyRXIzaE4iLCJtYWMiOiJmOWNkYWM2NTlkYzBmNzFkNjgzZGVhZGEwZTU3NTI1YjNhNmQ2NzcyODRmODMyZTczYmNiMzIyZjExYTBkMzg0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904664972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-447999252 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mGA2el7fJv5DoZo0hvYgdNAgRnfNxKkAzruqa0R0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447999252\", {\"maxDepth\":0})</script>\n"}}