{"__meta": {"id": "X767cf103f95f3c82913d5d758be865b8", "datetime": "2025-07-30 08:02:45", "utime": **********.657855, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862564.061923, "end": **********.657901, "duration": 1.595978021621704, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1753862564.061923, "relative_start": 0, "end": **********.330317, "relative_end": **********.330317, "duration": 1.2683939933776855, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330355, "relative_start": 1.2684319019317627, "end": **********.657906, "relative_end": 5.0067901611328125e-06, "duration": 0.32755112648010254, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0103, "accumulated_duration_str": "10.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.551946, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 74.757}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6117089, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 74.757, "width_percent": 15.631}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6258152, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 90.388, "width_percent": 9.612}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1825601103 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1825601103\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-268874791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-268874791\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1469112618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1469112618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1305559313 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlFXM2dRbUlrV2hkdGxIYVpqVDV5dVE9PSIsInZhbHVlIjoiQjdFTXphR1FsOUE1TXZBdDNSMWFDNkcrQSs1aTFsSnh3ZGxjM0xQcWhibEpMWjZwb1dFc1YyajU4TmF5ZUtTbTY3VVhnTGswaFNTOWhQNUxoNkhkUG9iRzU2V1h1dnZvZkdWWEVtZnhFR3lMMWsxM1YzczZ4QW5tSklTMXJPaE52TUhjcWxCbnhlSFUvaFFCQ24zN1p3WmN6NGZpT1pIUC9YWTFWcHhVWE1BNDhMQXdLaUZlNDFkc205a1FIVzNDN1Rvek4rUGF5QnBsSVhNQ3M4TFpBbllxMXZ2aW9oNmxVczBBNnRoR29ia3VvWm5ORGFrdWd1R0RFeUxlcTFSeTZqUGJUVDBZRDhhUXlMcEszZE9CdTgwWXhpVjFIR3dhZ2todGxBZ0k2YWp5U25QYnBxN2hUalZyZE96ZlN0R1NFb3pQaEJFUy9yMkRtQk5NOU55Tjkwd2pmMlV5dkV1WjBob3dHZC9WU0RYUkg2UElDbm1BQ2JvWDZxS3NubjVueldHUFQ4ZWk3djRkRlRKajFUTUk5RGJSKzlnSm5PZ3NNWGxEWGJ4YUdsV2Y5R3J6SDRmZ1BtNWtRTUx3Y25iTmlxMFgwRzhqdW1mOHZOQVpIZHRUZTdZZFdIaDNJWi94RU1ZakpKVjNyNDEwUFljbGt0T2dmN29mMG5xWmNzQS8iLCJtYWMiOiIyZmMxNzkyM2ExOTM4OTFmY2FlNzQ0NDEzNWQ4YjJjOTM1MDJkYzU4ZjU1ZGJmZDZlZTJmNmYwODM0NWIwMTljIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpXalZ1NDJyZ1FCOEczaDZVZVJUK2c9PSIsInZhbHVlIjoiNERYWHNaaE5XZXk0MDlmbCtITFBEaFNPTG9ZRHVFNVVHcmk0ZzI4NUVmSWpDcHlhQisyMHdEb0ZlWVpzMG8rSC9yaisrZm1NdTRuV2NjOGJzR1RZQ3A2T3hyRk1qdk5XWDFvSXJTYUI2NHdJd1Y4NDZ0ejBBamF1L3JLS0dDbnFXZ0xWcDlKS0xYc0FLY1lIRFlPM0ZERnYrYkpvNm16bTFRRmgxMmYyRFBQRkNJekp5VC9mVm5mRG9YeUNadzJEV1lIdllocVkrVzZPeVFMaWZGL0N1ZFdWckNzdnVGSHkrT0JvVHpGSCtqUE9HM29nZ09LNm5aZDFzNCtjZlNiTFd5cXpjMnNKZWNMWGtpcW1wQ2RKRWFrby8yS1FES0JpS2R6V3BOZEFENzcwSEgxLzRaTDFqTnlrSGdOWXNuSjFuN3R6aWxqR1hOaXNzOFc1aGxNeXh0d3FWaXEvNlZkWUtlaFFjK2JVbWVGc0VVb3F0YkhLbnozUDk5bmdhKzNvR253SmZtT1hkQVd2TVF3S2ZNVnA2WXRoZVpxNmVuQzVtWHJrNHJOdVlWYndGNldQY1Y2akdDK2VKR0JTZWNQUi8zZDJpdzlwMWxpR3ZEN1B1UnBOS2Q4dXJnUGdJeGorVnViR3F4Rk5LUjlLTWVZdjdlQmY2SG8ydWZoTml1WnkiLCJtYWMiOiI5OTQ5ZDBlNDVhZjgwMjZmNWMzMjIyZjQxOTI3ZDczNTI5OWFjNDJiMGZlNTFlOTRmYjg0N2Y3ZmRiZTg4NDFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305559313\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1688042464 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688042464\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-715417906 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:02:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhZa2cvRitUYmV6MzR3R1RoMlEreWc9PSIsInZhbHVlIjoiQ0NuZUppWGR5dVMrb0ZrUkh6aHlFeDRwRktjSm40NThkNWpmYXJEU2ZDTlZqbjVFZDd1QXoyYmJPdVJ1VXBkVUZjQzVPUUllKzB2bE95WFF4SnJWZmowSkNxaVRjVU8rcm9MeTYvUzdQMi9iK3RVQk94aCs5cW82bHFnSFNWcnkwUVZNZU8yOWVBSUt4QU5YNjcxQlUyNWpxNXprbjZ5RldXQ3E3QlowQnJMYzJIRFgxaHdlVWd6SWVaeWRPMW81cDdSYkpyRzFPdXlRNVB4K0h0TytCY1B3a3dCbjV4WWtxL3BUK1NZWlYrYWhwdWlaTnh5ekxQMUpBaHVxSTYrR3BSaWlmTEdGNE9IY0pPSW5QdmpEU1NXa01MYzl1ZXF4VGZpYzhZMmdSa3Y5VU44NWMxMUxkSm5BNklEMlB0Sk8vMnFXTE1JUml6R1N6TjhjaTVUbVlkdWg3c3VGdzdaajZNTUNSSDdqMGE0N2dFM2JKdHMxV2c5ZmlDSno3UE9WT3RJVWRWS0NKUVN0OUZqbklLTjdFbllTQkoxK1Z0ZDlSSWVtemE4NXpNbWNPSlhmd0c3ckhpMUNmaGV0WU1XT1FBTjkvM0gwVENRRzMwK2E0OWJsUkdNMlRoNW1Namp1K0E5TWNLTUNIWFRPRzBSWHE3WnZBeExmMXBxT2VmM04iLCJtYWMiOiJkMTJlMTJjNzY0ZjAxNjM1NjNhY2U0NzVmMWQyOTU2NTZmNjIzMDk5ZjRmZWExOGJiY2VlZDBiMDlmOTNiNjFjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1Wci9BRUtUQTlIL3IxTlV5SW10YkE9PSIsInZhbHVlIjoiTTVRR3kvQ2UxOER6dm14TVY0Q0F1VkV2RS9ZSW81bHdRK0tmM29hMWxHTlZkZGtwbVVjOVNsNlZ6S1llUTdJWFpjNjFSTXY1eHFyZGpZNzRmUVlZVHZNbjNRY0FzTUhyVEFITisvdDlLTHRNMkxMTUFkNUxEMkNSaEZMRnJ4ZjN6b282TjZ0L0duZ0Z5K0hWNE9FdDgvL2E0dU5jSDA2MVVpUjIva1RkRldUNVd0VEVWM3BzN3ZJNkJNZlFxRXdRNDV0TnAyeWt2OVF4M2JZTUlDMlM3ZTJtRDdqRnpNRFJCcHVCeHNHblY0WXRTQjMvbURLWWxZWTNYOGQvY2xtVUE3S2JMdHM4TGJTWSsyY0xQMHAzVXQwaG5HbXdwbkVLbzBMOEdaeXZjcHordmVJcWl4Y0cybENveGlFY3pYbnkvRXY5ZWVJdFpXN2tCeVJQTTNBa1FPcjdKRFdwYXZDcUNYYXVtRzhQVXFXRTljbWtjTWtBYXY2OVpKVCs1UEtsUlowdVdMNVhiREpNT1lCTWkwTUJrZk14Z2c5V2Y4WStwZFo5VUEyVXBPMTZ1ZUc1VnplczNVTW9VZFp0UktWWHZ5aTJZb0JXcm5jRXEveUxXTGF1ZXFNcU11ektkWU9mU3phM2Q5bXJ3RE1kK3Arb0ZNYkxkcHN5WTBvcEppdDUiLCJtYWMiOiI5NTIzMjE4NjYyYjhlOThkODVmNjJmY2ZhOTMxYWQzNDg4YWJhNThlMjc4MjI5YzE4Yjg3ZGVmZWIxYTA2OWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:02:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhZa2cvRitUYmV6MzR3R1RoMlEreWc9PSIsInZhbHVlIjoiQ0NuZUppWGR5dVMrb0ZrUkh6aHlFeDRwRktjSm40NThkNWpmYXJEU2ZDTlZqbjVFZDd1QXoyYmJPdVJ1VXBkVUZjQzVPUUllKzB2bE95WFF4SnJWZmowSkNxaVRjVU8rcm9MeTYvUzdQMi9iK3RVQk94aCs5cW82bHFnSFNWcnkwUVZNZU8yOWVBSUt4QU5YNjcxQlUyNWpxNXprbjZ5RldXQ3E3QlowQnJMYzJIRFgxaHdlVWd6SWVaeWRPMW81cDdSYkpyRzFPdXlRNVB4K0h0TytCY1B3a3dCbjV4WWtxL3BUK1NZWlYrYWhwdWlaTnh5ekxQMUpBaHVxSTYrR3BSaWlmTEdGNE9IY0pPSW5QdmpEU1NXa01MYzl1ZXF4VGZpYzhZMmdSa3Y5VU44NWMxMUxkSm5BNklEMlB0Sk8vMnFXTE1JUml6R1N6TjhjaTVUbVlkdWg3c3VGdzdaajZNTUNSSDdqMGE0N2dFM2JKdHMxV2c5ZmlDSno3UE9WT3RJVWRWS0NKUVN0OUZqbklLTjdFbllTQkoxK1Z0ZDlSSWVtemE4NXpNbWNPSlhmd0c3ckhpMUNmaGV0WU1XT1FBTjkvM0gwVENRRzMwK2E0OWJsUkdNMlRoNW1Namp1K0E5TWNLTUNIWFRPRzBSWHE3WnZBeExmMXBxT2VmM04iLCJtYWMiOiJkMTJlMTJjNzY0ZjAxNjM1NjNhY2U0NzVmMWQyOTU2NTZmNjIzMDk5ZjRmZWExOGJiY2VlZDBiMDlmOTNiNjFjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1Wci9BRUtUQTlIL3IxTlV5SW10YkE9PSIsInZhbHVlIjoiTTVRR3kvQ2UxOER6dm14TVY0Q0F1VkV2RS9ZSW81bHdRK0tmM29hMWxHTlZkZGtwbVVjOVNsNlZ6S1llUTdJWFpjNjFSTXY1eHFyZGpZNzRmUVlZVHZNbjNRY0FzTUhyVEFITisvdDlLTHRNMkxMTUFkNUxEMkNSaEZMRnJ4ZjN6b282TjZ0L0duZ0Z5K0hWNE9FdDgvL2E0dU5jSDA2MVVpUjIva1RkRldUNVd0VEVWM3BzN3ZJNkJNZlFxRXdRNDV0TnAyeWt2OVF4M2JZTUlDMlM3ZTJtRDdqRnpNRFJCcHVCeHNHblY0WXRTQjMvbURLWWxZWTNYOGQvY2xtVUE3S2JMdHM4TGJTWSsyY0xQMHAzVXQwaG5HbXdwbkVLbzBMOEdaeXZjcHordmVJcWl4Y0cybENveGlFY3pYbnkvRXY5ZWVJdFpXN2tCeVJQTTNBa1FPcjdKRFdwYXZDcUNYYXVtRzhQVXFXRTljbWtjTWtBYXY2OVpKVCs1UEtsUlowdVdMNVhiREpNT1lCTWkwTUJrZk14Z2c5V2Y4WStwZFo5VUEyVXBPMTZ1ZUc1VnplczNVTW9VZFp0UktWWHZ5aTJZb0JXcm5jRXEveUxXTGF1ZXFNcU11ektkWU9mU3phM2Q5bXJ3RE1kK3Arb0ZNYkxkcHN5WTBvcEppdDUiLCJtYWMiOiI5NTIzMjE4NjYyYjhlOThkODVmNjJmY2ZhOTMxYWQzNDg4YWJhNThlMjc4MjI5YzE4Yjg3ZGVmZWIxYTA2OWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:02:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715417906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1128222068 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128222068\", {\"maxDepth\":0})</script>\n"}}