{"__meta": {"id": "X38ad5dbea5592bf58d478fb4cb34259c", "datetime": "2025-07-30 05:18:51", "utime": **********.196961, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753852729.449695, "end": **********.19699, "duration": 1.7472949028015137, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1753852729.449695, "relative_start": 0, "end": **********.076327, "relative_end": **********.076327, "duration": 1.6266319751739502, "duration_str": "1.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076355, "relative_start": 1.***************, "end": **********.196994, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VjAhKo8S28a3O3AbEZVr2u5q82N8C5sggEvcx1hQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1410612094 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1410612094\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1567291031 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1567291031\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-5776529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-5776529\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-278762783 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278762783\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1671020583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671020583\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1026528577 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:18:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im8yN25ob3IxTkZ2dk1OMVJTVG5sS1E9PSIsInZhbHVlIjoiZkExWDJQVTM5cHVldjlqVWRkQlJzeEw1VTd5cjNsNDY5Vy9HbWtXR2Z5aGM4TjJFek5NL3FPTzZNbmI5ZzBWbTdEQnZpcS9UbkNjUk9MZnU0YkFPY3NFSW5OWW9icmU3RzhxMEs3RXd6SFcxejcyelBrc3duMmIxSGN3VUdqenJVdXJtZldkYncrVlFITXliOVBvUmtPclVxZ0R3ZnRxa2lTMzBYL3dFN0J5WXJCVUtRNXJMM3BPNGlqME4zczlnUjRxMzExOUJISWZTMXJuOGtVeVhEckpuTkRlM0Vsa2ZqUHJQQXp0MXNad0V1UUVzZnZTWitnVW84VklNWVJIWG9yY3ZrbndDajNpeFFqOW5IWVNUbW1rSDNKTmxCcnVVTHRlR2Q4Y3hWZGtLY211MnhqUjVQUGFmalJ1c3pmc3NiRTdaZVJLS2pwQSsyU2RwcVo3WXRkTlZGZ1RQM0E0RVhrcGRha2g4T2doZmJZODRhdFh4K2ttR0tpZHNPMGRLMm9tSlNTUGhhSTRjU0ZDeDNiZVdrQmIrTDZmdjB4U1haZVFNVU53SVZ3WWpJdDQ0T1pReURialVoNHYxa05WRkphbVNyZUZ2OEhmcGZYalZJWk5WSkk0WmVTcHQxbisxdGgyOTQwbXQ1NGpiTDdKcXdyejlnTUsrZnZRQmRXdFMiLCJtYWMiOiI4ZmYzYTIxNzM0YmI5YTMwNWRmNjA3M2FjMTQyOTg4MDZhZWMwMjc4NmI3MGI4YWI5ZTJjYmEyMWVjYzUyYTUzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:18:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkM0RmdzMTMwdm1oOEkza29jR2RybVE9PSIsInZhbHVlIjoiVFdGTlpyY3h2dDJoSE1hTzRQYWdScTY5RkNScWlVZWxEbFBYVmxqL0FNTkNyNWZSdVkzU0pkQWh3VXoxbmJkOEVKenl6cEswMFFUQTBGRTNpVHBwZTFyQ2xyK2Z0UE83RmtkS3RVMnlPdGs3S0Y3N3F4aVZKYVhUVnc3eS94WTBabkpkNDduSTVlUnlXM042bXR4Nm5WdTlvUTM4eFZlNi8zL0xteWlWb2hKN0tKTzR3czkvZUVDNzBaRHh5elRmTVlFNEtRRHpHRmJaOWdYNUlzN1FxYXFKTytTZlNJZXdBWnFHU1YvQ3lia2lhRDYzbzZEVHVMbktBaVBpUjI4RjdaS0FxYlNvcGtqWVkwNE02dktzeE9NYnJsUGFKRUdmUWlyREY5aDRROXVmK2pUd0hzaUFqUC9GRjBFbTYvaDlNZHg5T2RNQnlxSlhaWTFKOU9zYmI5enJwUGtab0t2Y3ZVaG12b2EyWkJTb1FaMHpwTmc2VU1TZTc4d2ZVdUFlK1pxOVlHVUNLczh4WXdST0ZPd3YzWlZEWG43REJoTmxjdVpIb1BDZG1DdEpQb0xqb3FTWXA2NWw2aVB3WHBpTkZpZUUva01RV3NvbzRFS3NPaEFRNURrTDZmNUdUNkhoZk9hRGxIOHlYaDNEUjhMZE5vTGQ4WHhwUCtnSWx3cEciLCJtYWMiOiJjZWVkMjBhZDU1NTY2ZTdhNTA2OGU5MTVkY2IyOTU5NDI5YTFiODNkZTgzMTczNmJlZTZkNDhhNDg5NWI2MWRjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:18:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im8yN25ob3IxTkZ2dk1OMVJTVG5sS1E9PSIsInZhbHVlIjoiZkExWDJQVTM5cHVldjlqVWRkQlJzeEw1VTd5cjNsNDY5Vy9HbWtXR2Z5aGM4TjJFek5NL3FPTzZNbmI5ZzBWbTdEQnZpcS9UbkNjUk9MZnU0YkFPY3NFSW5OWW9icmU3RzhxMEs3RXd6SFcxejcyelBrc3duMmIxSGN3VUdqenJVdXJtZldkYncrVlFITXliOVBvUmtPclVxZ0R3ZnRxa2lTMzBYL3dFN0J5WXJCVUtRNXJMM3BPNGlqME4zczlnUjRxMzExOUJISWZTMXJuOGtVeVhEckpuTkRlM0Vsa2ZqUHJQQXp0MXNad0V1UUVzZnZTWitnVW84VklNWVJIWG9yY3ZrbndDajNpeFFqOW5IWVNUbW1rSDNKTmxCcnVVTHRlR2Q4Y3hWZGtLY211MnhqUjVQUGFmalJ1c3pmc3NiRTdaZVJLS2pwQSsyU2RwcVo3WXRkTlZGZ1RQM0E0RVhrcGRha2g4T2doZmJZODRhdFh4K2ttR0tpZHNPMGRLMm9tSlNTUGhhSTRjU0ZDeDNiZVdrQmIrTDZmdjB4U1haZVFNVU53SVZ3WWpJdDQ0T1pReURialVoNHYxa05WRkphbVNyZUZ2OEhmcGZYalZJWk5WSkk0WmVTcHQxbisxdGgyOTQwbXQ1NGpiTDdKcXdyejlnTUsrZnZRQmRXdFMiLCJtYWMiOiI4ZmYzYTIxNzM0YmI5YTMwNWRmNjA3M2FjMTQyOTg4MDZhZWMwMjc4NmI3MGI4YWI5ZTJjYmEyMWVjYzUyYTUzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:18:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkM0RmdzMTMwdm1oOEkza29jR2RybVE9PSIsInZhbHVlIjoiVFdGTlpyY3h2dDJoSE1hTzRQYWdScTY5RkNScWlVZWxEbFBYVmxqL0FNTkNyNWZSdVkzU0pkQWh3VXoxbmJkOEVKenl6cEswMFFUQTBGRTNpVHBwZTFyQ2xyK2Z0UE83RmtkS3RVMnlPdGs3S0Y3N3F4aVZKYVhUVnc3eS94WTBabkpkNDduSTVlUnlXM042bXR4Nm5WdTlvUTM4eFZlNi8zL0xteWlWb2hKN0tKTzR3czkvZUVDNzBaRHh5elRmTVlFNEtRRHpHRmJaOWdYNUlzN1FxYXFKTytTZlNJZXdBWnFHU1YvQ3lia2lhRDYzbzZEVHVMbktBaVBpUjI4RjdaS0FxYlNvcGtqWVkwNE02dktzeE9NYnJsUGFKRUdmUWlyREY5aDRROXVmK2pUd0hzaUFqUC9GRjBFbTYvaDlNZHg5T2RNQnlxSlhaWTFKOU9zYmI5enJwUGtab0t2Y3ZVaG12b2EyWkJTb1FaMHpwTmc2VU1TZTc4d2ZVdUFlK1pxOVlHVUNLczh4WXdST0ZPd3YzWlZEWG43REJoTmxjdVpIb1BDZG1DdEpQb0xqb3FTWXA2NWw2aVB3WHBpTkZpZUUva01RV3NvbzRFS3NPaEFRNURrTDZmNUdUNkhoZk9hRGxIOHlYaDNEUjhMZE5vTGQ4WHhwUCtnSWx3cEciLCJtYWMiOiJjZWVkMjBhZDU1NTY2ZTdhNTA2OGU5MTVkY2IyOTU5NDI5YTFiODNkZTgzMTczNmJlZTZkNDhhNDg5NWI2MWRjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:18:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026528577\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1694135682 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VjAhKo8S28a3O3AbEZVr2u5q82N8C5sggEvcx1hQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694135682\", {\"maxDepth\":0})</script>\n"}}