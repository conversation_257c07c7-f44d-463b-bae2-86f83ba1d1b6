{"__meta": {"id": "X812a4e30580ca4ee91240041e89c5306", "datetime": "2025-07-30 08:11:42", "utime": **********.125533, "method": "GET", "uri": "/chart-of-account/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.662675, "end": **********.125567, "duration": 1.****************, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": **********.662675, "relative_start": 0, "end": **********.867269, "relative_end": **********.867269, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867304, "relative_start": 1.****************, "end": **********.125571, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x chartOfAccount.create", "param_count": null, "params": [], "start": **********.084776, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.phpchartOfAccount.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2FchartOfAccount%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "chartOfAccount.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.112523, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET chart-of-account/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "chart-of-account.create", "controller": "App\\Http\\Controllers\\ChartOfAccountController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=54\" onclick=\"\">app/Http/Controllers/ChartOfAccountController.php:54-76</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.031149999999999997, "accumulated_duration_str": "31.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.965421, "duration": 0.02566, "duration_str": "25.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.376}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.027379, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.376, "width_percent": 6.067}, {"sql": "select * from `chart_of_account_types` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChartOfAccountController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ChartOfAccountController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.041409, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ChartOfAccountController.php:57", "source": "app/Http/Controllers/ChartOfAccountController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FChartOfAccountController.php&line=57", "ajax": false, "filename": "ChartOfAccountController.php", "line": "57"}, "connection": "radhe_same", "start_percent": 88.443, "width_percent": 4.462}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4779}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0878189, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4779", "source": "app/Models/Utility.php:4779", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4779", "ajax": false, "filename": "Utility.php", "line": "4779"}, "connection": "radhe_same", "start_percent": 92.905, "width_percent": 4.109}, {"sql": "select * from `plans` where `plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4784}, {"index": 21, "namespace": "view", "name": "chartOfAccount.create", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/chartOfAccount/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.096257, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4784", "source": "app/Models/Utility.php:4784", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4784", "ajax": false, "filename": "Utility.php", "line": "4784"}, "connection": "radhe_same", "start_percent": 97.014, "width_percent": 2.986}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/chart-of-account\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chart-of-account/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InVGa1F3MysvNHFWZEMzVXRoZzY2ZVE9PSIsInZhbHVlIjoiZ0dSbUZidUNFMWY0bmdiMlRaeStPcGpPNjVwM083MFJ5cUo4SDVCbGJmdmE3dXQ1Qko3V1VaMUhXbzZQZEh4RE5udnY3TDhQaktJQ0ZGdjY3a3VaMnF1ZGRWVmRWZFFqcUNhc1RPbnVHajFyM0VSQ1JYUHlqMkNTS2xGWVh2alBuM1AwZlpOUFo2SHcvSWRWdXR0REV6dVEwUEprQVdmZ3p4U0lzN0g0N1BVWTBZUzJ3Y1VOUlhCYS9XNEUxZWZPUW53YXc0T3MxOEFvcWloMFJranRjYWd3a3EvbEpvS0xYbVczaGJ0dFVIbUhTTE42WGg0aHhEeVp6R1M3ZXlHaXJiVVZseC92MXBhc2ZTR3plUU0vQ2pBQi9FblhKQkhHKzQwRVZPSFh3QjJDeGlyU3J1WU9YNmllVGpQWDZzVk9CQXBTMHFVMGdjV2NTVjF6VFlYTmNBWndCQTNEak1ucDh4cW51RjVYZzFDaXROMEtacmtxMnhZUC9FWEVtZkRINVhYUitlRmx1UmVQMzVLUEJBYVhPWWU3WnlBeWNqZTFnbjF3cGNRb1ZYdmV2RVFlMkQ3bWF2UXZyQjJjVFJlRGZVNVNhRjVmaCt2dEZuUjFjb0RuT0gzRHNWd1RtT1M0SmQwT2pBZXlSNWREdzYxdUhsb1ViL0hPWXUxbFBYNUsiLCJtYWMiOiJhYTQ5ZGIxNmUwNWZmZDBiNTU2YzY4OTA3ZTM4ZDhiYmRhODQ1MzA2MTJmYmJmMTQ0NTZlZDBkMjQ2YjlhMTVhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InRUa2pJZ3ZuSGFQSE8vNGp5NXlxd0E9PSIsInZhbHVlIjoiR1dOZkJybzhFdWtxTjY3SXhONERwenBSMUowb21tM1kxeEtNOVB3VmRzd2Y2Y0NDSzhnY3VGUFU1U3dPS1ZuRGEweEMzOXZPVU5wWUxURTQ4RDZ3bS9ONDdWT2tnRnV1SW9aWWRyb2ZpRzFZR0drUXhOcGpLdklSZHpBTDlGUU15ZEJlb0NaSkdnaEw4ZmFmN0h0d0ZpOEM0aXdRd3M0S3kwemVnU1BVaEdiQWxVUmFFbWp3ajlTNnhYUTJhOEhhdWVXVW9QblJVTGp4QTF3SEhqRzJURmI5b1l0UVpjM0FqYjB3aXl6cjEvUnRGanZqNEc5NjhMc3hLTkxiWmtFRCtQOFRSSXgzaXgwWTIwNVR0LzZpNG82ekE1M0NldmMrdUQxRnRGc09NUFdsOC9mc0h4Z1Y1bFVqbW51NFhmb1prRVpyUmVvZE96ektvQ2tLSUpnZHdvSG10bzRwWGgyZjBoNHQyWmc2NzlyWXBONTZJWjNLVG4yZjFNRC9TVFNwZTBpby9nYXgrZU5FWVJMbVQzcjRlcGdZN1BWd25yWE4vY0dkVTVLWGhYYmZmR2twcXBMWTYrd2R5SHIxYTVJVWMzaEtjK3RIZFk5UFFMRVkzN0t5cXZZaVl3QlpGd21leTNMd2VIWUVvK1lHd0RtSlhNa0V2cjhDK0hUN3BqMU8iLCJtYWMiOiJjZTk5OGIxMmE3Yzc1NDA5MGU4MTU5ZjA5YWZhYjE3ZGExZGViNzQyYjQzMzAyMzBiZTY0OTQ5YjZkMTJjZTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1348014436 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348014436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1939467755 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:11:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9zRyt1MHd6dWtlcGw5cVNwN3ZsSFE9PSIsInZhbHVlIjoiaFFZK1dQOUZSNW55OGlUQVZTYkRBTWIvUUdMZHR1aG5RZEE4TzI3eWRjcEdZeXRrYzBzS3NxQU1WSjdPdldWQkxaVFRIVUJaN1hKQVBPSm1WZzdiSHFmQmRkRytPL2FuYVJ5NHgzU1JGNmkwL3ZzK0t2alZkRmxMZlFVUG1ZL2dCalpobmdLMGM3Vm1SaTI2TG14T1k1NXBaTU5va3Q2SHA1M1VWcWtSQ2NqTDdyczM0Nnc3am1LVXdUVmlUMDVNWkpqQTlpYkdIMEtTWWNMYVJHN1lET1ptai9mZjhvekVPczJlRDVUdUd6aG0rUjZTWUo1UDVEbDZBMk1WcTNUL3hXUkg4bHdvVXUzOWxORHhWUTI3b09neUNrdERWWnM3L1FiQXUyUE1zb3dweDJTczBDVFNFajJNcEtPc3ZySm04dVQvYjlnc3hJd1VDUU0rZkxza0s5S0lubS91a2FPVEdxVGJoblkzVzhsQXdQSENqc08wSkU1TXpsZjNFdEtQZ1dLQUhmTjQyMFpaY1dpN1A3bGwrcFJyUWhBYWltV2dZaTd1NVVYcEZkWWlvRlFQQTRqWmhwUUdSMS82WnJ2bi9NN09uc3U0TnMxM0IvQXoyQ0g3MERKS1BQbm00Z1dxNFJXTFdzcVp2WlhpQ3AyaCtuNDFaRDQ5RVZNMVV5ZFIiLCJtYWMiOiJiZDcyMjJmOGYxNjE2ZmVjMmM5ZTdmNDQ3MTMyZjA3ODU5NDYyMTJlZGUwMDBkOTMwMmY4ZWZlMjA0YjJiM2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRhRUZaRUIrODh5ek9wTHZXOVc5ZkE9PSIsInZhbHVlIjoiYTRtMGtUZ09DNGpFWE5wUkZBcmxEOC9pNVVjVSswdVg0QXVaanlSYTFvZ3pXcitXNXNISEFIaTdxYUZpYkZ5VFQ0S0ZUR0l0b0hKbmJlR2hxaGY0ODdPRzl0RjljRGR3Wm5IdjJkT3l4Z3Z1UWJ2alg5NFVueDZMMk9qakpNWE9hTFFaR095RHcvVHZvTjZaYmJmNHhhd2h4MzI4VmJNVlVXRnpPNnhXTGUwa1kzUk92aEFVSzJMVkdzRzdYc3BsR3hRVmVRMThLN1lSOTdGcjJQTnpnSHdKbERlZVpnalhIZUdxRnpYcjJQa0JHOW5tNmViYlFSaFJHeWVXM0xJempzZUhaVUJRNVVwMFJsM2pObGs5Wm1yNEtLUGhjN1VEVTNRbDBrL1ptREw5ZTlzNmxVbFVsdElIckp3dTVoUTZiUVFSalE3aTFzTUZrdGRIandHd2lYR0VvS0tGcGllaUs5NTkvOHdVTTZPQUNRbEJvOXFwQUFJMmNucCs5WkdkWW5BTXNVQUtZQmlzc3NWZ3RVRnVnVTluckFjSnp1VHY0WGo0dzEyTjhPVmViNXFGanRKNTZ0ZFRJTkg2OU5HWFVxcGRTdFJ2ekk5YlJ2TXYxZTJtdEVRTGtrSkZmeks5bElLbzFMV1poTUtjcnZvUVhJVWZ3MmxjdjM0NHVOUHoiLCJtYWMiOiJiNTQ4NjkwYjIxOTM0OTRjYTgzZDJmM2EyM2I2ZmY4YjhlZjBhN2Q4ZjQ0ZDUwNmM0NzRjNDc0MDYyMDYzMWIxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:11:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9zRyt1MHd6dWtlcGw5cVNwN3ZsSFE9PSIsInZhbHVlIjoiaFFZK1dQOUZSNW55OGlUQVZTYkRBTWIvUUdMZHR1aG5RZEE4TzI3eWRjcEdZeXRrYzBzS3NxQU1WSjdPdldWQkxaVFRIVUJaN1hKQVBPSm1WZzdiSHFmQmRkRytPL2FuYVJ5NHgzU1JGNmkwL3ZzK0t2alZkRmxMZlFVUG1ZL2dCalpobmdLMGM3Vm1SaTI2TG14T1k1NXBaTU5va3Q2SHA1M1VWcWtSQ2NqTDdyczM0Nnc3am1LVXdUVmlUMDVNWkpqQTlpYkdIMEtTWWNMYVJHN1lET1ptai9mZjhvekVPczJlRDVUdUd6aG0rUjZTWUo1UDVEbDZBMk1WcTNUL3hXUkg4bHdvVXUzOWxORHhWUTI3b09neUNrdERWWnM3L1FiQXUyUE1zb3dweDJTczBDVFNFajJNcEtPc3ZySm04dVQvYjlnc3hJd1VDUU0rZkxza0s5S0lubS91a2FPVEdxVGJoblkzVzhsQXdQSENqc08wSkU1TXpsZjNFdEtQZ1dLQUhmTjQyMFpaY1dpN1A3bGwrcFJyUWhBYWltV2dZaTd1NVVYcEZkWWlvRlFQQTRqWmhwUUdSMS82WnJ2bi9NN09uc3U0TnMxM0IvQXoyQ0g3MERKS1BQbm00Z1dxNFJXTFdzcVp2WlhpQ3AyaCtuNDFaRDQ5RVZNMVV5ZFIiLCJtYWMiOiJiZDcyMjJmOGYxNjE2ZmVjMmM5ZTdmNDQ3MTMyZjA3ODU5NDYyMTJlZGUwMDBkOTMwMmY4ZWZlMjA0YjJiM2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRhRUZaRUIrODh5ek9wTHZXOVc5ZkE9PSIsInZhbHVlIjoiYTRtMGtUZ09DNGpFWE5wUkZBcmxEOC9pNVVjVSswdVg0QXVaanlSYTFvZ3pXcitXNXNISEFIaTdxYUZpYkZ5VFQ0S0ZUR0l0b0hKbmJlR2hxaGY0ODdPRzl0RjljRGR3Wm5IdjJkT3l4Z3Z1UWJ2alg5NFVueDZMMk9qakpNWE9hTFFaR095RHcvVHZvTjZaYmJmNHhhd2h4MzI4VmJNVlVXRnpPNnhXTGUwa1kzUk92aEFVSzJMVkdzRzdYc3BsR3hRVmVRMThLN1lSOTdGcjJQTnpnSHdKbERlZVpnalhIZUdxRnpYcjJQa0JHOW5tNmViYlFSaFJHeWVXM0xJempzZUhaVUJRNVVwMFJsM2pObGs5Wm1yNEtLUGhjN1VEVTNRbDBrL1ptREw5ZTlzNmxVbFVsdElIckp3dTVoUTZiUVFSalE3aTFzTUZrdGRIandHd2lYR0VvS0tGcGllaUs5NTkvOHdVTTZPQUNRbEJvOXFwQUFJMmNucCs5WkdkWW5BTXNVQUtZQmlzc3NWZ3RVRnVnVTluckFjSnp1VHY0WGo0dzEyTjhPVmViNXFGanRKNTZ0ZFRJTkg2OU5HWFVxcGRTdFJ2ekk5YlJ2TXYxZTJtdEVRTGtrSkZmeks5bElLbzFMV1poTUtjcnZvUVhJVWZ3MmxjdjM0NHVOUHoiLCJtYWMiOiJiNTQ4NjkwYjIxOTM0OTRjYTgzZDJmM2EyM2I2ZmY4YjhlZjBhN2Q4ZjQ0ZDUwNmM0NzRjNDc0MDYyMDYzMWIxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:11:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939467755\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}