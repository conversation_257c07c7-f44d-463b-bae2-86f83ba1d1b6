<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add coupon permissions to existing company roles
        $couponPermissions = [
            'manage coupon',
            'create coupon',
            'edit coupon',
            'delete coupon',
        ];

        // Find all company roles
        $companyRoles = Role::where('name', 'company')->get();

        foreach ($companyRoles as $role) {
            foreach ($couponPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }

        // Also add to any users who have company type but might not have the role
        $companyUsers = \App\Models\User::where('type', 'company')->get();
        foreach ($companyUsers as $user) {
            foreach ($couponPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$user->hasPermissionTo($permission)) {
                    $user->givePermissionTo($permission);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove coupon permissions from company roles
        $couponPermissions = [
            'manage coupon',
            'create coupon',
            'edit coupon',
            'delete coupon',
        ];

        $companyRoles = Role::where('name', 'company')->get();

        foreach ($companyRoles as $role) {
            foreach ($couponPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && $role->hasPermissionTo($permission)) {
                    $role->revokePermissionTo($permission);
                }
            }
        }

        // Also remove from company users
        $companyUsers = \App\Models\User::where('type', 'company')->get();
        foreach ($companyUsers as $user) {
            foreach ($couponPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && $user->hasPermissionTo($permission)) {
                    $user->revokePermissionTo($permission);
                }
            }
        }
    }
};
