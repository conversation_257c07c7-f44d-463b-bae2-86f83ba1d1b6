# Subscription Management Feature

## Overview
This feature implements a comprehensive subscription management system in the Finance module's Sales tab. It includes a dynamic table with full CRUD operations, EMI tracking, customer management, and various interactive features.

## Features Implemented

### 1. Database Schema
**New Table: `subscriptions`**
- `id` - Primary key
- `subscription_id` - Unique subscription identifier (SUB-YYYY-XXXX format)
- `customer_id` - Foreign key to customers table
- `customer_name`, `customer_email`, `customer_phone` - Customer details
- `product_id` - Foreign key to product_services table
- `product_name`, `product_price` - Product details
- `down_payment`, `paid_amount`, `pending_amount`, `discount_amount` - Financial tracking
- `status` - Subscription status (active, cancelled, paused, expired)
- `next_emi_date` - Next EMI due date
- `start_date`, `end_date` - Subscription period
- `emi_count`, `total_emis`, `emi_amount` - EMI tracking
- `billing_cycle` - Payment frequency (monthly, quarterly, yearly)
- `notes` - Subscription notes
- `receipt_url` - Receipt document link
- `created_by` - User who created the subscription

### 2. Dynamic Subscription Table
**Columns Implemented:**
- **ID** - Unique subscription identifier
- **Status / Created At** - Status badge with creation date
- **Next EMI Date** - Due date with overdue indicators
- **Customer Name / Email / Phone** - Complete customer information
- **Product** - Associated product name
- **Product Price / Downpayment** - Financial breakdown
- **Paid Amount / Pending Amount** - Payment tracking
- **Discount Amount** - Applied discounts
- **Receipt** - Document link or N/A
- **Action** - Dropdown with interactive options

### 3. Action Menu Features
**Interactive Options:**
- **Cancel Subscription** - Changes status to cancelled
- **Subscription Notes** - Modal to view/edit notes
- **WhatsApp Chat** - Opens WhatsApp with customer phone
- **Edit** - Modal to update subscription details
- **Delete** - Permanent removal with confirmation

### 4. Create Subscription Modal
**Form Sections:**
- **Customer Details**
  - Customer Name (dropdown with existing customers)
  - Customer Email (auto-populated from selection)
  - Customer Contact (auto-populated from selection)
  - Add Customer button for new customers

- **Product Details**
  - Product selection dropdown
  - Product quantity input
  - Active date picker
  - Auto-populated product price
  - Invoice description textarea

- **Payment Details**
  - Payment method (Offline/Online radio buttons)
  - Down payment amount
  - Discount amount
  - Total EMIs configuration
  - Billing cycle selection

### 5. Backend Integration
**Controller Methods:**
- `storeSubscription()` - Create new subscription
- `getSubscription()` - Retrieve subscription details
- `cancelSubscription()` - Cancel subscription
- `updateSubscriptionNotes()` - Update notes
- `updateSubscription()` - Update subscription details
- `deleteSubscription()` - Delete subscription

**Routes:**
- `POST /finance/sales/store-subscription`
- `GET /finance/sales/subscription/{id}`
- `POST /finance/sales/subscription/{id}/cancel`
- `POST /finance/sales/subscription/{id}/update-notes`
- `POST /finance/sales/subscription/{id}/update`
- `DELETE /finance/sales/subscription/{id}`

## Usage Instructions

### Creating a New Subscription
1. Navigate to Finance → Sales → Subscription Plans tab
2. Click "Create Plan" button
3. Fill in customer details:
   - Select existing customer or add new one
   - Email and phone auto-populate
4. Configure product details:
   - Select product from dropdown
   - Set quantity and active date
   - Price auto-fills from product
5. Set payment terms:
   - Choose payment method
   - Enter down payment and discount
   - Configure EMI count and billing cycle
6. Click "Create Subscription"

### Managing Existing Subscriptions
**Cancel Subscription:**
- Click Action dropdown → Cancel Subscription
- Confirm cancellation in dialog

**Add/Edit Notes:**
- Click Action dropdown → Subscription Notes
- Edit notes in modal and save

**WhatsApp Integration:**
- Click Action dropdown → WhatsApp Chat
- Opens WhatsApp web/app with customer number

**Edit Subscription:**
- Click Action dropdown → Edit
- Update status, EMI date, amounts, receipt URL
- Save changes

**Delete Subscription:**
- Click Action dropdown → Delete
- Confirm permanent deletion

## Technical Features

### Automatic Calculations
- EMI amount calculation based on remaining balance
- Next EMI date calculation by billing cycle
- Pending amount tracking
- Progress percentage calculation

### Status Management
- Visual status badges with color coding
- Overdue detection and highlighting
- Status transitions (active → paused → cancelled)

### Data Validation
- Required field validation
- Email format validation
- Date range validation
- Numeric amount validation
- Foreign key constraints

### Security Features
- CSRF protection on all forms
- User-based data isolation (created_by filtering)
- Permission-based access control
- Input sanitization and validation

### User Experience
- Real-time form validation
- Auto-population of related fields
- Responsive design for mobile devices
- Loading states and error handling
- Success/error notifications

## Files Created/Modified

### New Files
1. `database/migrations/2025_07_30_000002_create_subscriptions_table.php`
2. `app/Models/Subscription.php`
3. `database/factories/SubscriptionFactory.php`
4. `tests/Feature/SubscriptionManagementTest.php`

### Modified Files
1. `resources/views/finance/tabs/sales.blade.php` - Complete UI implementation
2. `app/Http/Controllers/FinanceController.php` - Backend methods
3. `routes/web.php` - New routes for subscription management

## Testing
Run the feature tests:
```bash
php artisan test tests/Feature/SubscriptionManagementTest.php
```

## Database Setup
Run migrations to create the subscriptions table:
```bash
php artisan migrate
```

## Browser Compatibility
- Modern browsers with ES6 support
- Bootstrap 5 modal and dropdown compatibility
- jQuery-based AJAX interactions
- Responsive design for mobile devices

## Integration Points
- **Customer Management** - Links to existing customers table
- **Product Management** - Links to product_services table
- **User Management** - Tracks creator and enforces data isolation
- **WhatsApp Integration** - Direct chat links with customer phone numbers

## Future Enhancements
- Email notifications for due EMIs
- Payment gateway integration
- Automated EMI processing
- Subscription analytics dashboard
- Bulk operations for subscriptions
- Export functionality for subscription data
