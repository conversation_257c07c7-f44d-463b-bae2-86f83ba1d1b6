{"__meta": {"id": "X8ae229725871055aaf99afe6bf049f02", "datetime": "2025-07-30 08:26:40", "utime": **********.456314, "method": "GET", "uri": "/finance/sales/products/search?search=ti", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863997.122176, "end": **********.456387, "duration": 3.3342111110687256, "duration_str": "3.33s", "measures": [{"label": "Booting", "start": 1753863997.122176, "relative_start": 0, "end": **********.142971, "relative_end": **********.142971, "duration": 3.0207951068878174, "duration_str": "3.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.143001, "relative_start": 3.020825147628784, "end": **********.456395, "relative_end": 7.867813110351562e-06, "duration": 0.31339383125305176, "duration_str": "313ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46688720, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1041\" onclick=\"\">app/Http/Controllers/FinanceController.php:1041-1084</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013380000000000001, "accumulated_duration_str": "13.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.335748, "duration": 0.00973, "duration_str": "9.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 72.72}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3956552, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 72.72, "width_percent": 15.321}, {"sql": "select * from `product_services` where `created_by` = 79 and (`name` like '%ti%' or `sku` like '%ti%' or `description` like '%ti%') order by `name` asc", "type": "query", "params": [], "bindings": ["79", "%ti%", "%ti%", "%ti%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1055}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.417123, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1055", "source": "app/Http/Controllers/FinanceController.php:1055", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1055", "ajax": false, "filename": "FinanceController.php", "line": "1055"}, "connection": "radhe_same", "start_percent": 88.042, "width_percent": 11.958}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-43582497 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-43582497\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1587171289 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ti</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587171289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1425832030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1425832030\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-340260535 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlpFYTRzVVNKSW5GQjBkNkZXQnNiUHc9PSIsInZhbHVlIjoiblFlZmxYQmcvU2FERzV2UEdSN1Jqb2I3R1VOcGxzTm43cHlDVWdLZlo4bDFGYXQycjNsR2hMTUNhbUFEd3piOEdISDhEN0lxNEpYYWR4S2tON0dmSUlvSmZFSUlueEJQZXZ5cDlLdFZtQnBNSWlHZWJIbWptUS9icXpIYWpiWk9yUlFpVDdJVVJhTTNDS1ZqcWdLVkI5NzlqUHU0L2djQXVlaDBVL2FFUmo5Z2J5VUlvUjBFaVNwVW8rOENaYlRLY2pWa0VUekhDSGhQYjRhZlAyMVAwSFV6QURhYW1CM1QzUWpxbndEcFpMd1RVbWxaQ2RnbnB5aW9OK1h4T1IyaC8wTHN0Y245SGg3QlVpNFc3M0VpNjQ4NHZWclZ4TmkzL2xGU1ZUdHMvMHdGWjlaS1M4YmdSdG14NmI1bUVSNi8zSnNxNlFlKzJzV0JoWklzazM1anY5YlB5d0h5LzBLeW9ubUo5b2ZaWjIwcjRzUUpkVEhRNG1ScFhRQXZ4aWNyZW5wZHJyd3VzME96VUdMNHIzb0lxTTZtOWg0cTVFbW90czM3ZG1RWXRpOWY1R012L2Q1ZVFLL0NjbEtMMjJNaWJ2WlNWQUpsTlJEc0dVV0oyRGJsczBHWjFPSDRkeHpHL043R0NpMEFBNUI4ZmNaeWtDaEcrWERrWHIyZnB2aTkiLCJtYWMiOiI1ZWNjZjE1YmFmZWU0NTgxOWZjNDNhOWYxYTBhMTQ2ODczMTUzNTVhN2U3YjI4Y2E1MDdmYjE0MTBhM2ZmYmFkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImNYdG1GdHJYVFo3Y1JINjVwaHFodWc9PSIsInZhbHVlIjoiNGpwR3JiTlNGbENuOGpaNjZaTnVNbWZCM0MwamRNZVhMbEdTVmVFYkFBakFjYXJCUnp1eklTV2plT1I2UTBtMjg2djZYR3YzcXlRcXdKbjFCT1RmNlRCai9KVFJNR1M2Nzk5TmdrVTNvRS9UNEFlQlJDdndrYUY0MWlOamhoRWxxbW1GUHRJWDRIUEV0UjIwLzljd3A2VTZKZ0FKckd0NUwyc3EwSmFKUys3bXljVVRDK014Vk15NzB5eUlUd3VSSWtUa05jVkpEWjdvQjdJcmlyOXNHZFdqWGE4Y2w4NkVLeUJEaFhaY2IzeVplWW9XaDN4N0RGWTJOVDNEYUJyU2VGeUFzQlNMR3VlWTJJT0FCTDQ4aEtENXRYWk1SbmQ0eWNRSWU1Yi9TOXpzeDNhbDY5TFZQRXVVMmpjK1B3aFVHQkZ5ZkhFalJOWjg0RjZ6akp6VFJOTVVuVkhPeURIV3dVWnhJMHdLQVBNbmRYUVR5Rzk0QkRyQ1h6aVNYYmc5dEZsM1g3NXNadTRyaXJqM0ZzZmxkRXR6RHpOVThING1nTW05blMyallBS2R5MU4vdmdaTWVLdCsyeXZaWWZzNTB4dGN3WUEyY242K1RucHg5SUFmOXBCN1Fzd0g3NmZpYmJRUWdNVFdxM0pOaFl5aktQdGp3dGVMenhuTUxmQ1EiLCJtYWMiOiI2ZGU5YjJlOTUxOTcyNzZlZmFmYzY3ZGQzNzQwYTllNzMzODliMzNmMTNjMDcxN2IzOTNkMGQ4NzFmNGVhYWYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340260535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1575570123 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575570123\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-805024690 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:26:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImI5cVhRaTBaa0x5cWRFZ2JDa3dvQmc9PSIsInZhbHVlIjoiOGt4R1d3QXJJRUY2bWEwbkQxWkNZcDBWSjdmME96ZlRvLzNwcXFWc1ZDbFZ6ZlBUeVVlWlVuUEJDa3UrQTBRTHV5R2xkZ3RzY3JXaTloQ1dhT24xUXRteVFXaHd6SWhST2EzZjVFZm9sZUdabTVJaERiVStPOVp3QVdqakYzSnJyT1JBSjNtaHlqdlpnbUZ6ZTZrcnZSZlZmNlI5c21QRVdDWW1maVJFbjFtMXdZaGZEZzBxQlhZMnFIeURETVFicnVQVDhkVnZyZTkrNTllMXA2S3ZuTURIa2p0SFVNNHNBaXR6U2cxZ1lBNWFrSUxvRit6UUZZbkVxYkl4QTBFMk9ZYnRvQS9HbngxRXN1ZjZpN1hvTzRlQ0VtbUJ3cS8wMnNkN3pXV25oOE1BbU9RbktvQUVEU0ZidExpVEN4akVodGhuc0cwWk1VYkxxcHU0TnArUlVvNUNKeHpCTGczTXh6UVozMDJtT1hyWE9paStGMVVjR1FUTkFYZVdEUDIzZjFoWG5oQS80Tzh0MkhBWXB1NzhBY2NHcW9iUWp4QzhZZHBFbktxeGhjTzErOWRmRXJsazFzeG4ySVQxYkJwMWpnaVFjZmRqYkRtMDVkWjdsR2RJTnozVlZBaU51U28vb1huUDBKZTlYYlVjK3UxZ3Q0c21VcS9PTXlKVlVoYmsiLCJtYWMiOiI4ODdhODU4Nzc4OGI4NDA2NjBmMTk4NzE0MGQ1MTQ5YzQwN2I0ZGQzOTAwNjdjMjQwMzY1MDQ2NmFhMWUyM2JlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:26:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlNxR3E3YXg0SG0xT1h0dnpnQkJCa0E9PSIsInZhbHVlIjoiV1FhZ0gyVXhxdFFrMkQ3cU9kdUkwYkIvUTRvUzRkd01KRzNQTHNtaTlIa2JkSzgzTTRyY0hQNjhSTW9JRXo2V3BURStrWGd0YWRxRk0rbGxBSTF1WEVsSTVZTncxK0dJQXg0UnozckZiR1V5ZU1ZKzdpRUhrMkkySHJIejhGbitrQWVBRUdudmhEMEJ0RFRocDN4VGREaTJaN3pzZUhJSVV6VnhQckgvWEpmeklzMjZ2ZzQ3NGZSZ0F2ajE0L2lrVXcyMjM1VTRkUXlUU2x1STdrTjFQMFhDemNZeW42UVRyTzIvUmROT3hTYnZ2YUZ2dnR3enQvbnNVQzdzendhTWJKSEdBbGRoQ3kvUVA3cUFmbnVKSVdNSGp0RDVXaXhoUzYrbWR6ZUhMK3BKTkQ5dEppbWtmQm1QcEpaU3dTK0NaVjdQN2Y0L3FoWTBMSVlZTWxVc2c1QW9JaWRYdHoyenpSbWd4OXRnaUhaT201TzF3YW9UZGZ1clhPMzg0Z1UwNmlYaXJuWkdkbms1bzIvUTM5ZXI4b2FuR0ZvendPSXhNQmtqaGtwMm5hbEtpZWw0QVlJWlZqNVJYT2xnaGVPQzVPazRmUmVNZVFvMktKNU9MbG5BeWY5bE1NRllxMnlQRHhyOTVLeEZneDFWL2tGSHZwSnFnRnhXMW5FN0JVS1QiLCJtYWMiOiIzMDViNTNhYjQzNWRjNDY1ODQ3MzUzYjNhZmVjODk1ZDhhMGQzYzM0M2NmZTA0YzJiODgzOWRmZDVmZTc2N2NkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:26:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImI5cVhRaTBaa0x5cWRFZ2JDa3dvQmc9PSIsInZhbHVlIjoiOGt4R1d3QXJJRUY2bWEwbkQxWkNZcDBWSjdmME96ZlRvLzNwcXFWc1ZDbFZ6ZlBUeVVlWlVuUEJDa3UrQTBRTHV5R2xkZ3RzY3JXaTloQ1dhT24xUXRteVFXaHd6SWhST2EzZjVFZm9sZUdabTVJaERiVStPOVp3QVdqakYzSnJyT1JBSjNtaHlqdlpnbUZ6ZTZrcnZSZlZmNlI5c21QRVdDWW1maVJFbjFtMXdZaGZEZzBxQlhZMnFIeURETVFicnVQVDhkVnZyZTkrNTllMXA2S3ZuTURIa2p0SFVNNHNBaXR6U2cxZ1lBNWFrSUxvRit6UUZZbkVxYkl4QTBFMk9ZYnRvQS9HbngxRXN1ZjZpN1hvTzRlQ0VtbUJ3cS8wMnNkN3pXV25oOE1BbU9RbktvQUVEU0ZidExpVEN4akVodGhuc0cwWk1VYkxxcHU0TnArUlVvNUNKeHpCTGczTXh6UVozMDJtT1hyWE9paStGMVVjR1FUTkFYZVdEUDIzZjFoWG5oQS80Tzh0MkhBWXB1NzhBY2NHcW9iUWp4QzhZZHBFbktxeGhjTzErOWRmRXJsazFzeG4ySVQxYkJwMWpnaVFjZmRqYkRtMDVkWjdsR2RJTnozVlZBaU51U28vb1huUDBKZTlYYlVjK3UxZ3Q0c21VcS9PTXlKVlVoYmsiLCJtYWMiOiI4ODdhODU4Nzc4OGI4NDA2NjBmMTk4NzE0MGQ1MTQ5YzQwN2I0ZGQzOTAwNjdjMjQwMzY1MDQ2NmFhMWUyM2JlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:26:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlNxR3E3YXg0SG0xT1h0dnpnQkJCa0E9PSIsInZhbHVlIjoiV1FhZ0gyVXhxdFFrMkQ3cU9kdUkwYkIvUTRvUzRkd01KRzNQTHNtaTlIa2JkSzgzTTRyY0hQNjhSTW9JRXo2V3BURStrWGd0YWRxRk0rbGxBSTF1WEVsSTVZTncxK0dJQXg0UnozckZiR1V5ZU1ZKzdpRUhrMkkySHJIejhGbitrQWVBRUdudmhEMEJ0RFRocDN4VGREaTJaN3pzZUhJSVV6VnhQckgvWEpmeklzMjZ2ZzQ3NGZSZ0F2ajE0L2lrVXcyMjM1VTRkUXlUU2x1STdrTjFQMFhDemNZeW42UVRyTzIvUmROT3hTYnZ2YUZ2dnR3enQvbnNVQzdzendhTWJKSEdBbGRoQ3kvUVA3cUFmbnVKSVdNSGp0RDVXaXhoUzYrbWR6ZUhMK3BKTkQ5dEppbWtmQm1QcEpaU3dTK0NaVjdQN2Y0L3FoWTBMSVlZTWxVc2c1QW9JaWRYdHoyenpSbWd4OXRnaUhaT201TzF3YW9UZGZ1clhPMzg0Z1UwNmlYaXJuWkdkbms1bzIvUTM5ZXI4b2FuR0ZvendPSXhNQmtqaGtwMm5hbEtpZWw0QVlJWlZqNVJYT2xnaGVPQzVPazRmUmVNZVFvMktKNU9MbG5BeWY5bE1NRllxMnlQRHhyOTVLeEZneDFWL2tGSHZwSnFnRnhXMW5FN0JVS1QiLCJtYWMiOiIzMDViNTNhYjQzNWRjNDY1ODQ3MzUzYjNhZmVjODk1ZDhhMGQzYzM0M2NmZTA0YzJiODgzOWRmZDVmZTc2N2NkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:26:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805024690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-406716057 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406716057\", {\"maxDepth\":0})</script>\n"}}