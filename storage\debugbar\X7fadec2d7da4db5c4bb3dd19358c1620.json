{"__meta": {"id": "X7fadec2d7da4db5c4bb3dd19358c1620", "datetime": "2025-07-30 07:59:31", "utime": **********.342298, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 8, "messages": [{"message": "[07:59:27] LOG.info: Lead creation started {\n    \"user_id\": 79,\n    \"request_data\": {\n        \"_token\": \"LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP\",\n        \"name\": \"Gungun Rani\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"8654201236\",\n        \"date_of_birth\": \"2025-07-31\",\n        \"contact_type\": \"Lead\",\n        \"tags\": \"HElo\",\n        \"postal_code\": \"734429\",\n        \"city\": \"Siliguri\",\n        \"state\": \"Dadra and Nagar Haveli\",\n        \"country\": \"India\",\n        \"business_name\": \"NA\",\n        \"business_gst\": \"NA\",\n        \"business_state\": \"West Bengal\",\n        \"business_postal_code\": \"734429\",\n        \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\",\n        \"dnd_emails\": \"on\",\n        \"dnd_whatsapp\": \"on\",\n        \"dnd_sms\": \"on\",\n        \"dnd_calls\": \"on\",\n        \"user_id\": \"79\",\n        \"subject\": \"New Contact\",\n        \"pipeline_id\": null,\n        \"stage_id\": null,\n        \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":true,\\\"whatsapp\\\":true,\\\"sms\\\":true,\\\"calls\\\":true}\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.65234, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:28] LOG.info: Creating lead with data {\n    \"pipeline_id\": 23,\n    \"stage_id\": 86,\n    \"user_id\": \"79\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.881023, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:28] LOG.info: Lead saved successfully {\n    \"lead_id\": 13\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.898647, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:28] LOG.info: UserLead relationships created successfully", "message_html": null, "is_string": false, "label": "info", "time": **********.910698, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:29] LOG.info: Starting webhook dispatch for action: crm.lead_created {\n    \"timestamp\": \"2025-07-30T07:59:29.043635Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 13,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.049202, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:31] LOG.error: <PERSON>ho<PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-07-30T07:59:31.234096Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2163,\n    \"user_id\": 79,\n    \"entity_id\": 13,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_created\",\n        \"timestamp\": \"2025-07-30T07:59:29.070872Z\",\n        \"data\": {\n            \"name\": \"Gungun Rani\",\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"8654201236\",\n            \"subject\": \"New Contact\",\n            \"user_id\": \"79\",\n            \"pipeline_id\": 23,\n            \"stage_id\": 86,\n            \"created_by\": 79,\n            \"date\": \"2025-07-30\",\n            \"date_of_birth\": \"2025-07-31\",\n            \"type\": null,\n            \"status\": \"active\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": null,\n            \"opportunity_source\": null,\n            \"lead_value\": null,\n            \"next_follow_up_date\": null,\n            \"contact_type\": \"Lead\",\n            \"tags\": \"HElo\",\n            \"postal_code\": \"734429\",\n            \"city\": \"Siliguri\",\n            \"state\": \"Dadra and Nagar Haveli\",\n            \"country\": \"India\",\n            \"business_name\": \"NA\",\n            \"business_gst\": \"NA\",\n            \"business_state\": \"West Bengal\",\n            \"business_postal_code\": \"734429\",\n            \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\",\n            \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":true,\\\"whatsapp\\\":true,\\\"sms\\\":true,\\\"calls\\\":true}\",\n            \"updated_at\": \"2025-07-30T07:59:28.000000Z\",\n            \"created_at\": \"2025-07-30T07:59:28.000000Z\",\n            \"id\": 13,\n            \"stage\": {\n                \"id\": 86,\n                \"name\": \"New\",\n                \"pipeline_id\": 23,\n                \"created_by\": 79,\n                \"order\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:03.000000Z\"\n            },\n            \"pipeline\": {\n                \"id\": 23,\n                \"name\": \"OMX Digital Bot\",\n                \"created_by\": 79,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:42.000000Z\"\n            },\n            \"labels\": [],\n            \"sources\": [],\n            \"products\": [],\n            \"triggered_by\": {\n                \"user_id\": 79,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"Parichay Singha AI\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 79,\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.235387, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:31] LOG.warning: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-07-30T07:59:31.236181Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.237081, "xdebug_link": null, "collector": "log"}, {"message": "[07:59:31] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.318768, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753862364.381198, "end": **********.342483, "duration": 6.96128511428833, "duration_str": "6.96s", "measures": [{"label": "Booting", "start": 1753862364.381198, "relative_start": 0, "end": **********.414024, "relative_end": **********.414024, "duration": 3.0328261852264404, "duration_str": "3.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414053, "relative_start": 3.0328550338745117, "end": **********.342492, "relative_end": 9.059906005859375e-06, "duration": 3.928439140319824, "duration_str": "3.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57136920, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=252\" onclick=\"\">app/Http/Controllers/LeadController.php:252-608</a>"}, "queries": {"nb_statements": 22, "nb_failed_statements": 0, "accumulated_duration": 0.08557999999999998, "accumulated_duration_str": "85.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.575019, "duration": 0.009529999999999999, "duration_str": "9.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 11.136}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6361418, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 11.136, "width_percent": 2.349}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.678679, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 13.484, "width_percent": 2.582}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7002811, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 16.067, "width_percent": 2.185}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.723299, "duration": 0.01333, "duration_str": "13.33ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 18.252, "width_percent": 15.576}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.915237, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 33.828, "width_percent": 8.448}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.01217, "duration": 0.02494, "duration_str": "24.94ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 42.276, "width_percent": 29.142}, {"sql": "select * from `pipelines` where `created_by` = 79 and `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["79", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 353}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.864895, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:353", "source": "app/Http/Controllers/LeadController.php:353", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=353", "ajax": false, "filename": "LeadController.php", "line": "353"}, "connection": "radhe_same", "start_percent": 71.419, "width_percent": 0.783}, {"sql": "select * from `lead_stages` where `pipeline_id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 365}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.872276, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:365", "source": "app/Http/Controllers/LeadController.php:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=365", "ajax": false, "filename": "LeadController.php", "line": "365"}, "connection": "radhe_same", "start_percent": 72.201, "width_percent": 1.052}, {"sql": "insert into `leads` (`name`, `email`, `phone`, `subject`, `user_id`, `pipeline_id`, `stage_id`, `created_by`, `date`, `date_of_birth`, `type`, `status`, `opportunity_info`, `opportunity_description`, `opportunity_source`, `lead_value`, `next_follow_up_date`, `contact_type`, `tags`, `postal_code`, `city`, `state`, `country`, `business_name`, `business_gst`, `business_state`, `business_postal_code`, `business_address`, `dnd_settings`, `updated_at`, `created_at`) values ('Gungun Rani', '<EMAIL>', '8654201236', 'New Contact', '79', 23, 86, 79, '2025-07-30', '2025-07-31', '', 'active', '', '', '', '', '', 'Lead', 'HE<PERSON>', '734429', 'Siliguri', 'Dadra and Nagar Haveli', 'India', 'NA', 'NA', 'West Bengal', '734429', 'GANDAG<PERSON> JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429', '{\\\"all\\\":false,\\\"emails\\\":true,\\\"whatsapp\\\":true,\\\"sms\\\":true,\\\"calls\\\":true}', '2025-07-30 07:59:28', '2025-07-30 07:59:28')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "<EMAIL>", "8654201236", "New Contact", "79", "23", "86", "79", "2025-07-30", "2025-07-31", "", "active", "", "", "", "", "", "Lead", "H<PERSON>lo", "734429", "Siliguri", "Dadra and Nagar Haveli", "India", "NA", "NA", "West Bengal", "734429", "GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429", "{&quot;all&quot;:false,&quot;emails&quot;:true,&quot;whatsapp&quot;:true,&quot;sms&quot;:true,&quot;calls&quot;:true}", "2025-07-30 07:59:28", "2025-07-30 07:59:28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 450}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8862238, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:450", "source": "app/Http/Controllers/LeadController.php:450", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=450", "ajax": false, "filename": "LeadController.php", "line": "450"}, "connection": "radhe_same", "start_percent": 73.253, "width_percent": 3.985}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values ('79', 13, '2025-07-30 07:59:28', '2025-07-30 07:59:28')", "type": "query", "params": [], "bindings": ["79", "13", "2025-07-30 07:59:28", "2025-07-30 07:59:28"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 475}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9016569, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:475", "source": "app/Http/Controllers/LeadController.php:475", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=475", "ajax": false, "filename": "LeadController.php", "line": "475"}, "connection": "radhe_same", "start_percent": 77.238, "width_percent": 3.143}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 504}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.912302, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:504", "source": "app/Http/Controllers/LeadController.php:504", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=504", "ajax": false, "filename": "LeadController.php", "line": "504"}, "connection": "radhe_same", "start_percent": 80.381, "width_percent": 1.507}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 517}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.922685, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:517", "source": "app/Http/Controllers/LeadController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=517", "ajax": false, "filename": "LeadController.php", "line": "517"}, "connection": "radhe_same", "start_percent": 81.888, "width_percent": 1.683}, {"sql": "select * from `email_templates` where `name` LIKE 'lead_assigned' limit 1", "type": "query", "params": [], "bindings": ["lead_assigned"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 525}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.934876, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "radhe_same", "start_percent": 83.571, "width_percent": 1.262}, {"sql": "select * from `user_email_templates` where `template_id` = 4 and `user_id` = 79 limit 1", "type": "query", "params": [], "bindings": ["4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2422}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 525}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.944239, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2422", "source": "app/Models/Utility.php:2422", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2422", "ajax": false, "filename": "Utility.php", "line": "2422"}, "connection": "radhe_same", "start_percent": 84.833, "width_percent": 2.045}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 86 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["86"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 358}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9752529, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:358", "source": "app/Services/CrmWebhookDispatcher.php:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=358", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "358"}, "connection": "radhe_same", "start_percent": 86.878, "width_percent": 1.694}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 23 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 365}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.987358, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:365", "source": "app/Services/CrmWebhookDispatcher.php:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=365", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "365"}, "connection": "radhe_same", "start_percent": 88.572, "width_percent": 1.192}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}], "start": **********.997632, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": "app/Models/Lead.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "radhe_same", "start_percent": 89.764, "width_percent": 0.97}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 390}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}], "start": **********.0073879, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": "app/Models/Lead.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "radhe_same", "start_percent": 90.734, "width_percent": 1.285}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.02478, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "radhe_same", "start_percent": 92.019, "width_percent": 3.891}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 550}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0523531, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:61", "source": "app/Services/ModuleWebhookService.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=61", "ajax": false, "filename": "ModuleWebhookService.php", "line": "61"}, "connection": "radhe_same", "start_percent": 95.91, "width_percent": 1.963}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.289618, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 97.873, "width_percent": 2.127}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2789, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1374777167 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374777167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.823739, "xdebug_link": null}]}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-646430344 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-646430344\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-276085580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-276085580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1989421226 data-indent-pad=\"  \"><span class=sf-dump-note>array:25</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Gungun Rani</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">8654201236</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-31</span>\"\n  \"<span class=sf-dump-key>contact_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Lead</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">HElo</span>\"\n  \"<span class=sf-dump-key>postal_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">734429</span>\"\n  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Dadra and Nagar Haveli</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n  \"<span class=sf-dump-key>business_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">NA</span>\"\n  \"<span class=sf-dump-key>business_gst</span>\" => \"<span class=sf-dump-str title=\"2 characters\">NA</span>\"\n  \"<span class=sf-dump-key>business_state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>business_postal_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">734429</span>\"\n  \"<span class=sf-dump-key>business_address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429</span>\"\n  \"<span class=sf-dump-key>dnd_emails</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_whatsapp</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_sms</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_calls</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">79</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"11 characters\">New Contact</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>stage_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dnd_settings</span>\" => \"<span class=sf-dump-str title=\"67 characters\">{&quot;all&quot;:false,&quot;emails&quot;:true,&quot;whatsapp&quot;:true,&quot;sms&quot;:true,&quot;calls&quot;:true}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989421226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1930349614 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2762</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryD8tuyVdOwtWH8v2P</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjZaMGxJQ0RsQUhWWWh4aWM0VWdYVHc9PSIsInZhbHVlIjoidDJTYnFoVklyNVMwRFBNOXRYQld0Z2o5Qzk0cGM4OGVVQkxtUzZXMlZHdGtjcDZyak9oVnRTSGxjS2pRQnlnL2ZJOHROVlJIRERvS2hWOTdjN01Ddk5LV0g0aHJuOUtTYXRWVCszRFpMV3VJUG01NVZkSUk1YmpnMVNUb0RQcW9ncnduMk16WGxUYS83bHc4Q2w0Z0dOSVhUam5lUG9kRFVSejZEZHRaRkJROXBrTGdkQWxRMUNKaGVzTUNSTG1rTHpVd2RMOExIbW1GOWtqNVo0UURZN1lqQTcxVFdreks4T3Q5WXRYL0IraEFscEFQbmZkakk3RjBmZHhNaElvZVFKL2R1MlB0OUgvR01GL0wwMFpYMzZsKzFtaUFZcTJlYk9MTHhhQ1Y1NnpONnhRQ3NPL0J0QTlmdCs3VEhEVFdSQkFHVkhTTTBHQWlzcm5iMkM1NzBkd0tUNUtQNXFWemZkQ2tvN1Urc0lJeGpucDdLUTdEbW4xQTN5eWRTUUhXN1ZORlhmblVYZ1FpbVpJNEU1ZWNXeno1MDBsc2d3ZjdGODduWjE4Wm9ybTdoK3c3SStmK0JCWkFJMk1pM0R5aUhIcTNXV054VkFremJjemJIcyttM0FQaFJjbWpHRVZuWXdKNkFOWUpOc0QvTkpQVGlqaXlSenVCdUlsYUpZT3QiLCJtYWMiOiIxYmQzYjc1NTA0OTAxM2MyNzBjMGRmZTRkY2RkOWZkMThkNDUxOTBmZDhlZGFjZjk4NmE0MWIyZTJhNWY1Nzc1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik5UY2xKSHRINkRLVmc5dmhHMVg4emc9PSIsInZhbHVlIjoiQy9VbkRyV1lOZVczNXZLaG5UaGVHblFsWVNSeFZteTJaRWcwNm94b1oycmRrRTlkczROeTYwOWpCbjI5bVFVUXlKZUJnL1BwR09Jcis5V3RJdXdTUEZud3B4YnNPTHI1T04wMTdkVzFlVlh6RzFZbXFQNFRZYWtEUmNlQ2RTN1MwYmZDeGd4SWdwV3lYRmxjTFVwZlFzOTJCMUZzbnd5TFpKWDQzTzNIK2J6S2NkeGZvQXRUeHM3eXExeWpvSG5hZkQ5ZFZpTFkvbGVUUDNpdCtmbHNIR2ZpYXlkRFNTdW9WSlN1U256UEtyMFRrU3IrWGlKei9lNG5HenVoVWpKT3JaSkVMTEFML2haYjhqSWdMWXBiaGp2WnZud1pQYzkreXlOQmFpT25QZ2xNcXk2L1dXQkt0R1RaWkF1RmZEcjVXRDBTellqNXBMUG5sdkNtWGo3WlNsamU0VjlsRDVhelJPdzRRNmROdmFCek53cVFBWnJZU2ZtelFucTFrVXZPNmw0Z0M1SE9MVUc0a3lkam9kTDB1S3pFV200cjJCbUZFb3NOajlVMFh5ZW8rMk5oeVI3TThSLzBuQ3lOU2tWb0JDb3pkaUdPUnlKdjlmaFRMelNDUVZMTjBlN1VoeUlJRjl1QmxtcG1oVzY0eUljSld6MUJyY21wSTIxMmR0cWoiLCJtYWMiOiJhZmQzMThiY2MxNThkYTE5MGU5NThiNGUyNmUzOTU5Y2ZiYjZhNWYyOGE1YmYwYTFiN2Y4NDY0YzhlMTAwN2MzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930349614\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-738560128 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738560128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-850146763 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:59:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxLbEFpNXphc0ZDTTdVNnhOdEdZelE9PSIsInZhbHVlIjoiTUJpdG1HMlV1VnhROEd4WFhaU0tHdkh3eGhxa3JBTkdLclFZNTNEaXlSLzJpQVgvZ0J0R3hKaFJYQkZ0YTNYeVV2V3RSV1B3TXJPdFh4cU41KzVWU0c4Z2R4eG1jaGlzWUVMaFkvZFFPMGRpMUg4b3N2c0NCK1BUNm1oQ2paZEc3OUJtRXBnVFFvcmkvWkNGa2s4WWhCbjBVSU5GREU2dHl4c0VSWXNGVmpsaFpMNVBPUnZtelFSb0Zua3hIdHpUeDdRWEdGeDlobWE3R1dWZ1plUkdzckN3eVRZWkc1TlVSRjhDRVBrV1VBRlR0dnJIVnVVdEJNM1dHWlF0NWVuSXk4aUFoZnQ5TVhWK2R6ZktiZHZBbklqL2pEWmJ5RGk5Z0kvM0JVM3BDREhpemQ3eTFtd3haR1N6UDJNVmtCU0NMdGNCL0xUS2JjLzE1QXdOa0ExUENFblNOV01jZXF5MnhramJQQzZwQTUvbXFncDRDYjdZTkV6MlFpNzFKanhOWEdETnROSjNHNkNMbVlEdnBNZzlzMlkwR3hna3FyaUJFYlpaR3lqVjhpWGV1WDVEaTI0SEFrZ1pMZWNyZWhYRjl3K1VmN0RBUnNSWnZpWUgrckVpNStjZHIwekJDMUphSlJ4b1RzSFVXbjRLUU1VUHdBMStUaEl4SGxGdEx1eHMiLCJtYWMiOiIzMzFlNjFjOTAwN2MxMWI1MWE5OGIxODQ4YjcxNTcxZWFhY2JiOGRhZTg1MTIyNjMwMWUxNmM2Y2YwOTk1ZDg3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:59:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIvMzVUUUFRaXN5YjBTWFFlN29MeWc9PSIsInZhbHVlIjoicy9EalQxbmpxbjVqc0p1VEFRUEF1UzQxNzF3UFZ2alhhYktqaVRKMkpQbk00bUFhNk5TelNNTlFaTUFsTkFpNkwwNkVhZFFsMEpRM0g4NTJsWjVNZkIySmZ0U21SdTQyY3d4MUszTUwreDVDOE5UdUdNN3U3blEvaUM0VXVXVS9XWFV1TnNqdUd1NWc3Unp0TXNRdVZsY3QrYThMMk5YN0NncE9GMlg4bG15ZldjK0UyeUJDbWdJekZhKzI4ZDArcDh6ZndFd1lUMW9TbllKT3RmMkhUckRXRGM3eVlKaVpxNDFkejV1QVFEL3ZCQzg5VSthRjMrbU5YTVNBeXpPUWJPeWZaaVc4OW5KOG9OZHd4NG9EUHU0ZmtJQzd0Y3liL0lzazMyUGlieXlGSlNTVmNIbDFkbjYzdzVGcUEwUVNWN2RVSklrTTdLUVNkL3hocFkxNTNRd0t1QTVnVTV4M1VGV2o2d0pZVldPU21OVGgrWTAyTlZjblJJbDE0eHdidnE4dGxZcTJYbjltUlJ4bjRWYmZ0WVlVckd4ZnNMZkZDbCtQajVKVmJ3WjBSRXJrZlRBTHpmZTZXNmI0UmI5a0xsSFRTdDRiUFRsTzJkdkpyZkdIL3BzSkJ1Q3VKMHB6djMrTm1iL1BDaTY1Ni9xWHVKSU56cVlwOXpmR2c2bnEiLCJtYWMiOiJhMjgyYTVjODk5M2FkODIwZjU2ZGJlYmM0NmM1NWZkZTk1NmY0NTdlZmQ3MTc2MzA2YmYzYzk1OGU0ODBhZDYzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:59:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxLbEFpNXphc0ZDTTdVNnhOdEdZelE9PSIsInZhbHVlIjoiTUJpdG1HMlV1VnhROEd4WFhaU0tHdkh3eGhxa3JBTkdLclFZNTNEaXlSLzJpQVgvZ0J0R3hKaFJYQkZ0YTNYeVV2V3RSV1B3TXJPdFh4cU41KzVWU0c4Z2R4eG1jaGlzWUVMaFkvZFFPMGRpMUg4b3N2c0NCK1BUNm1oQ2paZEc3OUJtRXBnVFFvcmkvWkNGa2s4WWhCbjBVSU5GREU2dHl4c0VSWXNGVmpsaFpMNVBPUnZtelFSb0Zua3hIdHpUeDdRWEdGeDlobWE3R1dWZ1plUkdzckN3eVRZWkc1TlVSRjhDRVBrV1VBRlR0dnJIVnVVdEJNM1dHWlF0NWVuSXk4aUFoZnQ5TVhWK2R6ZktiZHZBbklqL2pEWmJ5RGk5Z0kvM0JVM3BDREhpemQ3eTFtd3haR1N6UDJNVmtCU0NMdGNCL0xUS2JjLzE1QXdOa0ExUENFblNOV01jZXF5MnhramJQQzZwQTUvbXFncDRDYjdZTkV6MlFpNzFKanhOWEdETnROSjNHNkNMbVlEdnBNZzlzMlkwR3hna3FyaUJFYlpaR3lqVjhpWGV1WDVEaTI0SEFrZ1pMZWNyZWhYRjl3K1VmN0RBUnNSWnZpWUgrckVpNStjZHIwekJDMUphSlJ4b1RzSFVXbjRLUU1VUHdBMStUaEl4SGxGdEx1eHMiLCJtYWMiOiIzMzFlNjFjOTAwN2MxMWI1MWE5OGIxODQ4YjcxNTcxZWFhY2JiOGRhZTg1MTIyNjMwMWUxNmM2Y2YwOTk1ZDg3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:59:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIvMzVUUUFRaXN5YjBTWFFlN29MeWc9PSIsInZhbHVlIjoicy9EalQxbmpxbjVqc0p1VEFRUEF1UzQxNzF3UFZ2alhhYktqaVRKMkpQbk00bUFhNk5TelNNTlFaTUFsTkFpNkwwNkVhZFFsMEpRM0g4NTJsWjVNZkIySmZ0U21SdTQyY3d4MUszTUwreDVDOE5UdUdNN3U3blEvaUM0VXVXVS9XWFV1TnNqdUd1NWc3Unp0TXNRdVZsY3QrYThMMk5YN0NncE9GMlg4bG15ZldjK0UyeUJDbWdJekZhKzI4ZDArcDh6ZndFd1lUMW9TbllKT3RmMkhUckRXRGM3eVlKaVpxNDFkejV1QVFEL3ZCQzg5VSthRjMrbU5YTVNBeXpPUWJPeWZaaVc4OW5KOG9OZHd4NG9EUHU0ZmtJQzd0Y3liL0lzazMyUGlieXlGSlNTVmNIbDFkbjYzdzVGcUEwUVNWN2RVSklrTTdLUVNkL3hocFkxNTNRd0t1QTVnVTV4M1VGV2o2d0pZVldPU21OVGgrWTAyTlZjblJJbDE0eHdidnE4dGxZcTJYbjltUlJ4bjRWYmZ0WVlVckd4ZnNMZkZDbCtQajVKVmJ3WjBSRXJrZlRBTHpmZTZXNmI0UmI5a0xsSFRTdDRiUFRsTzJkdkpyZkdIL3BzSkJ1Q3VKMHB6djMrTm1iL1BDaTY1Ni9xWHVKSU56cVlwOXpmR2c2bnEiLCJtYWMiOiJhMjgyYTVjODk5M2FkODIwZjU2ZGJlYmM0NmM1NWZkZTk1NmY0NTdlZmQ3MTc2MzA2YmYzYzk1OGU0ODBhZDYzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:59:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850146763\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-725135613 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725135613\", {\"maxDepth\":0})</script>\n"}}