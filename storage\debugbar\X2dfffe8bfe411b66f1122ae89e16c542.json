{"__meta": {"id": "X2dfffe8bfe411b66f1122ae89e16c542", "datetime": "2025-07-30 07:56:21", "utime": **********.633938, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:56:21] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.62632, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753862179.083014, "end": **********.63399, "duration": 2.550976037979126, "duration_str": "2.55s", "measures": [{"label": "Booting", "start": 1753862179.083014, "relative_start": 0, "end": **********.239542, "relative_end": **********.239542, "duration": 2.1565279960632324, "duration_str": "2.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.239581, "relative_start": 2.156567096710205, "end": **********.633995, "relative_end": 5.0067901611328125e-06, "duration": 0.39441394805908203, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48360224, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.025169999999999998, "accumulated_duration_str": "25.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.370612, "duration": 0.00705, "duration_str": "7.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 28.01}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.4103398, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 28.01, "width_percent": 6.555}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.422914, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 34.565, "width_percent": 6.516}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.431875, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 41.081, "width_percent": 3.894}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.470254, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 44.974, "width_percent": 5.721}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4936352, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 50.695, "width_percent": 5.284}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.506278, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 55.979, "width_percent": 4.052}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5194318, "duration": 0.010060000000000001, "duration_str": "10.06ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 60.032, "width_percent": 39.968}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1381962677 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1381962677\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-734546119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-734546119\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-814502968 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814502968\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1848733716 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IktwNTJqNkxYd0Y2L25TSHdTNGdreHc9PSIsInZhbHVlIjoiWm45N0VORzNNckc1cEVIUS9zMW9KYytrcDY4NkZLRThSSDJNUjJRdWdmTUhmdG5BS0EydEx2UytEdW1DTW1oNjJXWmJJb0RETVl4WmV2UjBUWjJHbUtXdFJQSUJweGRpT0NpNFNLZ2x5RHJFRXRZZlFwOUVKUXR6c25zd0gxMDlJYlU1Wjh3V09nZTMyWS9pekVVMzU2R0l2enZ1M0dpZDBpUDdBem1pMzUzWjJZMm9kdEQ1V1FwNDFYRFFoZXVIdEV4U1FERnovdk03RmZSRzRxUGpYaklZRlpvUHZKVXcyMmp5RGNQeW9Pcmtwam1TdW5peitGUGRGeEdPYXhzTzNuYXlFY2NMYUJqTGlabUZVUktITm1MYTRTMUZmNnd0enMycC9UVTAvQWZCaG1yV1dBUi9jcTFVUEtBTjVCR0ZTTzljc2MwdVZ0d0FxMHZtUnNZZEdYemphMkRWc0ZCVFU0WU1Sa0dBY0ZoanpHdk02enl3eU5IVkdSM05HK1RwaUhOeXAveHBvS1JsYWcrS0hMS0NkbjFRamx6b3czdklUcEUzbGkxK21icWs0S0JUTHRqRE5RZFFsSUtaYXBNQkJFQ3doR1diRStycXhNamNsUnA5MXkzYnQ4K2s0aldTdlBjWm1zdWJCYkRqRnZwRGZqeDdmODFxWVVyQmUvRUkiLCJtYWMiOiJjYzE4Mzk1MTcxNjc3MWNkMmZjYmZkODBjZDUyMWJkZWUyZDViOGRiODEzYzFkMzM5ODg0OTBmMzJmM2RmYzAwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlJTMEU5dFF4SGFZd21PcXlLUmdSSmc9PSIsInZhbHVlIjoiMTVCdFhJcDJ3aGpWSkVla3FvU0hLUU5MNXYyeFJvbFRPVXRycEdRb3N5MXZKUkFnT2Z4bWJPVDh6eG9jc1ROOHVHQzROcUpnQk16UXoxUTZDeWxJWkhEMEd3N0I2SGEyMGFHVHdNYkoxa1RiUGxvaVFLSTBQeTRIdVFLSG96c0NTZEtjdHBvUXdaTXpLdGhveXU4cGplT1ZuSUVxRE9XTjFoK0VlT1FXbXQ1Nkp5T3hEc0w5L3Q1R0cwd0RDU2liNVB3U1FZT1pLUmx3SitzZVlmNHpXeVNEOTBvY01iV1pOMU9nRi9JN1U4L2laZ3VLWHJMZm9YcjhtVXZCS1dycytJRmVxV0ZhU2NVVzFGeWpHNVNySFVRSGZVc3Qwa0tzbHd2bmRqTm9xQTA5d1FDS1Q4YnpZZlY5NEkwY3Y5UEhXZzcvOFlyV0hscnBMUlN0SW5UZm9YZ2xIL1IzL0pENWJrNHJHZVFnYlFiOCs4QWlyNFgzcUthS0pVejNFMDhKM3FGQS96czNvRUxiaGYrY1lQL1daRGZHNkF3Q0dteDQwYzRRa1RsVzB1NlYyc2VJbnFnM1BQOUdXQ0d0Y2lqbGwvWG1INmlMcTFqQmF6MHFhQnlPcFlmZllDTHhWdlN5MmpncmVZMkF6TngweWRINnRMZEpqMC9EVnVaSTk1bTAiLCJtYWMiOiIzN2I4NTU0NzZkMDgyN2RhNTljMTQ4YzY3MWMxMTg3ZTc0OWQ1ODVkZDA4YzNhMDJhNjhkZDc1YTVkODhlODZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848733716\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-136515020 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136515020\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-767481768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:56:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFFNEpLRERHbndJTGlkWjJ4bHRCaEE9PSIsInZhbHVlIjoibjZPb1hsZFZmcy84a0I5N2E3WUNpTXBFRGgyZUErU0J3aWNXTU5nd3BVREQxZUNwWE1vL1NOM0pYV1hLUHdBYitVcVYzRkNXYkM0OEdGbjJQVUdJcm5adFRGM01ZZXpCQ25QeE5UNnE5dVRMdytHbFVOdXh6QnE4S0tnWW8xNWNMSU1RUlBtVVBCeWlxL1AyUk5hNEIwdElGdWw1czg3SXArN2hrM0ZJUS9keHdjaGNaMGZ0YzFtZ1VYTHBvUEgyN0dpWDNDYWtzNGpsOWg2YnFPNkVyQ2NBMjhJeWIyRGZiaGRETjdrd0VWcnNTcFVBTWN0U0xlQWcxVnVlVEJiRFpWUFBZQkRjTWdvUHJ3YmFxUHJWRnlpQ3hKREtnM09rWURMVzQzWEVNN2RJcDA5Rlc3Y0ZyTEpMRXJHdEtCSWtyWGo2aEllMGhGR0pjK243Qkd6dm9jNUNSV2h2aHBYUGdrZi9JTDZyeUdidHEzVUYwNW1lY2xFdjBzUkp6YUR4U2pOMDZndW1Jb3ZZVENiZ3pvV0dSeWZLRjlLcFQzV05EeTdDblIwek4rbzlFRHB5WnNWUzJvMXFpOVI1SDB3YllKZ0FoYUIyWjJZRWpZc2xpd1IzaTg2Qjd5YnpBRUhtTlI0cGZzSFFwU2I1bGdWQUNZYTF4SGJmSWxROUV3cVciLCJtYWMiOiJiOTZkMzI0MzRlNjI1NTI2NTFiODg5ZWY3ODQzYTMxMDc0OWUzYzE5MWQ5N2Q4MGE5ODgxZjZmMGZjNTc4YjE5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:56:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllqYldFd2xDMkp3K3orS2R0T3ZwZkE9PSIsInZhbHVlIjoiSyt2eEUyeUdWYkFGTm9TSlpoQzBQYXZpMHlDNEJOZm0xdjVaK2JGSU1ITXFreFlQQXJWOUpWaFJqaXNNNy9qcHVHempGS1NEa2FWK0hhcE9mVkhUancwbjNoVjhlaXp4aVI5cUk5M0k4Rlo2WjgxQ0thYm04OFBrS0NFZ3FnUUhwY1Z2dWFqNVJwV0xyOUxHWW95VFp6ZHZjSzRYanVwQzV3UTR1RDA3MTcyV21ONjdNZ0JBTzZCVmdlaXZRSDV6YWg2WVE4ZFhkUFRDeGRsVGwvaDA1S1czWEZ6Z0k3K0JxTUQ3TkxVU2xCK0pVUEFZSUxYWEVDRDdjZ0Y5eVBITSt0MHdSc1BWVlR3MFJhQVpsOWM3WnU5MUhOdDlYYVJxVEdGazByRitZNGZKRTZQSG9EYTllOVY2aVlDZHlFTTJibHRwSkFTT0QzL3ZPeHAvZTNjd3BjTkE4d09UK3cvVFgwdHFwUXZHRDlFc2ZyQU1SOHlTVWV6cjBZQ3JiUEhsb2NiRHdETEFZNHVjdHE1TFBmdkVPUHpCWGhueDRYalAxS2VPNEo0VWxiNEFFWkgwTE9BRS9mZEQ3M1BGQTdrMkhMSkNwOWtrWG5GZHd1UFNsRmJ3N01DZ3dhc0NtMFBYMFlTR0ltMXkzMFArU0thZEYzMVJPYm8xTmtjNjVwRGoiLCJtYWMiOiIzMTBhZDVlNDNkNTkwYTM0Njg2YmY4Y2ExZGMyMWExNGI0Zjk1ZTFiYjAyNzMwNjQ1NzUzYzkyMjBlZGFmYWM1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:56:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFFNEpLRERHbndJTGlkWjJ4bHRCaEE9PSIsInZhbHVlIjoibjZPb1hsZFZmcy84a0I5N2E3WUNpTXBFRGgyZUErU0J3aWNXTU5nd3BVREQxZUNwWE1vL1NOM0pYV1hLUHdBYitVcVYzRkNXYkM0OEdGbjJQVUdJcm5adFRGM01ZZXpCQ25QeE5UNnE5dVRMdytHbFVOdXh6QnE4S0tnWW8xNWNMSU1RUlBtVVBCeWlxL1AyUk5hNEIwdElGdWw1czg3SXArN2hrM0ZJUS9keHdjaGNaMGZ0YzFtZ1VYTHBvUEgyN0dpWDNDYWtzNGpsOWg2YnFPNkVyQ2NBMjhJeWIyRGZiaGRETjdrd0VWcnNTcFVBTWN0U0xlQWcxVnVlVEJiRFpWUFBZQkRjTWdvUHJ3YmFxUHJWRnlpQ3hKREtnM09rWURMVzQzWEVNN2RJcDA5Rlc3Y0ZyTEpMRXJHdEtCSWtyWGo2aEllMGhGR0pjK243Qkd6dm9jNUNSV2h2aHBYUGdrZi9JTDZyeUdidHEzVUYwNW1lY2xFdjBzUkp6YUR4U2pOMDZndW1Jb3ZZVENiZ3pvV0dSeWZLRjlLcFQzV05EeTdDblIwek4rbzlFRHB5WnNWUzJvMXFpOVI1SDB3YllKZ0FoYUIyWjJZRWpZc2xpd1IzaTg2Qjd5YnpBRUhtTlI0cGZzSFFwU2I1bGdWQUNZYTF4SGJmSWxROUV3cVciLCJtYWMiOiJiOTZkMzI0MzRlNjI1NTI2NTFiODg5ZWY3ODQzYTMxMDc0OWUzYzE5MWQ5N2Q4MGE5ODgxZjZmMGZjNTc4YjE5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:56:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllqYldFd2xDMkp3K3orS2R0T3ZwZkE9PSIsInZhbHVlIjoiSyt2eEUyeUdWYkFGTm9TSlpoQzBQYXZpMHlDNEJOZm0xdjVaK2JGSU1ITXFreFlQQXJWOUpWaFJqaXNNNy9qcHVHempGS1NEa2FWK0hhcE9mVkhUancwbjNoVjhlaXp4aVI5cUk5M0k4Rlo2WjgxQ0thYm04OFBrS0NFZ3FnUUhwY1Z2dWFqNVJwV0xyOUxHWW95VFp6ZHZjSzRYanVwQzV3UTR1RDA3MTcyV21ONjdNZ0JBTzZCVmdlaXZRSDV6YWg2WVE4ZFhkUFRDeGRsVGwvaDA1S1czWEZ6Z0k3K0JxTUQ3TkxVU2xCK0pVUEFZSUxYWEVDRDdjZ0Y5eVBITSt0MHdSc1BWVlR3MFJhQVpsOWM3WnU5MUhOdDlYYVJxVEdGazByRitZNGZKRTZQSG9EYTllOVY2aVlDZHlFTTJibHRwSkFTT0QzL3ZPeHAvZTNjd3BjTkE4d09UK3cvVFgwdHFwUXZHRDlFc2ZyQU1SOHlTVWV6cjBZQ3JiUEhsb2NiRHdETEFZNHVjdHE1TFBmdkVPUHpCWGhueDRYalAxS2VPNEo0VWxiNEFFWkgwTE9BRS9mZEQ3M1BGQTdrMkhMSkNwOWtrWG5GZHd1UFNsRmJ3N01DZ3dhc0NtMFBYMFlTR0ltMXkzMFArU0thZEYzMVJPYm8xTmtjNjVwRGoiLCJtYWMiOiIzMTBhZDVlNDNkNTkwYTM0Njg2YmY4Y2ExZGMyMWExNGI0Zjk1ZTFiYjAyNzMwNjQ1NzUzYzkyMjBlZGFmYWM1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:56:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767481768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-227315075 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227315075\", {\"maxDepth\":0})</script>\n"}}