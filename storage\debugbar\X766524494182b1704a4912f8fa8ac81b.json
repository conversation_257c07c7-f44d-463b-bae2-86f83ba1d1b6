{"__meta": {"id": "X766524494182b1704a4912f8fa8ac81b", "datetime": "2025-07-30 08:10:38", "utime": **********.345529, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753863036.477187, "end": **********.345583, "duration": 1.8683960437774658, "duration_str": "1.87s", "measures": [{"label": "Booting", "start": 1753863036.477187, "relative_start": 0, "end": **********.168028, "relative_end": **********.168028, "duration": 1.6908411979675293, "duration_str": "1.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.168072, "relative_start": 1.***************, "end": **********.345588, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eN6u0cLCh5N2xF1FOxtQw3pTQ6vuR1MOAlEwiRMV", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-919586762 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-919586762\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-702311769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-702311769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-611961303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-611961303\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-268495061 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268495061\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92861393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-92861393\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-977563883 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:10:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1FWS9GdGV5aWMzdWczY0M5UDl4ZFE9PSIsInZhbHVlIjoiVGg4S3YwR2N4OWtwTU9zdExGMmtmYWwwZGYxKzZYelArQlkxYmNNVS9DRUVhSm4vTEpJaDVFTnR0NjZJT3JEVk9LNjBlUzMwUlpvQVJkSVgrR3QzZ1IzSHJmMXp5Z3ZRZU5PRm5oOGViSDh2TFJ2WTZnMlU0RjNuVU1yQzVwRDZHV2EvMEdUYzN5Rzl5SHRRVnh2K1FQOXBRelFFRnZ5WmYvWHNyWVFJTmtKVXZMZGI0T01qVmVhY3BCcE9xVTVZRGZjKzhKQWtBQlJ3TWhDZ2UyY3duUGZXT1ZLaFRRNmRwZHVDbHNmV3NsQUZ1RVloenFQOGw1cUt0MklQdU16TDdpM3F2QkViMU9OUThCdlU2c3RwTDhzNUtGY0JjSnZ4T2EzaElNekdZMThtdE55bEhiM2pKVllndzhyMzZiMXgrMW9LLzBIVFVxdmptMlIydWpkQThKQTduSzZHbzdGNlBsbzRodnVHY1hGTTN0MmRyam80dW5OVFVVQ2tHVWhqMkVrR0FrZGxZbjkxZFEwT1ZwcHV2WU9MMVVVU3BiUVlOZ3NUNXBVUzJrOFo1M3hORUhlcEpuOVhYdnBDL0ZNSFR3SDhkaWVlRjI3aFRDRXljYitRbmJLaGdaTTNLVWhqRWN6NGpUMEhmYldqZ21TMk1ZbDRtbkxTbHI2dHZVQ0wiLCJtYWMiOiIzYzA1ODQyNTY3OGExZjQzYTUwY2RjOWU1YzBjMTMyNmIzMDczODRiY2Y2Njg0Y2Y4ZTE1ODcxNGVhZjJjNzE1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:10:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFHcVFLcTNGRE9Od2VPeDF2dlkrU2c9PSIsInZhbHVlIjoiTVBhWXZOSmphNS8xSkgweXVSMXIzRHh0S0w3UFRncndNanJia04rRmJaUFpodlk5WXBIRHZTM3p0c3FyQ1VCZ2JyZktUdXhEVUwrRzB2enBPYlZzeVJVazJMMjdBQTYzMC9jVm1oMkRaRGZwa3VOQ3hBUTJpNWJqaXZXczQ0S1ZMU1dJa1lCUFl4YTk1Zkp3Ni9FbXZoU0w1M1VxcVBrclNqcW5mNElONExJQm1WOVY4dHI5bXJpUWxHTkM4UFRxNlg1SE9qcTZUWHVJSjBuTVRYNTYzZkYxSGhaM1VTQnAzOEhyR0NudE1NTmFMNGxIK3o4bG9MOXhteDlFVmxmdW9QQWpYNWpxU0w3ZWk1bGZYTExUeTJ2dUJyODEzNlFQMnpvVDJqWjlIMW1NMDRLdFppLzJML0g3a00zb0N2a01vZHlpV3ZOSWlrRDRYc0RpTXB0eU42VTlHQXM5c1BrSDF3YnJXTVkveXkyK3pKNERYdS9vaFlJaTNBUkxYUmtYdWhYM2ZzL2owMVVtaWRsNzQrbFRaNTlnRnp2WmhhajVlK1JXNWdUeURsbFViL0FkQ2ZsSjQ2SUxlRWRsODI4V3Zja1UwL3RWSUV6Zm4rYXVZTjVOUEtWMnE3czlra083MWRpTDg2ZUtSNWF6WWpEb05pVWh0MTJPQnFXdjZqZmwiLCJtYWMiOiIyOTU3MWJlMjQzNzFlYTI3NTkzZGJhMjU4MDgzY2UxYjYyYWEwNjlmMjM0YmY1NDcyNDY3ODZkODBkYmYyYTk4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:10:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1FWS9GdGV5aWMzdWczY0M5UDl4ZFE9PSIsInZhbHVlIjoiVGg4S3YwR2N4OWtwTU9zdExGMmtmYWwwZGYxKzZYelArQlkxYmNNVS9DRUVhSm4vTEpJaDVFTnR0NjZJT3JEVk9LNjBlUzMwUlpvQVJkSVgrR3QzZ1IzSHJmMXp5Z3ZRZU5PRm5oOGViSDh2TFJ2WTZnMlU0RjNuVU1yQzVwRDZHV2EvMEdUYzN5Rzl5SHRRVnh2K1FQOXBRelFFRnZ5WmYvWHNyWVFJTmtKVXZMZGI0T01qVmVhY3BCcE9xVTVZRGZjKzhKQWtBQlJ3TWhDZ2UyY3duUGZXT1ZLaFRRNmRwZHVDbHNmV3NsQUZ1RVloenFQOGw1cUt0MklQdU16TDdpM3F2QkViMU9OUThCdlU2c3RwTDhzNUtGY0JjSnZ4T2EzaElNekdZMThtdE55bEhiM2pKVllndzhyMzZiMXgrMW9LLzBIVFVxdmptMlIydWpkQThKQTduSzZHbzdGNlBsbzRodnVHY1hGTTN0MmRyam80dW5OVFVVQ2tHVWhqMkVrR0FrZGxZbjkxZFEwT1ZwcHV2WU9MMVVVU3BiUVlOZ3NUNXBVUzJrOFo1M3hORUhlcEpuOVhYdnBDL0ZNSFR3SDhkaWVlRjI3aFRDRXljYitRbmJLaGdaTTNLVWhqRWN6NGpUMEhmYldqZ21TMk1ZbDRtbkxTbHI2dHZVQ0wiLCJtYWMiOiIzYzA1ODQyNTY3OGExZjQzYTUwY2RjOWU1YzBjMTMyNmIzMDczODRiY2Y2Njg0Y2Y4ZTE1ODcxNGVhZjJjNzE1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:10:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFHcVFLcTNGRE9Od2VPeDF2dlkrU2c9PSIsInZhbHVlIjoiTVBhWXZOSmphNS8xSkgweXVSMXIzRHh0S0w3UFRncndNanJia04rRmJaUFpodlk5WXBIRHZTM3p0c3FyQ1VCZ2JyZktUdXhEVUwrRzB2enBPYlZzeVJVazJMMjdBQTYzMC9jVm1oMkRaRGZwa3VOQ3hBUTJpNWJqaXZXczQ0S1ZMU1dJa1lCUFl4YTk1Zkp3Ni9FbXZoU0w1M1VxcVBrclNqcW5mNElONExJQm1WOVY4dHI5bXJpUWxHTkM4UFRxNlg1SE9qcTZUWHVJSjBuTVRYNTYzZkYxSGhaM1VTQnAzOEhyR0NudE1NTmFMNGxIK3o4bG9MOXhteDlFVmxmdW9QQWpYNWpxU0w3ZWk1bGZYTExUeTJ2dUJyODEzNlFQMnpvVDJqWjlIMW1NMDRLdFppLzJML0g3a00zb0N2a01vZHlpV3ZOSWlrRDRYc0RpTXB0eU42VTlHQXM5c1BrSDF3YnJXTVkveXkyK3pKNERYdS9vaFlJaTNBUkxYUmtYdWhYM2ZzL2owMVVtaWRsNzQrbFRaNTlnRnp2WmhhajVlK1JXNWdUeURsbFViL0FkQ2ZsSjQ2SUxlRWRsODI4V3Zja1UwL3RWSUV6Zm4rYXVZTjVOUEtWMnE3czlra083MWRpTDg2ZUtSNWF6WWpEb05pVWh0MTJPQnFXdjZqZmwiLCJtYWMiOiIyOTU3MWJlMjQzNzFlYTI3NTkzZGJhMjU4MDgzY2UxYjYyYWEwNjlmMjM0YmY1NDcyNDY3ODZkODBkYmYyYTk4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:10:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977563883\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1040922102 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eN6u0cLCh5N2xF1FOxtQw3pTQ6vuR1MOAlEwiRMV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040922102\", {\"maxDepth\":0})</script>\n"}}