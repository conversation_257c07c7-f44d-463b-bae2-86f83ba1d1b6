<?php

namespace Database\Factories;

use App\Models\ProductServiceCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductServiceCategoryFactory extends Factory
{
    protected $model = ProductServiceCategory::class;

    public function definition()
    {
        return [
            'name' => $this->faker->words(2, true),
            'type' => $this->faker->randomElement(['product & service', 'income', 'expense']),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the category is for products and services.
     */
    public function productService()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'product & service',
            ];
        });
    }

    /**
     * Indicate that the category is for income.
     */
    public function income()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'income',
            ];
        });
    }

    /**
     * Indicate that the category is for expense.
     */
    public function expense()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'expense',
            ];
        });
    }
}
