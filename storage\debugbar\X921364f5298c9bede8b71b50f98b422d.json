{"__meta": {"id": "X921364f5298c9bede8b71b50f98b422d", "datetime": "2025-07-30 04:43:20", "utime": **********.82125, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753850599.918069, "end": **********.821277, "duration": 0.9032080173492432, "duration_str": "903ms", "measures": [{"label": "Booting", "start": 1753850599.918069, "relative_start": 0, "end": **********.752453, "relative_end": **********.752453, "duration": 0.8343842029571533, "duration_str": "834ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752466, "relative_start": 0.****************, "end": **********.82128, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "68.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2YSRNanDVBQxwL8pqPNELLia6OMKcu81uXKcwq5p", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2016535473 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2016535473\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-516604778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-516604778\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-217760926 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-217760926\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-549898535 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549898535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-869786285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-869786285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1926502674 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 04:43:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNmclh6ZEdDKzVCK2NodFlVVHNoU1E9PSIsInZhbHVlIjoiVVkrNTc2MktsMTBNRjQyekpJeG5jdzJ0U2xJdDJtV3k4Mjl2TkQyMG0rZlpzczQzNVAzTDJKbVJmdVRsT0pNMnBHNFhvZEdUbjd4dnJqY2t6ZXJ1V0RXUytYckdqOTRqQjZOQlNjNjdJbTZOdGNCeUs5TWJkak5RNW1zck9TMEpYOFZacmdDQzd0ZW51QkFuclI2dEJGcnY5aGVhb1VxNHNvN0c4VDBUVEl4VUxhd3lya3FTb0dtaXFiQmMvVjE0VkxsZDh5emd6RDFiTW96WmR2aGFsdittQy92RnRBRDJ1bXFIVW82M2p3WlNtck9JMVpyS2Z5K2tndWdFN2JmdW11SStCR1EvSWp3VDJ3OFdlWWR2NyttWittWndDSis1cllGU1lTYUk3dGVQdER4bUllcWFmNTVKUWNIaU82K3RvcGRmbmh0TktuTldrUWlydWxvOUxWZFdFZUdWditKNEFQRnF5SWxmQndqOE13NVZNdEtHNDZjV09ZM0t6YW01NWx4M0tka2tmZUx5aUNvcklQbGtEc2NRbmQzQmpaRkplSW5XZ2ZocEl0SVZSWU0zMG4zRCtFV0diOVc4M0tOUldLYmdjd1NEaDN5Tkhrcmh5NjV2KzhvZlE4a1BJc3hOd2lkVU1yaWR0bkZFOHphWlQ0ZW9NWEJNY2tvZVY4SG0iLCJtYWMiOiI2YTFhMDA2MTIzMjI4NWY4MGY4NGMxMzUyMzE0ZWY0MzY4NjdhMjMzZWVhMTRjNGVlYTUxYzkwZjkyYjBiNDM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 06:43:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imk1dTIvdEpYblVTb0tISlpLeCt1MXc9PSIsInZhbHVlIjoiTDFZWjNmWWlnZzNUU2hRWEpySGlqSVdJV2o2aWxqUlU3Vlc5c1hnNHkyZmdZNnFrTUV5TzVaOUZrSDl2dmZYckUxb3lWbnJwWEphdUtCZDFzM05UOEZNZVNXZ3NoQmQ3S281ZmdpVlNZYjJqOVRNOTZjZTlxaS9uQnFvaS92bnRFTTJBaUNjWXJCaTdxaFY4SGZqWHJtNUNHV1d3bnloZTJoTENPK2h1cURxUmJwTVdjamxpZ2s0K2ZacGwzektvQU1aMFdPS0R3WklETnFZdExaUXdWcW1VMXNSZHZHemQ1MFI1eUQxV1JrK1ZxMWc4SkR6WmoyMmNNaHJVd0JkS1B0cUxQcVY2Tk1mQUFnM0ZrMEt0c0tNTExRb0ZFVTI3bHhkbWxZMGI0SHgwZW5wOE1GcmxZWFkxczhYcWI3MjgzSGZhc1FuRDRuenRrQm1pb1J4cEgvaWpOS3hONU5mcHFaWWlrRWFoZVBlNVhqdEVSZUUvRW8vUHJ0dCtOWjl5cy91RjZpZWRUMW5iRjFYVHNlaUNrZDhndGFqMkpYSm1CTm5yT0pWVWd6QnYxU0VoSXhheUZxOFVRUWx4ZkVxRHRvTHRBcTRxWDdZUG5vRUkxK2w1SXdZMklhNmU0dVk4cWhudTdFb3NJRXlmOXZoaTZUWVlUaXl3OEtNMjFtMmQiLCJtYWMiOiJhYWQwY2Y1ZDBiN2VjMzRhMzMyOWNiZjlhZjVmNjRiYjlkMjdhZWIzYzJiNWE5YTMxODY4ZTg0N2YwYmU3NTdiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 06:43:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNmclh6ZEdDKzVCK2NodFlVVHNoU1E9PSIsInZhbHVlIjoiVVkrNTc2MktsMTBNRjQyekpJeG5jdzJ0U2xJdDJtV3k4Mjl2TkQyMG0rZlpzczQzNVAzTDJKbVJmdVRsT0pNMnBHNFhvZEdUbjd4dnJqY2t6ZXJ1V0RXUytYckdqOTRqQjZOQlNjNjdJbTZOdGNCeUs5TWJkak5RNW1zck9TMEpYOFZacmdDQzd0ZW51QkFuclI2dEJGcnY5aGVhb1VxNHNvN0c4VDBUVEl4VUxhd3lya3FTb0dtaXFiQmMvVjE0VkxsZDh5emd6RDFiTW96WmR2aGFsdittQy92RnRBRDJ1bXFIVW82M2p3WlNtck9JMVpyS2Z5K2tndWdFN2JmdW11SStCR1EvSWp3VDJ3OFdlWWR2NyttWittWndDSis1cllGU1lTYUk3dGVQdER4bUllcWFmNTVKUWNIaU82K3RvcGRmbmh0TktuTldrUWlydWxvOUxWZFdFZUdWditKNEFQRnF5SWxmQndqOE13NVZNdEtHNDZjV09ZM0t6YW01NWx4M0tka2tmZUx5aUNvcklQbGtEc2NRbmQzQmpaRkplSW5XZ2ZocEl0SVZSWU0zMG4zRCtFV0diOVc4M0tOUldLYmdjd1NEaDN5Tkhrcmh5NjV2KzhvZlE4a1BJc3hOd2lkVU1yaWR0bkZFOHphWlQ0ZW9NWEJNY2tvZVY4SG0iLCJtYWMiOiI2YTFhMDA2MTIzMjI4NWY4MGY4NGMxMzUyMzE0ZWY0MzY4NjdhMjMzZWVhMTRjNGVlYTUxYzkwZjkyYjBiNDM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 06:43:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imk1dTIvdEpYblVTb0tISlpLeCt1MXc9PSIsInZhbHVlIjoiTDFZWjNmWWlnZzNUU2hRWEpySGlqSVdJV2o2aWxqUlU3Vlc5c1hnNHkyZmdZNnFrTUV5TzVaOUZrSDl2dmZYckUxb3lWbnJwWEphdUtCZDFzM05UOEZNZVNXZ3NoQmQ3S281ZmdpVlNZYjJqOVRNOTZjZTlxaS9uQnFvaS92bnRFTTJBaUNjWXJCaTdxaFY4SGZqWHJtNUNHV1d3bnloZTJoTENPK2h1cURxUmJwTVdjamxpZ2s0K2ZacGwzektvQU1aMFdPS0R3WklETnFZdExaUXdWcW1VMXNSZHZHemQ1MFI1eUQxV1JrK1ZxMWc4SkR6WmoyMmNNaHJVd0JkS1B0cUxQcVY2Tk1mQUFnM0ZrMEt0c0tNTExRb0ZFVTI3bHhkbWxZMGI0SHgwZW5wOE1GcmxZWFkxczhYcWI3MjgzSGZhc1FuRDRuenRrQm1pb1J4cEgvaWpOS3hONU5mcHFaWWlrRWFoZVBlNVhqdEVSZUUvRW8vUHJ0dCtOWjl5cy91RjZpZWRUMW5iRjFYVHNlaUNrZDhndGFqMkpYSm1CTm5yT0pWVWd6QnYxU0VoSXhheUZxOFVRUWx4ZkVxRHRvTHRBcTRxWDdZUG5vRUkxK2w1SXdZMklhNmU0dVk4cWhudTdFb3NJRXlmOXZoaTZUWVlUaXl3OEtNMjFtMmQiLCJtYWMiOiJhYWQwY2Y1ZDBiN2VjMzRhMzMyOWNiZjlhZjVmNjRiYjlkMjdhZWIzYzJiNWE5YTMxODY4ZTg0N2YwYmU3NTdiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 06:43:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926502674\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-432982134 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2YSRNanDVBQxwL8pqPNELLia6OMKcu81uXKcwq5p</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432982134\", {\"maxDepth\":0})</script>\n"}}