{"__meta": {"id": "Xb00e676740076c49e87cdd0717d0fb7e", "datetime": "2025-07-30 02:46:18", "utime": **********.01151, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753843575.364392, "end": **********.011566, "duration": 2.6471738815307617, "duration_str": "2.65s", "measures": [{"label": "Booting", "start": 1753843575.364392, "relative_start": 0, "end": 1753843577.8127, "relative_end": 1753843577.8127, "duration": 2.448307991027832, "duration_str": "2.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753843577.812746, "relative_start": 2.****************, "end": **********.011571, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "199ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y9yKU6H6vsed2K84rjoh9LAHZIT6IRuP2HBcHFoP", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-988866412 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-988866412\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-740504784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-740504784\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-22432806 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-22432806\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1463494928 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463494928\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1140599700 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1140599700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1415234270 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 02:46:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikt6Wm1qN0tROUJjRzE2OXZkbWd3bXc9PSIsInZhbHVlIjoiZnBwZ2JDMGtzZStwclpwdWRXaTZLYWJhcmlpQzJRTjNLa1hVVmtiVW5zb1UyR1NhVjh3cHd4Wk9Yam5pVnN1TmtpaTB3UHMyVVNuTjRLMktiUzJ4YkN6N2dPbTlHVWtCQlh4Y2p2Vnlna1oveEhPVkpTM0Iyajg0NkR2aGFqOEI0bllPbStVd3NPUitpUHljbmVnK0ZZUjhOczZad3YvSElTL0VraEhMYzFqbFJ4MkhaUkhHYUlZYXlMSEpzNmRjUmxYTk8xbitVWTNNQWY3M1g3ZWRZL3VqWmtaTjR2c1hkcjdQK20xRlVCYmtoOWpiS1U5T2FZSE5LKzlWV3FwRVJZSXFmalg2RFRFOGdUZU41Q2RsNHZoVVBOTkUrU2dNYnh6c21JOFI0RFpsVUEzSTJaWmpLMTNjVGJ4bE94MW1ENmJZd0VuVTlnUGhDTTdzbHNuUnBLS0oyWjRXRFdEd2ZIcVN4UGtUR2YyZFBlOVpXZlJ5bkpmazN3cmdNakQvcWYwVmN2d3ZRNE1RelBBNFpjdUxrZmw1MmpCWlZUbFB4Y0llM1pseTdnWit2K3FkcUI3Q3ZzTlZvbDBSa2djS1FhNXVjL3lVM2dleTd6UEJWVHJzUVdzYXJablRpUjRNQlNTQlNKR3ZKZ0hCbVIzUUwzMGlvbjhWM3N5SGNqelgiLCJtYWMiOiIxNDI0MmJhN2QwNTRjOWQwNGIxNzJlYzdhMTkxODc2ODVhOGVjMmJhMWVmZTJiNWFhZGZmNzRmNDk5ZDJkMGViIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:46:17 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRtNWVxQVp5dW5Cb20zY0t0NmFHM1E9PSIsInZhbHVlIjoiK2xrYUZrYjVTcXJnNEp0RUZ6Qkx0MXdCNjBYam9OemtEN29ubHZkQWtqVXYrMTU1K1RGYTVNV0RGOVI3Qzh1ZGcwS3N6NkcybVRnY3k1Y2tXYm12ODRLTEhFUzJxK04rbmk3K1JZeUxxWHZSUDg5Z3BZMERIa2o4VlprWG9tL1p2VzhvQ0p0aGZnTEt6dGFycmJnUlFZVEkwY1BYV2I5RCtIRTQ4VTROMStzS0V6UzZncWhtNmdmaVlMdGxZQ29QQys4NzUzTVhKdjhWS2RTOXlIaHJQZDZCd2hFM1h3Y3VOWVBZUHlrblZoNlFOaGExREc4ZVNPWXZDZnNZNk95VHhlUGljekl5dHppV0dnY0Jzd3BKSE9KbzU0RHMvZDBjRWRBdkhVVGFFRTZhVDU3RVhuVHNZRmhOa3ZUS2dTSFRTNVhncGh6RlRJRFNPbXdCamUzMVJ4c1A2VVdHYlFmMk1uTC9RR0dFVGdZbU9IOGZjSWV4UjRsWUliQW5wNElZb0JXN1dxQ09tQmx0WUdUVlREcmlVMmJuY21iRHpVRUI0MTQ5Y3NoRUMzT2dWOURHVVYzTGVXZGUwbDBwMldwckFmSE5hL2FLLzdrVjBSRGdGc2hGNGNWT01vWDU3RlIwRHM3RGlhQnhlMjkyckwrc1NzNWNndnZwcUNUMUYxMy8iLCJtYWMiOiJlZmJlOGY3ZmM5MzNjMjUzODc5MGJjOTI4ODcyNmQ3ZGEyN2E3M2NhMGI1ZTQyYzkzNGU4MjgwNzI2YzIyYzViIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 04:46:17 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikt6Wm1qN0tROUJjRzE2OXZkbWd3bXc9PSIsInZhbHVlIjoiZnBwZ2JDMGtzZStwclpwdWRXaTZLYWJhcmlpQzJRTjNLa1hVVmtiVW5zb1UyR1NhVjh3cHd4Wk9Yam5pVnN1TmtpaTB3UHMyVVNuTjRLMktiUzJ4YkN6N2dPbTlHVWtCQlh4Y2p2Vnlna1oveEhPVkpTM0Iyajg0NkR2aGFqOEI0bllPbStVd3NPUitpUHljbmVnK0ZZUjhOczZad3YvSElTL0VraEhMYzFqbFJ4MkhaUkhHYUlZYXlMSEpzNmRjUmxYTk8xbitVWTNNQWY3M1g3ZWRZL3VqWmtaTjR2c1hkcjdQK20xRlVCYmtoOWpiS1U5T2FZSE5LKzlWV3FwRVJZSXFmalg2RFRFOGdUZU41Q2RsNHZoVVBOTkUrU2dNYnh6c21JOFI0RFpsVUEzSTJaWmpLMTNjVGJ4bE94MW1ENmJZd0VuVTlnUGhDTTdzbHNuUnBLS0oyWjRXRFdEd2ZIcVN4UGtUR2YyZFBlOVpXZlJ5bkpmazN3cmdNakQvcWYwVmN2d3ZRNE1RelBBNFpjdUxrZmw1MmpCWlZUbFB4Y0llM1pseTdnWit2K3FkcUI3Q3ZzTlZvbDBSa2djS1FhNXVjL3lVM2dleTd6UEJWVHJzUVdzYXJablRpUjRNQlNTQlNKR3ZKZ0hCbVIzUUwzMGlvbjhWM3N5SGNqelgiLCJtYWMiOiIxNDI0MmJhN2QwNTRjOWQwNGIxNzJlYzdhMTkxODc2ODVhOGVjMmJhMWVmZTJiNWFhZGZmNzRmNDk5ZDJkMGViIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:46:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRtNWVxQVp5dW5Cb20zY0t0NmFHM1E9PSIsInZhbHVlIjoiK2xrYUZrYjVTcXJnNEp0RUZ6Qkx0MXdCNjBYam9OemtEN29ubHZkQWtqVXYrMTU1K1RGYTVNV0RGOVI3Qzh1ZGcwS3N6NkcybVRnY3k1Y2tXYm12ODRLTEhFUzJxK04rbmk3K1JZeUxxWHZSUDg5Z3BZMERIa2o4VlprWG9tL1p2VzhvQ0p0aGZnTEt6dGFycmJnUlFZVEkwY1BYV2I5RCtIRTQ4VTROMStzS0V6UzZncWhtNmdmaVlMdGxZQ29QQys4NzUzTVhKdjhWS2RTOXlIaHJQZDZCd2hFM1h3Y3VOWVBZUHlrblZoNlFOaGExREc4ZVNPWXZDZnNZNk95VHhlUGljekl5dHppV0dnY0Jzd3BKSE9KbzU0RHMvZDBjRWRBdkhVVGFFRTZhVDU3RVhuVHNZRmhOa3ZUS2dTSFRTNVhncGh6RlRJRFNPbXdCamUzMVJ4c1A2VVdHYlFmMk1uTC9RR0dFVGdZbU9IOGZjSWV4UjRsWUliQW5wNElZb0JXN1dxQ09tQmx0WUdUVlREcmlVMmJuY21iRHpVRUI0MTQ5Y3NoRUMzT2dWOURHVVYzTGVXZGUwbDBwMldwckFmSE5hL2FLLzdrVjBSRGdGc2hGNGNWT01vWDU3RlIwRHM3RGlhQnhlMjkyckwrc1NzNWNndnZwcUNUMUYxMy8iLCJtYWMiOiJlZmJlOGY3ZmM5MzNjMjUzODc5MGJjOTI4ODcyNmQ3ZGEyN2E3M2NhMGI1ZTQyYzkzNGU4MjgwNzI2YzIyYzViIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 04:46:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415234270\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1662785937 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y9yKU6H6vsed2K84rjoh9LAHZIT6IRuP2HBcHFoP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662785937\", {\"maxDepth\":0})</script>\n"}}