{"__meta": {"id": "X0d2750910a96e85596370685ed34e1b1", "datetime": "2025-07-30 05:21:47", "utime": **********.136849, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753852905.736781, "end": **********.136887, "duration": 1.4001061916351318, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1753852905.736781, "relative_start": 0, "end": **********.031395, "relative_end": **********.031395, "duration": 1.2946140766143799, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.031415, "relative_start": 1.****************, "end": **********.136892, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3024\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1844 to 1850\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1844\" onclick=\"\">routes/web.php:1844-1850</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jzyKvAkXFnvTeUJEHh4hklUrPIaKYIpCyGlf0m3p", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1959539599 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1959539599\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-830588299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-830588299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256638078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256638078\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-683011134 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683011134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-809055313 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-809055313\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1302763195 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 05:21:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdjUzB3RU5xbG5lZzBVa0kvTThQU2c9PSIsInZhbHVlIjoiNzVjU0wycSsrQklDekZrV2lrYktHcm9odDZtZ3N4YnZrRmRrNzNRNE4wbndLOEhkd2VHWU9FMVVYRStjQ3B0ZXBsemtFV0hwK0FTSHNmNWZlekdUL0ZPbDBNWnVjR3dDOGtPcnB4N205Y0Z0WUg3a25wU09wNnlCQmo4OHZwS2daVENUWVJkSktUcnBxV0RKUi9PMS9UbTBZYjNJeG4wa1Nwd2hESm9DK0c4WVFoYWRLalkwbUt2YTNmbUlBZThzUWpWSmdsb1l3ZThRYlhjOVU1TXF3UnhoZ0hNVE5zWnNKZ0cyTUhMYUhPME5jOC9kK2U5S1FVOGxoem94MTloTis2KzJjaW5nbzAzUVVpRGQzeEw0SUI3MktlRzFpWE1tWmpxUXF1Wnh1Qlp6WEJxc2lyWERPb21hbzAxYkFYYzdLK2RVTnd3LzdURFRWUkJJcGdlVkQraTVFVHFTM2dZM0YvK1VFTzVMOHovQ1BERGcreW9yRGtrK1UxQzNJS2Z4RUViREVjT1NFVWlmaFRVNTVlWGZNVUdDQ3dyUFg2MC91bHh0d1ViMXMyKzZFcGJPd2tUNWg0T1REVTdvN25kOFdpNXRBTTZ3dy9mUGlrb1gzYWtMRWFHSDhNRjQ5OGZzVHF4ZmJkLzVHUlZmMFppd3Vjb1hlN1h0WUV6dm55disiLCJtYWMiOiJiZTg2MTUyMGQ2MjhmZjk0Yjg3MzhiYzZkZTM0YzJjYWYwNjE1OTY4NDU1YTAxY2E2MjllNjI4NjZkZmQxNzFiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:21:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJWSW5pSGtHL1BQYXRsblBlVFV2eUE9PSIsInZhbHVlIjoiQVdaa09jU1lpd1NBQ3BVa1hOdndiYUNoajRuRkJzUlQ1aGRTU0UzUHhGbTVFaEZPNFVYeVdiQy9yYVMvTXZ0b3laazZEN2h0VnFOdXJVaVBIVm95dGp3QVQ2bW9rUUU4VllocnFpQTIrWXhzOTNlRzl6QUlmQnNabk5XVnJjbG4yTWpVMDkxOEZ5RU0xSXcvMFIza3VCMGVYamJxdStobk44UmNSWkQyMExONmJrbGJtZElFSHpGU21OYmVJUjFYcUNERzQ5V1pTVmxjdEM0MXhWNEJpcVUwajZFVzNmUWZnOXZLMENXeWUvWkNkdzJFcnlPNTdUMlNJVmg2czlhOTlaYlUzVlRDVnBpOWl4cXU5MEdwTjE0Ynh4bFhrR2FnSithUkNqQ1gya1pkaE1XTHY5QUhMR1pSZWRpWWY3K1ZkMXVtZU1lVjhadFRKb0xmVEg0OTBPZkF1MHplbjNEd0hMeXpZb1NpdVorbHAySFU1UytVSDE1d29id1lYV09ZbXN5bFNCbEF1bjkzc3FEMmVLZHFnL2hUSkx1K24xd3h5YlRkSE5zVEJpWTVnejN3ZVZLU2ltWmZEM0U0Z0dzZ3JkWjJodkdhY0dCZTc0cnZMbmoyaEtHeTVhS1owSmFSSlRySm9PNE92TFJNdEgvaFJ5MFBwMDNTKzBxZU50NysiLCJtYWMiOiI4ODE5ODdmNjFkZjRmNTNmN2NkM2RlM2NmODM2MDg2YzEyN2I5Zjg5NGRkMzhjZmU3MjE1MDU3YmI5YTc2Y2NiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 07:21:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdjUzB3RU5xbG5lZzBVa0kvTThQU2c9PSIsInZhbHVlIjoiNzVjU0wycSsrQklDekZrV2lrYktHcm9odDZtZ3N4YnZrRmRrNzNRNE4wbndLOEhkd2VHWU9FMVVYRStjQ3B0ZXBsemtFV0hwK0FTSHNmNWZlekdUL0ZPbDBNWnVjR3dDOGtPcnB4N205Y0Z0WUg3a25wU09wNnlCQmo4OHZwS2daVENUWVJkSktUcnBxV0RKUi9PMS9UbTBZYjNJeG4wa1Nwd2hESm9DK0c4WVFoYWRLalkwbUt2YTNmbUlBZThzUWpWSmdsb1l3ZThRYlhjOVU1TXF3UnhoZ0hNVE5zWnNKZ0cyTUhMYUhPME5jOC9kK2U5S1FVOGxoem94MTloTis2KzJjaW5nbzAzUVVpRGQzeEw0SUI3MktlRzFpWE1tWmpxUXF1Wnh1Qlp6WEJxc2lyWERPb21hbzAxYkFYYzdLK2RVTnd3LzdURFRWUkJJcGdlVkQraTVFVHFTM2dZM0YvK1VFTzVMOHovQ1BERGcreW9yRGtrK1UxQzNJS2Z4RUViREVjT1NFVWlmaFRVNTVlWGZNVUdDQ3dyUFg2MC91bHh0d1ViMXMyKzZFcGJPd2tUNWg0T1REVTdvN25kOFdpNXRBTTZ3dy9mUGlrb1gzYWtMRWFHSDhNRjQ5OGZzVHF4ZmJkLzVHUlZmMFppd3Vjb1hlN1h0WUV6dm55disiLCJtYWMiOiJiZTg2MTUyMGQ2MjhmZjk0Yjg3MzhiYzZkZTM0YzJjYWYwNjE1OTY4NDU1YTAxY2E2MjllNjI4NjZkZmQxNzFiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:21:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJWSW5pSGtHL1BQYXRsblBlVFV2eUE9PSIsInZhbHVlIjoiQVdaa09jU1lpd1NBQ3BVa1hOdndiYUNoajRuRkJzUlQ1aGRTU0UzUHhGbTVFaEZPNFVYeVdiQy9yYVMvTXZ0b3laazZEN2h0VnFOdXJVaVBIVm95dGp3QVQ2bW9rUUU4VllocnFpQTIrWXhzOTNlRzl6QUlmQnNabk5XVnJjbG4yTWpVMDkxOEZ5RU0xSXcvMFIza3VCMGVYamJxdStobk44UmNSWkQyMExONmJrbGJtZElFSHpGU21OYmVJUjFYcUNERzQ5V1pTVmxjdEM0MXhWNEJpcVUwajZFVzNmUWZnOXZLMENXeWUvWkNkdzJFcnlPNTdUMlNJVmg2czlhOTlaYlUzVlRDVnBpOWl4cXU5MEdwTjE0Ynh4bFhrR2FnSithUkNqQ1gya1pkaE1XTHY5QUhMR1pSZWRpWWY3K1ZkMXVtZU1lVjhadFRKb0xmVEg0OTBPZkF1MHplbjNEd0hMeXpZb1NpdVorbHAySFU1UytVSDE1d29id1lYV09ZbXN5bFNCbEF1bjkzc3FEMmVLZHFnL2hUSkx1K24xd3h5YlRkSE5zVEJpWTVnejN3ZVZLU2ltWmZEM0U0Z0dzZ3JkWjJodkdhY0dCZTc0cnZMbmoyaEtHeTVhS1owSmFSSlRySm9PNE92TFJNdEgvaFJ5MFBwMDNTKzBxZU50NysiLCJtYWMiOiI4ODE5ODdmNjFkZjRmNTNmN2NkM2RlM2NmODM2MDg2YzEyN2I5Zjg5NGRkMzhjZmU3MjE1MDU3YmI5YTc2Y2NiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 07:21:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302763195\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1390235257 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jzyKvAkXFnvTeUJEHh4hklUrPIaKYIpCyGlf0m3p</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390235257\", {\"maxDepth\":0})</script>\n"}}