{"__meta": {"id": "X10c853770fad5ec1cbc71a66450005d3", "datetime": "2025-07-30 07:55:51", "utime": **********.049531, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862149.171858, "end": **********.049565, "duration": 1.8777070045471191, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1753862149.171858, "relative_start": 0, "end": 1753862150.86854, "relative_end": 1753862150.86854, "duration": 1.6966819763183594, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862150.86857, "relative_start": 1.****************, "end": **********.049569, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LCzM6rvLH5BqXFXFw7Oyr7BXFHZE7wMXvGfa8CNw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1542769501 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1542769501\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-542987642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-542987642\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-901054868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-901054868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-30764442 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30764442\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1441041241 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1441041241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 07:55:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRkelVMaFRKVFJQakpuWTU0NnZhSlE9PSIsInZhbHVlIjoieEsyMVNDU1J2VXU2RTVMazFkWVMxWUwyRjBlQjJocHZvd3YweHQwUWFpeXYwczZmQ3dZdHNMUmVoVDAxYU12MXJKT2YxYytBcFFmamMrYkdGN3lNYzJranRGOVdqMmhIY3VYYkw4RE5aQlhPYlMwWngyNWlsNmEyaXNYU3l3QkNVOUZWbHQ5cVQ5MFVJYklHQzlTSHJGZXNBRVN1MlpRdGtvVXQ2SGt5YVI1UWdXYm5jZkV2Tk8rS3F2T3pScE4reWxBcVdXUG56NDRNQUc1L0t2MzVJVG9SeGZWclF4TEdTKzNwN3orS2xLQlNrNFcvaHU4TGhIQll2d2g4TnpKV2ZiNktVOENyZXlxODljMUxCZVpyR2dRc1BjMURhMWwydkIwTE1WM2VPNW1EcEhqa09obDdkUFNLL2pHUmltUFBVcm1lRXFUOCs3RUJlM3NyalZuN0dsUm5uWGhRZVYxOURmUzdnTGxpdklTeHJzdjdJL1N5eDhWQjFyc0pzNXNRNUZHblNkZThmMTl2SzZ4ajIrZmlXK290TlBjRUx0NFhhUjAzM21HRis4UmhuYUpmRGRaQy81R21QU0dJNStuV1lyK0lGYlg2Ykl5TmhpQkpKTDdtYUZrSHI3Tk9hL1FqYmMwNEJVYXk1K3E5RjFTbWtOTlJUbVovT1BYVWVNaXgiLCJtYWMiOiI2ZGIzOTYyN2I5YTk3OGE4Nzk0MGIzODNjMDAyZjQ0ZjAwYWJkZTVmMzRhYzNkMjYzMzI5Nzg0NTYyMGFlZDI3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:55:50 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im9WOW5DVmFLeHNlaUlLV3dsZDIyVWc9PSIsInZhbHVlIjoiUjY0Qm04Vkc2K2lTNVg1U1JzZWM0YzJmVE01c0M0b2pKbVNwaXdNNTcxYmZLQVpGZ25ieXJEczFpcVRpcnhDUnpwY0FWY3k5WlJiTW1mdEJKZ1hRbFZZYjdxVGdRQVBiMWNUTy9VTU9wZlJONUphbmlZTnlVMis3dGRxdFE0bFNHSTZwZHVLZUFBRi80Sk81bjBzMXRyL2MycFYvb1dzY2FaWmFaTzFNMkdYY1lsMmt2OUJFZEZXbnBGZmhmdFJVanhsaGxsK3NKSy85d24rMXF6Wk40WU0xSXVxaWt1SlhkTzdhbWdudFFZbHBGUXVOODFsTjNtR2NlUXFqUGcrbW5oVWxJTUgzcnp0aldBaHN2OHlxempTWWJ1dVMrU2tHTGlzR2ZxdWlXamwzQjJQNFVsMzUvTXpBbkY4R3dKMHgwYWMwa2V5dHo1UURuQU9ud2RQZ1B0emJLSVpDODlOVFp2S0VockNxZnJROHRNbyt2LzE3WTVTbDAxeVdsNndWRnJXQnF5Q05XUkVyZ2dDQVZPTHpKL3h4dFAzWGFJR2VTVWlSUXlUVnNveU5RL0hnMmlmTFNsdFUwcThNdDhqeDdEN255MUZLcXV1TlAvMDk1SkhiS2FTRXhKcG9xaGFNZFFtY2FudlRoNnAzLzlnZ0hsM2xtZHcyMzBUN0dyVUoiLCJtYWMiOiJlOThkM2RlMDhjYzEzNDI4MTVmNDRlZThjOGVlY2Y2NmU5NmE1MWQ5NzAxOWRmZDc3NDkwYTQzNDcyNWRlOWE5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 09:55:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRkelVMaFRKVFJQakpuWTU0NnZhSlE9PSIsInZhbHVlIjoieEsyMVNDU1J2VXU2RTVMazFkWVMxWUwyRjBlQjJocHZvd3YweHQwUWFpeXYwczZmQ3dZdHNMUmVoVDAxYU12MXJKT2YxYytBcFFmamMrYkdGN3lNYzJranRGOVdqMmhIY3VYYkw4RE5aQlhPYlMwWngyNWlsNmEyaXNYU3l3QkNVOUZWbHQ5cVQ5MFVJYklHQzlTSHJGZXNBRVN1MlpRdGtvVXQ2SGt5YVI1UWdXYm5jZkV2Tk8rS3F2T3pScE4reWxBcVdXUG56NDRNQUc1L0t2MzVJVG9SeGZWclF4TEdTKzNwN3orS2xLQlNrNFcvaHU4TGhIQll2d2g4TnpKV2ZiNktVOENyZXlxODljMUxCZVpyR2dRc1BjMURhMWwydkIwTE1WM2VPNW1EcEhqa09obDdkUFNLL2pHUmltUFBVcm1lRXFUOCs3RUJlM3NyalZuN0dsUm5uWGhRZVYxOURmUzdnTGxpdklTeHJzdjdJL1N5eDhWQjFyc0pzNXNRNUZHblNkZThmMTl2SzZ4ajIrZmlXK290TlBjRUx0NFhhUjAzM21HRis4UmhuYUpmRGRaQy81R21QU0dJNStuV1lyK0lGYlg2Ykl5TmhpQkpKTDdtYUZrSHI3Tk9hL1FqYmMwNEJVYXk1K3E5RjFTbWtOTlJUbVovT1BYVWVNaXgiLCJtYWMiOiI2ZGIzOTYyN2I5YTk3OGE4Nzk0MGIzODNjMDAyZjQ0ZjAwYWJkZTVmMzRhYzNkMjYzMzI5Nzg0NTYyMGFlZDI3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:55:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im9WOW5DVmFLeHNlaUlLV3dsZDIyVWc9PSIsInZhbHVlIjoiUjY0Qm04Vkc2K2lTNVg1U1JzZWM0YzJmVE01c0M0b2pKbVNwaXdNNTcxYmZLQVpGZ25ieXJEczFpcVRpcnhDUnpwY0FWY3k5WlJiTW1mdEJKZ1hRbFZZYjdxVGdRQVBiMWNUTy9VTU9wZlJONUphbmlZTnlVMis3dGRxdFE0bFNHSTZwZHVLZUFBRi80Sk81bjBzMXRyL2MycFYvb1dzY2FaWmFaTzFNMkdYY1lsMmt2OUJFZEZXbnBGZmhmdFJVanhsaGxsK3NKSy85d24rMXF6Wk40WU0xSXVxaWt1SlhkTzdhbWdudFFZbHBGUXVOODFsTjNtR2NlUXFqUGcrbW5oVWxJTUgzcnp0aldBaHN2OHlxempTWWJ1dVMrU2tHTGlzR2ZxdWlXamwzQjJQNFVsMzUvTXpBbkY4R3dKMHgwYWMwa2V5dHo1UURuQU9ud2RQZ1B0emJLSVpDODlOVFp2S0VockNxZnJROHRNbyt2LzE3WTVTbDAxeVdsNndWRnJXQnF5Q05XUkVyZ2dDQVZPTHpKL3h4dFAzWGFJR2VTVWlSUXlUVnNveU5RL0hnMmlmTFNsdFUwcThNdDhqeDdEN255MUZLcXV1TlAvMDk1SkhiS2FTRXhKcG9xaGFNZFFtY2FudlRoNnAzLzlnZ0hsM2xtZHcyMzBUN0dyVUoiLCJtYWMiOiJlOThkM2RlMDhjYzEzNDI4MTVmNDRlZThjOGVlY2Y2NmU5NmE1MWQ5NzAxOWRmZDc3NDkwYTQzNDcyNWRlOWE5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 09:55:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LCzM6rvLH5BqXFXFw7Oyr7BXFHZE7wMXvGfa8CNw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}