{"__meta": {"id": "X760308a12cd02a413d644096755ca0f8", "datetime": "2025-07-30 08:09:32", "utime": **********.035027, "method": "POST", "uri": "/product-category/getaccount", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:09:32] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/product-category\\/getaccount\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.013612, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.076271, "end": **********.035154, "duration": 2.***************, "duration_str": "2.96s", "measures": [{"label": "Booting", "start": **********.076271, "relative_start": 0, "end": **********.651085, "relative_end": **********.651085, "duration": 2.****************, "duration_str": "2.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.651152, "relative_start": 2.***************, "end": **********.035164, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST product-category/getaccount", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getAccount", "namespace": null, "prefix": "", "where": [], "as": "productServiceCategory.getaccount", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=177\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:177-240</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.031729999999999994, "accumulated_duration_str": "31.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.764064, "duration": 0.01467, "duration_str": "14.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 46.234}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8066149, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 46.234, "width_percent": 4.003}, {"sql": "select `chart_of_accounts`.`id`, `chart_of_accounts`.`code`, `chart_of_accounts`.`name`, `chart_of_account_parents`.`account` from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` where `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 79", "type": "query", "params": [], "bindings": ["0", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8199582, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:231", "source": "app/Http/Controllers/ProductServiceCategoryController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=231", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "231"}, "connection": "radhe_same", "start_percent": 50.236, "width_percent": 13.583}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.857235, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 63.82, "width_percent": 3.467}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8837302, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 67.286, "width_percent": 3.656}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.895309, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 70.942, "width_percent": 4.318}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9086351, "duration": 0.00785, "duration_str": "7.85ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 75.26, "width_percent": 24.74}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/product-unit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/product-category/getaccount", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">product &amp; service</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">72</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/product-category</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjFPWWJSb3FDcjhXWHV1TUU3NkhvU3c9PSIsInZhbHVlIjoiZ0dRUFFCQWo2M1V3T0NEZ0FZNU9qLzROeThXMXNCcEJjQkxSL0M0MmZxQUc5YlZxMzlKaUhFK2ZmaHdYY3JkV3ArUkw5cE5oZzN3ZGdWYWRQbWdjQmp2MVFuNDlSWElUbVZ5eE9mTXpWSlBRaWpUNWswU3M3b2NwOVFVTk1kd0R2VS9OcC9vWmhNZGJyUmpjMWVSVkxmelBYOGoraVg2d1NVa1V5TVdZQ1Foak9yWjIzdVpqMS9QaXVMMnR5eVpKYmFodmoySjFNdndLYnlveThwdUdUQU1UZ2FMOUw0NS9kWkNzMVNEMU1MSTZwdFFMbFdxc0pPOEU4WmpXbWo1MExoTDhuc0NRcHQyWFQvZ0QveGhhcVg3Mmxab2NWRHIza0F6UzEzZUllQVUrWU5tWTZCTmxBNit4cDgyRXJDQTJSNE1zSHIzemVXWUsremYzeFFCZ2JCR0R4OWdrTU1qWWM1WlZEZ0J3ZnNEd0FPOUd3THNFYjNFWjd5YUVDQis0U1BGUlBwSG9NQ1FYaVBXbXl0ZXhJaVlsVG43ajhzdUN5alR2YzlVZEtUQ04xYlJoWW8yVC9SSnVsbm9WWXFUcTBhVDN6dUVkYXc0OUZ0M0M1SW4wL3hIUllOVEVIalg3Z2VjY1ljWUZNaUwxSWdPZ2ZwTGwvRTVUNlBDcytGZWYiLCJtYWMiOiIwZDNhMWI5OGIyNDFkOTliN2IzN2U0ZGE3NjBhZjgwZWRkNGQyOWMzMTU1OWZhNWQ0YWYzYmI0YThhMDMzY2ViIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik0xdDl2SXc4OWh4Qnd1RStuLzZxL3c9PSIsInZhbHVlIjoiODVpRzZoYUZNZTRXNS9xNEp0K09qZFo3allwS0pNaE12TEZDT0pQTGp2RUtVN0Q2dXBURytjK3JJbENyaExwbzJpenBOT09pK1l6TnNuZ29Nek1GZGZVMjdjODRRM3kweGlwY0Ftd3dONmpqQjhoRndrc3hLZUNVUGlLWW50U0d2UzdxYlNXcWdQb0dTZ2xEbE1BL0ZFWnJRSHQxM2YwUlF1d0d1UnE1LzVtZjNxWDZxMVRmbWpGTlpqNzhZWEIxZzFQanJ6Zko3enZUUDFtb2toKzJnZVhLeFZ2MnpISFFySjNrelBwSjlUaWVaVlRyWXdGejQ3cHZ2ZDZTQjBMZEVxeXRxUldhb0R2eTFCejM1RFZCUytiYlVnemJUcjYzb0F6a1g5dVRObnhIN2dmZTNjZkFHcTUxL2tpQ1piZ3dDbUZ6RDRQQVpXSC94WkVaQTYzL0U3a1VpcTQ3ZmxoZFowek82Qy85NWpZaXhJV1hsN1NzUlJyb0xyaEVpNGM2dGlzUENOVEhra0xQZEpDZ3JZa1BDTTJHR3dQQVAzcG1TcG5kOXpMa09nSnluV2NqY1NuOFArai9wRGowbGx4aHZNU1NWRkpoVHY5MXlmckVvaGZLWkF6QzFqN0o5a08zZDA3eWpONTN0M3V3eEdCQklKa0dvL0xvUDlsSnMvQmgiLCJtYWMiOiIwOTc0ODY5YjBjNTBjYTc3OTc3YmVkN2MwNjYxNzBhZjJkNGIxODQ0YTdiMTA2Yjc3MThiNjhkNTZkMWU2NjliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353530738 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:09:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRtL3hqVnA4SWpZK3dTaHYvd3MrV1E9PSIsInZhbHVlIjoiejRzMjdDTmp4d0tDWm9RM0gvZ3l3S0xMcFpMRzFxWlNSM05VVy9tVCtvRTVjbnpFSVJ3NFFOclpuYkxCWXpkV29QbW9sN0ZpYjhWNWlvVHl0T1ZsRzdxTlBCc3dRRU9COVh0b3hBSmNGTVE2WTBjSGJUMXBuS0k5WXhXaTBLdUNRVEFKV2pMWHV4NmZ5clFBYkFNUWM3T2szaTlmRG5jMU40VHkwMmVoTERicUhKdmkyc3EzUjJZTkRYZytlM2ViV0xVU3AxNlNVSGJHdlJRM255QmowVFFUV2ZkbiswaXQxZTZJVHVhUU1oOFViYm9EbkpnNXZzdVRuL0l2U0lWbElIQ2VxM3RUUktnUnNHTllpS2Jjd21UNTcyS2w3bzRkWUN3bU8zS0RUOTFDb1VXSVFWVHhCdDlEcVNzNlR2N0lMaWhxNjRWY3dmQzN3VEhDWGpELzlid0Fmb2I5NEFEa1dJdWlYYk9lK2VZbWhpYU4wQzNpdHFuYXY1U0xJdkhhbzlHZXNGakR4eWk4N3VXaTFQSmxGbVdkQzZzSUhhWExmbEs0clRwcUN2WGc0VWRRWnVUNkRlZUd3aW1iQ0U0Z3VsN3dRQVpnNHlDYnJoSC9mMk9QUzBLVi9iaG1jM3U4WGFta09NZVppK2ovRHNXdlpZRnZSY3Rsck1KRjNlWi8iLCJtYWMiOiI1Yjk5ZTNhMDdjNDA5NjI3YjI0MWIwMWFiNjg4MzA2NjFlZDE5ZTA5ZDA2MmZkMTg5ZDkyOTAyNDMzZWViNTg1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitMNmJ4bUk2b2VMVk42TXMyK0xCZ0E9PSIsInZhbHVlIjoiRFRMb1VrOUx3Y2tVby8zbjhyTDhJL0xlb1Y1UjJGdlkrbUhoNGdtUFc1TjBVQkhlbGwra0hXamY2b3dRSFJjVjRmdzJTVVZlK2tCWGtlNXRSajc1Z0JZS1R2OEFXNTNHZW94K284M0E0ekNkZ1hmWFc5MzhCSk14dTQ3cUZTWmdidndudlFvMlBMWUUzSlFVWC9hR1FVQ2ZyZ3pYMGxsL2huUWxTcm55SVAvUk93TWhtQnZWaVFJVHdncnlyVnVNT01yWUJRQ0pQSEVSRFJmMXpENm0wRWRuYXNxcStJK1M5bS9EdnoxMXpYeDJnVXNaMmgzS0htOTVNbWlMNVMyZms5RVhoWkxhSlVJR0l5ZEs4YnFGSXdFRFo5NDN5ZGh3VnhKRVREN3pYS3FIUms2T2ZuZm5mZjdPWGgxU2tQa09xREpycjMwQlBvNDM5TGNlNThtOXN0ejVHRis1TTBZcXY0YlU0c2lLZjdObis3eHoxMU51dFdyalBiWHRieWdjdUd2eFdDNzVlZnZXMXlUTGFmMm9vRVlMNndjcDlTU1hiOEVyZW1PSWswLzI2TzdXQ3BVRUdKNmFNZjZkNzh6YkVTY1pXaHB3QUhjWjEyeDd1OEVtMXowL0NRN3FTRjhzS21Oa1BqdkFqRUVnZnNRSmxoWXBIem1hV0NEUEUyc24iLCJtYWMiOiJlOTY0Y2ZmMmQ0NGRlMDk0YmEzYTc3ODY4Y2M5NjgxZjQxZmNlMzFmM2UyNzIwMmQ5NmQyZmFjYTE2NTM0MmY5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:09:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRtL3hqVnA4SWpZK3dTaHYvd3MrV1E9PSIsInZhbHVlIjoiejRzMjdDTmp4d0tDWm9RM0gvZ3l3S0xMcFpMRzFxWlNSM05VVy9tVCtvRTVjbnpFSVJ3NFFOclpuYkxCWXpkV29QbW9sN0ZpYjhWNWlvVHl0T1ZsRzdxTlBCc3dRRU9COVh0b3hBSmNGTVE2WTBjSGJUMXBuS0k5WXhXaTBLdUNRVEFKV2pMWHV4NmZ5clFBYkFNUWM3T2szaTlmRG5jMU40VHkwMmVoTERicUhKdmkyc3EzUjJZTkRYZytlM2ViV0xVU3AxNlNVSGJHdlJRM255QmowVFFUV2ZkbiswaXQxZTZJVHVhUU1oOFViYm9EbkpnNXZzdVRuL0l2U0lWbElIQ2VxM3RUUktnUnNHTllpS2Jjd21UNTcyS2w3bzRkWUN3bU8zS0RUOTFDb1VXSVFWVHhCdDlEcVNzNlR2N0lMaWhxNjRWY3dmQzN3VEhDWGpELzlid0Fmb2I5NEFEa1dJdWlYYk9lK2VZbWhpYU4wQzNpdHFuYXY1U0xJdkhhbzlHZXNGakR4eWk4N3VXaTFQSmxGbVdkQzZzSUhhWExmbEs0clRwcUN2WGc0VWRRWnVUNkRlZUd3aW1iQ0U0Z3VsN3dRQVpnNHlDYnJoSC9mMk9QUzBLVi9iaG1jM3U4WGFta09NZVppK2ovRHNXdlpZRnZSY3Rsck1KRjNlWi8iLCJtYWMiOiI1Yjk5ZTNhMDdjNDA5NjI3YjI0MWIwMWFiNjg4MzA2NjFlZDE5ZTA5ZDA2MmZkMTg5ZDkyOTAyNDMzZWViNTg1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitMNmJ4bUk2b2VMVk42TXMyK0xCZ0E9PSIsInZhbHVlIjoiRFRMb1VrOUx3Y2tVby8zbjhyTDhJL0xlb1Y1UjJGdlkrbUhoNGdtUFc1TjBVQkhlbGwra0hXamY2b3dRSFJjVjRmdzJTVVZlK2tCWGtlNXRSajc1Z0JZS1R2OEFXNTNHZW94K284M0E0ekNkZ1hmWFc5MzhCSk14dTQ3cUZTWmdidndudlFvMlBMWUUzSlFVWC9hR1FVQ2ZyZ3pYMGxsL2huUWxTcm55SVAvUk93TWhtQnZWaVFJVHdncnlyVnVNT01yWUJRQ0pQSEVSRFJmMXpENm0wRWRuYXNxcStJK1M5bS9EdnoxMXpYeDJnVXNaMmgzS0htOTVNbWlMNVMyZms5RVhoWkxhSlVJR0l5ZEs4YnFGSXdFRFo5NDN5ZGh3VnhKRVREN3pYS3FIUms2T2ZuZm5mZjdPWGgxU2tQa09xREpycjMwQlBvNDM5TGNlNThtOXN0ejVHRis1TTBZcXY0YlU0c2lLZjdObis3eHoxMU51dFdyalBiWHRieWdjdUd2eFdDNzVlZnZXMXlUTGFmMm9vRVlMNndjcDlTU1hiOEVyZW1PSWswLzI2TzdXQ3BVRUdKNmFNZjZkNzh6YkVTY1pXaHB3QUhjWjEyeDd1OEVtMXowL0NRN3FTRjhzS21Oa1BqdkFqRUVnZnNRSmxoWXBIem1hV0NEUEUyc24iLCJtYWMiOiJlOTY0Y2ZmMmQ0NGRlMDk0YmEzYTc3ODY4Y2M5NjgxZjQxZmNlMzFmM2UyNzIwMmQ5NmQyZmFjYTE2NTM0MmY5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:09:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353530738\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-236513113 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/product-unit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236513113\", {\"maxDepth\":0})</script>\n"}}