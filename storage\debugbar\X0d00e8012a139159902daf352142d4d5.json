{"__meta": {"id": "X0d00e8012a139159902daf352142d4d5", "datetime": "2025-07-30 08:01:59", "utime": **********.285965, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753862516.005819, "end": **********.28602, "duration": 3.280200958251953, "duration_str": "3.28s", "measures": [{"label": "Booting", "start": 1753862516.005819, "relative_start": 0, "end": 1753862518.172091, "relative_end": 1753862518.172091, "duration": 2.166271924972534, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753862518.172123, "relative_start": 2.166303873062134, "end": **********.286026, "relative_end": 5.9604644775390625e-06, "duration": 1.1139030456542969, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665256, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01107, "accumulated_duration_str": "11.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1665199, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 46.612}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.202564, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 46.612, "width_percent": 11.472}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21992, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 58.085, "width_percent": 21.138}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.237244, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 79.223, "width_percent": 20.777}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1866255426 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1866255426\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-382360178 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382360178\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-681304408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-681304408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImtKSTF6ZmIwb2dOQndqTnU5a1FrTkE9PSIsInZhbHVlIjoiY1kwU1hiUGNjczhTRHZlamF0VVpkejViTzRQVEE3UTFQWnA1bG5nTDJyN1Q0YXU4a2VjcnlMWWFYbk9YMFgxbURwSjV6S0t2Q1c3dVBYSGFrRFQ5Wll6SUF3MkN4UVlCNDNOMXVNNzU4RlBFSDZqMU5TWDBDSWhuUlNVczJBM3BFNytuQ0hXYU5hRE9CbFdPcHgrSVhYRGh5WFZOb2lYcFZwcmJkWEJ2bDN2azR1em9SczEzZkVycXBRNjRFZFRXZm9QNWE2eVJ1ZVlRcWtpeHozYWVqeXhjMTd3WjlnMHdxVzZxa1lNU3Byd3o2TUoyZ2NKVGZTQWg0SnNEN3Y2NVg0dlNGb0h2S0VKS2ZwWnpIeHIxVkMzT2JkVkpiUDc5ZHMybmVMd1lLWC83NlprSm5HNDlyVXhGK2FDMlQzdlMvVGtTallmMWpmREsrU2x3Wjd3aXZvT2xYVXpra0JxS1Eyc29Kd0NsYmd2RXRPYXViUVJzOUtTQVI1NWtPMTNQanlOczVQMHVKVU5OUkdVa0x1dHMzcjlVNmxEL1BXemhEYnZWRGZaNSttc1NBclJiZng3MkxOeCtaM2w0VVdOcGtjWkpJTndjZWxQdzVNRnFaeEoyWHdkdkhGVUJkN0sxcUEwS3Z6NjhlQ0tuUnV4dVd4UGJIZFkxRWplVnIrTFgiLCJtYWMiOiI0MTRjZTcxZWI1ZGVhN2U5YWMwZTc2NGQ0ODM3NmUzMjhkMTZlYzQ1MzY2NDFlNTM4YzcwYTNkMWIwYjcxNmQ5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpEMkxBU2RqTnozVTBVakhiL1BaZkE9PSIsInZhbHVlIjoiL3M1NTJrYzNkZllSbmNPN2VLWDk2RVBaY0RTUXhYR000UFpwdkhYR1p1bDlsblpxdFJRajNhNmZ4YXVlM1pCbnR2dmM5b3F5ajlZU3FxWTdsTGwzeHVsRlR4ZkZ5TWp4R3dxNmc5VDgxN3JTbEtyRk9Fd1ZQZzR6R1NYR3NpYW82YXp1OE5UTHpMMVpZb2xHbEROTHRTaDc5Z3ZVTlVqL1MvMithQWZFM2RVUlJrVHBZK0tUMSt5Y21BZk0zL29saEJpWFdRaDNRZ2dPVm00QkM5WkdYZzBEclYxYldDc3ZoZ1VhTzJKQmZBZmpIOG9vT2NGNTdqd0NJNFRsSmwyTE9LZ05ncm5vQW1QSWludy9FYW9VeWJld1Y1eTFNb1hVSUxsM1BQTjlSbjd3VGlublVSWDVVNzQ2K3RMRHh6aTlxcUY2eXZ3cjVvdVlUQjEwd0h6dWFUYUtaYitCYXdTWlNGSVJEVVZ4cjgyTXlqdytHUis0YTlsUVlpM3RvM3pESGlzeHBGdHJQeGkrV3NmRzNiNUFNR29tRjFaVFQ5MWhvdUtaNmJVWUxTMHBsK1g2RGMzQnNQTmR0WWRRNEtSRnhqcDNTSWc3NzJZc0YwOFRhODRMNWhMdG9veG9Ha2NGQTVhZVg5Vlc4ejJvQVREd25aZk41bG1tUlcvdGlTVGwiLCJtYWMiOiIxZmU3ZmFjN2Q5OGVmMjE1OTc2YWEwMWUxNWUwMzcyM2NhYWU2ZDA2NTJjNmMwYjM0MmM4OTkwMWI1ZDIzNTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1440542244 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1440542244\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-218405635 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 08:01:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVRazNuaGlrc1dZeVBNd3A2T1J2R3c9PSIsInZhbHVlIjoidklqVXFTbExhdi9WY3M1NVYwNkN4L1FkSkZxcmNwMHl4Ly93d3JDZXlzTnpJZjhCbHd6dEV3azlwMzhwQk1lWlNpWjR0dGdIWnBjYzVxZXhnVnk5RkZFTnBmbXlxNkZDaGY4bFBlRjczTVN3OXc3ZnFHek5RTjFhbEoyWTU3S1ZtckFGOU9STTUxRTJiYmdxM1V0WmIxNGo1UWFjN3hJdGVtVk1IanR2dWQ5emxIOFh6MUVNeEV1dmkwK084cUozUHBBQWI3dlF5WlB1aUhtTmVsdUs4WGtKV2JwTEVvaE1JUXZVdjVxREVQZCsyVm4ralVlaEIvQ3N0RG9xQWloQnU3SVlTdVUzZ1JRWmY4ZUI2eXRleEEySERSOC9NWDBBdi81ZXZvWUY1ZGhxS3EzWTYwZzNxb2M4LzNFYW9LMytVWHhuUDNReDVJSnVOM0lwb1JRUm5rWUxiM0FiVElvOG50emliNjk2Snk4eUFUTk5ibWJNY0doK2lpS2xoTklBbkVvZkV3RjAzUGVnY0dKcUVtUFZ0L3k2RlZSSGkyMm4rTktsQk0yeEJEdCsxcmM0RERIWTRDL2I2blpMNkZkL1d3WW5VR3IySTkzWC9tcVkvazJPQWxUYk5QS3FPUnRINTdaVFBuTHFwaW1vbjU5Z3FSY0U4SHBwMVo3ZXVVcDEiLCJtYWMiOiJmMWM0YjA5YjBkNjVjMzNjYTJhYjRhNzk4NDE5ZjUxZmJlMWNmMzA1ZTQyMzE5Yjc4NmMwZmEwMDdiZDBlN2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImoxOXVFcnk2SXFNbWMzaHp5ZzdJT3c9PSIsInZhbHVlIjoiZGk0b0Y1VjhqTjNpWWFCUVJyOWI1VTVtSnFxTjZ2UjFWdVRYNzdQMjNoSE5TMU1QcVBFb1F4Q1FXck0rTk00VEQxRUtQSCtEcDViR1hWNHFMRzhYaFd1ay9VREFGV2xUM05NdG1tSTB4RE9YZzR4Wk0rbnJ2ZjRQc0RGaWd3cStDQjVTYXpTTWRzVVJkN0F2TjFSbzJuSTlWVjBhSzRWUWhMckF2bG9MN05YeGZoVUwweGJsRy9XRE9aUHN1WDJCUEF6M0FhMWIwNFhrNjlXYVNVVWp6K3BsajVVc1huY09GdDM4SE83YzYvZHVOUC94c21XZERxWEZ5Z0hIZFRIZzlqdUN6NktKdGNCdDJ0aFFrdUlVbWtiT3lMQVR5U05lWmZHdjAwcFNiS0ZKZThGRVdzSzNLNVdCT1piZEtsNkVhUElOOE9ZMFZoRy85SGNpc1FuMGI5WmhYci9td0lZbDVLVTVpS3dUSSt1bGt1QnphZW1ObFB6TGd3bElqRkhhYmxXTG1XcFBCMEJMMVM2dVRZWDFuTnh0VTAyQW9UanpKRndCdHdhVmNSRGlJSW9zRCtTY1QwS2g5Vmd5MFl5TmtDTW5qN2ZSYlczdE5iUk1Ra2pqaDhORDlMdDBGNE5uVTk2NDFHY2NIMGtJc3VydktQUkVBM1h0bW8rby83dGYiLCJtYWMiOiJhNTM4NjhlMGM2ZGM0ZDQxMWZhOWY3MDRiYWRmZDc0NTdlM2ZjYjY0ZDNlZjI2ZGQxMTMwMWY3MWJhZmNiMGRhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 10:01:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVRazNuaGlrc1dZeVBNd3A2T1J2R3c9PSIsInZhbHVlIjoidklqVXFTbExhdi9WY3M1NVYwNkN4L1FkSkZxcmNwMHl4Ly93d3JDZXlzTnpJZjhCbHd6dEV3azlwMzhwQk1lWlNpWjR0dGdIWnBjYzVxZXhnVnk5RkZFTnBmbXlxNkZDaGY4bFBlRjczTVN3OXc3ZnFHek5RTjFhbEoyWTU3S1ZtckFGOU9STTUxRTJiYmdxM1V0WmIxNGo1UWFjN3hJdGVtVk1IanR2dWQ5emxIOFh6MUVNeEV1dmkwK084cUozUHBBQWI3dlF5WlB1aUhtTmVsdUs4WGtKV2JwTEVvaE1JUXZVdjVxREVQZCsyVm4ralVlaEIvQ3N0RG9xQWloQnU3SVlTdVUzZ1JRWmY4ZUI2eXRleEEySERSOC9NWDBBdi81ZXZvWUY1ZGhxS3EzWTYwZzNxb2M4LzNFYW9LMytVWHhuUDNReDVJSnVOM0lwb1JRUm5rWUxiM0FiVElvOG50emliNjk2Snk4eUFUTk5ibWJNY0doK2lpS2xoTklBbkVvZkV3RjAzUGVnY0dKcUVtUFZ0L3k2RlZSSGkyMm4rTktsQk0yeEJEdCsxcmM0RERIWTRDL2I2blpMNkZkL1d3WW5VR3IySTkzWC9tcVkvazJPQWxUYk5QS3FPUnRINTdaVFBuTHFwaW1vbjU5Z3FSY0U4SHBwMVo3ZXVVcDEiLCJtYWMiOiJmMWM0YjA5YjBkNjVjMzNjYTJhYjRhNzk4NDE5ZjUxZmJlMWNmMzA1ZTQyMzE5Yjc4NmMwZmEwMDdiZDBlN2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImoxOXVFcnk2SXFNbWMzaHp5ZzdJT3c9PSIsInZhbHVlIjoiZGk0b0Y1VjhqTjNpWWFCUVJyOWI1VTVtSnFxTjZ2UjFWdVRYNzdQMjNoSE5TMU1QcVBFb1F4Q1FXck0rTk00VEQxRUtQSCtEcDViR1hWNHFMRzhYaFd1ay9VREFGV2xUM05NdG1tSTB4RE9YZzR4Wk0rbnJ2ZjRQc0RGaWd3cStDQjVTYXpTTWRzVVJkN0F2TjFSbzJuSTlWVjBhSzRWUWhMckF2bG9MN05YeGZoVUwweGJsRy9XRE9aUHN1WDJCUEF6M0FhMWIwNFhrNjlXYVNVVWp6K3BsajVVc1huY09GdDM4SE83YzYvZHVOUC94c21XZERxWEZ5Z0hIZFRIZzlqdUN6NktKdGNCdDJ0aFFrdUlVbWtiT3lMQVR5U05lWmZHdjAwcFNiS0ZKZThGRVdzSzNLNVdCT1piZEtsNkVhUElOOE9ZMFZoRy85SGNpc1FuMGI5WmhYci9td0lZbDVLVTVpS3dUSSt1bGt1QnphZW1ObFB6TGd3bElqRkhhYmxXTG1XcFBCMEJMMVM2dVRZWDFuTnh0VTAyQW9UanpKRndCdHdhVmNSRGlJSW9zRCtTY1QwS2g5Vmd5MFl5TmtDTW5qN2ZSYlczdE5iUk1Ra2pqaDhORDlMdDBGNE5uVTk2NDFHY2NIMGtJc3VydktQUkVBM1h0bW8rby83dGYiLCJtYWMiOiJhNTM4NjhlMGM2ZGM0ZDQxMWZhOWY3MDRiYWRmZDc0NTdlM2ZjYjY0ZDNlZjI2ZGQxMTMwMWY3MWJhZmNiMGRhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 10:01:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218405635\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2029549031 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029549031\", {\"maxDepth\":0})</script>\n"}}